version: "3"
services:
  redis-server:
    image: redis
    container_name: redis-server
    ports:
      - "6379:6379"
  db:
    image: postgres
    restart: always
    # set shared memory limit when using docker-compose
    shm_size: 128mb
    # or set shared memory limit when deploy via swarm stack
    #volumes:
    #  - type: tmpfs
    #    target: /dev/shm
    #    tmpfs:
    #      size: 134217728 # 128*2^20 bytes = 128Mb
    environment:
      TZ: Asia/Shanghai
      POSTGRES_PASSWORD: 123456
      POSTGRES_USER: root
      POSTGRES_DB: ai-agent
    ports:
      - "5432:5432"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./temp/postgres:/var/lib/postgresql/data:rw
