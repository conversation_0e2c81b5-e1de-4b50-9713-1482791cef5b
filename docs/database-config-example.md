# 数据库配置示例

## 🗄️ OIDC专用GitHub App配置

### 直接SQL配置方式

```sql
-- 1. 配置OIDC专用GitHub App ID
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.id', '你的OIDC_APP_ID', 0, 'any')
ON CONFLICT (config_key, env_tag) 
DO UPDATE SET config_value = EXCLUDED.config_value, gmt_modified = CURRENT_TIMESTAMP;

-- 2. 配置OIDC专用GitHub App私钥（自动加密）
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.config.你的OIDC_APP_ID', '-----BEGIN RSA PRIVATE KEY-----
你的OIDC专用GitHub App私钥内容
-----END RSA PRIVATE KEY-----', 1, 'any')
ON CONFLICT (config_key, env_tag) 
DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;

-- 3. 配置OIDC专用GitHub App Client ID（可选）
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.client-id.你的OIDC_APP_ID', 'Iv1.你的OIDC_CLIENT_ID', 0, 'any')
ON CONFLICT (config_key, env_tag) 
DO UPDATE SET config_value = EXCLUDED.config_value, gmt_modified = CURRENT_TIMESTAMP;

-- 4. 配置OIDC专用GitHub App Client Secret（可选，自动加密）
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.client-secret.你的OIDC_APP_ID', '你的OIDC_CLIENT_SECRET', 1, 'any')
ON CONFLICT (config_key, env_tag) 
DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;

-- 5. 配置OIDC专用GitHub App Webhook Secret（可选，自动加密）
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.webhook-secret.你的OIDC_APP_ID', '你的OIDC_WEBHOOK_SECRET', 1, 'any')
ON CONFLICT (config_key, env_tag) 
DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;
```

### 配置说明

- **替换占位符**：将 `你的OIDC_APP_ID` 等替换为实际值
- **encrypt=1**：敏感信息（私钥、密钥）会自动加密存储
- **encrypt=0**：非敏感信息（App ID、Client ID）明文存储
- **env_tag='any'**：适用于所有环境，也可以设置为 'prod', 'dev' 等

## 🔍 验证配置

### 查询OIDC配置
```sql
-- 查看OIDC App ID
SELECT config_key, config_value, env_tag 
FROM t_config 
WHERE config_key = 'github.oidc.app.id';

-- 查看OIDC配置列表（私钥会显示为加密状态）
SELECT 
    config_key,
    CASE WHEN encrypt = 1 THEN '[已加密]' ELSE config_value END as display_value,
    encrypt,
    env_tag,
    gmt_modified
FROM t_config 
WHERE config_key LIKE 'github.oidc.app.%'
ORDER BY config_key;
```

### 查看所有GitHub配置
```sql
-- 对比传统配置和OIDC配置
SELECT 
    CASE 
        WHEN config_key LIKE 'github.oidc.app.%' THEN 'OIDC专用'
        WHEN config_key LIKE 'github.app.%' THEN '传统配置'
        ELSE '其他'
    END as config_type,
    config_key,
    CASE WHEN encrypt = 1 THEN '[已加密]' ELSE config_value END as display_value,
    env_tag
FROM t_config 
WHERE "group" = 'github'
ORDER BY config_type, config_key;
```

## 🛡️ 安全注意事项

### 1. 私钥安全
- 私钥内容会自动加密存储（encrypt=1）
- 不要在日志或其他地方明文记录私钥
- 定期轮换私钥

### 2. 数据库访问控制
- 限制对 `t_config` 表的访问权限
- 使用专用的数据库用户进行配置管理
- 启用数据库审计日志

### 3. 环境隔离
```sql
-- 生产环境配置
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.id', '生产环境APP_ID', 0, 'prod');

-- 开发环境配置
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.id', '开发环境APP_ID', 0, 'dev');
```

## 🔧 配置管理

### 删除OIDC配置
```sql
-- 删除特定App的所有OIDC配置
DELETE FROM t_config 
WHERE config_key LIKE 'github.oidc.app.%.你的OIDC_APP_ID' 
AND env_tag = 'any';

-- 删除OIDC App ID配置
DELETE FROM t_config 
WHERE config_key = 'github.oidc.app.id' 
AND env_tag = 'any';
```

### 更新配置
```sql
-- 更新OIDC App ID
UPDATE t_config 
SET config_value = '新的OIDC_APP_ID', gmt_modified = CURRENT_TIMESTAMP
WHERE config_key = 'github.oidc.app.id' AND env_tag = 'any';

-- 更新私钥（会自动重新加密）
UPDATE t_config 
SET config_value = '新的私钥内容', gmt_modified = CURRENT_TIMESTAMP
WHERE config_key = 'github.oidc.app.config.你的OIDC_APP_ID' AND env_tag = 'any';
```

## 📋 配置检查清单

配置完成后，请确认：

- [ ] OIDC App ID已设置：`github.oidc.app.id`
- [ ] OIDC私钥已配置并加密：`github.oidc.app.config.{appId}`
- [ ] 配置的env_tag正确（通常为'any'）
- [ ] 私钥格式正确（包含BEGIN/END标记）
- [ ] GitHub App已安装到目标仓库
- [ ] 服务重启后配置生效

## 🚀 快速配置脚本

如果你更喜欢使用脚本：

```bash
# 使用交互式脚本配置
./scripts/setup-github-app.sh setup-oidc-app

# 或者查看现有配置
./scripts/setup-github-app.sh list-apps
```

这样就完成了OIDC专用GitHub App的配置，与现有配置完全隔离，确保向后兼容性。
