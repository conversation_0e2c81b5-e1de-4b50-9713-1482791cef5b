# GitHub Actions OIDC Token Exchange - 快速启动指南

## 🚀 快速启动

### 1. 准备工作

#### 创建GitHub App
1. 访问 GitHub Settings > Developer settings > GitHub Apps
2. 点击 "New GitHub App"
3. 填写基本信息：
   - App name: 你的应用名称
   - Homepage URL: 你的服务地址
   - Webhook URL: `https://your-domain.com/v1/webhook/github` (如果需要)
4. 设置权限：
   - Repository permissions:
     - Contents: Read
     - Metadata: Read
     - Pull requests: Write (如果需要)
     - Issues: Write (如果需要)
5. 生成私钥并下载 `.pem` 文件
6. 记录 App ID

#### 安装GitHub App
1. 在GitHub App设置页面，点击 "Install App"
2. 选择要安装的组织或个人账户
3. 选择要授权的仓库

### 2. 配置服务

#### 编译服务
```bash
go build -o agent-controller ./main.go
```

#### 配置数据库
```bash
# 使用SQLite（开发环境）
cp config/demo-safe.toml config/server.toml

# 或配置PostgreSQL（生产环境）
# 编辑 config/server.toml 中的数据库配置
```

#### 配置GitHub App（安全方式）
```bash
# 使用配置脚本
./scripts/setup-github-app.sh setup-app

# 按提示输入：
# - GitHub App ID
# - 私钥文件路径
# - Client ID（可选）
# - Client Secret（可选）
# - Webhook Secret（可选）
```

#### 启动服务
```bash
./agent-controller server --config config/server.toml
```

### 3. 测试API

#### 健康检查
```bash
curl http://localhost:8080/v1/github-actions/health
```

#### 获取支持的权限
```bash
curl http://localhost:8080/v1/github-actions/permissions
```

#### 测试令牌交换（需要真实OIDC令牌）
```bash
curl -X POST http://localhost:8080/v1/github-actions/token-exchange \
  -H "Content-Type: application/json" \
  -d '{
    "oidc_token": "真实的OIDC令牌",
    "repository": "owner/repo",
    "permissions": {
      "contents": "read",
      "pull_requests": "write"
    }
  }'
```

### 4. GitHub Actions集成

#### 配置Workflow
```yaml
name: Use Installation Token
on: [push]

permissions:
  id-token: write  # 必须设置

jobs:
  example:
    runs-on: ubuntu-latest
    steps:
      - name: Get Installation Token
        id: get-token
        run: |
          # 获取OIDC令牌
          OIDC_TOKEN=$(curl -H "Authorization: bearer $ACTIONS_ID_TOKEN_REQUEST_TOKEN" \
            "$ACTIONS_ID_TOKEN_REQUEST_URL" | jq -r '.value')
          
          # 交换Installation Token
          RESPONSE=$(curl -X POST https://your-domain.com/v1/github-actions/token-exchange \
            -H "Content-Type: application/json" \
            -d "{
              \"oidc_token\": \"$OIDC_TOKEN\",
              \"repository\": \"${{ github.repository }}\",
              \"permissions\": {
                \"contents\": \"read\",
                \"pull_requests\": \"write\"
              }
            }")
          
          TOKEN=$(echo $RESPONSE | jq -r '.token')
          echo "::add-mask::$TOKEN"
          echo "token=$TOKEN" >> $GITHUB_OUTPUT

      - name: Use Installation Token
        run: |
          # 使用Installation Token调用GitHub API
          curl -H "Authorization: token ${{ steps.get-token.outputs.token }}" \
            https://api.github.com/repos/${{ github.repository }}/issues
```

## 🔧 管理命令

### 查看已配置的GitHub App
```bash
./scripts/setup-github-app.sh list-apps
```

### 删除GitHub App配置
```bash
./scripts/setup-github-app.sh remove-app
```

### 测试数据库连接
```bash
./scripts/setup-github-app.sh test-config
```

## 🛡️ 安全最佳实践

1. **私钥管理**
   - 私钥存储在数据库中并自动加密
   - 不要将私钥提交到版本控制系统
   - 定期轮换私钥

2. **网络安全**
   - 使用HTTPS部署服务
   - 配置防火墙限制访问
   - 启用访问日志和监控

3. **权限控制**
   - 只授予必要的最小权限
   - 定期审查GitHub App的权限
   - 监控Installation Token的使用

4. **环境隔离**
   - 使用不同的环境标签（env_tag）
   - 开发、测试、生产环境使用不同的GitHub App

## 🐛 故障排除

### 常见问题

1. **OIDC令牌验证失败**
   - 检查GitHub Actions是否设置了 `permissions.id-token: write`
   - 确认OIDC令牌未过期
   - 验证仓库名称格式正确

2. **Installation Token创建失败**
   - 确认GitHub App已安装到目标仓库
   - 检查GitHub App的权限配置
   - 验证私钥配置正确

3. **数据库连接问题**
   - 检查数据库配置
   - 确认数据库服务正在运行
   - 验证网络连接

### 日志查看
```bash
# 查看服务日志
tail -f logs/agent-controller.log

# 查看特定操作的日志
grep "OIDC token exchange" logs/agent-controller.log
```

## 📚 更多文档

- [详细API文档](github-actions-oidc.md)
- [安全配置指南](security-guide.md)
- [部署指南](deployment-guide.md)
