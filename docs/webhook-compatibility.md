# Webhook兼容性说明

## 🔄 统一Webhook处理

好消息！你的OIDC专用GitHub App**可以使用现有的webhook端点**，无需额外配置。

## 📡 Webhook配置

### 统一端点
```
POST https://your-domain.com/v1/github/webhook
```

### 支持的App类型
- ✅ **传统GitHub App**: 使用 `github.app.config.*` 配置
- ✅ **OIDC专用GitHub App**: 使用 `github.oidc.app.config.*` 配置

## 🔍 自动识别机制

系统会自动识别GitHub App类型：

```go
// 检查传统配置
appConfig := config.GetGithubAppConfig(appId)
isTraditionalApp := appConfig != ""

// 检查OIDC配置  
oidcAppConfig := config.GetGithubOIDCAppConfig(appId)
isOIDCApp := oidcAppConfig != ""
```

## 📋 处理差异

### 传统GitHub App安装
```
installation created → 创建Agent → 创建Identity → 存储到数据库
installation deleted → 删除Agent → 删除Identity → 清理数据库
```

### OIDC专用GitHub App安装
```
installation created → 记录日志 → 无需创建持久化记录
installation deleted → 记录日志 → 无需清理操作
```

## 🛠️ 配置步骤

### 1. 配置OIDC专用GitHub App
```sql
-- 配置OIDC App ID
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.id', '你的OIDC_APP_ID', 0, 'any');

-- 配置OIDC App私钥
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.config.你的OIDC_APP_ID', '你的私钥', 1, 'any');
```

### 2. 配置GitHub App Webhook
在GitHub App设置中配置：
- **Webhook URL**: `https://your-domain.com/v1/github/webhook`
- **Webhook Secret**: 可选，如果设置需要配置到数据库
- **Events**: 选择 `Installation` 事件

### 3. 验证配置
安装GitHub App后，查看日志：
```bash
# 查看安装日志
grep "GitHub App installation" logs/agent-controller.log

# 应该看到类似输出：
# processing GitHub App installation appId=789012 appType=oidc action=created
# OIDC GitHub App installation created appId=789012 installationId=12345
```

## 🔍 日志示例

### OIDC App安装成功
```
[INFO] processing GitHub App installation appId=789012 appType=oidc action=created
[INFO] OIDC GitHub App installation created appId=789012 installationId=12345 username=myorg
[INFO] OIDC App installation recorded, no identity created
```

### 传统App安装成功
```
[INFO] processing GitHub App installation appId=123456 appType=traditional action=created
[INFO] traditional GitHub App installation, creating agent and identity
[INFO] agent created agentId=agent_xxx
[INFO] identity created identityId=identity_xxx
```

### 配置错误
```
[ERROR] GitHub App not configured appId=999999
```

## 🚨 注意事项

### 1. App ID唯一性
- 每个GitHub App有唯一的App ID
- 系统根据App ID自动识别类型
- 不能同时配置传统和OIDC配置使用相同的App ID

### 2. Webhook Secret（可选）
如果GitHub App配置了Webhook Secret，需要在数据库中配置：
```sql
-- 传统App
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.app.webhook-secret.123456', 'secret', 1, 'any');

-- OIDC App  
INSERT INTO t_config ("group", config_key, config_value, encrypt, env_tag) 
VALUES ('github', 'github.oidc.app.webhook-secret.789012', 'secret', 1, 'any');
```

### 3. 权限要求
OIDC专用GitHub App需要的最小权限：
- **Repository permissions**:
  - Contents: Read
  - Metadata: Read
  - Pull requests: Write (如果需要)
- **Account permissions**: 无特殊要求

## 🔧 故障排除

### 问题1: Webhook接收失败
```bash
# 检查webhook端点是否可访问
curl -X POST https://your-domain.com/v1/github/webhook \
  -H "X-GitHub-Event: ping" \
  -d '{"zen": "test"}'
```

### 问题2: App配置未找到
```bash
# 检查配置是否存在
./scripts/setup-github-app.sh list-apps

# 或直接查询数据库
SELECT config_key, encrypt FROM t_config 
WHERE config_key LIKE 'github.oidc.app.%';
```

### 问题3: 权限不足
确认GitHub App已安装到目标仓库：
1. 访问 GitHub Settings > Applications > Installed GitHub Apps
2. 确认App已安装并授权到正确的仓库

## ✅ 验证清单

安装完成后，确认：
- [ ] GitHub App已创建并配置
- [ ] OIDC配置已添加到数据库
- [ ] Webhook URL已配置到GitHub App
- [ ] GitHub App已安装到目标仓库
- [ ] 安装webhook已成功接收（查看日志）
- [ ] OIDC令牌交换API正常工作

## 🎯 总结

通过扩展现有的installation handler，你的OIDC专用GitHub App可以：

1. ✅ **复用现有webhook端点** - 无需额外配置
2. ✅ **自动识别App类型** - 根据配置自动处理
3. ✅ **向后兼容** - 不影响现有传统App
4. ✅ **简化部署** - 统一的webhook处理逻辑

这样既满足了你的需求，又保持了系统的一致性和可维护性！
