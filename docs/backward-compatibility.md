# 向后兼容性说明

## 🔄 兼容性设计

为了确保新的GitHub Actions OIDC功能不影响现有的GitHub App配置和代码，我们采用了**完全隔离**的配置方案。

## 📊 配置对比

### 现有配置（保持不变）
```
传统GitHub App配置键：
- github.default.app.id              # 默认App ID
- github.app.config.{appId}          # App私钥
- github.app.client-id.{appId}       # Client ID
- github.app.client-secret.{appId}   # Client Secret
- github.app.webhook-secret.{appId}  # Webhook Secret
```

### 新增OIDC配置（完全独立）
```
OIDC专用GitHub App配置键：
- github.oidc.app.id                    # OIDC专用App ID
- github.oidc.app.config.{appId}        # OIDC App私钥
- github.oidc.app.client-id.{appId}     # OIDC Client ID
- github.oidc.app.client-secret.{appId} # OIDC Client Secret
- github.oidc.app.webhook-secret.{appId} # OIDC Webhook Secret
```

## 🛡️ 隔离机制

### 1. 配置键隔离
- 传统配置：`github.app.*`
- OIDC配置：`github.oidc.app.*`
- 完全不同的命名空间，避免冲突

### 2. 函数隔离
- 传统函数：`GetGithubAppConfig()`, `newAppClient()`
- OIDC函数：`GetGithubOIDCAppConfig()`, `newOIDCAppClient()`
- 独立的函数调用链，互不影响

### 3. 客户端隔离
- 传统客户端：使用传统配置创建GitHub客户端
- OIDC客户端：使用OIDC配置创建GitHub客户端
- 不同的App ID和私钥，完全独立

## 📝 配置步骤

### 1. 配置OIDC专用GitHub App

```bash
# 使用专用命令配置OIDC App
./scripts/setup-github-app.sh setup-oidc-app

# 输入你的新GitHub App信息：
# - OIDC GitHub App ID: 789012
# - 私钥文件路径: /path/to/oidc-app-private-key.pem
# - Client ID, Secret等（可选）
```

### 2. 验证配置

```bash
# 查看所有配置
./scripts/setup-github-app.sh list-apps

# 输出示例：
# 传统GitHub App配置:
# ====================
# app_id | private_key_status | env_tag | gmt_modified
# 123456 | [加密]             | any     | 2024-01-01 12:00:00
#
# OIDC专用GitHub App配置:
# =======================
# oidc_app_id | private_key_status | env_tag | gmt_modified
# 789012      | [加密]             | any     | 2024-01-01 12:30:00
#
# 默认配置:
# ==========
# 传统默认App ID: 123456
# OIDC专用App ID: 789012
```

## 🔍 代码路径分析

### 传统GitHub App代码路径
```
现有功能 → config.GetGithubAppConfig() → newAppClient() → GitHub API
```

### OIDC功能代码路径
```
OIDC功能 → config.GetGithubOIDCAppConfig() → newOIDCAppClient() → GitHub API
```

### 完全独立的调用链
- 传统功能继续使用 `github.default.app.id` 和 `github.app.config.*`
- OIDC功能使用 `github.oidc.app.id` 和 `github.oidc.app.config.*`
- 两套配置互不干扰

## ✅ 兼容性保证

### 1. 现有功能不受影响
- 所有现有的GitHub App功能继续正常工作
- 现有的配置键和值保持不变
- 现有的代码逻辑完全不变

### 2. 新功能完全独立
- OIDC功能使用独立的配置和代码路径
- 新增的API端点不影响现有端点
- 新的GitHub App可以与现有App并存

### 3. 部署兼容性
- 可以在现有部署上直接升级
- 不需要修改现有配置
- 只需要添加新的OIDC配置即可

## 🚀 升级步骤

### 1. 代码升级
```bash
# 拉取最新代码
git pull origin main

# 编译新版本
go build -o agent-controller ./main.go
```

### 2. 配置OIDC App
```bash
# 配置新的OIDC专用GitHub App
./scripts/setup-github-app.sh setup-oidc-app
```

### 3. 重启服务
```bash
# 停止现有服务
pkill agent-controller

# 启动新版本
./agent-controller server --config config/server.toml
```

### 4. 验证功能
```bash
# 验证传统功能仍然正常
curl http://localhost:8080/v1/webhook/github

# 验证新的OIDC功能
curl http://localhost:8080/v1/github-actions/health
```

## 🔧 故障排除

### 如果OIDC功能不工作
1. 检查OIDC配置是否正确：
   ```bash
   ./scripts/setup-github-app.sh list-apps
   ```

2. 确认OIDC App ID已设置：
   ```sql
   SELECT * FROM t_config WHERE config_key = 'github.oidc.app.id';
   ```

3. 验证OIDC App私钥已配置：
   ```sql
   SELECT config_key, encrypt FROM t_config WHERE config_key LIKE 'github.oidc.app.config.%';
   ```

### 如果传统功能受影响
这种情况不应该发生，因为：
- 传统配置键完全没有变化
- 传统代码路径完全没有变化
- 如果出现问题，可能是其他原因

## 📋 配置检查清单

- [ ] 现有GitHub App配置保持不变
- [ ] 新的OIDC GitHub App已创建并安装
- [ ] OIDC配置已正确添加到数据库
- [ ] 服务重启后两套功能都正常工作
- [ ] 传统功能测试通过
- [ ] OIDC功能测试通过

## 🎯 总结

这个向后兼容的设计确保了：
1. **零风险升级**：现有功能完全不受影响
2. **独立配置**：新旧配置完全隔离
3. **渐进式采用**：可以逐步迁移到新功能
4. **回滚能力**：如有问题可以快速回滚

你可以放心地在生产环境中部署这个更新，现有的GitHub App功能将继续正常工作。
