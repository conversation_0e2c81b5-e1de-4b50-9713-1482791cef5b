# GitHub Actions OIDC Token Exchange

这个功能允许GitHub Actions通过OIDC令牌换取临时的GitHub App Installation Token，避免在Actions中存储长期凭证。

## 工作流程

1. GitHub Actions运行时，GitHub会自动提供OIDC令牌
2. Actions调用我们的API端点，提供OIDC令牌和所需权限
3. 服务验证OIDC令牌的合法性
4. 服务使用GitHub App私钥生成Installation Token
5. 返回临时的Installation Token给Actions

## API端点

### POST /v1/github-actions/token-exchange

交换OIDC令牌为Installation Token。

**请求体：**
```json
{
  "oidc_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6...",
  "repository": "owner/repo",
  "permissions": {
    "contents": "read",
    "pull_requests": "write",
    "issues": "read"
  },
  "repositories": ["owner/repo"]
}
```

**响应：**
```json
{
  "token": "ghs_1234567890abcdef...",
  "expires_at": "2024-01-01T12:00:00Z",
  "permissions": {
    "contents": "read",
    "pull_requests": "write",
    "issues": "read"
  },
  "repositories": [
    {
      "id": 123456,
      "name": "repo",
      "full_name": "owner/repo",
      "private": false
    }
  ]
}
```

## 配置

需要在配置中设置以下参数：

```toml
[github]
# 默认的GitHub App ID
"github.default.app.id" = "123456"

# GitHub App的私钥（PEM格式）
"github.app.config.123456" = """
-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA...
-----END RSA PRIVATE KEY-----
"""

# 是否启用OIDC功能
"github.oidc.enabled" = "true"

# OIDC令牌的audience（可选，用于额外验证）
"github.oidc.audience" = "your-service-audience"
```

## GitHub Actions使用示例

```yaml
name: Use Installation Token
on: [push]

permissions:
  id-token: write  # 必须设置，用于获取OIDC令牌

jobs:
  example:
    runs-on: ubuntu-latest
    steps:
      - name: Get Installation Token
        id: get-token
        run: |
          # 获取OIDC令牌
          OIDC_TOKEN=$(curl -H "Authorization: bearer $ACTIONS_ID_TOKEN_REQUEST_TOKEN" \
            "$ACTIONS_ID_TOKEN_REQUEST_URL&audience=your-service-audience" | jq -r '.value')
          
          # 交换Installation Token
          RESPONSE=$(curl -X POST https://your-service.com/v1/github-actions/token-exchange \
            -H "Content-Type: application/json" \
            -d "{
              \"oidc_token\": \"$OIDC_TOKEN\",
              \"repository\": \"${{ github.repository }}\",
              \"permissions\": {
                \"contents\": \"read\",
                \"pull_requests\": \"write\"
              }
            }")
          
          TOKEN=$(echo $RESPONSE | jq -r '.token')
          echo "::add-mask::$TOKEN"
          echo "token=$TOKEN" >> $GITHUB_OUTPUT

      - name: Use Installation Token
        run: |
          # 使用Installation Token调用GitHub API
          curl -H "Authorization: token ${{ steps.get-token.outputs.token }}" \
            https://api.github.com/repos/${{ github.repository }}/issues
```

## 安全考虑

1. **OIDC令牌验证**：服务会验证OIDC令牌的签名、issuer、audience和过期时间
2. **仓库权限**：只能为OIDC令牌中指定的仓库生成Installation Token
3. **权限限制**：可以限制Installation Token的权限范围
4. **时效性**：Installation Token有过期时间（通常1小时）
5. **审计日志**：所有令牌交换操作都会记录日志

## 支持的权限

- `contents`: 仓库内容读写
- `metadata`: 仓库元数据读写
- `pull_requests`: PR读写
- `issues`: Issue读写
- `actions`: Actions读写
- `checks`: 检查读写
- `statuses`: 状态读写
- `deployments`: 部署读写
- `packages`: 包读写
- `pages`: Pages读写

每个权限可以设置为 `read` 或 `write`。

## 错误处理

常见错误码：

- `400`: 请求参数错误
- `401`: OIDC令牌无效或过期
- `403`: 权限不足或仓库不存在
- `404`: GitHub App安装未找到
- `500`: 服务内部错误

## 监控和调试

可以通过以下方式监控和调试：

1. 查看服务日志中的OIDC验证和令牌交换记录
2. 使用 `/v1/github-actions/validate-oidc` 端点验证OIDC令牌
3. 检查GitHub App的安装状态和权限配置
