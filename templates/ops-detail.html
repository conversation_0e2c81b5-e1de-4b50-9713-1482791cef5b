<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话详情 - 智能体排查系统</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script>
        // 初始化marked配置
        // 初始化marked配置
        document.addEventListener('DOMContentLoaded', function() {
            window.marked.setOptions({
                breaks: true,
                gfm: true
            });
        });
    </script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0;
            padding: 0;
            width: 100%;
        }

        * {
            box-sizing: border-box;
        }

        .container {
            flex: 1;
            width: 100%;
            height: 100%;
            padding: 16px;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            overflow-y: auto;
        }



        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        #events {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow-x: hidden;
            overflow-y: auto;
        }



        .events-container {
            flex: 1;
            width: 100%;
            overflow: auto;
            -webkit-overflow-scrolling: touch;
        }



        .events-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
            width: 100%;
            min-width: 800px;
        }

        .events-header {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border-radius: 8px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
        }

        .header-cell {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .time-cell {
            width: 180px;
        }

        .id-cell {
            width: 200px;
        }

        .type-cell {
            width: 120px;
        }

        .brief-cell {
            flex: 1;
        }

        .event-row {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .event-row:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .event-row.expanded {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .event-summary {
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 16px;
        }

        .event-header {
            display: flex;
            align-items: center;
            gap: 16px;
            flex: 1;
        }

        .event-time {
            color: #666;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .event-id {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .event-id code {
            font-family: monospace;
            color: #2196f3;
        }

        .event-brief {
            flex: 1;
            color: #333;
        }

        .toggle-icon {
            color: #666;
            transition: transform 0.3s ease;
        }

        .event-row.expanded .toggle-icon {
            color: #2196f3;
        }

        .event-detail {
            border-top: 1px solid #eee;
            padding: 16px;
            background: #f8f9fa;
        }

        .json-data {
            margin-top: 16px;
        }

        .json-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .json-header h4 {
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 0;
        }

        .copy-json-btn {
            background: #e3f2fd;
            color: #2196f3;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .copy-json-btn:hover {
            background: #bbdefb;
        }

        .copy-json-btn.copied {
            background: #c8e6c9;
            color: #4caf50;
        }

        /* 类型标签样式 */
        .event-type {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
            min-width: 100px;
            color: white;
        }

        /* 不同类型的颜色 */
        .event-type.runtime {
            background-color: #2196f3;
        }

        .event-type.agent {
            background-color: #4caf50;
        }

        .event-type.action {
            background-color: #ff9800;
        }

        .event-type.agent_task {
            background-color: #9c27b0;
        }

        .event-type.llm_message {
            background-color: #3f51b5;
        }

        /* Token 使用统计样式 */
        .token-usage-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .token-usage-info h4 {
            margin: 0 0 12px 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .model-usage {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 10px;
        }

        .model-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 10px;
        }

        .usage-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            flex: 1;
            min-width: 200px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
        }

        .stat-value {
            font-family: monospace;
            color: #2196f3;
        }

        .page-size-selector {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-size-selector select {
            padding: 6px 12px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            padding: 20px 0;
        }

        .pagination button {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 40px;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination button:hover:not(:disabled) {
            background: #f8f9fa;
        }

        .page-info {
            font-size: 14px;
            color: #666;
        }

        .main-tabs-container {
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .main-tabs {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
        }

        .tabs-group {
            display: flex;
            gap: 10px;
        }

        .main-tab {
            padding: 12px 24px;
            border: none;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .main-tab:hover {
            background: #e9ecef;
            color: #333;
        }

        .main-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .refresh-button {
            padding: 12px 24px;
            border: none;
            background: #f8f9fa;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .refresh-button:hover {
            background: #e9ecef;
            color: #333;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 16px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .tab-content.active {
            display: block;
        }

        .header .subtitle {
            color: #666;
            font-size: 1.1rem;
            font-weight: 400;
        }

        /* 返回按钮 */
        .back-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            margin-bottom: 30px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* 信息卡片 */
        .info-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: linear-gradient(135deg, #f8f9ff, #f0f2ff);
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .info-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
        }

        .info-content {
            flex: 1;
        }

        .info-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 4px;
            font-weight: 500;
        }

        .action-info .info-item {
            margin-bottom: 10px;
            line-height: 1.6;
        }

        .action-info .file-content {
            margin-top: 15px;
        }

        .action-info .code-block {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
            overflow-x: auto;
            font-family: Monaco, Consolas, 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        /* 评论相关样式 */
        .comment-info {
            padding: 15px;
        }

        .comment-status {
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .comment-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 15px;
            color: #666;
            font-size: 0.9em;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 4px 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }

        .meta-item i {
            font-size: 0.9em;
            color: #888;
        }

        .comment-status.success {
            background-color: #e3f9e5;
            color: #1b4400;
            border: 1px solid #a3e9a4;
        }

        .comment-status.failed {
            background-color: #fbe9e7;
            color: #c41d00;
            border: 1px solid #ffab91;
            flex-direction: column;
            align-items: flex-start;
        }

        .error-details {
            margin-top: 8px;
            font-size: 0.9em;
            padding: 8px 12px;
            background-color: rgba(255, 255, 255, 0.5);
            border-radius: 4px;
            width: 100%;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .error-details i {
            color: #c41d00;
        }

        .exit-info {
            padding: 15px;
        }

        .exit-status {
            padding: 15px;
            border-radius: 6px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .exit-status i {
            font-size: 1.5em;
            margin-bottom: 5px;
        }

        .exit-status.success {
            background-color: #e3f9e5;
            color: #1b4400;
            border: 1px solid #a3e9a4;
        }

        .exit-status.error {
            background-color: #fbe9e7;
            color: #c41d00;
            border: 1px solid #ffab91;
        }

        .status-text, .reason-text {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-value, .reason-value {
            font-weight: 500;
            padding: 2px 8px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 4px;
        }

        .review-info {
            padding: 15px;
        }

        .commit-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .sha-info {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .sha-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-family: monospace;
        }

        .sha-label {
            color: #666;
            font-weight: 500;
        }

        .sha-value {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 600;
        }

        .changes-info {
            margin-top: 20px;
        }

        .changes-info h4 {
            margin-bottom: 15px;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .change-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .file-path {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 8px;
            font-family: monospace;
        }

        .diff-content {
            margin: 0;
            padding: 15px;
            background: #fff;
            font-size: 0.9em;
            overflow-x: auto;
            white-space: pre;
        }

        .review-request-info {
            padding: 15px;
        }

        .request-meta {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .meta-label {
            color: #666;
            font-weight: 500;
        }

        .meta-value {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: 600;
        }

        .comments-section {
            margin-top: 20px;
        }

        .comments-section h4 {
            margin-bottom: 15px;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .comment-item {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .comment-item .comment-meta {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .get-comment-info {
            padding: 15px;
        }

        .comment-header {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .code-position-info {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .comments-thread {
            margin-top: 20px;
        }

        .comments-thread h4 {
            margin-bottom: 15px;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .thread-comment {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .thread-comment .comment-header {
            background: #f8f9fa;
            border: none;
            border-bottom: 1px solid #e9ecef;
            border-radius: 0;
            margin: 0;
        }

        .meta-item.success {
            color: #1b4400;
        }

        .meta-item.failed {
            color: #c41d00;
        }

        .username {
            font-weight: 600;
            color: #0366d6;
        }

        .update-comment-info {
            padding: 15px;
        }

        .error-message {
            background: #fbe9e7;
            color: #c41d00;
            border: 1px solid #ffab91;
            border-radius: 6px;
            padding: 12px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .comment-content-section {
            margin-top: 20px;
        }

        .comment-content-section h4 {
            margin-bottom: 15px;
            color: #495057;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .comment-content {
            background: #fff;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            overflow: hidden;
        }

        .markdown-content {
            padding: 20px;
            font-size: 14px;
            line-height: 1.6;
            color: #24292e;
        }

        /* Markdown 样式优化 */
        .markdown-content h1,
        .markdown-content h2,
        .markdown-content h3,
        .markdown-content h4,
        .markdown-content h5,
        .markdown-content h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }

        .markdown-content h2 {
            padding-bottom: 0.3em;
            border-bottom: 1px solid #eaecef;
        }

        .markdown-content blockquote {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
            margin: 0 0 16px 0;
        }

        .markdown-content pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f6f8fa;
            border-radius: 6px;
        }

        .markdown-content code {
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(27,31,35,0.05);
            border-radius: 6px;
        }

        .markdown-content table {
            border-spacing: 0;
            border-collapse: collapse;
            margin: 16px 0;
            width: 100%;
        }

        .markdown-content table th,
        .markdown-content table td {
            padding: 6px 13px;
            border: 1px solid #dfe2e5;
        }

        .markdown-content table tr:nth-child(2n) {
            background-color: #f6f8fa;
        }

        .markdown-content ul,
        .markdown-content ol {
            padding-left: 2em;
            margin-bottom: 16px;
        }

        .markdown-content details {
            margin: 16px 0;
        }

        .markdown-content summary {
            cursor: pointer;
            padding: 8px 0;
        }

        .info-value {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            font-family: 'Monaco', 'Menlo', monospace;
        }

        /* 主标签页 */
        .main-tabs-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        }

        .main-tabs {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        .tabs-group {
            display: flex;
            gap: 10px;
        }

        .main-tab {
            padding: 12px 24px;
            border: none;
            background: transparent;
            color: #666;
            font-size: 1rem;
            font-weight: 500;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .main-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .main-tab:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        /* 刷新按钮 */
        .refresh-button {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .refresh-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        /* 内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 子标签页 */
        .sub-tabs {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .sub-tab {
            padding: 15px 20px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 200px;
            backdrop-filter: blur(10px);
        }

        .sub-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .sub-tab:hover:not(.active) {
            border-color: #667eea;
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
        }

        .sub-tab small {
            display: block;
            margin-top: 5px;
            opacity: 0.8;
            font-size: 0.85rem;
        }

        /* 快照内容 */
        .snapshot-content {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 区块样式 */
        .section-block {
            margin-bottom: 30px;
            border-radius: 12px;
            overflow-x: hidden;
            overflow-y: auto;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .section-block h3 {
            padding: 20px 25px;
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .basic-info h3 {
            background: linear-gradient(135deg, #2196F3, #1976D2);
        }

        .metadata-info h3 {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
        }

        .actions-info h3 {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
        }

        .context-info h3 {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }

        .json-section {
            background: #f8f9fa;
            border-radius: 0 0 12px 12px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .json-section pre {
            margin: 0;
            padding: 25px;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.6;
            color: #2d3748;
            background: transparent;
            overflow-x: auto;
        }

        /* 动作项样式 */
        .action-item {
            background: #f8f9fa;
            margin: 15px 0;
            border-radius: 12px;
            overflow-x: hidden;
            overflow-y: auto;
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .action-item:hover {
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .action-header {
            padding: 20px 25px;
            cursor: pointer;
            background: white;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .action-header:hover {
            background: #f0f2ff;
        }

        .action-info {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: center;
        }

        .action-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .action-type {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .action-status {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
        }

        .action-time {
            color: #666;
            font-size: 0.9rem;
        }

        .toggle-icon {
            font-size: 1.2rem;
            color: #667eea;
            transition: transform 0.3s ease;
        }

        .toggle-icon.expanded {
            transform: rotate(90deg);
        }

        .action-detail {
            background: #f8f9fa;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }

        .action-detail pre {
            margin: 0;
            padding: 25px;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 13px;
            line-height: 1.6;
            color: #2d3748;
            background: transparent;
        }

        /* 事件表格样式 */
        .events-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .events-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
            background: white;
            border-radius: 12px;
            overflow-x: hidden;
            overflow-y: auto;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .events-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 0.95rem;
        }

        .events-table td {
            padding: 15px;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }

        .events-table tr:hover {
            background: rgba(102, 126, 234, 0.02);
        }

        .events-table tr:last-child td {
            border-bottom: none;
        }

        /* 类型标签 */
        .type-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .type-runtime .type-badge {
            background: linear-gradient(135deg, #2196F3, #1976D2);
            color: white;
        }

        .type-agent .type-badge {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
        }

        .type-action .type-badge {
            background: linear-gradient(135deg, #9C27B0, #7B1FA2);
            color: white;
        }

        .type-agent-task .type-badge {
            background: linear-gradient(135deg, #00BCD4, #0097A7);
            color: white;
        }

        .type-llm-message .type-badge {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
        }

        /* 详情按钮 */
        .detail-button {
            padding: 8px 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .detail-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .event-data {
            margin-top: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .event-data pre {
            margin: 0;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Consolas', monospace;
            font-size: 12px;
            line-height: 1.5;
            color: #2d3748;
            background: transparent;
            overflow-x: auto;
        }

        /* 分页器 */
        .pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            padding: 25px;
            background: linear-gradient(135deg, #f8f9ff, #f0f2ff);
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .pagination-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .pagination button {
            padding: 10px 16px;
            border: 1px solid #ddd;
            background: white;
            color: #333;
            cursor: pointer;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .pagination button:disabled {
            background: #f5f5f5;
            color: #999;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .pagination button:hover:not(:disabled) {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
        }

        /* MR Action 样式 */
        .mr-action {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 12px;
        }

        .mr-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .mr-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .mr-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .mr-status.open {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .mr-status.merged {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .mr-status.closed {
            background-color: #ffebee;
            color: #c62828;
        }

        .mr-info {
            margin-bottom: 16px;
        }

        .mr-info table,
        .file-info table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }

        .mr-info table td {
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            vertical-align: top;
        }

        .mr-info table td:first-child {
            width: 100px;
            background: #f8f9fa;
            color: #666;
            font-weight: 500;
        }

        .mr-info table td:last-child {
            font-family: monospace;
            word-break: break-all;
            color: #333;
        }

        .mr-changes {
            margin-top: 16px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }

        .mr-changes-header {
            padding: 8px 12px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
        }

        .mr-change-item {
            padding: 8px 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .mr-change-item:last-child {
            border-bottom: none;
        }

        .mr-file-path {
            font-family: monospace;
            color: #333;
        }

        .mr-file-status {
            font-size: 0.9em;
            padding: 2px 6px;
            border-radius: 3px;
            background: #e8eaf6;
            color: #3f51b5;
            margin-right: 8px;
        }

        .mr-file-diff {
            display: none;
            padding: 12px;
            background: #f8f9fa;
            border-top: 1px solid #e0e0e0;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 13px;
            overflow-x: auto;
        }

        .view-diff-btn {
            padding: 2px 8px;
            font-size: 12px;
            border: 1px solid #ccc;
            border-radius: 3px;
            background: white;
            cursor: pointer;
            color: #666;
        }

        .view-diff-btn:hover {
            background: #f0f0f0;
        }

        .view-json-btn {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            color: #495057;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s;
        }

        .view-json-btn:hover {
            background: #e9ecef;
            border-color: #ced4da;
        }

        .view-json-btn i {
            font-size: 0.9em;
        }

        .page-info {
            padding: 0 20px;
            color: #666;
            font-weight: 500;
        }

        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #666;
            font-weight: 500;
        }

        .page-size-selector select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            color: #333;
            font-weight: 500;
        }

        /* 加载状态 */
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .loading i {
            font-size: 2rem;
            margin-bottom: 15px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #ccc;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #999;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .main-tabs {
                flex-direction: column;
                align-items: stretch;
            }

            .tabs-group {
                justify-content: center;
            }

            .sub-tabs {
                flex-direction: column;
            }

            .sub-tab {
                min-width: auto;
            }

            .events-table {
                font-size: 0.9rem;
            }

            .events-table th,
            .events-table td {
                padding: 10px 8px;
            }

            .pagination {
                flex-direction: column;
                gap: 15px;
            }

            .pagination-controls {
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a6fd8, #6a4190);
        }

        /* LLM消息Markdown样式 */
        .llm-message .code-block {
            background: #f8f9fa;
            border-radius: 4px;
            margin: 10px 0;
            overflow: hidden;
        }

        .llm-message .code-block .code-language {
            padding: 4px 8px;
            background: #e8f0fe;
            color: #1a73e8;
            font-size: 0.85em;
            border-bottom: 1px solid #e0e0e0;
        }

        .llm-message .code-block pre {
            margin: 0;
            padding: 12px;
            overflow-x: auto;
        }

        .llm-message code {
            font-family: 'Courier New', Courier, monospace;
            font-size: 0.9em;
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            border: 1px solid #e0e0e0;
        }

        .llm-message strong {
            font-weight: 600;
            color: #1a73e8;
        }

        .llm-message em {
            font-style: italic;
            color: #5f6368;
        }

        .llm-message br {
            display: block;
            margin: 8px 0;
            content: "";
        }

        /* LLM消息样式 */
        .llm-message {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
            overflow: hidden;
        }

        .llm-message .model-info {
            background: #1a73e8;
            color: white;
            padding: 12px 20px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .llm-message .model-info i {
            font-size: 1.2em;
        }

        .llm-message .conversation {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .llm-message .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 6px;
        }

        .llm-message .message.system {
            background: #e8f0fe;
            border-left: 4px solid #1a73e8;
        }

        .llm-message .message.assistant {
            background: #f1f3f4;
            border-left: 4px solid #34a853;
        }

        .llm-message .message.user {
            background: #fff;
            border-left: 4px solid #fbbc04;
        }

        .llm-message .message .role {
            font-size: 0.9em;
            color: #5f6368;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .llm-message .message .content {
            white-space: pre-wrap;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .llm-message .response {
            padding: 20px;
        }

        .llm-message .response-section {
            margin-bottom: 20px;
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }

        .llm-message .response-section.reasoning {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
        }

        .llm-message .response-section.reasoning .section-header {
            background: #e8f0fe;
            color: #1a73e8;
        }

        .llm-message .response-section.answer {
            background: #fff;
            border: 1px solid #34a853;
        }

        .llm-message .response-section.answer .section-header {
            background: #e6f4ea;
            color: #34a853;
        }

        .llm-message .section-header {
            padding: 10px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .llm-message .collapsible-section {
            margin-bottom: 10px;
        }

        .llm-message .section-header {
            padding: 10px 15px;
            background: #f3f8ff;
            border: 1px solid #e8f0fe;
            border-radius: 6px;
            color: #1a73e8;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            user-select: none;
        }

        .llm-message .section-header:hover {
            background: #e8f0fe;
        }

        .llm-message .section-content {
            display: none;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 6px 6px;
            overflow: hidden;
        }

        .llm-message .collapsible-section.expanded .section-header {
            border-radius: 6px 6px 0 0;
        }

        .llm-message .collapsible-section.expanded .section-content {
            display: block;
        }

        .llm-message .toggle-icon {
            transition: transform 0.2s ease;
        }

        .llm-message .collapsible-section.expanded .toggle-icon {
            transform: rotate(180deg);
        }

        .llm-message .response-section .content {
            padding: 15px;
            white-space: pre-wrap;
            line-height: 1.5;
            background: #fff;
        }

        .llm-message .tool-calls {
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .llm-message .tool-call {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .llm-message .tool-name {
            padding: 10px 15px;
            background: #f3f8ff;
            color: #1a73e8;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            border-bottom: 1px solid #e8f0fe;
        }

        .llm-message .tool-name .index {
            background: #1a73e8;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.85em;
            min-width: 24px;
            text-align: center;
        }

        .llm-message .tool-name i {
            color: #1967d2;
        }

        .llm-message .tool-args {
            padding: 15px;
            background: #fff;
        }

        .llm-message .tool-args pre {
            margin: 0;
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            overflow-x: auto;
            font-size: 0.9em;
        }

        .llm-message .token-usage {
            background: #fff;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }

        .llm-message .token-usage .section-header {
            background: #f3f8ff;
            color: #1a73e8;
            padding: 10px 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
            border-bottom: 1px solid #e8f0fe;
        }

        .llm-message .usage-stats {
            padding: 15px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .llm-message .stat-item {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 10px;
            border: 1px solid #e0e0e0;
        }

        .llm-message .stat-item.total {
            background: #f3f8ff;
            border-color: #1a73e8;
            color: #1a73e8;
        }

        .llm-message .stat-label {
            font-size: 0.9em;
            color: #5f6368;
        }

        .llm-message .stat-value {
            font-weight: 600;
            color: #1a73e8;
        }

        .json-section {
            margin: 10px 0;
        }

        .json-section .section-header {
            padding: 10px 15px;
            background: #f3f8ff;
            border: 1px solid #e8f0fe;
            border-radius: 6px;
            color: #1a73e8;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            user-select: none;
        }

        .json-section .section-header:hover {
            background: #e8f0fe;
        }

        .json-section .section-content {
            display: none;
            border: 1px solid #e0e0e0;
            border-top: none;
            border-radius: 0 0 6px 6px;
            overflow: hidden;
        }

        .json-section.expanded .section-header {
            border-radius: 6px 6px 0 0;
        }

        .json-section.expanded .section-content {
            display: block;
        }

        .json-section .json-content {
            margin: 0;
            padding: 15px;
            background: #fff;
            font-family: monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
        }

        .llm-message .stat-label {
            color: #5f6368;
        }

        .llm-message .stat-value {
            font-weight: 500;
            color: #1a73e8;
        }

        .llm-message .stat-item.total {
            font-weight: 500;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1><i class="fas fa-robot"></i> 智能体排查系统</h1>
        <div class="subtitle">会话详情分析与调试工具</div>
    </div>

    <a href="/admin/ops" class="back-button">
        <i class="fas fa-arrow-left"></i>
        返回智能体排查
    </a>

    <div class="info-section">
        <div class="info-grid">
            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-user"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">用户ID</div>
                    <div class="info-value">{{.UserId}}</div>
                </div>
            </div>
            <div class="info-item">
                <i class="fas fa-comments"></i>
                <div class="info-content">
                    <div class="info-label">会话ID</div>
                    <div class="info-value">{{.SessionId}}</div>
                </div>
            </div>
        </div>
    </div>

    <div class="main-tabs-container">
        <div class="main-tabs">
            <div class="tabs-group">
                <button class="main-tab active" onclick="switchMainTab('snapshots')">
                    <i class="fas fa-camera"></i> 智能体快照数据
                </button>
                <button class="main-tab" onclick="switchMainTab('eventList')">
                    <i class="fas fa-list-ul"></i> 事件记录
                </button>
            </div>
            <button class="refresh-button" onclick="refreshData()">
                <i class="fas fa-sync-alt"></i>
                刷新数据
            </button>
        </div>
    </div>

    <div id="snapshots" class="tab-content active">
        <div class="sub-tabs" id="snapshotTabs">
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <div>正在加载快照数据...</div>
            </div>
        </div>
        <div id="snapshotContents">
            <!-- 快照内容将通过JavaScript动态生成 -->
        </div>
    </div>

    <div id="eventList" class="tab-content">
        <!-- 事件列表将通过JavaScript动态生成 -->
    </div>
</div>

<script>
    // 全局变量存储快照数据
    let snapshotData = [];

    // 切换主标签页
    function switchMainTab(tabId) {
        // 移除所有标签页的active类
        document.querySelectorAll('.main-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
            content.style.display = 'none';
        });

        // 激活点击的标签页
        const clickedTab = document.querySelector(`.main-tab[onclick*="'${tabId}'`)
        if (clickedTab) {
            clickedTab.classList.add('active');
        }
        const content = document.getElementById(tabId);
        if (content) {
            content.classList.add('active');
            content.style.display = 'block';
        }

        // 如果是事件标签页，加载事件数据
        if (tabId === 'eventList') {
            loadEvents(1);
        }
    }

    function switchSubTab(index) {
        document.querySelectorAll('.sub-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.snapshot-content').forEach(content => {
            content.style.display = 'none';
        });

        document.querySelector(`.sub-tab[data-index="${index}"]`).classList.add('active');
        document.querySelector(`.snapshot-content[data-index="${index}"]`).style.display = 'block';
    }

    // 刷新数据
    async function refreshData() {
        try {
            const sessionId = '{{.SessionId}}';
            const userId = '{{.UserId}}';
            const response = await fetch(`/admin/agent-snapshots?sessionId=${sessionId}&pageNumber=1&pageSize=100`, {
                headers: {
                    'X-User-ID': userId
                }
            });
            const data = await response.json();

            if (data.code === 'Ok') {
                snapshotData = data.data.items;
                renderSnapshots();
            }
        } catch (error) {
            console.error('获取数据失败:', error);
            document.getElementById('snapshotTabs').innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>数据加载失败</h3>
                    <p>请检查网络连接后重试</p>
                </div>
            `;
        }
    }

    // 渲染快照数据
    function renderSnapshots() {
        if (!snapshotData || snapshotData.length === 0) {
            document.getElementById('snapshotTabs').innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>暂无快照数据</h3>
                        <p>当前会话还没有生成快照数据</p>
                    </div>
                `;
            document.getElementById('snapshotContents').innerHTML = '';
            return;
        }

        const tabsHtml = snapshotData.map((snapshot, index) => `
                <div class="sub-tab ${index === 0 ? 'active' : ''}"
                     onclick="switchSubTab(${index})"
                     data-index="${index}">
                    <strong>${snapshot.agentId}</strong>
                    <small><i class="fas fa-clock"></i> ${formatTimestamp(snapshot.createTimestamp)}</small>
                </div>
            `).join('');

        const contentsHtml = snapshotData.map((snapshot, index) => {
            const metadata = snapshot.data.metadata || {};
            const state = snapshot.data.state || {};
            const actions = state.actions || [];
            const context = state.context || {};

            // 构建基本信息对象
            const basicInfo = {
                agentId: snapshot.agentId,
                sessionId: snapshot.sessionId,
                createTimestamp: formatTimestamp(snapshot.createTimestamp),
                updateTimestamp: formatTimestamp(snapshot.updateTimestamp)
            };

            // 处理元数据
            const metadataForDisplay = {
                agent_name: metadata.agent_name || '-',
                saved_at: formatTimestamp(metadata.saved_at)
            };

            return `
                    <div class="snapshot-content"
                         data-index="${index}"
                         style="display: ${index === 0 ? 'block' : 'none'}">

                        <div class="section-block basic-info">
                            <h3><i class="fas fa-info-circle"></i> 基本信息</h3>
                            <div class="json-section">
                                <pre>${formatJsonContent(basicInfo)}</pre>
                            </div>
                        </div>

                        <div class="section-block metadata-info">
                            <h3><i class="fas fa-tags"></i> 元数据</h3>
                            <div class="json-section">
                                <pre>${formatJsonContent(metadataForDisplay)}</pre>
                            </div>
                        </div>

                        <div class="section-block actions-info">
                            <h3><i class="fas fa-cogs"></i> 动作记录 (${actions.length})</h3>
                            ${actions.length === 0 ?
                '<div class="empty-state"><i class="fas fa-inbox"></i><h3>暂无动作记录</h3></div>' :
                actions.map((action, actionIndex) => {
                    const keyInfo = {
                        'Action Type': action.action_type,
                        'Time': formatTimestamp(action.executed_at),
                        'Status': action.status,
                        'Action ID': action.action_id
                    };

                    const fullAction = JSON.parse(JSON.stringify(action));
                    delete fullAction.action_id;

                    // 为CommentMergeRequestAction类型定制展示
                    if (action.action_type === 'CommentMergeRequestAction') {
                        const comment = action.comment || {};
                        const result = action.result || {};
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="comment-info">
                                        <div class="comment-status ${result.succeed ? 'success' : 'failed'}">
                                            <i class="fas ${result.succeed ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            ${result.succeed ? '评论成功' : '评论失败'}
                                            ${result.comment_id ? `(评论ID: ${result.comment_id})` : ''}
                                            ${!result.succeed && result.error ? `
                                                <div class="error-details">
                                                    <i class="fas fa-exclamation-circle"></i>
                                                    ${result.error}
                                                </div>
                                            ` : ''}
                                        </div>
                                        <div class="comment-meta">
                                            ${comment.reply_to_comment_id ? `<div class="meta-item"><i class="fas fa-reply"></i> 回复评论: ${comment.reply_to_comment_id}</div>` : ''}
                                            ${comment.start_line && comment.end_line ?
                            `<div class="meta-item"><i class="fas fa-code"></i> 行号: ${comment.start_line}${comment.start_line !== comment.end_line ? `-${comment.end_line}` : ''}</div>`
                            : ''
                        }
                                            ${comment.path ? `<div class="meta-item"><i class="fas fa-file-code"></i> 文件: ${comment.path}</div>` : ''}
                                        </div>
                                        <div class="comment-content">
                                            ${comment.content ? `
                                                <div class="markdown-content">${window.marked.parse(comment.content)}</div>
                                            ` : '无评论内容'}
                                        </div>
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 为UpdateCommentAction类型定制展示
                    if (action.action_type === 'UpdateCommentAction') {
                        const result = action.result || {};
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="update-comment-info">
                                        <div class="comment-header">
                                            <div class="meta-item">
                                                <i class="fas fa-hashtag"></i>
                                                <span class="meta-label">评论ID:</span>
                                                <span class="meta-value">${action.comment_id}</span>
                                            </div>
                                            <div class="meta-item ${result.succeed ? 'success' : 'failed'}">
                                                <i class="fas ${result.succeed ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                                <span class="meta-label">状态:</span>
                                                <span class="meta-value">${result.succeed ? '更新成功' : '更新失败'}</span>
                                            </div>
                                        </div>
                                        ${!result.succeed && result.error ? `
                                            <div class="error-message">
                                                <i class="fas fa-exclamation-circle"></i>
                                                ${result.error}
                                            </div>
                                        ` : ''}
                                        <div class="comment-content-section">
                                            <h4><i class="fas fa-edit"></i> 更新内容</h4>
                                            <div class="comment-content">
                                                ${action.content ? `
                                                    <div class="markdown-content">${window.marked.parse(action.content)}</div>
                                                ` : '无评论内容'}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 为GetCommentAction类型定制展示
                    if (action.action_type === 'GetCommentAction') {
                        const result = action.result || {};
                        const commentInfo = result.comment_info || {};
                        const codePosition = commentInfo.code_position || {};
                        const comments = commentInfo.comments || [];
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="get-comment-info">
                                        <div class="comment-header">
                                            <div class="meta-item">
                                                <i class="fas fa-hashtag"></i>
                                                <span class="meta-label">评论ID:</span>
                                                <span class="meta-value">${action.comment_id}</span>
                                            </div>
                                            <div class="meta-item ${result.succeed ? 'success' : 'failed'}">
                                                <i class="fas ${result.succeed ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                                <span class="meta-label">状态:</span>
                                                <span class="meta-value">${result.succeed ? '成功' : '失败'}</span>
                                            </div>
                                        </div>
                                        ${codePosition.path ? `
                                            <div class="code-position-info">
                                                <div class="meta-item">
                                                    <i class="fas fa-file-code"></i>
                                                    <span class="meta-label">文件路径:</span>
                                                    <span class="meta-value">${codePosition.path}</span>
                                                </div>
                                                ${codePosition.start_line ? `
                                                    <div class="meta-item">
                                                        <i class="fas fa-code"></i>
                                                        <span class="meta-label">行号:</span>
                                                        <span class="meta-value">${codePosition.start_line}${codePosition.start_line !== codePosition.end_line ? `-${codePosition.end_line}` : ''}</span>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        ` : ''}
                                        ${comments.length > 0 ? `
                                            <div class="comments-thread">
                                                <h4><i class="fas fa-comments"></i> 评论内容</h4>
                                                ${comments.map(comment => `
                                                    <div class="thread-comment">
                                                        <div class="comment-header">
                                                            <div class="meta-item">
                                                                <i class="fas fa-user"></i>
                                                                <span class="meta-value username">${comment.username}</span>
                                                            </div>
                                                            <div class="meta-item">
                                                                <i class="fas fa-hashtag"></i>
                                                                <span class="meta-value">${comment.id}</span>
                                                            </div>
                                                        </div>
                                                        <div class="comment-content">
                                                            ${comment.content ? `
                                                                <div class="markdown-content">${window.marked.parse(comment.content)}</div>
                                                            ` : '无评论内容'}
                                                        </div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 为ReviewMergeRequestAction类型定制展示
                    if (action.action_type === 'ReviewMergeRequestAction') {
                        const comments = action.comments || [];
                        const result = action.result || {};
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="review-request-info">
                                        <div class="request-meta">
                                            <div class="meta-item">
                                                <i class="fas fa-hashtag"></i>
                                                <span class="meta-label">请求ID:</span>
                                                <span class="meta-value">${result.requestId || '-'}</span>
                                            </div>
                                            ${action.commit_id ? `
                                                <div class="meta-item">
                                                    <i class="fas fa-code-commit"></i>
                                                    <span class="meta-label">提交ID:</span>
                                                    <span class="meta-value">${action.commit_id.substring(0, 8)}</span>
                                                </div>
                                            ` : ''}
                                        </div>
                                        ${comments.length > 0 ? `
                                            <div class="comments-section">
                                                <h4><i class="fas fa-comments"></i> 评论列表 (${comments.length})</h4>
                                                ${comments.map(comment => `
                                                    <div class="comment-item">
                                                        <div class="comment-meta">
                                                            ${comment.path ? `<div class="meta-item"><i class="fas fa-file-code"></i> 文件: ${comment.path}</div>` : ''}
                                                            ${comment.start_line && comment.end_line ?
                            `<div class="meta-item"><i class="fas fa-code"></i> 行号: ${comment.start_line}${comment.start_line !== comment.end_line ? `-${comment.end_line}` : ''}</div>`
                            : ''
                        }
                                                            ${comment.reply_to_comment_id ? `<div class="meta-item"><i class="fas fa-reply"></i> 回复评论: ${comment.reply_to_comment_id}</div>` : ''}
                                                        </div>
                                                        <div class="comment-content">
                                                            ${comment.content ? `
                                                                <div class="markdown-content">${window.marked.parse(comment.content)}</div>
                                                            ` : '无评论内容'}
                                                        </div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 为GetIncrementalReviewRequest类型定制展示
                    if (action.action_type === 'GetIncrementalReviewRequest') {
                        const result = action.result || {};
                        const review = result.incremental_review_request || {};
                        const changes = review.changes || [];
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="review-info">
                                        <div class="commit-info">
                                            <div class="sha-info">
                                                <div class="sha-item">
                                                    <i class="fas fa-code-branch"></i>
                                                    <span class="sha-label">基础提交:</span>
                                                    <span class="sha-value">${action.base_sha.substring(0, 8)}</span>
                                                </div>
                                                <div class="sha-item">
                                                    <i class="fas fa-arrow-right"></i>
                                                    <span class="sha-label">目标提交:</span>
                                                    <span class="sha-value">${action.head_sha.substring(0, 8)}</span>
                                                </div>
                                            </div>
                                        </div>
                                        ${changes.length > 0 ? `
                                            <div class="changes-info">
                                                <h4><i class="fas fa-exchange-alt"></i> 变更文件 (${changes.length})</h4>
                                                ${changes.map(change => `
                                                    <div class="change-item">
                                                        <div class="file-path">
                                                            <i class="fas fa-file-code"></i>
                                                            ${change.new_path}
                                                        </div>
                                                        ${change.diff ? `
                                                            <pre class="diff-content">${escapeHtml(change.diff)}</pre>
                                                        ` : ''}
                                                        <div class="json-section collapsible-section">
                                                            <div class="section-header" onclick="toggleCollapse(this)">
                                                                <i class="fas fa-chevron-down toggle-icon"></i>
                                                                <i class="fas fa-code"></i>
                                                                <span>JSON数据</span>
                                                            </div>
                                                            <div class="section-content">
                                                                <pre class="json-content">${formatJsonContent(change)}</pre>
                                                            </div>
                                                        </div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 为ApplyMergeRequestAction类型定制展示
                    if (action.action_type === 'ApplyMergeRequestCommentAction') {
                        const comment = action.request_comment || {};
                        const result = action.result || {};
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="comment-info">
                                        <div class="comment-status ${result.succeed ? 'success' : 'failed'}">
                                            <i class="fas ${result.succeed ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            ${result.succeed ? '评论操作成功' : '评论操作失败'}
                                            ${action.comment_id ? `(评论 ID: ${action.comment_id})` : ''}
                                            ${!result.succeed && action.error ? `
                                                <div class="error-details">
                                                    <i class="fas fa-exclamation-circle"></i>
                                                    ${action.error}
                                                </div>
                                            ` : ''}
                                        </div>
                                        <div class="comment-meta">
                                            ${comment.reply_to_comment_id ? `<div class="meta-item"><i class="fas fa-reply"></i> 回复评论: ${comment.reply_to_comment_id}</div>` : ''}
                                            ${comment.start_line && comment.end_line ?
                            `<div class="meta-item"><i class="fas fa-code"></i> 行号: ${comment.start_line}${comment.start_line !== comment.end_line ? `-${comment.end_line}` : ''}</div>`
                            : ''
                        }
                                            ${comment.path ? `<div class="meta-item"><i class="fas fa-file-code"></i> 文件: ${comment.path}</div>` : ''}
                                        </div>
                                        <div class="comment-content">
                                            ${comment.content ? `
                                                <div class="markdown-content">${formatContent(comment.content)}</div>
                                            ` : '无评论内容'}
                                        </div>
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 为ExitAction类型定制展示
                    if (action.action_type === 'ExitAction') {
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="exit-info">
                                        <div class="exit-status ${action.exit_status}">
                                            <i class="fas ${action.exit_status === 'success' ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                                            <div class="status-text">
                                                退出状态: <span class="status-value">${action.exit_status}</span>
                                            </div>
                                            ${action.reason ? `
                                                <div class="reason-text">
                                                    退出原因: <span class="reason-value">${action.reason}</span>
                                                </div>
                                            ` : ''}
                                        </div>
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 为GetRepositoryFileAction类型定制展示
                    if (action.action_type === 'GetRepositoryFileAction') {
                        const args = action.args || {};
                        const result = action.result || {};
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="file-info">
                                        <table>
                                            <tr>
                                                <td>文件路径</td>
                                                <td>${escapeHtml(args.path || '-')}</td>
                                            </tr>
                                            <tr>
                                                <td>Git引用</td>
                                                <td>${escapeHtml(args.ref || '-')}</td>
                                            </tr>
                                        </table>
                                        ${result.content ? `
                                            <div class="file-content">
                                                <div class="file-content-header">文件内容</div>
                                                <pre class="code-block">${escapeHtml(result.content)}</pre>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }
                    // 为GetMergeRequestAction类型定制展示
                    if (action.action_type === 'GetMergeRequestAction' && action.result?.mr_info) {
                        const mrInfo = action.result.mr_info;
                        return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                    <div class="action-info">
                                        <span class="action-badge action-type">${action.action_type}</span>
                                        <span class="action-badge action-status">${action.status || 'completed'}</span>
                                        <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                    </div>
                                    <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                                </div>
                                <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                    <div style="text-align: right; padding: 8px;">
                                        <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                            <i class="fas fa-code"></i> 查看JSON
                                        </button>
                                    </div>
                                    <div class="mr-info">
                                        <table>
                                            <tr>
                                                <td>标题</td>
                                                <td>${mrInfo.title || '未命名合并请求'}</td>
                                            </tr>
                                            <tr>
                                                <td>状态</td>
                                                <td><span class="mr-status ${mrInfo.state?.toLowerCase() || 'open'}">${mrInfo.state || 'OPEN'}</span></td>
                                            </tr>
                                            <tr>
                                                <td>来源分支</td>
                                                <td>${mrInfo.source_branch || '-'}</td>
                                            </tr>
                                            <tr>
                                                <td>目标分支</td>
                                                <td>${mrInfo.target_branch || '-'}</td>
                                            </tr>
                                            <tr>
                                                <td>创建者</td>
                                                <td>${mrInfo.author?.name || '-'}</td>
                                            </tr>
                                            <tr>
                                                <td>创建时间</td>
                                                <td>${mrInfo.created_at || '-'}</td>
                                            </tr>
                                        </table>
                                    </div>
                                    ${mrInfo.changes ? `
                                        <div class="mr-changes">
                                            <div class="mr-changes-header">
                                                变更文件 (${mrInfo.changes.length})
                                            </div>
                                            ${mrInfo.changes.map((change, index) => `
                                                <div class="mr-change-item" onclick="event.stopPropagation();">
                                                    <span class="mr-file-path">${change.new_path || change.old_path || '未知文件'}</span>
                                                    <div style="display: flex; align-items: center;">
                                                        <span class="mr-file-status">
                                                            ${change.deleted_file ? '删除' : change.new_file ? '新增' : '修改'}
                                                        </span>
                                                        <button class="view-diff-btn" onclick="toggleDiff('${action.action_id}', ${index})">
                                                            <i class="fas fa-code"></i> 查看Diff
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="mr-file-diff" id="diff-${action.action_id}-${index}" onclick="event.stopPropagation();">${change.diff || '无Diff信息'}</div>
                                            `).join('')}
                                        </div>
                                    ` : ''}
                                </div>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        `;
                    }

                    // 其他Action类型的展示
                    return `
                        <div class="action-item">
                            <div class="action-header" onclick="toggleAction('${action.action_id}')" style="cursor: pointer;">
                                <div class="action-info">
                                    <span class="action-badge action-type">${action.action_type}</span>
                                    <span class="action-badge action-status">${action.status || 'completed'}</span>
                                    <span class="action-time"><i class="fas fa-clock"></i> ${keyInfo['Time']}</span>
                                </div>
                                <i class="fas fa-chevron-right toggle-icon" id="icon-${action.action_id}"></i>
                            </div>
                            <div class="action-detail" id="action-${action.action_id}" style="display: none;">
                                <div style="text-align: right; padding: 8px;">
                                    <button class="view-json-btn" onclick="toggleJsonView('${action.action_id}')">
                                        <i class="fas fa-code"></i> 查看JSON
                                    </button>
                                </div>
                                <pre>${formatJsonContent(fullAction)}</pre>
                                <div class="action-json" id="json-${action.action_id}" style="display: none;">
                                    <pre>${formatJsonContent(action)}</pre>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('')
            }
                        </div>

                        <div class="section-block context-info">
                            <h3><i class="fas fa-sitemap"></i> 上下文信息</h3>
                            <div class="json-section">
                                <pre>${formatJsonContent(context)}</pre>
                            </div>
                        </div>
                    </div>
                `;
        }).join('');

        document.getElementById('snapshotTabs').innerHTML = tabsHtml;
        document.getElementById('snapshotContents').innerHTML = contentsHtml;
    }

    // 添加折叠切换函数
    function toggleDiff(actionId, index) {
        const diffElement = document.getElementById(`diff-${actionId}-${index}`);
        if (!diffElement) {
            console.error(`找不到元素: diff-${actionId}-${index}`);
            return;
        }

        if (diffElement.style.display === 'none') {
            diffElement.style.display = 'block';
        } else {
            diffElement.style.display = 'none';
        }
    }

    function toggleJsonView(actionId) {
        const jsonElement = document.getElementById(`json-${actionId}`);
        if (!jsonElement) {
            console.error(`找不到元素: json-${actionId}`);
            return;
        }

        if (jsonElement.style.display === 'none') {
            jsonElement.style.display = 'block';
        } else {
            jsonElement.style.display = 'none';
        }
    }

    function toggleAction(actionId) {
        const detailElement = document.getElementById(`action-${actionId}`);
        const iconElement = document.getElementById(`icon-${actionId}`);
        if (!detailElement || !iconElement) {
            console.error(`找不到元素: action-${actionId} 或 icon-${actionId}`);
            return;
        }

        const isHidden = detailElement.style.display === 'none' || !detailElement.style.display;

        if (isHidden) {
            detailElement.style.display = 'block';
            iconElement.classList.remove('fa-chevron-right');
            iconElement.classList.add('fa-chevron-down');
        } else {
            detailElement.style.display = 'none';
            iconElement.classList.remove('fa-chevron-down');
            iconElement.classList.add('fa-chevron-right');

            // 如果有json元素，也同步切换显示状态
            const jsonElement = document.getElementById(`json-${action.action_id}`);
            if (jsonElement) {
                jsonElement.style.display = 'none';
            }
        }
    }

    // 格式化时间戳
    function formatTimestamp(timestamp) {
        if (!timestamp) return '-';

        // 处理字符串类型
        if (typeof timestamp === 'string') {
            timestamp = parseFloat(timestamp);
        }

        // 处理秒级时间戳
        if (timestamp < 10000000000) {
            timestamp *= 1000;
        }

        const date = new Date(timestamp);
        if (isNaN(date.getTime())) return '-';

        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }

    // 格式化内容，处理Markdown格式
    function formatContent(content) {
        if (!content) return '';

        let formattedContent = escapeHtml(content);

        // 处理代码块
        const codeBlockRegex = /```([\w]*)?\n([\s\S]*?)```/g;
        formattedContent = formattedContent.replace(codeBlockRegex, (match, language, code) => {
            return `<div class="code-block">
                ${language ? `<div class="code-language">${language}</div>` : ''}
                <pre><code>${code.trim()}</code></pre>
            </div>`;
        });

        // 处理行内代码
        formattedContent = formattedContent.replace(/`([^`]+)`/g, '<code>$1</code>');

        // 处理粗体
        formattedContent = formattedContent.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');

        // 处理斜体
        formattedContent = formattedContent.replace(/\*([^*]+)\*/g, '<em>$1</em>');

        // 处理换行符
        formattedContent = formattedContent.replace(/\n/g, '<br>');

        return formattedContent;
    }

    // 格式化LLM消息内容，只处理换行符
    function formatLLMContent(content) {
        if (!content) return '';
        return escapeHtml(content).replace(/\n/g, '<br>');
    }

    // 事件分页数据
    let eventData = {
        currentPage: 1,
        pageSize: 100,
        totalSize: 0,
        items: []
    };

    // 加载事件数据
    async function loadEvents(pageNumber) {
        // 验证页码
        if (pageNumber < 1) pageNumber = 1;
        const totalPages = Math.ceil(eventData.totalSize / eventData.pageSize);
        if (totalPages > 0 && pageNumber > totalPages) {
            pageNumber = totalPages;
        }
        // 显示加载状态
        document.getElementById('eventList').innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner"></i>
                <div>正在加载事件数据...</div>
            </div>
        `;

        try {
            const sessionId = '{{.SessionId}}';
            const userId = '{{.UserId}}';
            const response = await fetch(`/admin/sessions/${sessionId}/events?withIdAsc=true&pageNumber=${pageNumber}&pageSize=${eventData.pageSize}`, {
                headers: {
                    'X-User-ID': userId
                }
            });
            const data = await response.json();

            if (data.code === 'Ok') {
                eventData.events = data.data.items;
                eventData.totalSize = data.data.totalSize;
                eventData.currentPage = pageNumber;
                renderEvents();
            } else {
                console.error('加载事件数据失败:', data);
                document.getElementById('eventList').innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>数据加载失败</h3>
                        <p>请检查网络连接后重试</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('加载事件数据失败:', error);
            document.getElementById('eventList').innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>网络连接失败</h3>
                    <p>请检查网络连接后重试</p>
                </div>
            `;
        }
    }

    // 更新渲染事件列表的函数
    function renderEvents() {
        const eventsContainer = document.getElementById('eventList');
        const totalPages = Math.ceil(eventData.totalSize / eventData.pageSize);

        // 添加表头
        let content = `
            <div class="events-header">
                <div class="header-cell time-cell"><i class="fas fa-clock"></i> 时间</div>
                <div class="header-cell id-cell"><i class="fas fa-fingerprint"></i> 事件ID</div>
                <div class="header-cell type-cell">类型</div>
                <div class="header-cell brief-cell">描述</div>
            </div>
        `;

        if (!eventData.events || eventData.events.length === 0) {
            eventsContainer.innerHTML = `
                ${content}
                <div class="empty-state">
                    <i class="fas fa-inbox"></i>
                    <div>
                        <h3>暂无事件数据</h3>
                        <p>该会话暂时没有事件数据</p>
                    </div>
                </div>
            `;
            return;
        }

        const tableRows = eventData.events.map(event => {
            const eventTime = event.createTimestamp ? formatTimestamp(event.createTimestamp) : '-';
            const eventType = event.property?.type || '-';
            const data = event.property?.data || {};
            const briefInfo = getBriefInfo(event);
            const typeClass = getTypeClass(event);

            return `
                <div class="event-row ${typeClass}">
                    <div class="event-header" onclick="toggleEventDetail(this)">
                        <div class="event-icon">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="event-time"><i class="fas fa-clock"></i> ${eventTime}</div>
                        <div class="event-id"><i class="fas fa-fingerprint"></i> <code>${event.eventId}</code></div>
                        <div class="event-type ${eventType}">${eventType}</div>
                        <div class="event-brief">${briefInfo}</div>
                    </div>
                    <div class="event-detail" style="display: none;">
                        ${(() => {
                if (eventType === 'llm_message') {
                    return `
                                    <div class="llm-message">
                                        <div class="model-info">
                                            <i class="fas fa-robot"></i> ${escapeHtml(data.model_name || '未知模型')}
                                        </div>

                                        <div class="conversation collapsible-section">
                                            <div class="section-header" onclick="toggleCollapse(this)">
                                                <i class="fas fa-chevron-down toggle-icon"></i>
                                                <span>对话内容</span>
                                            </div>
                                            <div class="section-content">
                                                ${(data.messages || []).map(msg => `
                                                    <div class="message ${msg.role}">
                                                        <div class="role">${escapeHtml(msg.role)}</div>
                                                        <div class="content">${formatLLMContent(msg.content)}</div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>

                                        ${data.response ? `
                                            <div class="response">
                                                ${data.response.message?.reasoning_content ? `
                                                    <div class="response-section reasoning collapsible-section">
                                                        <div class="section-header" onclick="toggleCollapse(this)">
                                                            <i class="fas fa-chevron-down toggle-icon"></i>
                                                            <i class="fas fa-brain"></i>
                                                            <span>推理过程</span>
                                                        </div>
                                                        <div class="section-content">
                                                            <div class="content">${formatLLMContent(data.response.message.reasoning_content)}</div>
                                                        </div>
                                                    </div>
                                                ` : ''}

                                                <div class="response-section answer collapsible-section">
                                                    <div class="section-header" onclick="toggleCollapse(this)">
                                                        <i class="fas fa-chevron-down toggle-icon"></i>
                                                        <i class="fas fa-comment-dots"></i>
                                                        <span>响应内容</span>
                                                    </div>
                                                    <div class="section-content">
                                                        <div class="content">${formatLLMContent(data.response.message?.content || '')}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}

                                        ${data.response?.token_usage ? `
                                            <div class="token-usage-info">
                                                <h4><i class="fas fa-chart-bar"></i> Token 使用统计</h4>
                                                <div class="model-usage">
                                                    <div class="usage-stats">
                                                        <div class="stat-item">
                                                            <span class="stat-label">Prompt Tokens:</span>
                                                            <span class="stat-value">${data.response.token_usage.prompt_tokens.toLocaleString()}</span>
                                                        </div>
                                                        <div class="stat-item">
                                                            <span class="stat-label">Completion Tokens:</span>
                                                            <span class="stat-value">${data.response.token_usage.completion_tokens.toLocaleString()}</span>
                                                        </div>
                                                        <div class="stat-item total">
                                                            <span class="stat-label">Total Tokens:</span>
                                                            <span class="stat-value">${data.response.token_usage.total_tokens.toLocaleString()}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                `;
                } else if (eventType === 'agent_task' && data.type === 'one_task_llm_usage') {
                    return `
                                    <div class="token-usage-info">
                                        <h4><i class="fas fa-chart-bar"></i> Token 使用统计</h4>
                                        ${Object.entries(data.data).map(([model, usage]) => `
                                            <div class="model-usage">
                                                <div class="model-name"><i class="fas fa-robot"></i> ${model}</div>
                                                <div class="usage-stats">
                                                    <div class="stat-item">
                                                        <span class="stat-label">Prompt Tokens:</span>
                                                        <span class="stat-value">${usage.prompt_tokens.toLocaleString()}</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-label">Completion Tokens:</span>
                                                        <span class="stat-value">${usage.completion_tokens.toLocaleString()}</span>
                                                    </div>
                                                    <div class="stat-item total">
                                                        <span class="stat-label">Total Tokens:</span>
                                                        <span class="stat-value">${usage.total_tokens.toLocaleString()}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                `;
                } else if (eventType === 'llm_message') {
                    return `
                                    <div class="llm-message">
                                        <div class="model-info">
                                            <i class="fas fa-robot"></i> ${escapeHtml(data.model_name || '未知模型')}
                                        </div>

                                        <div class="conversation collapsible-section">
                                            <div class="section-header" onclick="toggleCollapse(this)">
                                                <i class="fas fa-chevron-down toggle-icon"></i>
                                                <span>对话内容</span>
                                            </div>
                                            <div class="section-content">
                                                ${(data.messages || []).map(msg => `
                                                    <div class="message ${msg.role}">
                                                        <div class="role">${escapeHtml(msg.role)}</div>
                                                        <div class="content">${formatLLMContent(msg.content)}</div>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        </div>

                                        ${data.response ? `
                                            <div class="response">
                                                ${data.response.message?.reasoning_content ? `
                                                    <div class="response-section reasoning collapsible-section">
                                                        <div class="section-header" onclick="toggleCollapse(this)">
                                                            <i class="fas fa-chevron-down toggle-icon"></i>
                                                            <i class="fas fa-brain"></i>
                                                            <span>推理过程</span>
                                                        </div>
                                                        <div class="section-content">
                                                            <div class="content">${formatLLMContent(data.response.message.reasoning_content)}</div>
                                                        </div>
                                                    </div>
                                                ` : ''}

                                                <div class="response-section answer collapsible-section">
                                                    <div class="section-header" onclick="toggleCollapse(this)">
                                                        <i class="fas fa-chevron-down toggle-icon"></i>
                                                        <i class="fas fa-comment-dots"></i>
                                                        <span>响应内容</span>
                                                    </div>
                                                    <div class="section-content">
                                                        <div class="content">${formatLLMContent(data.response.message?.content || '')}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}

                                        ${data.response?.token_usage ? `
                                            <div class="token-usage-info">
                                                <h4><i class="fas fa-chart-bar"></i> Token 使用统计</h4>
                                                <div class="model-usage">
                                                    <div class="usage-stats">
                                                        <div class="stat-item">
                                                            <span class="stat-label">Prompt Tokens:</span>
                                                            <span class="stat-value">${data.response.token_usage.prompt_tokens.toLocaleString()}</span>
                                                        </div>
                                                        <div class="stat-item">
                                                            <span class="stat-label">Completion Tokens:</span>
                                                            <span class="stat-value">${data.response.token_usage.completion_tokens.toLocaleString()}</span>
                                                        </div>
                                                        <div class="stat-item total">
                                                            <span class="stat-label">Total Tokens:</span>
                                                            <span class="stat-value">${data.response.token_usage.total_tokens.toLocaleString()}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        ` : ''}
                                    </div>
                                `;
                } else {
                    return '';
                }
            })()}
                        <div class="json-data">
                            <div class="json-header">
                                <h4><i class="fas fa-code"></i> 原始数据</h4>
                                <button class="copy-json-btn" onclick="event.stopPropagation(); copyJsonData(this);" data-json="${escapeJsonAttribute(JSON.stringify(data))}">
                                    <i class="fas fa-copy"></i> 复制
                                </button>
                            </div>
                            <pre>${formatJsonContent(data)}</pre>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        const html = `
            <div class="events-container">
                ${content}
                <div class="events-list">
                    ${tableRows}
                </div>
                <div class="pagination">
                    <button onclick="loadEvents(1)" ${eventData.currentPage === 1 ? 'disabled' : ''}>
                        <i class="fas fa-angle-double-left"></i>
                    </button>
                    <button onclick="loadEvents(${eventData.currentPage - 1})" ${eventData.currentPage === 1 ? 'disabled' : ''}>
                        <i class="fas fa-angle-left"></i>
                    </button>
                    <span class="page-info">第 ${eventData.currentPage} 页，共 ${totalPages} 页</span>
                    <button onclick="loadEvents(${eventData.currentPage + 1})" ${eventData.currentPage === totalPages ? 'disabled' : ''}>
                        <i class="fas fa-angle-right"></i>
                    </button>
                    <button onclick="loadEvents(${totalPages})" ${eventData.currentPage === totalPages ? 'disabled' : ''}>
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>
                <div class="page-size-selector">
                    每页显示：
                    <select onchange="changePageSize(this.value)">
                        <option value="10" ${eventData.pageSize === 10 ? 'selected' : ''}>10</option>
                        <option value="20" ${eventData.pageSize === 20 ? 'selected' : ''}>20</option>
                        <option value="50" ${eventData.pageSize === 50 ? 'selected' : ''}>50</option>
                        <option value="100" ${eventData.pageSize === 100 ? 'selected' : ''}>100</option>
                    </select>
                    条
                </div>
            </div>
        `;

        eventsContainer.innerHTML = html;
    }

    // 处理页面大小变化
    function changePageSize(newSize) {
        // 更新每页显示数量
        eventData.pageSize = parseInt(newSize);
        // 重置到第一页
        eventData.currentPage = 1;
        // 重新加载数据
        loadEvents(1);
    }

    // 切换事件数据显示/隐藏
    function toggleEventDetail(header) {
        const row = header.closest('.event-row');
        const detail = row.querySelector('.event-detail');
        const toggleIcon = header.querySelector('.event-icon i');

        if (detail.style.display === 'none') {
            detail.style.display = 'block';
            toggleIcon.classList.remove('fa-chevron-down');
            toggleIcon.classList.add('fa-chevron-up');
            row.classList.add('expanded');
        } else {
            detail.style.display = 'none';
            toggleIcon.classList.remove('fa-chevron-up');
            toggleIcon.classList.add('fa-chevron-down');
            row.classList.remove('expanded');
        }
    }

    // 更新获取简要信息的函数
    function getBriefInfo(event) {
        const type = event.property?.type;
        const data = event.property?.data || {};

        switch (type) {
            case 'runtime':
            case 'agent':
                const lifecycle = data.lifecycle || '-';
                const hasError = data.error ? '⚠️ 有错误' : '✅ 无错误';
                return `${lifecycle} | ${hasError}`;

            case 'action':
                return `${data.action_type || '-'} | ${data.lifecycle || '-'}`;

            case 'agent_task':
                switch(data.type) {
                    case 'accumulate_llm_usage':
                        return '📊 累计大模型消耗';
                    case 'one_task_llm_usage':
                        return '📈 本次大模型消耗';
                    case 'mr_statistic':
                        return '📋 MR变更统计';
                    default:
                        return '-';
                }

            case 'llm_message':
                return '💬 大模型消息';

            default:
                return '-';
        }
    }

    // 获取类型样式
    function getTypeClass(event) {
        const type = event.property?.type;

        switch (type) {
            case 'runtime':
                return 'type-runtime';
            case 'agent':
                return 'type-agent';
            case 'action':
                return 'type-action';
            case 'agent_task':
                return 'type-agent-task';
            case 'llm_message':
                return 'type-llm-message';
            default:
                return '';
        }
    }

    function toggleCollapse(header) {
        const section = header.closest('.collapsible-section');
        section.classList.toggle('expanded');
    }

    function escapeHtml(unsafe) {
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 转义 JSON 字符串用于 HTML 属性
    function escapeJsonAttribute(str) {
        return str.replace(/"/g, '&quot;');
    }

    // 复制 JSON 数据
    function copyJsonData(button) {
        const jsonData = button.getAttribute('data-json');
        // 解码 HTML 实体
        const decodedJson = jsonData.replace(/&quot;/g, '"');
        navigator.clipboard.writeText(decodedJson).then(() => {
            const icon = button.querySelector('i');
            const originalText = button.innerHTML;

            button.innerHTML = '<i class="fas fa-check"></i> 已复制';
            button.classList.add('copied');

            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('copied');
            }, 2000);
        }).catch(err => {
            console.error('复制失败:', err);
            alert('复制失败，请重试');
        });
    }

    // 格式化JSON显示，处理HTML内容
    function formatJsonContent(obj) {
        try {
            const formatted = JSON.stringify(obj, (key, value) => {
                if (typeof value === 'string') {
                    return escapeHtml(value);
                }
                return value;
            }, 2);
            return formatted;
        } catch (error) {
            console.error('JSON格式化失败:', error);
            return '格式化失败';
        }
    }

    // 页面加载完成后自动加载数据
    window.onload = function () {
        refreshData();
    };
</script>
</body>
</html>
