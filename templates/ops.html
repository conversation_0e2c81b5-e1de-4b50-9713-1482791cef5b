<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体排查系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            width: 100%;
            padding: 16px;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
        }

        .user-id-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .input-group {
            margin-bottom: 15px;
            display: none;
        }

        input {
            padding: 8px 12px;
            width: 200px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.15s ease-in-out;
        }

        input:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 2px rgba(33,150,243,0.25);
        }

        button {
            padding: 8px 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        button:hover {
            background-color: #45a049;
        }

        .display-user-id {
            margin-top: 20px;
            font-size: 16px;
        }

        .user-id-value {
            font-weight: bold;
            color: #2196F3;
        }

        .config-button {
            background-color: #2196F3;
        }

        .config-button:hover {
            background-color: #1976D2;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            z-index: 1000;
        }

        .toast.show {
            opacity: 1;
        }

        /* 表格样式 */
        #dataSection {
            display: flex;
            flex-direction: column;
        }

        #tableContainer {
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            min-width: 800px;
            border-collapse: separate;
            border-spacing: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .data-table th, .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .data-table th {
            background: rgba(255, 255, 255, 0.9);
            font-weight: 600;
            color: #495057;
            white-space: nowrap;
            border-bottom: 2px solid rgba(102, 126, 234, 0.2);
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

        .data-table tr:hover {
            background-color: rgba(255, 255, 255, 0.8);
        }

        .state {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 0.9rem;
            color: white;
        }

        .state-Running {
            background-color: #2196f3;
        }

        .state-Success {
            background-color: #4caf50;
        }

        .state-Error {
            background-color: #f44336;
        }

        .state-Pending {
            background-color: #ff9800;
        }

        .refresh-button {
            background-color: #4caf50;
            margin-top: 20px;
            margin-bottom: 20px;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .refresh-button i {
            font-size: 14px;
        }

        .refresh-button:hover {
            background-color: #F57C00;
        }

        .external-link {
            color: #2196F3;
            text-decoration: none;
        }

        .external-link:hover {
            text-decoration: underline;
        }
        .action-button {
            background-color: #673ab7;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: background-color 0.2s;
        }

        .action-button i {
            font-size: 14px;
        }

        .action-button:hover {
            background-color: #5E35B1;
        }

        /* 调整表格中操作按钮列的宽度 */
        .data-table td:last-child,
        .data-table th:last-child {
            width: 180px;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1><i class="fas fa-robot"></i> 智能体排查系统</h1>
    </div>

    <div class="user-id-section">
        <div class="display-user-id">
            当前用户ID：<span id="currentUserId" class="user-id-value">检查中...</span>
            <button onclick="showConfig()" id="configButton" class="config-button" style="display: none;">修改配置</button>
        </div>
        <div class="input-group" id="inputGroup">
            <label for="userId">请输入用户ID：</label>
            <input type="text" id="userId" placeholder="输入用户ID">
            <button onclick="setUserId()">确定</button>
        </div>
    </div>

    <div id="dataSection" style="display: none;">
        <button onclick="refreshData()" class="refresh-button">
            <i class="fas fa-sync-alt"></i> 刷新执行记录
        </button>
        <div id="tableContainer"></div>
    </div>

    <div id="toast" class="toast"></div>

    <script>
        function showToast(message, duration = 2000) {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
            }, duration);
        }

        function initializeUserIdCheck() {
            const storedUserId = localStorage.getItem('userId');
            const currentUserIdSpan = document.getElementById('currentUserId');
            const inputGroup = document.getElementById('inputGroup');
            const configButton = document.getElementById('configButton');
            const dataSection = document.getElementById('dataSection');

            if (storedUserId) {
                currentUserIdSpan.textContent = storedUserId;
                inputGroup.style.display = 'none';
                configButton.style.display = 'inline-block';
                dataSection.style.display = 'block';
                refreshData(); // 自动加载数据
            } else {
                currentUserIdSpan.textContent = '未设置';
                inputGroup.style.display = 'block';
                configButton.style.display = 'none';
                dataSection.style.display = 'none';
            }
        }

        function showConfig() {
            const inputGroup = document.getElementById('inputGroup');
            const userId = localStorage.getItem('userId');
            document.getElementById('userId').value = userId || '';
            inputGroup.style.display = 'block';
        }

        function setUserId() {
            const userIdInput = document.getElementById('userId');
            const userId = userIdInput.value.trim();

            if (!userId) {
                showToast('请输入用户ID');
                return;
            }

            localStorage.setItem('userId', userId);
            initializeUserIdCheck();
            showToast('设置成功');
        }

        function formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        async function refreshData() {
            const userId = localStorage.getItem('userId');
            if (!userId) {
                showToast('请先设置用户ID');
                return;
            }

            try {
                const response = await fetch(`/admin/agent-sessions?pageNumber=1&pageSize=100`, {
                    headers: {
                        'x-user-id': userId
                    }
                });
                const data = await response.json();

                if (data.code === 'Ok') {
                    renderTable(data.data.items);
                    showToast('数据刷新成功');
                } else {
                    showToast('获取数据失败');
                }
            } catch (error) {
                showToast('请求失败：' + error.message);
            }
        }

        function renderTable(items) {
            const tableHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>会话名称</th>
                        <th>代理名称</th>
                        <th>仓库名称</th>
                        <th>作者</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>外部链接</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map(item => `
                        <tr>
                            <td>${item.sessionName || '-'}</td>
                            <td>${item.agentName || '-'}</td>
                            <td>${item.repositoryName || '-'}</td>
                            <td>${item.author || '-'}</td>
                            <td><span class="state state-${item.state}">${item.state}</span></td>
                            <td>${formatTimestamp(item.createTimestamp)}</td>
                            <td>${item.externalLink ?
                `<a href="${item.externalLink}" target="_blank" class="external-link">查看</a>` :
                '-'}</td>
                            <td>
                                <a href="/admin/ops-detail?user-id=${getCurrentUserId()}&session-id=${item.sessionId}"
                                   class="action-button"
                                   target="_blank">
                                    <i class="fas fa-external-link-alt"></i>
                                    查看详细信息
                                </a>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
            document.getElementById('tableContainer').innerHTML = tableHTML;
        }

        // 确保有获取当前用户ID的函数
        function getCurrentUserId() {
            return localStorage.getItem('userId');
        }

        window.onload = initializeUserIdCheck;
    </script>
</div>
</body>
</html>
