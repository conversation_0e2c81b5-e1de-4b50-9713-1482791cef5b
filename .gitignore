
# Binaries for programs and initPlugins
*.exe
*.exe~
*.dll
*.so
*.dylib
bin

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Kubernetes Generated files - skip generated files, except for vendored files

!vendor/**/zz_generated.*

# editor and IDE paraphernalia
.idea
.vscode
*.swp
*.swo
*~
*.gv
*.gv.svg


vendor/
.ssh/
/logs/

go.sum
config/*.toml

/sql/config/*_config.sql

coverage.out
coverage.html

.DS_store

temp/
main
agent-controller