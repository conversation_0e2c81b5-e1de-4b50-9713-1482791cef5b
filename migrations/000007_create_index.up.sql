CREATE INDEX idx_session_composite ON t_session(user_id, session_state, gmt_create);
CREATE INDEX idx_merge_request_session_id ON t_merge_request(session_id);
CREATE INDEX idx_agent_user_id ON t_agent(user_id);
create index idx_task_session_id_state on t_agent_task (session_id, state);
create index idx_repository_composite on t_repository (user_id, repo_url);
create index t_session_composite on t_session (user_id, identity_id);