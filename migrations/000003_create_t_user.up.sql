CREATE TABLE IF NOT EXISTS t_user
(
    id               bigserial PRIMARY KEY,                 -- 主键
    user_id          varchar(64) NOT NULL UNIQUE,           -- 用户ID，唯一键
    user_name        varchar(128),                          -- 用户名称
    avatar_url       varchar(512),                          -- 头像地址
    user_type        varchar(64),                           -- 用户类型，如 customer、sub、AssumedRoleUser
    is_inner_customer boolean DEFAULT false,                -- 是否内部用户，默认false(f),pgsql查询t表示true，f表示false
    site_type        varchar(64),                           -- 站点类型，如 domestic、intl
    gmt_create       timestamptz DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    gmt_modified     timestamptz DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

COMMENT ON TABLE t_user IS '用户信息表';

COMMENT ON COLUMN t_user.id IS '主键';
COMMENT ON COLUMN t_user.user_id IS '用户ID，唯一键';
COMMENT ON COLUMN t_user.user_name IS '用户名称';
COMMENT ON COLUMN t_user.avatar_url IS '头像地址';
COMMENT ON COLUMN t_user.user_type IS '用户类型，如 customer、sub、AssumedRoleUser';
COMMENT ON COLUMN t_user.is_inner_customer IS '是否内部用户，t（true）/f(false)，默认f';
COMMENT ON COLUMN t_user.site_type IS '站点类型，如 domestic、intl';
COMMENT ON COLUMN t_user.gmt_create IS '创建时间';
COMMENT ON COLUMN t_user.gmt_modified IS '更新时间';

-- 创建唯一索引
CREATE UNIQUE INDEX IF NOT EXISTS idx_t_user_user_id ON t_user (user_id);