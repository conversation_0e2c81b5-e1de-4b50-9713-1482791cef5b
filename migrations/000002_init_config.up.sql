INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('sonarQube','code-quality','Sonar Qube','静态扫描工具，检测漏洞与代码异味','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}, {"enum": ["BLOCKER", "CRITICAL", "MAJOR", "MINOR"], "name": "problemLevel", "type": "string", "alias": "最低问题输出级别", "required": true, "description": "最低问题输出级别"}]');
INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('biome','code-quality','Biome','代码格式化和 linting，检测语法错误和代码质量问题','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}]');
INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('eslint','code-quality','ESLint','可插拔的 JavaScript/TypeScript linter','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}]');
INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('golangciLint','code-quality','golangci-lint','Go 语言的 linters 集合，检查代码质量和潜在错误','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}, {"name": "configFile", "type": "string", "alias": "配置文件", "required": false, "description": "可选配置文件路径"}]');
INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('ruff','code-quality','Ruff','高性能 Python linter 和代码格式化工具','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}]');
INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('shellCheck','config-quality','ShellCheck','Shell 脚本的 linter','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}]');
INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('yamlLint','config-quality','YamlLint','YAML 文件的 linter','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}]');
INSERT INTO "t_code_review_tool_schema" ("name","type","alias","description","version","enabled","properties") VALUES ('gitLeaks','security','Gitleaks','检测代码中的密码、API 密钥和敏感信息','1.0.0','t','[{"name": "enable", "type": "bool", "alias": "开关", "required": true, "description": "开关工具"}]');


--component.agent-runtime.endpoint AR无损发布会自动更新这个地址，这个地址仅适用于开服流程
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('component','component.agent-runtime.endpoint','http://agent-runtime',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('component','component.sandbox-server.endpoint','http://sandbox-server',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('agent','agent.webhook.endpoint','',0,'*');


INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.yunxiao.endpoint','http://devops-nezha.yunxiao:9080',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.yunxiao.cdn-path','public',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.icon','',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.title','集成智能体',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.description','集成智能体',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.env','dedicated',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.id','0e209be7-51e8-4b8b-b8e7-fd5900e24c5e',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.home-page-version','1.0.1',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.teamix-ui-version','1.5.27',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.navigation.sdk.version','2.3.18',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.codelib-version','1.1.0',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.ingress.prefix','/agents',0,'*');