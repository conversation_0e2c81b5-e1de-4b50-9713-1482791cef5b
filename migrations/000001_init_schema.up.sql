CREATE TABLE IF NOT EXISTS t_config
(
    id           SERIAL                         NOT NULL PRIMARY KEY,
    "group"      VARCHAR(128)                   NOT NULL,
    config_key   VARCHAR(128)                   NOT NULL,
    config_value TEXT,
    encrypt      SMALLINT                       NOT NULL DEFAULT 0,
    env_tag      VARCHAR(64)                    NOT NULL,
    gmt_create   TIMESTAMP(3) WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    gmt_modified TIMESTAMP(3) WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,

    UNIQUE (config_key, env_tag)
    );
COMMENT ON COLUMN t_config."group" IS '分组';
COMMENT ON COLUMN t_config.config_key IS '配置键';
COMMENT ON COLUMN t_config.config_value IS '配置值';
COMMENT ON COLUMN t_config.encrypt IS '是否加密配置值';
COMMENT ON COLUMN t_config.env_tag IS '生效环境';
COMMENT ON COLUMN t_config.gmt_create IS '创建时间';
COMMENT ON COLUMN t_config.gmt_modified IS '修改时间';


CREATE TABLE IF NOT EXISTS t_agent_class
(
    id           SERIAL                         NOT NULL PRIMARY KEY,
    class_name   VARCHAR(128)                   NOT NULL,
    agent_type   VARCHAR(32)                    NOT NULL,
    gmt_create   TIMESTAMP(3) WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    gmt_modified TIMESTAMP(3) WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
    );
COMMENT ON COLUMN t_agent_class."class_name" IS 'agent class名';
COMMENT ON COLUMN t_agent_class."agent_type" IS 'agent类型： CodeReview';
COMMENT ON COLUMN t_agent_class.gmt_create IS '创建时间';
COMMENT ON COLUMN t_agent_class.gmt_modified IS '修改时间';

CREATE TABLE t_agent
(
    id               bigserial PRIMARY KEY,
    agent_id         varchar(64) UNIQUE NOT NULL,
    user_id          varchar(64)        NOT NULL,
    agent_name       varchar(64)        NOT NULL,
    agent_property   jsonb,
    gmt_create       timestamptz DEFAULT CURRENT_TIMESTAMP,
    gmt_modified     timestamptz DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_agent IS '存储 Agent 的基本信息';
COMMENT ON COLUMN t_agent.id IS '主键';
COMMENT ON COLUMN t_agent.agent_id IS '业务主键 唯一键';
COMMENT ON COLUMN t_agent.user_id IS '用户ID 资源归属账号';
COMMENT ON COLUMN t_agent.agent_name IS 'Agent名称/类型';
COMMENT ON COLUMN t_agent.agent_property IS 'Agent配置属性 JSON格式';
COMMENT ON COLUMN t_agent.gmt_create IS '创建时间';
COMMENT ON COLUMN t_agent.gmt_modified IS '更新时间';

CREATE TABLE IF NOT EXISTS t_agent_snapshot
(
    id               bigserial PRIMARY KEY,
    agent_id         varchar(64)        NOT NULL,
    session_id       varchar(64)        NOT NULL,
    data jsonb,
    gmt_create       timestamptz DEFAULT CURRENT_TIMESTAMP,
    gmt_modified     timestamptz DEFAULT CURRENT_TIMESTAMP,
    unique (agent_id, session_id)
    );

COMMENT ON TABLE t_agent_snapshot IS '存储 Agent快照数据';
COMMENT ON COLUMN t_agent_snapshot.id IS '主键';
COMMENT ON COLUMN t_agent_snapshot.session_id IS '会话ID';
COMMENT ON COLUMN t_agent_snapshot.data IS 'Agent快照数据';
COMMENT ON COLUMN t_agent_snapshot.gmt_create IS '创建时间';
COMMENT ON COLUMN t_agent_snapshot.gmt_modified IS '更新时间';


CREATE TABLE IF NOT EXISTS t_identity
(
    id                bigserial PRIMARY KEY,
    identity_id       varchar(64) UNIQUE  NOT NULL,          -- 业务主键 唯一键
    platform_endpoint varchar(4096)       NOT NULL,          -- 代码平台访问地址
    platform_username varchar(512)        NOT NULL,          -- 代码平台用户名
    platform_token    varchar(255)        NOT NULL,          -- 代码平台用户token
    platform_property jsonb,
    source            varchar(64)         NOT NULL,          -- 平台类型
    user_id           varchar(64)         NOT NULL,          -- 用户ID 资源归属账号
    webhook_token     varchar(255) UNIQUE NOT NULL,          -- 访问webhook使用的token
    agent_id          varchar(64)         NOT NULL,          -- Agent ID
    gmt_create        timestamptz DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    gmt_modified      timestamptz DEFAULT CURRENT_TIMESTAMP  -- 更新时间
    );

COMMENT ON TABLE t_identity IS '存储虚拟用户身份信息';
COMMENT ON COLUMN t_identity.id IS '主键';
COMMENT ON COLUMN t_identity.identity_id IS '业务主键 唯一键';
COMMENT ON COLUMN t_identity.platform_endpoint IS '代码平台访问地址';
COMMENT ON COLUMN t_identity.platform_username IS '代码平台用户名';
COMMENT ON COLUMN t_identity.platform_token IS '代码平台用户token';
COMMENT ON COLUMN t_identity.source IS '平台类型：云效 / Gitlab';
COMMENT ON COLUMN t_identity.user_id IS '用户ID 资源归属账号';
COMMENT ON COLUMN t_identity.webhook_token IS '访问webhook使用的token';
COMMENT ON COLUMN t_identity.agent_id IS 'Agent ID';
COMMENT ON COLUMN t_identity.gmt_create IS '创建时间';
COMMENT ON COLUMN t_identity.gmt_modified IS '更新时间';

-- 创建索引
CREATE INDEX idx_t_identity_agent_id ON t_identity (agent_id);
CREATE INDEX idx_t_identity_webhook_token ON t_identity (webhook_token);


CREATE TABLE t_session
(
    id            bigserial PRIMARY KEY,
    session_id    varchar(64) UNIQUE NOT NULL,
    identity_id   varchar(64)        NOT NULL,
    user_id       varchar(64)        NOT NULL,
    session_state varchar(32)        NOT NULL,
    session_property jsonb,
    metadata jsonb,
    gmt_create    timestamptz DEFAULT CURRENT_TIMESTAMP,
    gmt_modified  timestamptz DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE t_session IS '存储会话信息';
COMMENT ON COLUMN t_session.id IS '主键';
COMMENT ON COLUMN t_session.session_id IS '业务主键 唯一键';
COMMENT ON COLUMN t_session.identity_id IS '调用身份信息';
COMMENT ON COLUMN t_session.user_id IS '用户ID 资源归属账号';
COMMENT ON COLUMN t_session.session_state IS '会话状态';
COMMENT ON COLUMN t_session.gmt_create IS '创建时间';
COMMENT ON COLUMN t_session.gmt_modified IS '更新时间';


CREATE TABLE t_repository
(
    id       bigserial PRIMARY KEY,
    repo_id  varchar(64) NOT NULL,
    user_id  varchar(64) NOT NULL,
    source   varchar(64) NOT NULL,
    repo_url text        NOT NULL,
    repo_name varchar(256) NOT NULL,
    setting jsonb,
    last_review_time timestamptz DEFAULT CURRENT_TIMESTAMP,
    gmt_create    timestamptz DEFAULT CURRENT_TIMESTAMP,
    gmt_modified  timestamptz DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (repo_id)
);

COMMENT ON TABLE t_repository IS '存储仓库基本信息';
COMMENT ON COLUMN t_repository.id IS '主键';
COMMENT ON COLUMN t_repository.repo_id IS '仓库ID';
COMMENT ON COLUMN t_repository.user_id IS '用户ID 资源归属账号';
COMMENT ON COLUMN t_repository.source IS '平台类型：云效 / Gitlab';
COMMENT ON COLUMN t_repository.repo_url IS '仓库地址';


CREATE TABLE t_merge_request
(
    id      bigserial PRIMARY KEY,
    session_id   varchar(64) NOT NULL UNIQUE,
    repo_id varchar(64) NOT NULL,
    property jsonb,
    metric jsonb,
    gmt_create       timestamptz DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    gmt_modified     timestamptz DEFAULT CURRENT_TIMESTAMP -- 更新时间
);

COMMENT ON TABLE t_merge_request IS '存储合并请求基本信息';
COMMENT ON COLUMN t_merge_request.id IS '主键';
COMMENT ON COLUMN t_merge_request.session_id IS '会话ID';
COMMENT ON COLUMN t_merge_request.property IS 'MR 属性';
COMMENT ON COLUMN t_merge_request.metric IS 'MR 统计数据';

CREATE TABLE t_webhook_access
(
    id               bigserial PRIMARY KEY,                 -- 主键
    identity_id    varchar(64) NOT NULL,                  -- 调用身份
    source           varchar(64) NOT NULL,
    event_type       varchar(64) NOT NULL,                  -- 事件类型
    event_properties jsonb        NOT NULL,                  -- 事件数据 JSON格式
    gmt_create       timestamptz DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    gmt_modified     timestamptz DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);

COMMENT ON TABLE t_webhook_access IS '记录Webhook访问请求';
COMMENT ON COLUMN t_webhook_access.id IS '主键';
COMMENT ON COLUMN t_webhook_access.identity_id IS '调用身份';
COMMENT ON COLUMN t_webhook_access.event_type IS '事件类型';
COMMENT ON COLUMN t_webhook_access.event_properties IS '事件数据 JSON格式';
COMMENT ON COLUMN t_webhook_access.gmt_create IS '创建时间';
COMMENT ON COLUMN t_webhook_access.gmt_modified IS '更新时间';
-- 创建索引
CREATE INDEX idx_t_webhook_access_identity_id ON t_webhook_access (identity_id);

CREATE TABLE IF NOT EXISTS t_event
(
    id               bigserial PRIMARY KEY,                 -- 主键
    event_id         varchar(64) NOT NULL,
    session_id       varchar(64) NOT NULL,
    event_properties jsonb        NOT NULL,
    gmt_create       timestamptz DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    gmt_modified     timestamptz DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
    UNIQUE (event_id)
    );
COMMENT ON TABLE t_event IS '记录事件';
COMMENT ON COLUMN t_event.id IS '主键';
COMMENT ON COLUMN t_event.event_id IS '事件ID';
COMMENT ON COLUMN t_event.session_id IS '会话ID';
COMMENT ON COLUMN t_event.event_properties IS '事件数据 JSON格式';
COMMENT ON COLUMN t_event.gmt_create IS '创建时间';
COMMENT ON COLUMN t_event.gmt_modified IS '更新时间';

CREATE TABLE IF NOT EXISTS t_agent_task
(
    id           bigserial primary key,
    task_id      varchar(64) not null,
    session_id   varchar(64) not null,
    state        varchar(64) not null,
    failed_count integer     default 0,
    request      jsonb       not null,
    gmt_create   timestamptz DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    gmt_modified timestamptz DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    UNIQUE (task_id)
    );

COMMENT ON TABLE t_agent_task IS 'Agent任务';

CREATE TABLE IF NOT EXISTS t_github_installation_identity
(
    id              bigserial PRIMARY KEY,
    installation_id int8      NOT NULL,
    user_id         varchar(64) NOT NULL,
    login_user_type varchar(32) NOT NULL,
    login_user_id   varchar(64) NOT NULL,
    gmt_create      timestamptz DEFAULT CURRENT_TIMESTAMP,
    gmt_modified    timestamptz DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (installation_id)
    );
COMMENT ON TABLE t_github_installation_identity IS 'Github安装阿里云身份';
COMMENT ON COLUMN t_github_installation_identity.id IS '主键';
COMMENT ON COLUMN t_github_installation_identity.installation_id IS 'Github App 安装ID';
COMMENT ON COLUMN t_github_installation_identity.user_id IS '阿里云主账号ID';
COMMENT ON COLUMN t_github_installation_identity.login_user_type IS '阿里云登陆账号类型(account、user、role)';
COMMENT ON COLUMN t_github_installation_identity.login_user_id IS '阿里云登陆账号阿里云ID';
COMMENT ON COLUMN t_github_installation_identity.gmt_create IS '创建时间';
COMMENT ON COLUMN t_github_installation_identity.gmt_modified IS '修改时间';

CREATE TABLE IF NOT EXISTS t_code_review_feedback
(
    id                bigserial PRIMARY KEY,
    session_id        VARCHAR(255) NOT NULL,
    suggestion_id     VARCHAR(255) NOT NULL,
    feedback_type     VARCHAR(10) NOT NULL CHECK (feedback_type IN ('helpful', 'neutral', 'misleading')),
    feedback_content  VARCHAR(1024) DEFAULT '',
    gmt_create        timestamptz DEFAULT CURRENT_TIMESTAMP,
    gmt_modified      timestamptz DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (suggestion_id, session_id)
    );
COMMENT ON TABLE t_code_review_feedback IS '用户评论反馈';
COMMENT ON COLUMN t_code_review_feedback.id IS '主键';
COMMENT ON COLUMN t_code_review_feedback.suggestion_id IS '对应 ar 里原始的 suggestion id';
COMMENT ON COLUMN t_code_review_feedback.session_id IS 'session Id';
COMMENT ON COLUMN t_code_review_feedback.feedback_type IS '反馈类型';
COMMENT ON COLUMN t_code_review_feedback.feedback_content IS '具体反馈内容';


create table t_code_review_tool_schema
(
    id           bigserial
        constraint t_code_review_tool_schema_pk
            primary key,
    name         varchar(64) not null
        constraint t_code_review_tool_schema_pk_2
            unique,
    type         varchar(64) not null,
    alias        varchar(256),
    description  text,
    version      varchar(32),
    enabled      boolean,
    properties   jsonb,
    gmt_create   timestamp with time zone,
    gmt_modified timestamp with time zone
);
comment on table t_code_review_tool_schema is '代码评审工具定义';
comment on column t_code_review_tool_schema.name is '工具名称';
comment on column t_code_review_tool_schema.type is '工具类别';
comment on column t_code_review_tool_schema.alias is '工具别名';
comment on column t_code_review_tool_schema.description is '描述';
comment on column t_code_review_tool_schema.version is '版本';
comment on column t_code_review_tool_schema.enabled is '是否启用';
comment on column t_code_review_tool_schema.properties is '工具配置属性';
