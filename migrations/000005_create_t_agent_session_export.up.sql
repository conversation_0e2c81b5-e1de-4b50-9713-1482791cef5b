CREATE TABLE t_agent_session_export (
    id bigserial PRIMARY KEY,
    organization_id VARCHAR(128) NOT NULL,
    operator_id VARCHAR(128),
    operator_name VARCHAR(128),
    ip VARCHAR(32),
    query JSONB,
    gmt_create TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建 organization_id 的索引
CREATE INDEX idx_agent_session_export_org_id ON t_agent_session_export(organization_id);
COMMENT ON COLUMN t_agent_session_export.gmt_create IS '创建时间';