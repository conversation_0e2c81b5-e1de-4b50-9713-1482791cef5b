CREATE TABLE t_token_usage (
   id BIGSERIAL PRIMARY KEY,
   sessions_id VARCHAR(64),
   model_name VARCHAR(64),
   prompt_tokens BIGINT,
   completion_tokens BIGINT,
   total_tokens BIGINT,
   gmt_create TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加字段注释
COMMENT ON COLUMN t_token_usage.gmt_create IS '创建时间';
-- 创建 sessions_id 的索引
CREATE INDEX idx_token_usage_sessions_id ON t_token_usage(sessions_id);
-- 创建时间索引
CREATE INDEX idx_token_usage_gmt_create ON t_token_usage(gmt_create);

CREATE TABLE t_mr_statistic (
    id BIGSERIAL PRIMARY KEY,
    sessions_id VARCHAR,
    file_count BIGINT,
    del_char_count BIGINT,
    new_char_count BIGINT,
    del_line_count BIGINT,
    new_line_count BIGINT,
    gmt_create TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 添加字段注释
COMMENT ON COLUMN t_mr_statistic.gmt_create IS '创建时间';

-- 创建 sessions_id 的索引
CREATE INDEX idx_mr_statistic_sessions_id ON t_mr_statistic(sessions_id);
-- 创建时间索引
CREATE INDEX idx_mr_statistic_gmt_create ON t_mr_statistic(gmt_create);
