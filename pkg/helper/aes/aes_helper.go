package aes

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	errors2 "github.com/pkg/errors"
	"io"
	"os"
)

const (
	envKeyAESKey = "AES_KEY"
)

// EncryptString 加密字符串并返回base64编码的结果
func EncryptString(plainText string) (string, error) {
	key := os.Getenv(envKeyAESKey)
	if len(key) == 0 {
		return "", errors2.<PERSON>rro<PERSON>("empty AES Key")
	}
	return aesEncryptString(key, plainText)
}

// DecryptString 解密base64编码的字符串
func DecryptString(cipherText string) (string, error) {
	key := os.Getenv(envKeyAESKey)
	if len(key) == 0 {
		return "", errors2.<PERSON><PERSON><PERSON>("empty AES Key")
	}
	return aesDecryptString(key, cipherText)
}

// aesEncryptString 加密字符串并返回base64编码的结果
func aesEncryptString(key, plainText string) (string, error) {
	// 将字符串转换为字节数组
	byteText := []byte(plainText)
	byteKey := []byte(key)

	// 创建AES块实例
	block, err := aes.NewCipher(byteKey)
	if err != nil {
		return "", errors2.WithStack(err)
	}

	// PKCS#7填充
	padding := aes.BlockSize - len(byteText)%aes.BlockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	byteText = append(byteText, padText...)

	// 初始向量IV必须是唯一的，但不需要是保密的
	cipherText := make([]byte, aes.BlockSize+len(byteText))
	iv := cipherText[:aes.BlockSize]
	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return "", errors2.WithStack(err)
	}

	// 使用CBC模式加密
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(cipherText[aes.BlockSize:], byteText)

	// 返回base64编码的加密字符串
	return base64.StdEncoding.EncodeToString(cipherText), nil
}

// aesDecryptString 解密base64编码的字符串
func aesDecryptString(key, cipherText string) (string, error) {
	byteText, err := base64.StdEncoding.DecodeString(cipherText)
	if err != nil {
		return "", errors2.WithStack(err)
	}
	byteKey := []byte(key)

	// 创建AES块实例
	block, err := aes.NewCipher(byteKey)
	if err != nil {
		return "", errors2.WithStack(err)
	}

	if len(byteText) < aes.BlockSize {
		return "", errors2.Errorf("ciphertext too short")
	}

	iv := byteText[:aes.BlockSize]
	byteText = byteText[aes.BlockSize:]

	// CBC模式
	mode := cipher.NewCBCDecrypter(block, iv)
	mode.CryptBlocks(byteText, byteText)

	// PKCS#7去填充
	padding := int(byteText[len(byteText)-1])
	if padding < 1 || padding > aes.BlockSize {
		return "", errors2.Errorf("invalid padding")
	}
	byteText = byteText[:len(byteText)-padding]

	return string(byteText), nil
}

const (
	AES128KeySize = 16 // 128 bits
	AES192KeySize = 24 // 192 bits
	AES256KeySize = 32 // 256 bits
)

// GenerateAESKey 生成指定长度的AES密钥
func GenerateAESKey(keySize int) ([]byte, error) {
	key := make([]byte, keySize)
	_, err := rand.Read(key)
	if err != nil {
		return nil, err
	}
	return key, nil
}
