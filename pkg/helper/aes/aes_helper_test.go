package aes

import (
	"fmt"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
)

func TestAESEncryptDecrypt(t *testing.T) {
	key := os.Getenv(envKeyAESKey)
	if len(key) == 0 {
		key = "1234567890123456"
		_ = os.Setenv(envKeyAESKey, key)
	}
	values := []string{
		"abababab",
	}
	fmt.Println("aes_key: " + key)
	for _, v := range values {
		encryptString, err := EncryptString(v)
		if err != nil {
			panic(err)
		}
		decryptString, err := DecryptString(encryptString)
		if err != nil {
			panic(err)
		}
		if v != decryptString {
			panic(errors.Errorf("value mismacth, raw: %s, encrypt: %s, decrypt: %s", v, encryptString, decryptString))
		}
		fmt.Println("--------------")
		fmt.Println("raw:" + v)
		fmt.Println("encrypt:" + encryptString)
		fmt.Println("decrypt:" + decryptString)
	}
}

func TestAESDecrypt(t *testing.T) {
	key := os.Getenv(envKeyAESKey)
	if len(key) == 0 {
		key = "1234567890123456"
	}
	values := []string{
		// 待解密内容
	}
	for _, v := range values {
		decryptString, err := aesDecryptString(key, v)
		if err != nil {
			panic(err)
		}
		fmt.Println(fmt.Sprintf("%s, decrpt: %s", v, decryptString))
	}
}

func TestGenerateAESKey(t *testing.T) {
	key, err := GenerateAESKey(AES128KeySize)
	assert.Nil(t, err)
	fmt.Printf("Generated AES Key: %x\n", key)
}
