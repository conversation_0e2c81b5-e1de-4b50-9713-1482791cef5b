package helper

import (
	"bytes"
	"compress/gzip"
	"crypto/md5"
	"encoding/hex"
	mapset "github.com/deckarep/golang-set/v2"
	"github.com/spf13/cast"
	"unicode/utf8"
)

func CalculateMD5(text string) string {
	hash := md5.Sum([]byte(text))
	return hex.EncodeToString(hash[:])
}

func ToBoolSafely(s string) bool {
	booleanValue, err := cast.ToBoolE(s)
	if err != nil {
		return false
	}
	return booleanValue
}

func GzipCompress(input []byte) ([]byte, error) {
	var buf bytes.Buffer
	zw := gzip.NewWriter(&buf)

	_, err := zw.Write(input)
	if err != nil {
		return nil, err
	}

	err = zw.Close()
	if err != nil {
		return nil, err
	}

	// 返回压缩后的数据
	return buf.Bytes(), nil
}

func BitsToGigabytes(bits float64) float64 {
	const bitsPerGByte = 1024 * 1024 * 1024 * 8
	return bits / bitsPerGByte
}

func TruncateString(str string, maxLength int) string {
	if utf8.RuneCountInString(str) > maxLength {
		return string([]rune(str)[:maxLength])
	}
	return str
}

// FindCommonStrings 返回两个切片的公共元素
func FindCommonStrings(slice1, slice2 []string) []string {
	s1 := mapset.NewThreadUnsafeSet(slice1...)
	s2 := mapset.NewThreadUnsafeSet(slice2...)
	return s1.Intersect(s2).ToSlice()
}
