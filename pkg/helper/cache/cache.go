package cache

import (
	"github.com/dgraph-io/ristretto"
	"github.com/pkg/errors"
)

func NewCache() *ristretto.Cache {
	cache, _ := NewCacheWithE()
	return cache
}

func NewCacheWithE() (*ristretto.Cache, error) {
	if cache, err := ristretto.NewCache(&ristretto.Config{
		NumCounters: 1e7,
		MaxCost:     1e8, // Maximum cost of cache.
		BufferItems: 64,
		Metrics:     true,
	}); err == nil {
		return cache, nil
	} else {
		return nil, errors.WithStack(err)
	}
}
