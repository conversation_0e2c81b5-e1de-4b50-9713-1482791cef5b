package helper

import (
	"bytes"
	"compress/gzip"
	"io/ioutil"
	"testing"
)

func TestGzipCompress(t *testing.T) {
	cases := []struct {
		input    []byte
		expected []byte
		err      error
	}{
		{[]byte("Hello, World!"), nil, nil},
		{[]byte(""), nil, nil},
		{[]byte("1234567890"), nil, nil},
		{[]byte(`{"name":"<PERSON>","age":30}`), nil, nil},
		{[]byte(`[{"name":"<PERSON>","age":30},{"name":"<PERSON>","age":25}]`), nil, nil},
		{[]byte(`{"menu": {"id": "file", "value": "File", "popup": {"menuitem": [{"value": "New", "onclick": "CreateNewDoc()"},{"value": "Open", "onclick": "OpenDoc()"},{"value": "Save", "onclick": "SaveDoc()"}]}}}`), nil, nil},
	}

	for _, c := range cases {
		t.Run(string(c.input), func(t *testing.T) {
			output, err := GzipCompress(c.input)
			if err != nil {
				t.Errorf("GzipCompress(%q) returned error: %v", c.input, err)
				return
			}

			// 解压缩以验证输出
			r, err := gzip.NewReader(bytes.NewReader(output))
			if err != nil {
				t.Errorf("gzip.NewReader returned error: %v", err)
				return
			}
			decompressed, err := ioutil.ReadAll(r)
			if err != nil {
				t.Errorf("ioutil.ReadAll returned error: %v", err)
				return
			}
			r.Close()

			if !bytes.Equal(decompressed, c.input) {
				t.Errorf("Decompressed data = %q; want %q", decompressed, c.input)
			}
		})
	}
}

func TestBitsToGigabytes(t *testing.T) {
	testCases := []struct {
		bits     float64
		expected float64
	}{
		{8589934592.0, 1.0},    // 1 GB
		{17179869184.0, 2.0},   // 2 GB
		{4294967296.0, 0.5},    // 0.5 GB
		{34359738368.0, 4.0},   // 4 GB
		{274877906944.0, 32.0}, // 32 GB
		{0.0, 0.0},             // 0 GB
		{-8589934592.0, -1.0},  // -1 GB (负值测试)
	}

	for _, tc := range testCases {
		result := BitsToGigabytes(tc.bits)
		if result != tc.expected {
			t.Errorf("BitsToGigabytes(%v) = %v; expected %v", tc.bits, result, tc.expected)
		}
	}
}

func TestCalculateMD5(t *testing.T) {
	type args struct {
		text string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "case-abc",
			args: args{text: "abc"},
			want: "900150983cd24fb0d6963f7d28e17f72",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CalculateMD5(tt.args.text); got != tt.want {
				t.Errorf("CalculateMD5() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestToBoolSafely(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		args args
		want bool
	}{
		{
			args: args{s: "true"},
			want: true,
		},
		{
			args: args{s: "True"},
			want: true,
		},
		{
			args: args{s: "TRUE"},
			want: true,
		},
		{
			args: args{s: "a"},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.args.s, func(t *testing.T) {
			if got := ToBoolSafely(tt.args.s); got != tt.want {
				t.Errorf("ToBoolSafely() = %v, want %v", got, tt.want)
			}
		})
	}
}
