package i18n

import (
	"fmt"
	"gopkg.in/yaml.v3"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
)

type Locale string

const (
	English            = Locale("en_US")
	Japanese           = Locale("ja_JP")
	SimplifiedChinese  = Locale("zh_CN")
	TraditionalChinese = Locale("zh_TW")
)

var (
	replacement = regexp.MustCompile("\\{\\d*}")
)

func MustInitI18n() {
	i18nDir := os.Getenv("I18N_DIR")
	if i18nDir == "" {
		i18nDir = "./locales"
	}
	err := LoadDirectory(i18nDir)
	if err != nil {
		panic(err)
	}
}

var (
	translations = make(map[Locale]map[string]interface{})
	mutex        sync.RWMutex
)

// LoadDirectory 加载指定目录下的所有语言包
func LoadDirectory(dir string) error {
	// 读取主目录
	entries, err := os.ReadDir(dir)
	if err != nil {
		return fmt.Errorf("failed to read i18n directory: %w", err)
	}

	// 遍历所有语言目录
	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		locale := Locale(entry.Name())
		if err := loadLocale(dir, locale); err != nil {
			return fmt.Errorf("failed to load locale %s: %w", locale, err)
		}
	}

	return nil
}

// loadLocale 加载单个语言的所有翻译文件
func loadLocale(dir string, locale Locale) error {
	mutex.Lock()
	defer mutex.Unlock()

	localeDir := filepath.Join(dir, string(locale))
	files, err := os.ReadDir(localeDir)
	if err != nil {
		return fmt.Errorf("failed to read locale directory: %w", err)
	}

	trans := make(map[string]interface{})

	for _, file := range files {
		if filepath.Ext(file.Name()) != ".yaml" && filepath.Ext(file.Name()) != ".yml" {
			continue
		}

		filePath := filepath.Join(localeDir, file.Name())
		data, err := os.ReadFile(filePath)
		if err != nil {
			return fmt.Errorf("failed to read file %s: %w", filePath, err)
		}

		var fileContent map[string]interface{}
		if err := yaml.Unmarshal(data, &fileContent); err != nil {
			return fmt.Errorf("failed to parse yaml file %s: %w", filePath, err)
		}

		for k, v := range fileContent {
			trans[k] = v
		}
	}

	translations[locale] = trans
	return nil
}

// Localize 获取指定key的翻译内容
func Localize(locale Locale, key string, params ...any) string {
	mutex.RLock()
	defer mutex.RUnlock()

	// 获取语言包
	trans, ok := translations[locale]
	if !ok {
		return key
	}

	// 按照点号分隔符解析key
	value := getNestedValue(trans, key)
	if value == nil {
		return key
	}

	// 转换为字符串
	text, ok := value.(string)
	if !ok {
		return key
	}

	// 如果有参数，进行格式化
	if len(params) > 0 {
		return fmt.Sprintf(text, params...)
	}

	return text
}

// getNestedValue 获取嵌套的值
func getNestedValue(m map[string]interface{}, key string) interface{} {
	parts := strings.Split(key, ".")
	current := m

	for i, part := range parts {
		if i == len(parts)-1 {
			return current[part]
		}

		next, ok := current[part].(map[string]interface{})
		if !ok {
			return nil
		}
		current = next
	}

	return nil
}

// ClearTranslations 清除翻译缓存
func ClearTranslations() {
	mutex.Lock()
	defer mutex.Unlock()
	translations = make(map[Locale]map[string]interface{})
}

func LocalizeOrDefault(locale Locale, key string, defaultValue string, objs ...any) string {
	val := Localize(locale, key, objs...)
	if val != "" {
		return val
	}
	return defaultValue
}

func format(formatter string, objs ...any) string {
	if len(objs) == 0 {
		return formatter
	}
	f := strings.ReplaceAll(formatter, "%", "%%")
	// mcms中参数占位符是从0开始，但fmt中的占位参数从1开始，这里要做下标转换
	replaceIndex := func(group string) string {
		if len(group) == 2 {
			return "%v"
		}
		idx, _ := strconv.Atoi(group[1 : len(group)-1])
		return "%[" + strconv.Itoa(idx+1) + "]v"
	}
	f = replacement.ReplaceAllStringFunc(f, replaceIndex)
	return fmt.Sprintf(f, objs...)
}
