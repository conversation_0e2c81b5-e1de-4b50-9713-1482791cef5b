package helper

import (
	"github.com/google/uuid"
	"github.com/rs/xid"
	"strings"
)

func NewIDWithPrefix(prefix string) string {
	return prefix + NewId()
}

func NewId() string {
	return xid.New().String()
}

func NewUUID() string {
	id := uuid.New()
	return strings.ToUpper(id.String())
}

func NewUUIDLower() string {
	id := uuid.New()
	return strings.ToLower(id.String())
}

func NewRequestId() string {
	return NewUUID()
}

func NewTraceId() string {
	return NewUUIDLower()
}
