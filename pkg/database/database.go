package database

import (
	"errors"
	"fmt"
	"github.com/glebarez/sqlite"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"log"
	"os"
	"sync"
	"time"
)

const (
	MysqlDriver      = "mysql"
	SqlLiteDriver    = "sqlite"
	PostgresqlDriver = "postgresql"
)

// 获取db
var (
	db   *gorm.DB
	once sync.Once
)

func GetDatabase() *gorm.DB {
	return db
}

type Config struct {
	Host     string
	Port     uint32
	User     string
	Password string
	Driver   string
	Name     string

	MaxIdleConns           *int
	MaxOpenConns           *int
	ConnMaxLifetimeSeconds *int64

	Params map[string]string
}

func SetupDB(cfg *Config) (*gorm.DB, error) {
	var err error
	once.Do(func() {
		db, err = initDB(cfg)
	})
	return db, err
}

// 初始化db
func initDB(cfg *Config) (*gorm.DB, error) {
	if cfg == nil {
		return nil, errors.New("database config is nil")
	}
	var db *gorm.DB
	var err error
	switch cfg.Driver {
	case MysqlDriver:
		dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local", cfg.User, cfg.Password, cfg.Host, cfg.Port, cfg.Name)
		db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{
			// fixme 替换成合适的日志框架构造
			Logger: logger.New(log.New(os.Stdout, "\r\n", log.LstdFlags), logger.Config{
				IgnoreRecordNotFoundError: true,
			}),
		})
	case SqlLiteDriver:
		db, err = gorm.Open(sqlite.Open("aiagent.db"), &gorm.Config{})
	case PostgresqlDriver:
		var dsn string
		if cfg.User == "" {
			dsn = fmt.Sprintf("host=%s dbname=%s port=%d sslmode=disable TimeZone=Asia/Shanghai", cfg.Host, cfg.Name, cfg.Port)
		} else {
			dsn = fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%d sslmode=disable TimeZone=Asia/Shanghai", cfg.Host, cfg.User, cfg.Password, cfg.Name, cfg.Port)
		}

		db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
			// fixme 替换成合适的日志框架构造
			Logger: logger.New(log.New(os.Stdout, "\r\n", log.LstdFlags), logger.Config{
				IgnoreRecordNotFoundError: true,
			}),
		})
	default:
		return nil, fmt.Errorf("unsupported driver: %s", cfg.Driver)
	}

	if err != nil {
		return nil, err
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}
	if cfg.MaxIdleConns != nil {
		sqlDB.SetMaxIdleConns(*cfg.MaxIdleConns)
	}

	if cfg.MaxOpenConns != nil {
		sqlDB.SetMaxOpenConns(*cfg.MaxOpenConns)
	}

	if cfg.ConnMaxLifetimeSeconds != nil {
		sqlDB.SetConnMaxLifetime(time.Duration(*cfg.ConnMaxLifetimeSeconds) * time.Second)
	}

	return db, err
}
