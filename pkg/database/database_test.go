package database

import (
	"github.com/smartystreets/goconvey/convey"
	"k8s.io/utils/ptr"
	"testing"
)

func TestSetupDB(t *testing.T) {
	convey.Convey("Test setup", t, func() {
		cfg := &Config{
			Driver:                 SqlLiteDriver,
			MaxIdleConns:           ptr.To(10),
			MaxOpenConns:           ptr.To(10),
			ConnMaxLifetimeSeconds: ptr.To(int64(10)),
		}

		gormDb, err := SetupDB(cfg)
		convey.So(err, convey.ShouldBeNil)
		convey.So(gormDb, convey.ShouldNotBeNil)
	})
}
