package sandbox

import (
	"context"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/component"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	errors2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/sandbox"
)

var _ comncore.SandboxService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) CreateSandbox(ctx base.Context, request *sandbox.CreateSandboxRequest) (*sandbox.SandboxInfo, error) {
	return component.CreateSandbox(ctx, request)
}

func (s *service) CreateOrLoadSandbox(ctx base.Context, sessionId string, cfg *sandbox.Config) (*sandbox.SandboxInfo, error) {
	ctx.GetLogger().Info("create or load sandbox", "sessionId", sessionId)
	// 先通过sessionId 去获取沙箱信息
	if sandboxInfo, err := component.GetAvailableSandbox(ctx, sessionId); err != nil {
		return nil, errors.WithStack(err)
	} else if sandboxInfo != nil {
		return sandboxInfo, nil
	}
	// 没有存在的沙箱则创建一个
	resp, err := s.CreateSandbox(ctx, &sandbox.CreateSandboxRequest{
		SessionId:        sessionId,
		K8sRuntimeConfig: cfg.K8sRuntimeConfig,
		SandboxConfig:    cfg.SandboxConfig})
	if err != nil {
		errors2.AlertError(ctx, errors2.AlertScopeSandbox, []string{sessionId, ctx.GetUid()}, "create sandbox failed", err)
		return nil, errors.WithStack(err)
	}
	return resp, nil
}

func (s *service) DeleteSandbox(ctx base.Context, sandboxId string) error {
	return component.DeleteSandbox(ctx, sandboxId)
}

func (s *service) DeleteSandboxForSession(ctx base.Context, sessionId string) error {
	if sandboxInfo, err := component.GetAvailableSandbox(ctx, sessionId); err != nil {
		return errors.WithStack(err)
	} else if sandboxInfo != nil {
		sbId := sandboxInfo.SandboxId
		return component.DeleteSandbox(ctx, sbId)
	}
	return nil
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "SandboxService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.SandboxService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
