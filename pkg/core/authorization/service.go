package authorization

import (
	"context"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/authorization"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/yunxiao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
)

var _ comncore.AuthorizationService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) VerifyOperationPermission(ctx base.Context, operation authorization.Operation) error {
	yunxiaoCtx := base.GetYunxiaoUserFromContext(ctx)
	if yunxiaoCtx == nil {
		// 非云效上下文直接放通
		return nil
	}
	if config.GetOrDefaultBool(config.KeySystemYunxiaoAuthorizationDisabled, false) {
		// 通过配置可以临时关闭云效权限验证
		return nil
	}
	//校验用户当前权限是否拥有operation
	pass, err := yunxiao.CheckPermission(ctx, []string{string(operation)})
	if err != nil {
		return err
	}
	if !pass {
		return errors.New(codes.ErrForbidden, operation)
	}
	return nil
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "AuthorizationService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.AuthorizationService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
