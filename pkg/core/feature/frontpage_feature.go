package feature

import (
	mapset "github.com/deckarep/golang-set/v2"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/authorization"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/feature"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/yunxiao"
)

func getFrontPageFeature(ctx base.Context) (*feature.FeatureInfo, error) {
	if !base.IsYunxiaoUser(ctx) {
		return nil, nil
	}
	valueSet := mapset.NewThreadUnsafeSet[string]()
	userPermissionResp, err := yunxiao.ListUserPermissions(ctx)
	if err != nil {
		return nil, err
	}
	for _, p := range userPermissionResp.Permissions {
		permissions := authorization.Operation(p).ToFrontPagePermissions()
		for _, permission := range permissions {
			valueSet.Add(permission)
		}
	}

	return &feature.FeatureInfo{
		Name:  "agents.front-page.show",
		Value: valueSet.ToSlice(),
	}, nil
}

func mockFrontPageFullFeature(ctx base.Context) (*feature.FeatureInfo, error) {
	ops := []authorization.Operation{
		authorization.ViewAgentLogs,
		authorization.ViewAgentConfiguration,
		authorization.ManageAgentConfiguration,
		authorization.ToggleAgent,
		authorization.DownloadAgentLogs,
	}
	valueSet := mapset.NewThreadUnsafeSet[string]()
	for _, op := range ops {
		for _, permission := range op.ToFrontPagePermissions() {
			valueSet.Add(permission)
		}
	}
	return &feature.FeatureInfo{
		Name:  "agents.front-page.show",
		Value: valueSet.ToSlice(),
	}, nil
}
