package feature

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/feature"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
)

const (
	modeAccessToken  = "AccessToken"
	modeAgentConnect = "AgentConnect"
)

func getGitlabAccessMode(ctx base.Context) (*feature.FeatureInfo, error) {
	// todo 根据用户购买情况，选择展示AccessToken / AgentConnect

	mode := config.GetGitlabIntegrationMode(ctx.GetUid())
	switch mode {
	case modeAccessToken, modeAgentConnect:
	default:
		mode = modeAccessToken
	}
	return &feature.FeatureInfo{
		Name:  "agents.code-review-agent.gitlab.identity.mode",
		Value: mode, // AccessToken / AgentConnect
	}, nil
}
