package feature

import (
	"context"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/feature"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"reflect"
	"runtime"
	"strings"
)

var _ comncore.FeatureService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "FeatureService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func (s *service) ListUserFeatures(ctx base.Context) ([]feature.FeatureInfo, error) {
	featureFuncs := []func(base.Context) (*feature.FeatureInfo, error){
		getGitlabAccessMode,
	}
	if config.GetOrDefaultBool(config.KeySystemFrontPagePermissionMockEnabled, false) {
		// 通过Mock返回前端权限
		featureFuncs = append(featureFuncs, mockFrontPageFullFeature)
	} else {
		featureFuncs = append(featureFuncs, getFrontPageFeature)
	}
	result := make([]feature.FeatureInfo, 0, len(featureFuncs))
	for _, f := range featureFuncs {
		feat, err := f(ctx)
		if err != nil {
			funcName := runtime.FuncForPC(reflect.ValueOf(f).Pointer()).Name()
			shortFuncName := funcName[strings.LastIndex(funcName, ".")+1:]
			ctx.GetLogger().Error("failed to get feature", "func", shortFuncName, "err", err)
			return nil, err
		}
		if feat == nil {
			continue
		}
		result = append(result, *feat)
	}

	return result, nil
}

func New() comncore.FeatureService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
