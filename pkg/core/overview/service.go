package overview

import (
	"context"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/overview"
)

var _ comncore.OverviewService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) ListUserMetrics(ctx base.Context) ([]overview.MetricInfo, error) {
	sessionMetrics, err := s.core.GetSessionService().GetMergeRequestMetrics(ctx)
	if err != nil {
		return nil, err
	}
	return sessionMetrics, nil
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "OverviewService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.OverviewService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
