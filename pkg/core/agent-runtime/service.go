package agent_runtime

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/component"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	errors2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/sandbox"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"k8s.io/utils/ptr"
	"net/url"
	"time"
)

var _ comncore.AgentRuntimeService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

type agentTaskInfo struct {
	AgentId    string         `json:"agent_id,omitempty"`
	AgentName  string         `json:"agent_name,omitempty"`
	Parameters map[string]any `json:"parameters,omitempty"`
	AbortUrl   string         `json:"abort_url,omitempty"`
}

func (s *service) StepSession(ctx base.Context, sessionId string, req *agentruntime.StepSessionRequest) error {
	agentId := req.AgentId
	agentInfo, err := s.core.GetAgentService().GetAgent(ctx, agentId)
	if err != nil {
		return errors.WithStack(err)
	}
	// 运行时加载沙箱配置
	if err := s.LoadSandboxConfig(ctx, agentId, sessionId, req); err != nil {
		ctx.GetLogger().Error("load sandbox config failed", "err", err)
		return errors.WithStack(err)
	}
	// 运行Agent时加载LLM配置，减少敏感信息落库次数
	if agentInfo.AgentProperty != nil {
		if agentInfo.AgentProperty.ModelName != "" && agentInfo.AgentProperty.ModelProvider != "" {
			switch agentInfo.AgentProperty.ModelProvider {
			case agent.ModelProviderSystem:
				req.LLMConfig = &agentruntime.LlmConfig{
					ApiKey:    config.Get(config.KeyAgentLLMApiKey),
					BaseUrl:   config.GetOrDefault(config.KeyAgentLLMBaseUrl, config.DefaultAgentLLMBaseUrl),
					ModelName: agentInfo.AgentProperty.ModelName,
				}
			}
		}
	}

	resp, err := component.AgentRuntimeStepSession(ctx, sessionId, req)
	if err != nil {
		errors2.AlertError(ctx, errors2.AlertScopeAgentRuntime, []string{agentId, sessionId, ctx.GetUid()}, "step session failed", err)
		return errors.WithStack(err)
	}
	info := &agentTaskInfo{
		AgentId:    agentId,
		AgentName:  req.AgentName,
		Parameters: req.TaskConfig.Parameters,
		AbortUrl:   resp.Data.AbortUrl,
	}
	_ = saveTaskInfo(ctx, sessionId, info)
	return nil
}

const (
	defaultTaskInfoTTL = 1 * time.Hour
)

func taskInfoKey(sessionId string) string {
	return fmt.Sprintf("task_info_%s", sessionId)
}

func saveTaskInfo(ctx base.Context, sessionId string, info *agentTaskInfo) error {
	cli := helper.GetRedis()
	marshal, err := json.Marshal(info)
	if err != nil {
		ctx.GetLogger().Error("marshal agent task info failed", "info", info, "err", err)
		return errors.WithStack(err)
	}
	key := taskInfoKey(sessionId)
	if err := cli.Set(ctx, key, string(marshal), defaultTaskInfoTTL).Err(); err != nil {
		ctx.GetLogger().Error("save agent task info failed", "info", info, "err", err)
		return errors.WithStack(err)
	}
	return nil
}

func loadTaskInfo(ctx base.Context, sessionId string) (*agentTaskInfo, error) {
	cli := helper.GetRedis()
	key := taskInfoKey(sessionId)
	val, err := cli.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) {
			ctx.GetLogger().Warn("agent task info not found")
			return nil, nil
		}
		ctx.GetLogger().Error("load agent task info failed", "err", err)
		return nil, errors.WithStack(err)
	}
	var info agentTaskInfo
	if err := json.Unmarshal([]byte(val), &info); err != nil {
		ctx.GetLogger().Error("unmarshal agent task info failed", "info", val, "err", err)
		return nil, errors.WithStack(err)
	}
	return &info, nil
}

func (s *service) CleanTaskInfo(ctx base.Context, sessionId string) error {
	key := taskInfoKey(sessionId)
	cli := helper.GetRedis()
	if err := cli.Del(ctx, key).Err(); err != nil {
		if errors.Is(err, redis.Nil) {
			ctx.GetLogger().Info("agent task info not found", "key", key)
			return nil
		}
		ctx.GetLogger().Error("clean agent task info failed", "key", key, "err", err)
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) AbortSession(ctx base.Context, sessionId string, options *agentruntime.AbortSessionOptions) error {
	info, err := loadTaskInfo(ctx, sessionId)
	if err != nil {
		return errors.WithStack(err)
	}
	if info == nil {
		// 如果没有找到任务信息，则认为没有需要终止的任务，不返回错误
		return nil
	}
	if options != nil {
		// 不匹配的情况下，认为没有需要终止的任务，不返回错误
		if options.MatchAgentName != "" && options.MatchAgentName != info.AgentName {
			ctx.GetLogger().Warn("agent name not match", "expect", options.MatchAgentName, "actual", info.AgentName)
			return nil
		}
		if options.MatchTaskParameters != nil {
			match := true
			for k, v := range options.MatchTaskParameters {
				if info.Parameters[k] != v {
					ctx.GetLogger().Info("task parameters not match", "expect", options.MatchTaskParameters, "actual", info.Parameters)
					match = false
					break
				}
			}
			for k, vArr := range options.MatchTaskParametersAnyValue {
				keyMatched := false
				for _, v := range vArr {
					if info.Parameters[k] == v {
						keyMatched = true
						break
					}
				}
				if !keyMatched {
					ctx.GetLogger().Info("task parameters not match", "expect", options.MatchTaskParameters, "actual", info.Parameters)
					match = false
				}
			}
			if !match {
				return nil
			}
		}
	}
	if info.AbortUrl == "" {
		// 缺少AbortURL是系统内部异常情况
		ctx.GetLogger().Error("abort url not found", "session_id", sessionId)
		return errors.WithStack(errors.New("abort url not found"))
	}
	if err := s.core.GetSessionService().UpdateSession(ctx, sessionId, &session.UpdateSessionRequest{State: ptr.To(session.Aborting)}); err != nil {
		ctx.GetLogger().Error("update session state to aborting failed", "sessionId", sessionId, "err", err)
		return errors.WithStack(err)
	}
	if err := component.AgentRuntimeAbortSession(ctx, info.AbortUrl, &agentruntime.AbortSessionRequest{AgentId: info.AgentId}); err != nil {
		return errors.WithStack(err)
	}
	if options != nil && len(options.CallbackOnSuccessFns) > 0 {
		for _, fn := range options.CallbackOnSuccessFns {
			if err := fn(ctx, sessionId); err != nil {
				ctx.GetLogger().Error("callback failed", "err", err)
				return errors.WithStack(err)
			}
		}
	}
	if err := s.core.GetSessionService().UpdateSession(ctx, sessionId, &session.UpdateSessionRequest{State: ptr.To(session.Abort)}); err != nil {
		ctx.GetLogger().Error("update session state to abort failed", "sessionId", sessionId, "err", err)
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) LoadSandboxConfig(ctx base.Context, agentId string, sessionId string, req *agentruntime.StepSessionRequest) error {
	agentInfo, err := s.core.GetAgentService().GetAgent(ctx, agentId)
	if err != nil {
		return errors.WithStack(err)
	}
	initSandbox := true
	if agentInfo.AgentProperty != nil {
		if agentInfo.AgentProperty.SkipSandbox {
			ctx.GetLogger().Info("skip init sandbox due to agent property")
			initSandbox = false
			req.SetCodeReviewFetchCode(ptr.To(false))
		}
	}
	if !initSandbox {
		return nil
	}
	sessionInfo, err := s.core.GetSessionService().GetSession(ctx, sessionId)
	if err != nil {
		return errors.WithStack(err)
	}
	idInfo, err := s.core.GetIdentityService().GetIdentity(ctx, sessionInfo.IdentityId)
	if err != nil {
		return errors.WithStack(err)
	}
	gitToken, err := s.core.GetIdentityService().GetGitToken(ctx, nil, idInfo)
	if err != nil {
		// 允许继续
		gitToken = ""
		errors2.AlertError(ctx, errors2.AlertScopeAgent, []string{agentId, sessionId, idInfo.IdentityId}, "get git token failed", err)
	}

	// 默认开启沙箱持久化，只允许Codeaone沙箱不持久化
	enablePersist := true
	if idInfo.Source == identity.SourceCodeaone {
		enablePersist = false
	}

	// 默认关闭沙箱pod的sidecar，只有sonarqube启动时，将以sidecar容器启动
	enableSidecar := false
	if agentInfo.AgentProperty != nil {
		if agentInfo.AgentProperty.CodeReview != nil {
			if agentInfo.AgentProperty.CodeReview.ToolSetting != nil {
				if agentInfo.AgentProperty.CodeReview.ToolSetting.SonarQube != nil && agentInfo.AgentProperty.CodeReview.ToolSetting.SonarQube.Enable {
					enableSidecar = true
				}
			}
		}
	}

	cfg := &sandbox.Config{
		K8sRuntimeConfig: &sandbox.K8sRuntimeConfig{
			Namespace: "metis",
		},
		SandboxConfig: &sandbox.SandboxConfig{
			GitConfig: &sandbox.GitConfig{
				Username: idInfo.PlatformUsername,
				Token:    gitToken,
			},
			PersistConfig: &sandbox.PersistConfig{
				Enable: enablePersist,
			},
			SidecarConfig: &sandbox.SidecarConfig{
				Enable: enableSidecar,
			},
		},
	}
	if idInfo.PlatformProperty != nil {
		// 如果配置了SNI，则为沙箱配置HostAlias解析
		if idInfo.PlatformProperty.Sni != "" && idInfo.PlatformProperty.SandboxEntrypoint != "" {
			u, err := url.Parse(idInfo.PlatformProperty.SandboxEntrypoint)
			if err != nil {
				ctx.GetLogger().Error("parse sandbox entrypoint failed", "url", idInfo.PlatformProperty.SandboxEntrypoint, "err", err)
				return errors.WithStack(err)
			}
			cfg.K8sRuntimeConfig.HostAliases = append(cfg.K8sRuntimeConfig.HostAliases, sandbox.HostAlias{
				Hostnames: []string{idInfo.PlatformProperty.Sni},
				IP:        u.Hostname(),
			})
		}
	}
	if sbc, err := s.core.GetSandboxService().CreateOrLoadSandbox(ctx, sessionId, cfg); err != nil {
		// fixme 未来有新的Agent时，如果沙箱没有，则不能继续运行。 此时需要调整这段逻辑
		// 如果沙箱初始化失败，则不拉取代码，保证CodeReviewAgent运行成功率
		ctx.GetLogger().Warn("create or load sandbox failed, try to run agent without sandbox", "sessionId", sessionId, "err", err)
		req.SetSandboxInitFailed()
	} else {
		req.SandboxConfig = &agentruntime.SandboxConfig{
			Endpoint: sbc.ServerEndpoint,
		}
	}
	return nil
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "AgentRuntimeService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.AgentRuntimeService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
