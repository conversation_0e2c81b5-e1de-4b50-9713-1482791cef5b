package event

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"time"
)

type eventEntity struct {
	ID          int64           `gorm:"column:id;primaryKey"`
	EventId     string          `gorm:"column:event_id"`
	SessionId   string          `gorm:"column:session_id"`
	Property    *event.Property `gorm:"column:event_properties;serializer:json"`
	GmtCreate   time.Time       `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified time.Time       `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (e *eventEntity) TableName() string {
	return "t_event"
}

func (e *eventEntity) ToEventInfo() *event.Event {
	return &event.Event{
		EventId:         e.EventId,
		Property:        e.Property,
		SessionId:       e.SessionId,
		CreateTimestamp: e.GmtCreate.UnixMilli(),
		UpdateTimestamp: e.GmtModified.UnixMilli(),
	}
}

type tokenUsageEntity struct {
	ID               int64     `gorm:"column:id;primaryKey"`
	SessionsId       string    `gorm:"column:sessions_id"`
	ModelName        string    `gorm:"column:model_name"`
	PromptTokens     int64     `gorm:"column:prompt_tokens"`
	CompletionTokens int64     `gorm:"column:completion_tokens"`
	TotalTokens      int64     `gorm:"column:total_tokens"`
	GmtCreate        time.Time `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
}

func (tue *tokenUsageEntity) TableName() string {
	return "t_token_usage"
}

type mrStatisticEntity struct {
	ID           int64     `gorm:"column:id;primaryKey"`
	SessionsId   string    `gorm:"column:sessions_id"`
	FileCount    int64     `gorm:"column:file_count"`
	DelCharCount int64     `gorm:"column:del_char_count"`
	NewCharCount int64     `gorm:"column:new_char_count"`
	DelLineCount int64     `gorm:"column:del_line_count"`
	NewLineCount int64     `gorm:"column:new_line_count"`
	GmtCreate    time.Time `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
}

func (mse *mrStatisticEntity) TableName() string {
	return "t_mr_statistic"
}
