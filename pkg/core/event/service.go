package event

import (
	"context"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/yunxiao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
)

var _ comncore.EventService = (*service)(nil)

type eventHandler func(base.Context, *event.Event)

type service struct {
	started  chan struct{}
	ready    bool
	core     comncore.Core
	handlers []eventHandler
}

func (s *service) CollectEvent(ctx base.Context, event *event.Event) error {
	if event == nil {
		return nil
	}
	if event.EventId == "" {
		event.EventId = helper.NewIDWithPrefix("e-")
	}
	entity := &eventEntity{
		EventId:   event.EventId,
		SessionId: event.SessionId,
		Property:  event.Property,
	}
	if err := dao.Save(ctx, entity); err != nil {
		return errors.WithStack(err)
	}
	for _, h := range s.handlers {
		h(ctx, event)
	}
	return nil
}

func (s *service) ListEvents(ctx base.Context, options *event.ListEventsOptions) (*page.PagedItems[event.Event], error) {
	opt := dao.NewDbOptions(dao.WithKV("session_id", options.SessionId))
	opt.AddPage(dao.WithPage(options.Page.PageNumber, options.Page.PageSize))
	if options.WithIdAsc {
		opt.AddPage(dao.WithFieldOrderAsc("id"))
	} else {
		opt.AddPage(dao.WithFieldOrderDesc("id"))
	}
	entities, total, err := dao.ListEntitiesWithPage(ctx, &eventEntity{}, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	items := page.TransferSlice(entities, func(entity *eventEntity) event.Event {
		return *entity.ToEventInfo()
	})
	return &page.PagedItems[event.Event]{
		Items:      items,
		PageNumber: options.Page.PageNumber,
		PageSize:   options.Page.PageSize,
		TotalSize:  total,
	}, nil
}

func (s *service) Audit(ctx base.Context, e *event.AuditEvent) {
	ctx.GetLogger().Info("receive audit event", "event", e)
	if base.IsYunxiaoUser(ctx) {
		s.AuditByYunxiao(ctx, e)
	}
}

func (s *service) AuditByYunxiao(ctx base.Context, event *event.AuditEvent) {
	err := yunxiao.PublishAuditEvent(ctx, event.ToCloudEvent())
	if err != nil {
		ctx.GetLogger().Error("publish audit event to yunxiao failed", "error", err)
	}
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "EventService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.EventService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
		handlers: []eventHandler{
			RecordTokenUsage,
			RecordMergeRequestStatistics,
		},
	}
}
