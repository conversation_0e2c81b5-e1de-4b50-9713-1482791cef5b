package event

import (
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"time"
)

func RecordTokenUsage(ctx base.Context, e *event.Event) {
	if e == nil || e.Property == nil || e.Property.Type != event.TypeAgentTask {
		return
	}
	data := e.Property.Data
	if data == nil {
		return
	}
	now := time.Now()
	var entities []*tokenUsageEntity
	if typ, ok := data["type"].(string); ok && typ == "one_task_llm_usage" {
		if usageData, ok := data["data"].(map[string]any); ok && usageData != nil {
			for modelName, usage := range usageData {
				usageMap, ok := usage.(map[string]any)
				if !ok {
					continue
				}
				entities = append(entities, &tokenUsageEntity{
					SessionsId:       e.SessionId,
					ModelName:        modelName,
					PromptTokens:     cast.ToInt64(usageMap["prompt_tokens"]),
					CompletionTokens: cast.ToInt64(usageMap["completion_tokens"]),
					TotalTokens:      cast.ToInt64(usageMap["total_tokens"]),
					GmtCreate:        now,
				})
			}
		}
	}
	if len(entities) > 0 {
		if err := dao.InsertEntities(ctx, entities); err != nil {
			commonerrors.AlertError(ctx, commonerrors.AlertScopeAgent, []string{e.SessionId}, "failed to insert token usage", err)
		}
	}
}

func RecordMergeRequestStatistics(ctx base.Context, e *event.Event) {
	if e == nil || e.Property == nil || e.Property.Type != event.TypeAgentTask {
		return
	}
	data := e.Property.Data
	if data == nil {
		return
	}
	if typ, ok := data["type"].(string); ok && typ == "mr_statistic" {
		if mrData, ok := data["data"].(map[string]any); ok && mrData != nil {
			entity := &mrStatisticEntity{
				SessionsId:   e.SessionId,
				FileCount:    cast.ToInt64(mrData["file_cnt"]),
				DelCharCount: cast.ToInt64(mrData["del_char_cnt"]),
				DelLineCount: cast.ToInt64(mrData["del_line_cnt"]),
				NewCharCount: cast.ToInt64(mrData["new_char_cnt"]),
				NewLineCount: cast.ToInt64(mrData["new_line_cnt"]),
				GmtCreate:    time.Now(),
			}
			if err := dao.InsertEntity(ctx, entity); err != nil {
				commonerrors.AlertError(ctx, commonerrors.AlertScopeAgent, []string{e.SessionId}, "failed to insert merge request statistics", err)
			}
		}
	}
}
