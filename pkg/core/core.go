package core

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	commcore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/database"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	acredis "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/redis"
	"log/slog"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"
)

var (
	setupLogger = slog.Default().With("logger", "setup")
)

var _ commcore.Core = (*coreService)(nil)

type Options struct {
	config               *config.Config
	apiServer            commcore.RegistrableService
	agentService         commcore.AgentService
	codePlatformService  commcore.CodePlatformService
	webhookService       commcore.WebhookService
	sessionService       commcore.SessionService
	agentRuntimeService  commcore.AgentRuntimeService
	sandboxService       commcore.SandboxService
	identityService      commcore.IdentityService
	eventService         commcore.EventService
	cronjobService       commcore.CronjobService
	callbackService      commcore.CallbackService
	agentConnectService  commcore.AgentConnectService
	statsService         commcore.StatsService
	repositoryService    commcore.RepositoryService
	featureService       commcore.FeatureService
	overviewService      commcore.OverviewService
	authorizationService commcore.AuthorizationService
	userService          commcore.UserService
}

type Option func(opts *Options)

type coreService struct {
	config *config.Config
	// 全部服务
	allServices []commcore.RegistrableService
	started     chan struct{}
	stopping    bool
	internalCtx context.Context
	cancel      context.CancelFunc
	errorChan   chan error
	ready       bool

	// services

	apiServer            commcore.RegistrableService
	agentService         commcore.AgentService
	codePlatformService  commcore.CodePlatformService
	webhookService       commcore.WebhookService
	sessionService       commcore.SessionService
	agentRuntimeService  commcore.AgentRuntimeService
	sandboxService       commcore.SandboxService
	identityService      commcore.IdentityService
	eventService         commcore.EventService
	cronjobService       commcore.CronjobService
	callbackService      commcore.CallbackService
	agentConnectService  commcore.AgentConnectService
	statsService         commcore.StatsService
	repositoryService    commcore.RepositoryService
	featureService       commcore.FeatureService
	overviewService      commcore.OverviewService
	authorizationService commcore.AuthorizationService
	userService          commcore.UserService
}

func WithConfig(config *config.Config) Option {
	return func(opts *Options) {
		opts.config = config
	}
}

func WithApiServer(apiServer commcore.RegistrableService) Option {
	return func(opts *Options) {
		opts.apiServer = apiServer
	}
}

func WithAgentService(agentService commcore.AgentService) Option {
	return func(opts *Options) {
		opts.agentService = agentService
	}
}
func WithWebhookService(webhookService commcore.WebhookService) Option {
	return func(opts *Options) {
		opts.webhookService = webhookService
	}
}
func WithSessionService(sessionService commcore.SessionService) Option {
	return func(opts *Options) {
		opts.sessionService = sessionService
	}
}
func WithAgentRuntimeService(agentRuntimeService commcore.AgentRuntimeService) Option {
	return func(opts *Options) {
		opts.agentRuntimeService = agentRuntimeService
	}
}
func WithSandboxService(sandboxService commcore.SandboxService) Option {
	return func(opts *Options) {
		opts.sandboxService = sandboxService
	}
}
func WithIdentityService(identityService commcore.IdentityService) Option {
	return func(opts *Options) {
		opts.identityService = identityService
	}
}

func WithCodePlatformService(codePlatformService commcore.CodePlatformService) Option {
	return func(opts *Options) {
		opts.codePlatformService = codePlatformService
	}
}
func WithEventService(eventService commcore.EventService) Option {
	return func(opts *Options) {
		opts.eventService = eventService
	}
}
func WithCronjobService(cronjobService commcore.CronjobService) Option {
	return func(opts *Options) {
		opts.cronjobService = cronjobService
	}
}

func WithCallbackService(callbackService commcore.CallbackService) Option {
	return func(opts *Options) {
		opts.callbackService = callbackService
	}
}

func WithAgentConnectService(agentConnectService commcore.AgentConnectService) Option {
	return func(opts *Options) {
		opts.agentConnectService = agentConnectService
	}
}

func WithStatsService(service commcore.StatsService) Option {
	return func(opts *Options) {
		opts.statsService = service
	}
}

func WithRepositoryService(repoService commcore.RepositoryService) Option {
	return func(opts *Options) {
		opts.repositoryService = repoService
	}
}

func WithFeatureService(featureService commcore.FeatureService) Option {
	return func(opts *Options) {
		opts.featureService = featureService
	}
}

func WithOverviewService(overviewService commcore.OverviewService) Option {
	return func(opts *Options) {
		opts.overviewService = overviewService
	}
}
func WithAuthorizationService(authorizationService commcore.AuthorizationService) Option {
	return func(opts *Options) {
		opts.authorizationService = authorizationService
	}
}

func WithUserService(userService commcore.UserService) Option {
	return func(opts *Options) {
		opts.userService = userService
	}
}

func NewCore(opts ...Option) commcore.Core {
	coreOpts := &Options{}
	for _, opt := range opts {
		opt(coreOpts)
	}

	core := &coreService{
		config:    coreOpts.config,
		started:   make(chan struct{}),
		errorChan: make(chan error),

		apiServer:            coreOpts.apiServer,
		agentService:         coreOpts.agentService,
		codePlatformService:  coreOpts.codePlatformService,
		webhookService:       coreOpts.webhookService,
		sessionService:       coreOpts.sessionService,
		agentRuntimeService:  coreOpts.agentRuntimeService,
		sandboxService:       coreOpts.sandboxService,
		identityService:      coreOpts.identityService,
		eventService:         coreOpts.eventService,
		cronjobService:       coreOpts.cronjobService,
		callbackService:      coreOpts.callbackService,
		agentConnectService:  coreOpts.agentConnectService,
		statsService:         coreOpts.statsService,
		repositoryService:    coreOpts.repositoryService,
		featureService:       coreOpts.featureService,
		overviewService:      coreOpts.overviewService,
		authorizationService: coreOpts.authorizationService,
		userService:          coreOpts.userService,
	}

	core.allServices = []commcore.RegistrableService{
		core.apiServer,
		core.agentService,
		core.codePlatformService,
		core.sessionService,
		core.webhookService,
		core.agentRuntimeService,
		core.sandboxService,
		core.identityService,
		core.eventService,
		core.cronjobService,
		core.callbackService,
		core.agentConnectService,
		core.statsService,
		core.repositoryService,
		core.featureService,
		core.overviewService,
		core.authorizationService,
		core.userService,
	}

	return core
}

func (c *coreService) GetConfig() config.Config {
	if c.config == nil {
		return config.Config{}
	}

	return *c.config
}

func (c *coreService) Start(ctx context.Context) error {
	st := time.Now()
	// 监听错误事件，做好退出
	go func() {
		for {
			select {
			case <-ctx.Done():
				setupLogger.Error("context is done, core is going to stop")
				return
			case err := <-c.errorChan:
				setupLogger.Error("error detected when starting up, shutting down...", "error", err)
				os.Exit(1)
			case <-c.started:
				setupLogger.Info(fmt.Sprintf("core service is started in %d ms", time.Since(st).Milliseconds()))
				return
			}
		}
	}()

	commcore.InitCore(c)
	c.internalCtx, c.cancel = context.WithCancel(ctx)

	if err := c.initMiddleware(); err != nil {
		setupLogger.Error("Fail to init middleware", "error", err)
		return err
	}

	c.startServices()
	stopChan := make(chan os.Signal, 2)
	signal.Notify(stopChan, os.Interrupt, syscall.SIGTERM)

	// 注册监控数据
	prometheus.MustRegister(c.Metrics()...)
	close(c.started)
	c.ready = true

	// 两次信号强制终止
	for {
		select {
		case err := <-c.errorChan:
			setupLogger.Error("error detected, shutting down...", "error", err)
			c.Stop()
			os.Exit(1)
		case <-stopChan:
			if c.stopping {
				setupLogger.Error("received continuous stopping signal, shutdown immediately, may not graceful")
				os.Exit(1)
			}
			setupLogger.Info("received stopping signal, try stopping core gracefully")
			go c.Stop()
		case <-c.internalCtx.Done():
			setupLogger.Info("core is stopped gracefully")
			return nil
		}
	}

	return nil
}

func (c *coreService) Started() <-chan struct{} {
	return c.started
}

func (c *coreService) IsReady() bool {
	return c.ready
}

func (c *coreService) Metrics() []prometheus.Collector {
	var cc []prometheus.Collector
	for _, s := range c.allServices {
		cc = append(cc, s.Metrics()...)
	}
	return cc
}

func (c *coreService) Stop() {
	defer c.cancel()
	c.stopping = true
	setupLogger.Info("stopping core...")

	// 先依次停止服务，各服务是串行停止，不考虑依赖关系
	for _, s := range c.allServices {
		setupLogger.Info("stopping service [" + s.GetName() + "]")
		s.Stop()
	}

	// 再停止中间件
	if acredis.GetClient() != nil {
		setupLogger.Info("stopping redis...")
		err := acredis.GetClient().Close()
		if err != nil {
			setupLogger.Error("close redis client failed", "err", err)
		}
	}

	// 停止配置更新
	config.StopListening()
}

func (c *coreService) ErrorChan() chan error {
	return c.errorChan
}

func (c *coreService) startServices() {
	// 注册所有的服务
	for _, s := range c.allServices {
		s.Register(c)
	}

	wg := sync.WaitGroup{}
	// 并行启动所有服务，需要服务在Start方法中处理好依赖
	for i := range c.allServices {
		wg.Add(1)
		s := c.allServices[i]
		go func() {
			st := time.Now()
			setupLogger.Info("starting service [" + s.GetName() + "]")
			if err := s.Start(c.internalCtx); err != nil {
				setupLogger.Error("fail to start service "+s.GetName(), "error", fmt.Sprintf("err:%+v", err))
				c.errorChan <- err
			}
			<-s.Started()
			if s.IsReady() {
				setupLogger.Info(fmt.Sprintf("service is up and ready [%s] in %d ms", s.GetName(), time.Since(st).Milliseconds()))
				wg.Done()
			} else {
				msg := "service is not ready [" + s.GetName() + "]"
				setupLogger.Error(msg)
				c.errorChan <- errors.New(msg)
			}
		}()
	}

	wg.Wait()
}

func (c *coreService) initMiddleware() error {
	//初始化DB
	databaseConfig := c.config.Database
	db, err := database.SetupDB(&database.Config{
		Host:                   databaseConfig.Host,
		Port:                   databaseConfig.Port,
		User:                   databaseConfig.User,
		Password:               databaseConfig.Password,
		Driver:                 databaseConfig.Driver,
		Name:                   databaseConfig.Name,
		MaxIdleConns:           databaseConfig.MaxIdleConns,
		MaxOpenConns:           databaseConfig.MaxOpenConns,
		ConnMaxLifetimeSeconds: databaseConfig.ConnMaxLifetimeSeconds,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	// 初始化配置
	if err = config.Init(c.internalCtx, db, time.Second*30); err != nil {
		setupLogger.Error("fail to reload config from db", "error", err)
		return errors.WithStack(err)
	}

	// 初始化美杜莎

	//初始化redis
	redisConfig := c.config.Redis
	if redisClient, err := acredis.Setup(&redis.Options{
		Addr:     fmt.Sprintf("%s:%d", redisConfig.Host, redisConfig.Port),
		Password: redisConfig.Password,
		DB:       redisConfig.DB,
	}); err != nil {
		setupLogger.Error("fail to init redis", "error", err)
		return errors.WithStack(err)
	} else {
		helper.InitRedis(redisClient)
	}

	return nil
}

func (c *coreService) GetAgentService() commcore.AgentService {
	return c.agentService
}
func (c *coreService) GetWebhookService() commcore.WebhookService {
	return c.webhookService
}

func (c *coreService) GetSessionService() commcore.SessionService {
	return c.sessionService
}
func (c *coreService) GetAgentRuntimeService() commcore.AgentRuntimeService {
	return c.agentRuntimeService
}
func (c *coreService) GetSandboxService() commcore.SandboxService {
	return c.sandboxService
}
func (c *coreService) GetIdentityService() commcore.IdentityService {
	return c.identityService
}
func (c *coreService) GetEventService() commcore.EventService {
	return c.eventService
}
func (c *coreService) GetCodePlatformService() commcore.CodePlatformService {
	return c.codePlatformService
}
func (c *coreService) GetCronjobService() commcore.CronjobService {
	return c.cronjobService
}
func (c *coreService) GetCallbackService() commcore.CallbackService {
	return c.callbackService
}

func (c *coreService) GetAgentConnectService() commcore.AgentConnectService {
	return c.agentConnectService
}

func (c *coreService) GetStatsService() commcore.StatsService {
	return c.statsService
}

func (c *coreService) GetRepositoryService() commcore.RepositoryService {
	return c.repositoryService
}

func (c *coreService) GetFeatureService() commcore.FeatureService {
	return c.featureService
}

func (c *coreService) GetOverviewService() commcore.OverviewService {
	return c.overviewService
}
func (c *coreService) GetAuthorizationService() commcore.AuthorizationService {
	return c.authorizationService
}
func (c *coreService) GetUserService() commcore.UserService {
	return c.userService
}
