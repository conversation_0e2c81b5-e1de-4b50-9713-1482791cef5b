package session

import (
	errors2 "github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"k8s.io/utils/ptr"
	"time"
)

// closeOutdatedSessions 关闭超期会话，保护系统
func closeOutdatedSessions() error {
	if !config.GetOrDefaultBool(config.KeySessionAutoCloseEnabled, true) {
		// 已关闭
		return nil
	}
	ctx := base.NewContextForAdmin(helper.NewTraceId(), "close-outdated-sessions")
	ctx.GetLogger().Info("closeOutdatedSessions")
	now := time.Now()
	if keepDays := config.GetOrDefaultInt(config.KeySessionIdleKeepDays, 30); keepDays > 0 {
		olderThan := now.AddDate(0, 0, -keepDays)
		opt := dao.NewDbOptions(dao.WithGmtCreateBefore(olderThan),
			withSessionTypes(session.CodeReview, session.GithubCodeReview, session.GitlabCodeReview, session.CodeaoneCodeReview),
			dao.WithKeyNotEqualValue("session_state", session.Finished),
			dao.WithSelect("session_id"))

		entities, _, err := dao.ListEntitiesAutoPage(ctx, &sessionEntity{}, opt)
		if err != nil {
			ctx.GetLogger().Error("ListEntitiesAutoPage failed", "err", errors.FormatFullStack(err))
			return errors2.WithStack(err)
		}
		var sessionIds []string
		for _, se := range entities {
			sessionIds = append(sessionIds, se.SessionId)
		}
		ctx.GetLogger().Info("mark sessions to finished", "sessionIds", sessionIds)
		for _, sessionId := range sessionIds {
			if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionId, &session.UpdateSessionRequest{State: ptr.To(session.Finished)}); err != nil {
				ctx.GetLogger().Error("UpdateSession failed", "err", errors.FormatFullStack(err))
				// 继续下一个
			}
		}
	}
	ctx.GetLogger().Info("closeOutdatedSessions finished")

	return nil
}
