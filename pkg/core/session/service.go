package session

import (
	"context"
	"strconv"
	"time"

	mapset "github.com/deckarep/golang-set/v2"
	errors2 "github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/cronjob"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	event2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
)

var _ comncore.SessionService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) UpdateSession(ctx base.Context, sessionId string, req *session.UpdateSessionRequest) error {
	if req == nil {
		return nil
	}
	entity := &sessionEntity{}
	if req.State != nil {
		entity.SessionState = *req.State
	}
	if req.Property != nil {
		entity.Property = req.Property
	}
	if req.Metadata != nil {
		entity.Metadata = req.Metadata
	}
	if err := dao.UpdateEntity(ctx, entity, dao.NewDbOptions(dao.WithKV("session_id", sessionId))); err != nil {
		return errors2.WithStack(err)
	}
	if entity.SessionState.Idle() {
		if err := s.core.GetAgentRuntimeService().CleanTaskInfo(ctx, sessionId); err != nil {
			return errors2.WithStack(err)
		}
	}
	if entity.SessionState == session.Finished {
		if err := s.core.GetSandboxService().DeleteSandboxForSession(ctx, sessionId); err != nil {
			//不阻塞
			errors.AlertError(ctx, errors.AlertScopeSandbox, []string{sessionId}, "Delete Sandbox Failed", err)
		}
	}
	return nil
}

func (s *service) GetSession(ctx base.Context, sessionId string) (*session.Info, error) {
	entity, err := dao.GetEntity(ctx, &sessionEntity{}, dao.NewDbOptions(dao.WithKV("session_id", sessionId)))
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	return entity.ToInfo(), nil
}

func (s *service) GetGitlabCodeReviewSession(ctx base.Context, identityId string, mergeRequestId string, projectId string) (*session.Info, error) {
	if entity, err := dao.GetEntity(ctx, &sessionEntity{}, dao.NewDbOptions(
		dao.WithKV("identity_id", identityId),
		dao.WithKV("session_property->>'type'", session.CodeReview),
		dao.WithKV("session_property->'codeReview'->>'mergeRequestId'", mergeRequestId),
		dao.WithKV("session_property->'codeReview'->>'projectId'", projectId),
	)); err == nil {
		return entity.ToInfo(), nil
	} else if !errors.Is(err, codes.ErrRecordNotFound) {
		return nil, err
	}

	return nil, nil
}

func (s *service) CreateOrLoadSession(ctx base.Context, req *session.CreateSessionRequest) (*session.Info, error) {
	switch req.Property.Type {
	case session.GitlabCodeReview:
		return s.CreateOrLoadForCodeReview(ctx, req.IdentityId, req.Property.CodeReview.MergeRequestId, req.Property.CodeReview.ProjectId)
	case session.GithubCodeReview:
		return s.createOrLoadForGithubCodeReview(ctx, req)
	case session.AiDeveloper:
		return s.createForAiDeveloper(ctx, req)
	case session.CodeupCodeReview:
		return s.createOrLoadForCodeupCodeReview(ctx, req)
	case session.CodeaoneCodeReview:
		return s.createOrLoadForCodeaoneCodeReview(ctx, req)
	default:
		return nil, errors.New(codes.ErrInvalidParameter, "type", req.Property.Type)
	}
}

func (s *service) createForAiDeveloper(ctx base.Context, req *session.CreateSessionRequest) (*session.Info, error) {
	idInfo, err := s.core.GetIdentityService().GetIdentity(ctx, req.IdentityId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	entity := &sessionEntity{
		SessionId:    helper.NewIDWithPrefix("s-"),
		UserId:       idInfo.UserId,
		SessionState: session.Init,
		IdentityId:   req.IdentityId,
		Property:     req.Property,
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		return nil, err
	}
	return entity.ToInfo(), nil
}

func (s *service) createOrLoadForGithubCodeReview(ctx base.Context, req *session.CreateSessionRequest) (*session.Info, error) {
	if entity, err := dao.GetEntity(ctx, &sessionEntity{}, dao.NewDbOptions(
		dao.WithKV("identity_id", req.IdentityId),
		dao.WithKV("session_property->>'type'", session.GithubCodeReview),
		dao.WithKV("session_property->'githubCodeReview'->>'owner'", req.Property.GithubCodeReview.Owner),
		dao.WithKV("session_property->'githubCodeReview'->>'repo'", req.Property.GithubCodeReview.Repo),
		dao.WithKV("session_property->'githubCodeReview'->'number'", req.Property.GithubCodeReview.Number),
	)); err == nil {
		return entity.ToInfo(), nil
	} else if !errors.Is(err, codes.ErrRecordNotFound) {
		return nil, err
	}
	// fixme 目前没有身份底座，所以创建session需要从identity中获取user_id
	idInfo, err := s.core.GetIdentityService().GetIdentity(ctx, req.IdentityId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	entity := &sessionEntity{
		SessionId:    helper.NewIDWithPrefix("s-"),
		UserId:       idInfo.UserId,
		SessionState: session.Idle,
		IdentityId:   req.IdentityId,
		Property:     req.Property,
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		return nil, err
	}
	return entity.ToInfo(), nil
}

func (s *service) createOrLoadForCodeupCodeReview(ctx base.Context, req *session.CreateSessionRequest) (*session.Info, error) {
	if entity, err := dao.GetEntity(ctx, &sessionEntity{}, dao.NewDbOptions(
		dao.WithKV("identity_id", req.IdentityId),
		dao.WithKV("session_property->>'type'", session.CodeupCodeReview),
		dao.WithKV("session_property->'codeupCodeReview'->>'organizationId'", req.Property.CodeupCodeReview.OrganizationId),
		dao.WithKV("session_property->'codeupCodeReview'->>'repositoryId'", req.Property.CodeupCodeReview.RepositoryId),
		dao.WithKV("session_property->'codeupCodeReview'->'localId'", req.Property.CodeupCodeReview.LocalId),
	)); err == nil {
		return entity.ToInfo(), nil
	} else if !errors.Is(err, codes.ErrRecordNotFound) {
		return nil, err
	}
	entity := &sessionEntity{
		SessionId:    helper.NewIDWithPrefix("s-"),
		UserId:       ctx.GetUid(),
		SessionState: session.Idle,
		IdentityId:   req.IdentityId,
		Property:     req.Property,
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		return nil, err
	}
	return entity.ToInfo(), nil
}

func (s *service) createOrLoadForCodeaoneCodeReview(ctx base.Context, req *session.CreateSessionRequest) (*session.Info, error) {
	if entity, err := dao.GetEntity(ctx, &sessionEntity{}, dao.NewDbOptions(
		dao.WithKV("identity_id", req.IdentityId),
		dao.WithKV("session_property->>'type'", session.CodeaoneCodeReview),
		dao.WithKV("session_property->'codeaoneCodeReview'->>'mergeRequestId'", strconv.FormatInt(req.Property.CodeAoneCodeReview.MergeRequestId, 10)),
		dao.WithKV("session_property->'codeaoneCodeReview'->>'projectId'", strconv.FormatInt(req.Property.CodeAoneCodeReview.ProjectId, 10)),
	)); err == nil {
		return entity.ToInfo(), nil
	} else if !errors.Is(err, codes.ErrRecordNotFound) {
		return nil, err
	}
	idInfo, err := s.core.GetIdentityService().GetIdentity(ctx, req.IdentityId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	entity := &sessionEntity{
		SessionId:    helper.NewIDWithPrefix("s-"),
		UserId:       idInfo.UserId,
		SessionState: session.Idle,
		IdentityId:   req.IdentityId,
		Property:     req.Property,
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		return nil, err
	}
	return entity.ToInfo(), nil
}

func (s *service) CreateOrLoadForCodeReview(ctx base.Context, identityId string, mergeRequestId string, projectId string) (*session.Info, error) {
	if entity, err := dao.GetEntity(ctx, &sessionEntity{}, dao.NewDbOptions(
		dao.WithKV("identity_id", identityId),
		dao.WithKV("session_property->>'type'", session.CodeReview),
		dao.WithKV("session_property->'codeReview'->>'mergeRequestId'", mergeRequestId),
		dao.WithKV("session_property->'codeReview'->>'projectId'", projectId),
	)); err == nil {
		return entity.ToInfo(), nil
	} else if !errors.Is(err, codes.ErrRecordNotFound) {
		return nil, err
	}
	// fixme 目前没有身份底座，所以创建session需要从identity中获取user_id
	idInfo, err := s.core.GetIdentityService().GetIdentity(ctx, identityId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	entity := &sessionEntity{
		SessionId:    helper.NewIDWithPrefix("s-"),
		UserId:       idInfo.UserId,
		SessionState: session.Idle,
		IdentityId:   identityId,
		Property: &session.Property{
			Type: session.CodeReview,
			CodeReview: &session.GitlabCodeReviewProperty{
				MergeRequestId: mergeRequestId,
				ProjectId:      projectId,
			},
		},
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		return nil, err
	}
	return entity.ToInfo(), nil
}

func (s *service) ListSessions(ctx base.Context, req *session.ListSessionsRequest) (*page.PagedItems[*session.Info], error) {
	if err := req.Validate(); err != nil {
		return nil, errors2.WithStack(err)
	}
	opt := dao.NewDbOptions()
	opt.AddPage(dao.WithPage(req.PagesParams.PageNumber, req.PagesParams.PageSize))
	if !req.WithoutUserId {
		opt.AddWhere(dao.WithKV("user_id", ctx.GetUid()))
	}
	if req.WithIdDesc {
		opt.AddPage(dao.WithFieldOrderDesc("id"))
	} else {
		opt.AddPage(dao.WithFieldOrderAsc("id"))
	}
	if req.GithubPullRequestNumber > 0 {
		opt.AddWhere(dao.WithKV("(session_property->'githubCodeReview'->>'number')::int", req.GithubPullRequestNumber))
	}
	if req.GithubRepo != "" {
		opt.AddWhere(dao.WithKV("session_property->'githubCodeReview'->>'repo'", req.GithubRepo))
	}
	if req.GithubOwner != "" {
		opt.AddWhere(dao.WithKV("session_property->'githubCodeReview'->>'owner'", req.GithubOwner))
	}
	if req.IdentityId != "" {
		opt.AddWhere(dao.WithKV("identity_id", req.IdentityId))
	}
	if req.CodeupOrganizationId != "" {
		opt.AddWhere(dao.WithKV("session_property->'codeupCodeReview'->>'organizationId'", req.CodeupOrganizationId))
	}
	if req.CodeupLocalId > 0 {
		opt.AddWhere(dao.WithKV("(session_property->'codeupCodeReview'->>'localId')::int", req.CodeupLocalId))
	}
	if req.CodeupRepositoryId != "" {
		opt.AddWhere(dao.WithKV("session_property->'codeupCodeReview'->>'repositoryId'", req.CodeupRepositoryId))
	}
	if req.GitlabProjectId != "" {
		opt.AddWhere(dao.WithKV("session_property->'codeReview'->>'projectId'", req.GitlabProjectId))
	}
	if req.GitlabMergeRequestId != "" {
		opt.AddWhere(dao.WithKV("session_property->'codeReview'->>'mergeRequestId'", req.GitlabMergeRequestId))
	}
	if req.SessionName != "" {
		opt.AddWhere(dao.WithKV("metadata->>'sessionName'", req.SessionName))
	}
	if req.RepositoryId != "" {
		opt.AddWhere(dao.WithKV("metadata->>'repositoryId'", req.RepositoryId))
	}
	if req.Author != "" {
		opt.AddWhere(dao.WithKV("metadata->>'author'", req.Author))
	}
	if len(req.States) > 0 {
		opt.AddWhere(dao.WithKeyValues("session_state", req.States...))
	}
	if req.FromTimestamp > 0 {
		t := time.UnixMilli(req.FromTimestamp)
		opt.AddWhere(dao.WithGreaterThan("gmt_create", t))
	}
	if req.ToTimestamp > 0 {
		t := time.UnixMilli(req.ToTimestamp)
		opt.AddWhere(dao.WithLessThan("gmt_create", t))
	}
	if len(req.ExcludedStates) > 0 {
		opt.AddWhere(dao.WithKeyNotInValues("session_state", req.ExcludedStates...))
	}

	entities, total, err := dao.ListEntitiesWithPage(ctx, &sessionEntity{}, opt)
	if err != nil {
		return nil, err
	}
	return &page.PagedItems[*session.Info]{
		Items:      page.TransferSlice(entities, func(e *sessionEntity) *session.Info { return e.ToInfo() }),
		PageNumber: req.PageNumber,
		PageSize:   req.PageSize,
		TotalSize:  total,
	}, nil

}

func (s *service) ListAgentSessions(ctx base.Context, req *session.ListAgentSessionsRequest) (*page.PagedItems[*session.AgentSessionInfo], error) {
	if err := req.Validate(); err != nil {
		return nil, errors2.WithStack(err)
	}

	sessionStateSet := mapset.NewThreadUnsafeSet[session.State]()
	for _, as := range req.States {
		states := as.MapToStates()
		for _, state := range states {
			sessionStateSet.Add(state)
		}
	}

	resp, err := s.ListSessions(ctx, &session.ListSessionsRequest{
		PagesParams:    req.PagesParams,
		SessionName:    req.SessionName,
		RepositoryId:   req.RepositoryId,
		Author:         req.Author,
		FromTimestamp:  req.FromTimestamp,
		ToTimestamp:    req.ToTimestamp,
		WithIdDesc:     true,
		States:         sessionStateSet.ToSlice(),
		ExcludedStates: []session.State{session.Skipped},
	})
	if err != nil {
		return nil, err
	}
	var sessionIds []string
	repoIdSet := mapset.NewThreadUnsafeSet[string]()
	for _, item := range resp.Items {
		if item.Metadata != nil {
			repoIdSet.Add(item.Metadata.RepositoryId)
		}
		sessionIds = append(sessionIds, item.SessionId)
	}
	repoMap := map[string]*repository.RepositoryInfo{}
	if !repoIdSet.IsEmpty() {
		repositories, err := s.core.GetRepositoryService().ListRepositories(ctx, &repository.ListRepositoryOptions{
			RepositoryIds:  repoIdSet.ToSlice(),
			NameAndUrlOnly: true,
		})
		if err != nil {
			return nil, err
		}
		for _, repo := range repositories.Items {
			repoMap[repo.RepositoryId] = repo
		}
	}
	mrInfoMap := map[string]*session.MergeRequestProperty{}
	if len(sessionIds) > 0 {
		opt := dao.NewDbOptions(dao.WithKeyValues("session_id", sessionIds...), dao.WithSelect("session_id,property"))
		propertyOnly, _, err := dao.ListEntitiesAutoPage(ctx, &mergeRequestEntity{}, opt)
		if err != nil {
			return nil, err
		}
		for _, mr := range propertyOnly {
			mrInfoMap[mr.SessionId] = mr.Property
		}
	}

	items := page.TransferSlice(resp.Items, func(i *session.Info) *session.AgentSessionInfo {
		asi := &session.AgentSessionInfo{
			SessionId:       i.SessionId,
			State:           i.State.ToAgentSessionState(),
			ExternalLink:    "",
			CreateTimestamp: i.CreateTimestamp,
		}
		if i.Metadata != nil {
			asi.AgentId = i.Metadata.AgentId
			asi.AgentName = i.Metadata.AgentName
			asi.SessionName = i.Metadata.SessionName
			asi.RepositoryId = i.Metadata.RepositoryId
			asi.Author = i.Metadata.Author
			if repo, ok := repoMap[i.Metadata.RepositoryId]; ok {
				asi.RepositoryName = repo.Name
			}
		}
		if mrInfo, ok := mrInfoMap[i.SessionId]; ok && mrInfo != nil {
			asi.ExternalLink = mrInfo.Url
		}
		return asi
	})

	return &page.PagedItems[*session.AgentSessionInfo]{
		Items:      items,
		PageNumber: req.PageNumber,
		PageSize:   req.PageSize,
		TotalSize:  resp.TotalSize,
	}, nil
}

func (s *service) RecordExportAgentSession(ctx base.Context, req *session.ExportAgentSessionsRequest) {
	ip := ""
	if ginCtx := ctx.GetGinContext(); ginCtx != nil {
		ip = ginCtx.ClientIP()
	}
	orgId := ctx.GetUid()
	operatorId := ctx.GetLoginUid()
	name := ""

	if yunxiaoUser := base.GetYunxiaoUserFromContext(ctx); yunxiaoUser != nil {
		name = yunxiaoUser.GetName()
	}
	entity := &agentSessionExportEntity{
		OrganizationId: orgId,
		OperatorId:     operatorId,
		OperatorName:   name,
		Ip:             ip,
		Query:          req,
		GmtCreate:      time.Now(),
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		ctx.GetLogger().Error("failed to record export agent session", err)
	}
}

func (s *service) ListAgentSessionExportHistory(ctx base.Context, req *session.ListAgentSessionExportHistoryRequest) (*page.PagedItems[*session.ExportAgentSessionOperation], error) {
	opt := dao.NewDbOptions(dao.WithKV("organization_id", ctx.GetUid()))
	opt.AddPage(dao.WithFieldOrderDesc("id"))
	opt.AddPage(dao.WithPage(req.PagesParams.PageNumber, req.PagesParams.PageSize))
	entities, total, err := dao.ListEntitiesWithPage(ctx, &agentSessionExportEntity{}, opt)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	return &page.PagedItems[*session.ExportAgentSessionOperation]{
		Items: page.TransferSlice(entities, func(e *agentSessionExportEntity) *session.ExportAgentSessionOperation {
			return e.ToExportAgentSessionOperation()
		}),
		PageNumber: req.PageNumber,
		PageSize:   req.PageSize,
		TotalSize:  total,
	}, nil
}

func (s *service) GetSessionTokenUsage(ctx base.Context, sessionId string) (*session.GetSessionTokenUsageResponse, error) {
	// 通过EventService查询所有event
	eventService := s.core.GetEventService()
	pageNum := 1
	pageSize := 100
	usageMap := make(map[string]*session.TokenUsage)
	for {
		options := &event2.ListEventsOptions{
			Page:      page.PagesParams{PageNumber: pageNum, PageSize: pageSize},
			SessionId: sessionId,
		}
		result, err := eventService.ListEvents(ctx, options)
		if err != nil {
			ctx.GetLogger().Error("GetSessionTokenUsage query failed", "err", err)
			return nil, err
		}
		for _, e := range result.Items {
			if e.Property == nil {
				continue
			}
			if e.Property.Type != "agent_task" {
				continue
			}
			if e.Property.Data == nil {
				continue
			}
			// 只处理 type=one_task_llm_usage 的数据
			dataType, ok := e.Property.Data["type"].(string)
			if !ok || dataType != "one_task_llm_usage" {
				continue
			}
			// 取data下的data字段
			dataMap, ok := e.Property.Data["data"].(map[string]any)
			if !ok {
				continue
			}
			for model, v := range dataMap {
				modelName := model
				valMap, ok := v.(map[string]any)
				if !ok {
					continue
				}
				totalTokens := int64(0)
				promptTokens := int64(0)
				completionTokens := int64(0)
				if t, ok := valMap["total_tokens"].(float64); ok {
					totalTokens = int64(t)
				} else if t, ok := valMap["total_tokens"].(int64); ok {
					totalTokens = t
				}
				if t, ok := valMap["prompt_tokens"].(float64); ok {
					promptTokens = int64(t)
				} else if t, ok := valMap["prompt_tokens"].(int64); ok {
					promptTokens = t
				}
				if t, ok := valMap["completion_tokens"].(float64); ok {
					completionTokens = int64(t)
				} else if t, ok := valMap["completion_tokens"].(int64); ok {
					completionTokens = t
				}
				tu, ok := usageMap[modelName]
				if !ok {
					tu = &session.TokenUsage{ModelName: modelName}
					usageMap[modelName] = tu
				}
				tu.TotalTokens += totalTokens
				tu.PromptTokens += promptTokens
				tu.CompletionTokens += completionTokens
			}
		}
		if len(result.Items) < pageSize {
			break
		}
		pageNum++
	}

	var usages []*session.TokenUsage
	for _, usage := range usageMap {
		usages = append(usages, usage)
	}
	return &session.GetSessionTokenUsageResponse{Usages: usages}, nil
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	<-s.core.GetCronjobService().Started()
	if err := s.core.GetCronjobService().RegisterJob(cronjob.NewJob(
		cronjob.WithName("session.task.scan.merge-request.metric"),
		cronjob.WithCronExpression("0 0 * * *"),
		cronjob.WithTask(s.scanMergeRequest),
		cronjob.WithSingletonMode(true),
	)); err != nil {
		return errors2.WithStack(err)
	}
	if err := s.core.GetCronjobService().RegisterJob(cronjob.NewJob(
		cronjob.WithName("session.outdated.auto-close"),
		cronjob.WithCronExpression("15 0 * * *"),
		cronjob.WithTask(closeOutdatedSessions),
		cronjob.WithSingletonMode(true),
	)); err != nil {
		return errors2.WithStack(err)
	}
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "SessionService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.SessionService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
