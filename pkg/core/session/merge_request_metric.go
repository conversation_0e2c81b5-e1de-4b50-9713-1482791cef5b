package session

import (
	"fmt"
	mapset "github.com/deckarep/golang-set/v2"
	errors2 "github.com/pkg/errors"
	"github.com/sourcegraph/conc/pool"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"modernc.org/mathutil"
	"regexp"
	"strconv"
	"strings"
	"time"
)

func (s *service) scanMergeRequest() error {
	adminCtx := base.NewContextForAdmin(helper.NewIDWithPrefix("mr-metric-scan-"), "scanMergeRequest")
	sessions, _, err := dao.ListEntitiesAutoPage(adminCtx, &sessionEntity{},
		dao.NewDbOptions(
			dao.WithKeyNotEqualValue("session_state", session.Finished),
			// fixme github 暂不支持
			withSessionTypes(session.CodeReview, session.GitlabCodeReview),
			dao.WithGmtCreateAfter(time.Now().Add(-time.Hour*24*time.Duration(config.GetOrDefaultInt(config.KeyCodeReviewMetricScanStopAfterCreationInDays, 7)))),
		))
	if err != nil {
		adminCtx.GetLogger().Error("ListEntitiesAutoPage failed", "err", errors.FormatFullStack(err))
		return errors2.WithStack(err)
	}
	adminCtx.GetLogger().Info("scanMergeRequest", "sessions", len(sessions))
	p := pool.New().WithMaxGoroutines(100)
	for _, se := range sessions {
		p.Go(func() {
			if err := s.scanMergeRequestForSession(adminCtx, se, false); err != nil {
				adminCtx.GetLogger().Error("scanMergeRequestForSession failed", "sessionId", se.SessionId, "err", errors.FormatFullStack(err))
			}
		})
	}
	p.Wait()
	adminCtx.GetLogger().Info("scanMergeRequest finished", "sessions", len(sessions))
	return nil
}

func (s *service) ScanMergeRequestMetrics(ctx base.Context, sessionId string, ignoreClosed bool) error {
	entity, err := dao.GetEntity(ctx, &sessionEntity{}, dao.NewDbOptions(dao.WithKV("session_id", sessionId)))
	if err != nil {
		return errors2.WithStack(err)
	}
	return s.scanMergeRequestForSession(ctx, entity, ignoreClosed)
}

func (s *service) scanMergeRequestForSession(ctx base.Context, se *sessionEntity, ignoreClosed bool) error {
	if se.Property.Type != session.CodeReview {
		return nil
	}
	mre := &mergeRequestEntity{
		SessionId: se.SessionId,
	}
	if e, err := dao.GetEntity(ctx, &mergeRequestEntity{}, dao.NewDbOptions(dao.WithKV("session_id", se.SessionId))); err != nil {
		if !errors.Is(err, codes.ErrRecordNotFound) {
			return errors2.WithStack(err)
		}
		// not found情况会新建
	} else {
		mre = e
	}

	mrId := se.Property.CodeReview.MergeRequestId
	proId := se.Property.CodeReview.ProjectId
	idInfo, err := s.core.GetIdentityService().GetIdentity(ctx, se.IdentityId)
	if err != nil {
		return errors2.WithStack(err)
	}
	mrInfo, err := s.core.GetCodePlatformService().GetMergeRequest(ctx, se.SessionId, &codeplatform.GetMergeRequestOptions{
		WithCommits: true,
	})
	if err != nil {
		return errors2.WithStack(err)
	}
	if mrInfo.State == "closed" || mrInfo.State == "merged" {
		// 关闭的 mr，直接返回
		if !ignoreClosed {
			ctx.GetLogger().Info("MergeRequest is closed, skip scanMergeRequestForSession", "sessionId", se.SessionId)
			return nil
		}
		ctx.GetLogger().Info("Continue to scan MergeRequest metrics")
	}
	mre.Property = &session.MergeRequestProperty{
		Source:          idInfo.Source,
		MergeRequestId:  mrId,
		ProjectId:       proId,
		SourceBranch:    mrInfo.SourceBranch,
		SourceRef:       mrInfo.SourceRef,
		TargetBranch:    mrInfo.TargetBranch,
		SourceProjectId: cast.ToString(mrInfo.SourceProjectID),
		TargetProjectId: cast.ToString(mrInfo.TargetProjectID),
		Title:           mrInfo.Title,
		Description:     mrInfo.Description,
		State:           mrInfo.State,
		Url:             mrInfo.WebURL,
		Author:          mrInfo.AuthorName,
		CreateTimestamp: mrInfo.CreatedAt.Unix(),
		UpdateTimestamp: mrInfo.UpdatedAt.Unix(),
	}

	if metric, err := calcMergeRequestMetric(ctx, se.SessionId, idInfo, mrInfo); err != nil {
		ctx.GetLogger().Error("calcMergeRequestMetric failed", "err", err)
		return errors2.WithStack(err)
	} else {
		mre.Metric = metric
	}

	return dao.Save(ctx, mre)
}

func calcMergeRequestDetail(ctx base.Context, mrInfo *codeplatform.MergeRequest) (*session.MergeRequestDetail, error) {
	mrNewLineCount := 0
	mrNewCharacterCount := 0
	mrDeleteLineCount := 0
	mrDeleteCharacterCount := 0

	fileSet := mapset.NewThreadUnsafeSet[string]()
	for _, change := range mrInfo.Changes {
		fileSet.Add(change.NewPath)
		split := strings.Split(change.Diff, "\n")
		newLine := 0
		deleteLine := 0
		newCharacterCount := 0
		deleteCharacterCount := 0
		for _, s := range split {
			if strings.HasPrefix(s, "+") {
				newLine += 1
				newCharacterCount += len(s) - 1
			} else if strings.HasPrefix(s, "-") {
				deleteLine += 1
				deleteCharacterCount += len(s) - 1
			}
		}
		mrNewLineCount += newLine
		mrNewCharacterCount += newCharacterCount
		mrDeleteLineCount += deleteLine
		mrDeleteCharacterCount += deleteCharacterCount
	}

	return &session.MergeRequestDetail{
		FileCount:            len(fileSet.ToSlice()),
		NewLineCount:         mrNewLineCount,
		NewCharacterCount:    mrNewCharacterCount,
		DeleteLineCount:      mrDeleteLineCount,
		DeleteCharacterCount: mrDeleteCharacterCount,
	}, nil
}

func calcMergeRequestMetric(ctx base.Context, sessionId string, idInfo *identity.IdentityInfo, mrInfo *codeplatform.MergeRequest) (*session.MergeRequestMetric, error) {
	l := ctx.GetLogger().With("sessionId", sessionId)
	mrDetail, err := calcMergeRequestDetail(ctx, mrInfo)
	if err != nil {
		l.Error("calcMergeRequestDetail failed", "err", err)
		return nil, errors2.WithStack(err)
	}
	metric := &session.MergeRequestMetric{
		MergeRequestDetail: mrDetail,
	}
	discussions, err := core.GetCoreService().GetCodePlatformService().ListMergeRequestAllComments(ctx, sessionId, &codeplatform.ListMergeRequestAllCommentsRequest{})
	if err != nil {
		l.Error("ListMergeRequestAllComments failed", "err", err)
		return nil, errors2.WithStack(err)
	}

	commitDiffs, err := core.GetCoreService().GetCodePlatformService().ListAllMergeRequestCommitDiffs(ctx, sessionId, &codeplatform.ListAllMergeRequestCommitDiffsRequest{})
	if err != nil {
		l.Error("ListAllMergeRequestCommitDiffs failed", "err", err)
		return nil, errors2.WithStack(err)
	}

	agentName := idInfo.PlatformUsername

	suggestionAcceptedIdSet := mapset.NewThreadUnsafeSet[string]()
	re := regexp.MustCompile("(?s)```suggestion.*?\n(.*?)```")
	for _, d := range discussions {
		// 这里数据模型是讨论， 一个讨论下面包含N个子评论
		// 我们认为一个讨论是一个建议，讨论下的子评论都是围绕这一个建议进行
		if len(d.Comments) == 0 {
			continue
		}
		repliedCountInThisDiscussion := 0
		suggestionAccepted := false
		metric.AgentCodeSuggestionCount += 1
		resolved := d.Resolved
		for _, c := range d.Comments {
			if c.Username == agentName {
				metric.AgentCommentCount += 1
				if resolved {
					metric.AgentCommentResolvedCount += 1
				}
				if d.Type == codeplatform.CommentTypeDiscussionNote && strings.Contains(c.Content, "```suggestion") {
					// 含有SuggestionCode， 扫描Commit是否有匹配内容
					matches := re.FindAllStringSubmatch(c.Content, -1)
					if len(matches) > 0 && len(matches[0]) >= 2 {
						suggestionCode := strings.TrimSpace(matches[0][1])
						if containsSuggestionCode(suggestionCode, commitDiffs) {
							suggestionAccepted = true
							l.Info("found code suggestion applied", "commentId", c.ID)
							suggestionAcceptedIdSet.Add(c.ID)
						}
					}
				}
			} else {
				if strings.Contains(c.Content, fmt.Sprintf("@%s", agentName)) {
					metric.AgentMentionedCount += 1
				}
				repliedCountInThisDiscussion += 1
			}
		}
		if !suggestionAccepted && d.CodePosition != nil {
			// 代码行评论，进一步扫描Commit，对应的代码行是否被改动，若改动则认为用户接受了建议
			if checkCommitsForSuggestionAccepted(ctx, d.CodePosition, d.Comments[0], commitDiffs) {
				l.Info("found code suggestion applied", "rootCommentId", d.Comments[0].ID)
				suggestionAcceptedIdSet.Add(d.Comments[0].ID)
				suggestionAccepted = true
			}
		}
		if suggestionAccepted {
			metric.AgentCodeSuggestionAppliedCount += 1
		}
		if d.Comments[0].Username == agentName {
			// 由Agent发起的讨论，所有非Agent的评论都认为是回复
			metric.AgentCommentRepliedCount += repliedCountInThisDiscussion
		}
	}
	l.Info("code suggestion accepted info", "commentIds", suggestionAcceptedIdSet.ToSlice())

	return metric, nil
}

func containsSuggestionCode(suggestionCode string, commitDiffs []*codeplatform.CommitDiff) bool {
	for _, cd := range commitDiffs {
		split := strings.Split(cd.Diff, "\n")
		code := ""
		for _, line := range split {
			if strings.HasPrefix(line, "+") {
				code += line[1:] + "\n"
			}
		}
		code = strings.TrimSpace(code)
		if strings.EqualFold(suggestionCode, code) {
			return true
		}
	}
	return false
}

type CodeBlock struct {
	OldCode                []string
	NewCode                []string
	OldCodeStartLineNumber int
	NewCodeStartLineNumber int
	OldCodeEndLineNumber   int
	NewCodeEndLineNumber   int
}

func ParseDiff(ctx base.Context, diffContent string) []*CodeBlock {
	// 如果包含文件信息头，去掉前两行
	if strings.HasPrefix(diffContent, "---") {
		lines := strings.Split(diffContent, "\n")
		if len(lines) > 2 {
			diffContent = strings.Join(lines[2:], "\n")
		}
	}

	// 匹配所有的hunk
	hunkPattern := regexp.MustCompile(`@@ -\d+(?:,\d+)? \+\d+(?:,\d+)? @@.*(?:\n[-+ ].*)*`)
	hunks := hunkPattern.FindAllString(diffContent, -1)

	if len(hunks) == 0 {
		ctx.GetLogger().Warn("no hunk found in diff content", "diffContent", diffContent)
	}

	results := make([]*CodeBlock, 0)
	for _, hunk := range hunks {
		codeBlock, err := parseHunk(hunk)
		if err != nil {
			ctx.GetLogger().Warn("failed to parse hunk", "hunk", hunk, "err", err)
			continue
		}
		results = append(results, codeBlock)
	}

	return results
}

func parseHunk(hunkContent string) (*CodeBlock, error) {
	lines := strings.Split(hunkContent, "\n")
	result := &CodeBlock{
		OldCode: make([]string, 0),
		NewCode: make([]string, 0),
	}

	if len(lines) == 0 {
		return result, fmt.Errorf("empty hunk")
	}

	// 解析hunk头
	headerPattern := regexp.MustCompile(`@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@`)
	matches := headerPattern.FindStringSubmatch(lines[0])
	if len(matches) < 5 {
		return result, fmt.Errorf("invalid hunk header format")
	}

	// 解析行号信息
	oldStart, _ := strconv.Atoi(matches[1])
	oldCount := 1
	if matches[2] != "" {
		oldCount, _ = strconv.Atoi(matches[2])
	}

	newStart, _ := strconv.Atoi(matches[3])
	newCount := 1
	if matches[4] != "" {
		newCount, _ = strconv.Atoi(matches[4])
	}

	result.OldCodeStartLineNumber = oldStart
	result.NewCodeStartLineNumber = newStart
	result.OldCodeEndLineNumber = mathutil.Max(0, oldStart+oldCount-1)
	result.NewCodeEndLineNumber = mathutil.Max(0, newStart+newCount-1)

	// 处理代码行
	oldLineNum := oldStart
	newLineNum := newStart

	for _, line := range lines[1:] {
		if line == "" || strings.HasPrefix(line, "\\ No newline") {
			continue
		}

		switch line[0] {
		case '-':
			result.OldCode = append(result.OldCode, fmt.Sprintf("%d:%s", oldLineNum, line[1:]))
			oldLineNum++
		case '+':
			result.NewCode = append(result.NewCode, fmt.Sprintf("%d:%s", newLineNum, line[1:]))
			newLineNum++
		default:
			result.OldCode = append(result.OldCode, fmt.Sprintf("%d:%s", oldLineNum, line))
			result.NewCode = append(result.NewCode, fmt.Sprintf("%d:%s", newLineNum, line))
			oldLineNum++
			newLineNum++
		}
	}

	return result, nil
}

// checkCommitsForSuggestionAccepted 检查评论时的commitId 之后的提交 是否有对应代码行的变动。 以此判断建议接受
// commitDiffs的排序是按照commitId提交的降序，该排序由代码平台ListCommits保证，commitDiffs是拆分到每一个Diff块，所以有相同的commitId
func checkCommitsForSuggestionAccepted(ctx base.Context, codePosition *codeplatform.CodePosition, comment *codeplatform.Comment, commitDiffs []*codeplatform.CommitDiff) bool {
	if codePosition == nil {
		return false
	}
	commitId := comment.CommitId
	idx := -1
	for i, cd := range commitDiffs {
		if cd.CommitId == commitId {
			idx = i
			break
		}
	}
	if idx < 0 {
		// 在commitDiffs中没有找到对应的commitId，则认为用户可能force push了
		// 比较难评价这个行为，先认为没有接受
		return false
	}
	interestedDiffs := commitDiffs[:idx]
	if len(interestedDiffs) == 0 {
		return false
	}
	path := util.GetString(codePosition.Path)
	startLine := -1
	endLine := -1
	if codePosition.StartLine != nil {
		startLine = *codePosition.StartLine
	}
	if codePosition.EndLine != nil {
		endLine = *codePosition.EndLine
	} else {
		endLine = startLine
	}
	if startLine < 0 {
		// 有问题的分支，评论的行号不应该为空
		return false
	}

	// 从旧commit到新commit顺序，进行分析
	for i := idx - 1; i >= 0; i-- {
		diff := interestedDiffs[i]
		if diff.NewPath != path && diff.OldPath != path {
			continue
		}
		if diff.NewPath != path {
			// 新路径不匹配，则判断是否删除/移动了文件
			if diff.DeletedFile {
				// 删掉了先假设用户接受建议
				return true
			} else if !diff.RenamedFile {
				// 不是删除也不是移动，则认为无关
				continue
			}
			// 移动的情况继续往下走，比较代码行是否改动
		}
		codeBlocks := ParseDiff(ctx, diff.Diff)
		for _, cb := range codeBlocks {
			if util.AreIntervalsOverlapping(startLine, endLine, cb.OldCodeStartLineNumber, cb.OldCodeEndLineNumber) {
				// 改动行与评论行发生了重叠，则认为用户接受
				ctx.GetLogger().Info("code suggestion accepted due to related code changed", "commentId", comment.ID, "commitId", commitId, "path", path, "startLine", startLine, "endLine", endLine, "cb", cb)
				return true
			}
		}

	}

	return false
}
