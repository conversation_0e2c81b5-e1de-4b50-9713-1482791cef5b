package session

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"time"
)

type sessionEntity struct {
	ID           int64             `gorm:"column:id;primaryKey"`
	SessionId    string            `gorm:"column:session_id"`
	IdentityId   string            `gorm:"column:identity_id"`
	Property     *session.Property `gorm:"column:session_property;serializer:json"`
	UserId       string            `gorm:"column:user_id"`
	Metadata     *session.Metadata `gorm:"column:metadata;serializer:json"`
	SessionState session.State     `gorm:"column:session_state"`
	GmtCreate    time.Time         `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified  time.Time         `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (s *sessionEntity) TableName() string {
	return "t_session"
}

func (s *sessionEntity) ToInfo() *session.Info {
	return &session.Info{
		SessionId:       s.SessionId,
		IdentityId:      s.IdentityId,
		Property:        s.Property,
		State:           s.SessionState,
		Metadata:        s.Metadata,
		UserId:          s.UserId,
		CreateTimestamp: s.GmtCreate.UnixMilli(),
		UpdateTimestamp: s.GmtModified.UnixMilli(),
	}
}

type mergeRequestEntity struct {
	ID          int64                         `gorm:"column:id;primaryKey"`
	SessionId   string                        `gorm:"column:session_id"`
	RepoId      string                        `gorm:"column:repo_id"`
	Property    *session.MergeRequestProperty `gorm:"column:property;serializer:json"`
	Metric      *session.MergeRequestMetric   `gorm:"column:metric;serializer:json"`
	GmtCreate   time.Time                     `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified time.Time                     `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (mre *mergeRequestEntity) TableName() string {
	return "t_merge_request"
}

type agentSessionExportEntity struct {
	ID             int64                               `gorm:"column:id;primaryKey"`
	OrganizationId string                              `gorm:"column:organization_id"`
	OperatorId     string                              `gorm:"column:operator_id"`
	OperatorName   string                              `gorm:"column:operator_name"`
	Ip             string                              `gorm:"column:ip"`
	Query          *session.ExportAgentSessionsRequest `gorm:"column:query;serializer:json"`
	GmtCreate      time.Time                           `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
}

func (asee *agentSessionExportEntity) TableName() string {
	return "t_agent_session_export"
}

func (asee *agentSessionExportEntity) ToExportAgentSessionOperation() *session.ExportAgentSessionOperation {
	return &session.ExportAgentSessionOperation{
		OperatorId:      asee.OperatorId,
		OperatorName:    asee.OperatorName,
		Query:           asee.Query,
		Ip:              asee.Ip,
		CreateTimestamp: asee.GmtCreate.UnixMilli(),
	}
}
