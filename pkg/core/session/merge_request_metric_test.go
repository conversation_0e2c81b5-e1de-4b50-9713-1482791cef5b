package session

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"regexp"
	"testing"
)

func TestReg(t *testing.T) {
	re := regexp.MustCompile("(?s)```suggestion(.*?)```")
	s := "**逻辑处理优化**\n\n在`getDeptIds`方法中，每次循环后都创建了一个新的`ArrayList`。建议将`ids`的初始化移到循环外部，减少不必要的内存开销。\n\n```suggestion\nList<Long> ids = new ArrayList<>(deptIds);\nredisUtils.set(key, ids, 1, TimeUnit.DAYS);\nreturn ids;\n```"

	result := re.FindAllStringSubmatch(s, -1)
	fmt.Println(result)
}

func TestParseMultipleHunksForGitlab(t *testing.T) {
	data := `@@ -380,15 +370,12 @@ public class PromotionServiceImpl implements PromotionService {
             throw new IllegalArgumentException(\"部分产品不存在\");
         }
         
-        // 错误8: 在Calendar对象上直接加365天
         Calendar calendar = Calendar.getInstance();
         LocalDateTime startDate = LocalDateTime.now();
         
-        // 错误4: 重复代码，创建促销的逻辑与createPromotion方法重复
         Promotion flashSale = new Promotion();
         flashSale.setName(\"限时闪购\");
         flashSale.setDescription(\"限时\" + hours + \"小时特价\");
-        // 错误2: 使用魔法值
         flashSale.setType(\"flash_sale\");
         flashSale.setDiscountRate(discountRate);
         flashSale.setStartDate(startDate);
@@ -400,7 +387,6 @@ public class PromotionServiceImpl implements PromotionService {
         
         // 设置30天后过期的促销活动（用于测试）
         Calendar futureCalendar = Calendar.getInstance();
-        // 错误8: 在Calendar对象上直接加365天
         futureCalendar.add(Calendar.DAY_OF_YEAR, 365);
         
         flashSale.setApplicableProducts(products);`
	ctx := base.NewContextForBareUser("test", "test", "test", base.SiteTypeUnknown)
	codeBlocks := ParseDiff(ctx, data)
	assert.Equal(t, 2, len(codeBlocks))
	assert.Equal(t, 380, codeBlocks[0].OldCodeStartLineNumber)
	assert.Equal(t, 394, codeBlocks[0].OldCodeEndLineNumber)
	assert.Equal(t, 370, codeBlocks[0].NewCodeStartLineNumber)
	assert.Equal(t, 381, codeBlocks[0].NewCodeEndLineNumber)

	assert.Equal(t, 400, codeBlocks[1].OldCodeStartLineNumber)
	assert.Equal(t, 406, codeBlocks[1].OldCodeEndLineNumber)
	assert.Equal(t, 387, codeBlocks[1].NewCodeStartLineNumber)
	assert.Equal(t, 392, codeBlocks[1].NewCodeEndLineNumber)
}

func TestParseMultipleHunksForCodeup(t *testing.T) {
	data := `--- /dev/null
+++ b/app-configs/chart/values2.yaml
@@ -0,0 +1,66 @@
+# Default values for spring-boot-sample.
+# This is a YAML-formatted file.
+# Declare variables to be passed into your templates.
+
+replicaCount: 1
+
+image:
+  repository: nginx
+  pullPolicy: IfNotPresent
+
+imagePullSecrets: []
+nameOverride: \"\"
+fullnameOverride: \"\"
+
+serviceAccount:
+  # Specifies whether a service account should be created
+  create: true
+  # The name of the service account to use.
+  # If not set and create is true, a name is generated using the fullname template
+  name:
+
+podSecurityContext: {}
+  # fsGroup: 2000
+
+securityContext: {}
+  # capabilities:
+  #   drop:
+  #   - ALL
+  # readOnlyRootFilesystem: true
+  # runAsNonRoot: true
+  # runAsUser: 1000
+
+service:
+  type: ClusterIP
+  port: 80
+
+ingress:
+  enabled: false
+  annotations: {}
+    # kubernetes.io/ingress.class: nginx
+    # kubernetes.io/tls-acme: \"true\"
+  hosts:
+    - host: chart-example.local
+      paths: []
+  tls: []
+  #  - secretName: chart-example-tls
+  #    hosts:
+  #      - chart-example.local
+
+resources: {}
+  # We usually recommend not to specify default resources and to leave this as a conscious
+  # choice for the user. This also increases chances charts run on environments with little
+  # resources, such as Minikube. If you do want to specify resources, uncomment the following
+  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
+  # limits:
+  #   cpu: 100m
+  #   memory: 128Mi
+  # requests:
+  #   cpu: 100m
+  #   memory: 128Mi
+
+nodeSelector: {}
+
+tolerations: []
+
+affinity: {}
\\ No newline at end of file
`
	ctx := base.NewContextForBareUser("test", "test", "test", base.SiteTypeUnknown)
	codeBlocks := ParseDiff(ctx, data)
	assert.Equal(t, 1, len(codeBlocks))
	assert.Equal(t, 0, codeBlocks[0].OldCodeStartLineNumber)
	assert.Equal(t, 0, codeBlocks[0].OldCodeEndLineNumber)
	assert.Equal(t, 1, codeBlocks[0].NewCodeStartLineNumber)
	assert.Equal(t, 66, codeBlocks[0].NewCodeEndLineNumber)

}
