package session

import (
	"fmt"
	errors2 "github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	i18n "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/in18"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"gopkg.in/yaml.v3"
	"k8s.io/utils/ptr"
	"strings"
)

func (s *service) GetCodeReviewProperty(ctx base.Context, sessionId string) (*agent.RepositoryAgentRule, error) {
	sessionInfo, err := s.GetSession(ctx, sessionId)
	if err != nil {
		return nil, err
	}
	if rule, err := s.TryGetCodeReviewPropertyFromRepository(ctx, nil, sessionInfo); err == nil && rule != nil {
		ctx.GetLogger().Info("found repository rule file, using it", "rule", rule)
		return rule, nil
	} else if err != nil {
		ctx.GetLogger().Warn("get code review rule failed, ignore it", "err", err)
	}
	// 先获取仓库规则，查看是否使用仓库级规则
	if sessionInfo.Metadata != nil {
		repoInfo, err := s.core.GetRepositoryService().GetRepository(ctx, sessionInfo.Metadata.RepositoryId)
		if err != nil {
			return nil, err
		}
		if repoInfo.Setting != nil && repoInfo.Setting.CustomCodeReviewRule {
			ctx.GetLogger().Info("found code review rule for repository", "repoId", repoInfo.RepositoryId, "url", repoInfo.Url, "rule", repoInfo.Setting.CodeReview)
			return agent.BuildRepositoryAgentRule(true, repoInfo.Setting.CodeReview), nil
		}
	}
	// 使用智能体级规则
	idInfo, err := s.core.GetIdentityService().GetIdentity(ctx, sessionInfo.IdentityId)
	if err != nil {
		return nil, err
	}
	agentInfo, err := s.core.GetAgentService().GetAgent(ctx, idInfo.AgentId)
	if err != nil {
		return nil, err
	}
	if agentInfo.AgentProperty != nil {
		switch sessionInfo.Property.Type {
		case session.CodeReview, session.CodeaoneCodeReview:
			ctx.GetLogger().Info("found code review rule for agent", "agentId", agentInfo.AgentId, "rule", agentInfo.AgentProperty.CodeReview)
			return agent.BuildRepositoryAgentRule(true, agentInfo.AgentProperty.CodeReview), nil
		case session.GithubCodeReview:
			ctx.GetLogger().Info("found github code review rule for agent", "agentId", agentInfo.AgentId, "rule", agentInfo.AgentProperty.GithubCodeReview)
			return agent.BuildRepositoryAgentRule(true, agentInfo.AgentProperty.GithubCodeReview), nil
		default:
			return nil, errors.New(codes.ServerInternalError, "unsupported code platform", sessionInfo.Property.Type)
		}
	}
	defaultRule := agent.NewDefaultCodeReviewProperty()
	ctx.GetLogger().Info("could not found repository or agent rule, using system default", "rule", defaultRule)
	return agent.BuildRepositoryAgentRule(true, defaultRule), nil
}

func (s *service) TryGetCodeReviewPropertyFromRepository(ctx base.Context, sessionId *string, sessionInfo *session.Info) (*agent.RepositoryAgentRule, error) {
	if sessionInfo == nil && sessionId == nil {
		return nil, errors.New(codes.ErrInvalidParameterWithEmpty, "sessionId or sessionInfo")
	}
	if sessionInfo == nil {
		if info, err := s.GetSession(ctx, *sessionId); err != nil {
			return nil, err
		} else {
			sessionInfo = info
		}
	}
	switch sessionInfo.Property.Type {
	case session.CodeReview, session.GitlabCodeReview, session.GithubCodeReview, session.CodeaoneCodeReview:
	default:
		// 其他平台先不支持从仓库读取规则
		return nil, nil
	}

	mrInfo, err := s.core.GetCodePlatformService().GetMergeRequest(ctx, sessionInfo.SessionId, &codeplatform.GetMergeRequestOptions{
		WithCommits: false,
	})
	if err != nil {
		return nil, err
	}
	ref := mrInfo.SourceRef
	filePath := ".lingma-agents.yaml"
	yamlFile, err := s.core.GetCodePlatformService().GetRepositoryFile(ctx, sessionInfo.SessionId, &codeplatform.GetRepositoryFileOpts{
		Ref:  ptr.To(ref),
		Path: filePath,
	})
	if err != nil {
		return nil, err
	}
	yamlContent := yamlFile.GetContent()
	ctx.GetLogger().Info("found repository rule yaml", "yaml", yamlContent)
	var agentRule agent.RepositoryAgentRule
	if err := yaml.Unmarshal([]byte(yamlContent), &agentRule); err != nil {
		return nil, errors2.WithStack(err)
	}

	return &agentRule, nil
}

func (s *service) ProcessCodeReviewEligibility(ctx base.Context, sessionId *string, sessionInfo *session.Info, rule *agent.CodeReviewProperty) (bool, error) {
	if sessionId == nil && sessionInfo == nil {
		return false, errors.New(codes.ErrInvalidParameterWithEmpty, "sessionId or sessionInfo")
	}
	if sessionInfo == nil {
		if info, err := s.GetSession(ctx, *sessionId); err != nil {
			return false, err
		} else {
			sessionInfo = info
		}
	}

	filter := agent.NewDefaultTriggerFilter()
	if rule != nil && rule.TriggerFilter != nil {
		filter = rule.TriggerFilter
	}
	locale := ctx.GetLocale()
	if rule != nil && rule.OutputSetting != nil {
		// 通过智能体配置覆盖语言
		locale = rule.OutputSetting.Language.ToCodeReviewTaskValue()
	}
	if reason, err := findSkipReason(ctx, locale, sessionInfo.SessionId, filter); err != nil {
		return false, err
	} else if reason != nil {
		comment := generateSkipComment(ctx, locale, reason)
		if _, err := core.GetCoreService().GetCodePlatformService().CreateMergeRequestComment(ctx, sessionInfo.SessionId,
			&codeplatform.CreateMergeRequestCommentOpts{Content: comment},
		); err != nil {
			return false, errors2.WithStack(err)
		}
		return false, nil
	}

	return true, nil
}

func findSkipReason(ctx base.Context, locale i18n.Locale, sessionId string, filter *agent.CodeReviewTriggerFilter) (*agent.SkipReason, error) {
	mr, err := core.GetCoreService().GetCodePlatformService().GetMergeRequest(ctx, sessionId, &codeplatform.GetMergeRequestOptions{
		WithCommits: false,
	})
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	i18nKeyPrefix := "skip-review."
	if ctx.GetActBy() == base.ActByCodeupWebhook {
		i18nKeyPrefix = "skip-review-codeup."
	}
	getKey := func(suffix string) string {
		return fmt.Sprintf("%s%s", i18nKeyPrefix, suffix)
	}

	files := mr.GetFileCount()
	if files > filter.FileLimit {
		return &agent.SkipReason{
			Reason:      i18n.Localize(locale, getKey("file-limit.reason")),
			Description: i18n.Localize(locale, getKey("file-limit.description")),
			Details: []agent.DetailGroup{
				{
					Title: i18n.Localize(ctx.GetLocale(), getKey("file-limit.item-title")),
					Items: []string{i18n.Localize(ctx.GetLocale(), getKey("file-limit.item-description"), files, filter.FileLimit)},
				},
			},
		}, nil
	}

	lines := mr.GetDiffLines()
	if lines > filter.LineLimit {
		return &agent.SkipReason{
			Reason:      i18n.Localize(locale, getKey("line-limit.reason")),
			Description: i18n.Localize(locale, getKey("line-limit.description")),
			Details: []agent.DetailGroup{
				{
					Title: i18n.Localize(locale, getKey("line-limit.item-title")),
					Items: []string{i18n.Localize(locale, getKey("line-limit.item-description"), lines, filter.LineLimit)},
				},
			},
		}, nil
	}
	commits, err := core.GetCoreService().GetCodePlatformService().ListMergeRequestCommits(ctx, sessionId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	if len(commits) > filter.CommitLimit {
		return &agent.SkipReason{
			Reason:      i18n.Localize(locale, getKey("commit-limit.reason")),
			Description: i18n.Localize(locale, getKey("commit-limit.description")),
			Details: []agent.DetailGroup{
				{
					Title: i18n.Localize(locale, getKey("commit-limit.item-title")),
					Items: []string{i18n.Localize(locale, getKey("commit-limit.item-description"), len(commits), filter.CommitLimit)},
				},
			},
		}, nil
	}

	var matchedKeywords []string
	for _, titleKeyword := range filter.TitleKeywords {
		if strings.Contains(mr.Title, titleKeyword) {
			matchedKeywords = append(matchedKeywords, titleKeyword)
		}
	}
	if len(matchedKeywords) > 0 {
		return &agent.SkipReason{
			Reason:      i18n.Localize(locale, getKey("title-keyword-filter.reason")),
			Description: i18n.Localize(locale, getKey("title-keyword-filter.description")),
			Details: []agent.DetailGroup{
				{
					Title: i18n.Localize(locale, getKey("title-keyword-filter.item-title")),
					Items: matchedKeywords,
				},
			},
		}, nil
	}
	var targetBranchMatches []string
	for _, targetBranchGlob := range filter.TargetBranchGlobs {
		if util.IsGlobMatch(targetBranchGlob, mr.TargetBranch) {
			targetBranchMatches = append(targetBranchMatches, targetBranchGlob)
		}
	}
	if len(targetBranchMatches) > 0 {
		return &agent.SkipReason{
			Reason:      i18n.Localize(locale, getKey("target-branch-filter.reason")),
			Description: i18n.Localize(locale, getKey("target-branch-filter.description")),
			Details: []agent.DetailGroup{
				{
					Title: i18n.Localize(locale, getKey("target-branch-filter.item-title")),
					Items: targetBranchMatches,
				},
			},
		}, nil
	}

	var sourceBranchMatches []string
	for _, sourceBranchGlob := range filter.SourceBranchGlobs {
		if util.IsGlobMatch(sourceBranchGlob, mr.SourceRef) {
			sourceBranchMatches = append(sourceBranchMatches, sourceBranchGlob)
		}
	}
	if len(sourceBranchMatches) > 0 {
		return &agent.SkipReason{
			Reason:      i18n.Localize(locale, getKey("source-branch-filter.reason")),
			Description: i18n.Localize(locale, getKey("source-branch-filter.description")),
			Details: []agent.DetailGroup{
				{
					Title: i18n.Localize(locale, getKey("source-branch-filter.item-title")),
					Items: sourceBranchMatches,
				},
			},
		}, nil
	}

	return nil, nil
}

func generateSkipComment(ctx base.Context, locale i18n.Locale, skipReason *agent.SkipReason) string {
	comment := i18n.Localize(locale, "skip-review.comment-title")
	comment += "\n\n---\n\n"
	comment += "<blockquote>\n"
	comment += fmt.Sprintf("<h4>%s</h4>\n\n", skipReason.Reason)
	comment += skipReason.Description + "\n\n"

	if len(skipReason.Details) > 0 {
		for _, group := range skipReason.Details {
			comment += "<details><summary>" + group.Title + "</summary>\n\n"
			for _, item := range group.Items {
				comment += "- " + item + "\n\n"
			}
			comment += "</details>\n"
		}
	}
	comment += "</blockquote>"

	return comment
}
