package session

import (
	errors2 "github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/overview"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/sql"
	"math"
	"time"
)

func (s *service) RecordMergeRequestInfo(ctx base.Context, sessionId string, repoId string, info *session.MergeRequestProperty) error {
	entity := &mergeRequestEntity{SessionId: sessionId}
	if existEntity, err := dao.GetEntity(ctx, entity, dao.NewDbOptions(dao.WithKV("session_id", sessionId))); err == nil {
		if !errors.Is(err, codes.ErrRecordNotFound) {
			return errors2.WithStack(err)
		}
		entity = existEntity

	} else if !errors.Is(err, codes.ErrRecordNotFound) {
		return errors2.WithStack(err)
	}
	entity.RepoId = repoId
	entity.Property = info
	return dao.Save(ctx, entity)
}

func (s *service) GetMergeRequestMetrics(ctx base.Context) ([]overview.MetricInfo, error) {
	results := make([]overview.MetricInfo, 0)
	gatherMetricFuncs := []func(base.Context) (*overview.MetricInfo, error){
		getMergeRequestCountMetrics,
		getCodeReviewCommentCount,
		getCodeSuggestionApplyRatioMetrics,
		getMergeRequestTimeMetrics,
	}
	for _, f := range gatherMetricFuncs {
		if metric, err := f(ctx); err == nil {
			results = append(results, *metric)
		} else {
			return nil, errors2.WithStack(err)
		}
	}

	return results, nil
}

func buildMetricsByRawScan(ctx base.Context,
	sqlTemplate sql.Template[singleValue],
	timeDuration time.Duration,
	metricName overview.MetricName) (*overview.MetricInfo, error) {

	now := time.Now()
	compareScan, err := dao.RawScan(ctx, sqlTemplate, ctx.GetUid(), now.Add(-2*timeDuration), now.Add(-timeDuration))
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	lastScan, err := dao.RawScan(ctx, sqlTemplate, ctx.GetUid(), now.Add(-timeDuration), now)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	compareRatio := calcCompareRatio(lastScan.Value, compareScan.Value)
	return &overview.MetricInfo{
		Name:         metricName,
		Value:        keep2Digital(lastScan.Value),
		CompareRatio: keep2Digital(compareRatio),
		Period:       overview.Period7day,
	}, nil
}

func getCodeSuggestionApplyRatioMetrics(ctx base.Context) (*overview.MetricInfo, error) {
	return buildMetricsByRawScan(ctx, codeSuggestionApplyRatioSqlTemplate, 7*24*time.Hour, overview.AverageCodeSuggestionApplyRatio)

}

func getCodeReviewCommentCount(ctx base.Context) (*overview.MetricInfo, error) {
	return buildMetricsByRawScan(ctx, codeReviewAgentCommentCountSqlTemplate, 7*24*time.Hour, overview.CodeReviewAgentCommentCount)
}

func getMergeRequestCountMetrics(ctx base.Context) (*overview.MetricInfo, error) {
	return buildMetricsByRawScan(ctx, mergeRequestCountSqlTemplate, 7*24*time.Hour, overview.MergeRequestCount)
}

func getMergeRequestTimeMetrics(ctx base.Context) (*overview.MetricInfo, error) {
	return buildMetricsByRawScan(ctx, sqlAverageMergeRequestTimeSqlTemplate, 7*24*time.Hour, overview.AverageMergeRequestTime)
}

func calcCompareRatio(latestValue float64, compareValue float64) float64 {
	if compareValue == 0 {
		return 0
	}
	return (latestValue - compareValue) / compareValue
}

func keep2Digital(value float64) float64 {
	return math.Round(value*100) / 100
}

type singleValue struct {
	Value float64 `json:"value"`
}

var (
	codeReviewAgentCommentCountSqlTemplate = sql.Template[singleValue]{
		Sql: `
select
  	sum((metric -> 'agentCommentCount')::int) as value
from
  	"t_merge_request" as tmr
inner join "t_session" as ts 
ON "tmr"."session_id" = ts."session_id"
	AND ts."user_id" = ?
    AND ts.session_state != 'Skipped'
	AND ts.gmt_create > ?
	AND ts.gmt_create < ?
`,
	}
	codeSuggestionApplyRatioSqlTemplate = sql.Template[singleValue]{
		Sql: `SELECT 
    (AVG(
        CAST((metric->>'agentCodeSuggestionAppliedCount')::int AS DECIMAL) /
        NULLIF(CAST((metric->>'agentCodeSuggestionCount')::int AS DECIMAL), 0)
    ) * 100) as value
FROM "t_merge_request" as tmr
INNER JOIN "t_session" as ts
ON "tmr"."session_id" = ts."session_id"
	AND ts."user_id" = ?
    AND ts.session_state != 'Skipped'
	AND ts.gmt_create > ?
	AND ts.gmt_create < ?`,
	}
	mergeRequestCountSqlTemplate = sql.Template[singleValue]{
		Sql: `
select
  	count(*) as value
from
  	"t_session" as ts
WHERE  ts."user_id" = ?
	AND ts.gmt_create > ?
	AND ts.gmt_create < ?
    AND ts.session_state != 'Skipped'
`,
	}
	sqlAverageMergeRequestTimeSqlTemplate = sql.Template[singleValue]{
		Sql: `
SELECT 
    ROUND(
        AVG(EXTRACT(EPOCH FROM (ts.gmt_modified - ts.gmt_create))/3600)::numeric, 
        2
    ) as value
FROM t_session as ts 
WHERE ts.session_state = 'Finished'
  	AND "session_property" ->> 'type' = 'CodeReview'
	AND ts."user_id" = ?
	AND ts.gmt_create > ?
	AND ts.gmt_create < ?
	AND ts.session_state != 'Skipped'
`,
	}
)
