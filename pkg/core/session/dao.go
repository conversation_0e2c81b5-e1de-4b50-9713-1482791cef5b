package session

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
)

func withSessionType(typ string) dao.DbWhereOption {
	return dao.WithKV("session_property ->> 'type'", typ)
}

func withSessionTypes(types ...session.Type) dao.DbWhereOption {
	return dao.WithKeyValues("session_property ->> 'type'", types...)
}
