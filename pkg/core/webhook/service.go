package webhook

import (
	"context"
	"encoding/json"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/component"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/user"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/codeaone"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/codeup"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/github"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/gitlab"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
)

var _ comncore.WebhookService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "WebhookService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}
func (s *service) GitlabWebhook(ctx base.Context, token string, data map[string]any) (string, error) {
	ctx.GetLogger().Info("receive gitlab webhook request", "token", token, "data", data)
	idInfo, err := s.core.GetIdentityService().GetIdentityByToken(ctx, token)
	if err != nil {
		return "", errors.WithStack(err)
	}
	// 保证后续调用均是正确UserInfo
	ctx = base.NewContextWithUserOverride(ctx, idInfo.UserId, "")
	marshal, err := json.Marshal(data)
	if err != nil {
		return "", errors.WithStack(err)
	}
	var payload webhook.GitlabWebhookPayload
	if err := json.Unmarshal(marshal, &payload); err != nil {
		return "", errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return "", errors.WithStack(err)
	}
	access := &webhookAccessEntity{
		IdentityId:      idInfo.IdentityId,
		Source:          idInfo.Source,
		EventType:       string(payload.EventType),
		EventProperties: data,
	}
	if err := dao.Save(ctx, access); err != nil {
		return "", errors.WithStack(err)
	}
	// gitlab场景 一定能找到identityInfo，直接触发外部webhook
	s.triggerExternalHandlers(ctx, idInfo, string(payload.EventType), data)
	sid := ""
	if h := gitlab.GetGitlabWebhookHandler(payload.EventType); h != nil {
		if sid, err = h(ctx, idInfo, &payload); err != nil {
			return "", errors.WithStack(err)
		}
	} else {
		ctx.GetLogger().Warn("no handler found for gitlab webhook request, ignore it", "eventType", payload.EventType)
	}

	return sid, nil
}

func (s *service) GithubWebhook(ctx base.Context, event webhook.GithubEvent, data map[string]any) error {
	ctx.GetLogger().Info("receive github webhook request", "event", event, "data", data)
	tryFoundIdentity := func() *identity.IdentityInfo {
		// 判空，可能是非 installation 相关事件，例如 注销授权等
		if data["installation"] == nil {
			return nil
		}
		// 尝试从payload中获取用户ID
		if v, ok := data["installation"].(map[string]any)["id"]; ok {
			installationId := cast.ToInt64(v)
			info, err := s.core.GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId)
			if err != nil {
				if commonerrors.Is(err, codes.ErrRecordNotFound) {
					ctx.GetLogger().Warn("no identity found, ignore it")
				} else {
					ctx.GetLogger().Error("get identity by github installation id failed", "installationId", installationId, "err", err)
				}
				return nil
			}
			return info
		}
		return nil
	}
	idInfo := tryFoundIdentity()
	if idInfo != nil {
		// 找到了就覆盖一下UID
		ctx = base.NewContextWithUserOverride(ctx, idInfo.UserId, "")
	}

	// 未验证的身份只允许安装操作，发生PR事件且安装没超限制时，在PR下发表关联提示评论，其他场景忽略。
	// github场景 installation事件没有identityInfo,需要由handler处理之后创建identity再触发外部handler
	if !github.IsValidGithubUser(ctx.GetUid()) && event != webhook.GithubInstallationEvent {
		identityId := ""
		if idInfo != nil {
			identityId = idInfo.IdentityId
		}
		ctx.GetLogger().Warn("Identity not bind to aliyun account", "identityId", identityId, "userId", ctx.GetUid(), "event", event)
		validInstallationNumber, err := comncore.GetCoreService().GetCallbackService().GetValidInstallationCount(ctx)
		if err != nil {
			return errors.WithStack(err)
		}
		installationLimit := config.GetOrDefaultInt64(config.KeyGithubAppInstallationLimit, 100)
		if validInstallationNumber >= installationLimit {
			ctx.GetLogger().Info("installation number exceeds limit, ignore PR event instead of send bind hint ", "installation number", validInstallationNumber, "limit", installationLimit)
		} else if event == webhook.GithubPullRequestEvent {
			if err := (github.GetGithubWebhookHandler(event))(ctx, data); err != nil {
				return errors.WithStack(err)
			}
		}
	} else if h := github.GetGithubWebhookHandler(event); h != nil {
		if err := h(ctx, data); err != nil {
			return errors.WithStack(err)
		}
	} else {
		ctx.GetLogger().Warn("no handler found for github webhook request, ignore it", "event", event)
	}

	if idInfo == nil {
		if idInfo = tryFoundIdentity(); idInfo != nil {
			ctx = base.NewContextWithUserOverride(ctx, idInfo.UserId, "")
		} else {
			ctx.GetLogger().Warn("no identity found after event handler, ignore it")
			idInfo = &identity.IdentityInfo{
				Source: identity.SourceGithub,
			}
		}
	}
	if github.IsValidGithubUser(ctx.GetUid()) {
		s.triggerExternalHandlers(ctx, idInfo, string(event), data)
	}

	access := &webhookAccessEntity{
		IdentityId:      idInfo.IdentityId,
		Source:          identity.SourceGithub,
		EventType:       string(event),
		EventProperties: data,
	}
	if err := dao.Save(ctx, access); err != nil {
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("github webhook processed")
	return nil
}

func (s *service) CodeupWebhook(ctx base.Context, data map[string]any) error {
	event := webhook.CodeupEvent("")
	if d, ok := data["data"].(map[string]any); ok {
		if v, ok := d["object_kind"].(string); ok {
			event = webhook.CodeupEvent(v)
		}
	}

	ctx.GetLogger().Info("receive codeup webhook request", "event", event, "data", data)
	if basicPayload, err := util.ConvertMapTo[webhook.CodeupWebhookBasicPayload](ctx, data); err != nil {
		ctx.GetLogger().Error("convert codeup webhook basic payload failed", "err", err)
		// 继续吧
	} else if err := basicPayload.Validate(); err != nil {
		ctx.GetLogger().Error("validate codeup webhook basic payload failed", "err", err)
		// 行吧 不用管
	} else {
		if orgId, err := basicPayload.GetOrganizationId(); err != nil {
			ctx.GetLogger().Error("get codeup organization id failed", "err", err)
		} else {
			// 替换上下文，注入用户信息
			ctx = base.NewContextForBareUser(orgId, ctx.GetTraceId(), base.ActByCodeupWebhook, base.SiteTypeUnknown)
			u := &user.User{UserId: ctx.GetUid(), UserType: user.UserTypeCodeup}
			if err := s.core.GetUserService().SaveUserIfNotExist(ctx, u); err != nil {
				ctx.GetLogger().Error("failed to save user", "user", u, "err", err)
				return errors.WithStack(err)
			}
		}
	}
	if h := codeup.GetCodeupWebhookHandler(event); h != nil {
		if err := h(ctx, data); err != nil {
			return errors.WithStack(err)
		}
	} else {
		ctx.GetLogger().Warn("no handler found for codeup event, ignore it", "event", event)
	}
	// todo  按需求触发外部组件处理Webhook
	// s.triggerExternalHandlers(ctx, nil, string(event), data)
	access := &webhookAccessEntity{
		// fixme 记录Identity
		IdentityId:      "",
		Source:          identity.SourceCodeup,
		EventType:       string(event),
		EventProperties: data,
	}
	if err := dao.Save(ctx, access); err != nil {
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("codeup webhook processed")
	return nil

}

func (s *service) CodeaoneWebhook(ctx base.Context, token string, data map[string]any) error {
	idInfo, err := s.core.GetIdentityService().GetIdentityByToken(ctx, token)
	if err != nil {
		return errors.WithStack(err)
	}
	ctx = base.NewContextWithUserOverride(ctx, idInfo.UserId, "")
	event := webhook.CodeaoneEvent("")
	if v, ok := data["object_kind"].(string); ok {
		event = webhook.CodeaoneEvent(v)
	}
	ctx.GetLogger().Info("receive code-aone webhook request", "event", event, "data", data)
	if h := codeaone.GetCodeaoneWebhookHandler(event); h != nil {
		if err := h(ctx, idInfo, data); err != nil {
			return errors.WithStack(err)
		}
	} else {
		ctx.GetLogger().Warn("no handler found for code-aone event, ignore it", "event", event)
	}
	access := &webhookAccessEntity{
		IdentityId:      idInfo.IdentityId,
		Source:          identity.SourceCodeaone,
		EventType:       string(event),
		EventProperties: data,
	}
	if err := dao.Save(ctx, access); err != nil {
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("code-aone webhook processed")
	return nil
}

func (s *service) triggerExternalHandlers(ctx base.Context, idInfo *identity.IdentityInfo, event string, data map[string]any) {
	handlerConfigStr := config.GetOrDefault(config.KeyWebhookExternalHandlers, "[]")
	var handlerUris []string
	if err := json.Unmarshal([]byte(handlerConfigStr), &handlerUris); err != nil {
		ctx.GetLogger().Error("unmarshal webhook external handlers failed", "err", err)
		return
	}
	if len(handlerUris) == 0 {
		return
	}
	for _, uri := range handlerUris {
		// todo 根据idInfo构造newCtx，当前没有身份系统，先pass
		ctx.GetLogger().Info("trigger external webhook", "uri", uri)
		newCtx := base.NewBackgroundContext(ctx)
		// todo 增加一个超时时间控制
		go util.SafeGoroutineFunc(newCtx, func() {
			if err := component.TriggerWebhook(newCtx, uri, idInfo.IdentityId, string(idInfo.Source), event, data); err != nil {
				// 失败了打个日志就好了，也拯救不了什么
				commonerrors.AlertError(newCtx, commonerrors.AlertScopeExternalWebhook, []string{uri, idInfo.IdentityId}, "trigger external webhook failed", err)
			}
		})
	}
}

func New() comncore.WebhookService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
