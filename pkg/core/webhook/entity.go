package webhook

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"time"
)

type webhookAccessEntity struct {
	ID              int64           `gorm:"column:id;primaryKey"`
	IdentityId      string          `gorm:"column:identity_id"`
	EventType       string          `gorm:"column:event_type"`
	Source          identity.Source `gorm:"column:source"`
	EventProperties map[string]any  `gorm:"column:event_properties;serializer:json"`
	GmtCreate       time.Time       `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified     time.Time       `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (*webhookAccessEntity) TableName() string {
	return "t_webhook_access"
}
