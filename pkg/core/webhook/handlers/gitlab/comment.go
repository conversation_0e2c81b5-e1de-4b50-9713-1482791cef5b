package gitlab

import (
	"fmt"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"net/url"
	"strings"
)

func isAgentDiscussion(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, commentId string) (bool, error) {
	session, err := core.GetCoreService().GetSessionService().GetGitlabCodeReviewSession(ctx, idInfo.IdentityId, mergeRequestId, projectId)
	if err != nil {
		return false, errors.WithStack(err)
	}

	if session == nil {
		ctx.GetLogger().Info("session is not found, comment ignored")
		return false, nil
	}

	discussion, err := core.GetCoreService().GetCodePlatformService().ListMergeRequestComments(ctx, session.SessionId, &codeplatform.ListMergeRequestCommentsRequest{CommentId: commentId})
	if err != nil {
		return false, errors.WithStack(err)
	}
	if discussion.Comments[0].Username == idInfo.PlatformUsername {
		return true, nil
	}
	return false, nil
}

func GitlabCommentHandler(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.GitlabWebhookPayload) (string, error) {
	action := payload.ObjectAttributes["action"]
	switch action {
	// 某些版本的 note 事件 没有action字段
	case "create", "", nil:
	default:
		ctx.GetLogger().Info("ignore action", "action", action)
		return "", nil
	}
	userName := payload.User.Username
	if userName == idInfo.PlatformUsername {
		ctx.GetLogger().Info("ignore bot comment", "userName", userName)
		return "", nil
	}
	note := payload.ObjectAttributes["note"]
	commentId, err := cast.ToStringE(payload.ObjectAttributes["id"])
	if err != nil {
		ctx.GetLogger().Error("failed to get note id", "err", err)
		return "", errors.WithStack(err)
	}
	//普通note评论（非讨论）的type为nil
	if payload.ObjectAttributes["type"] != nil {
		discussionId := payload.GetDiscussionId()
		commentId = strings.Join([]string{discussionId, commentId}, "-")
		isAgentDiscussion, err := isAgentDiscussion(ctx, idInfo, cast.ToString(payload.Project.Id), cast.ToString(payload.MergeRequest.Iid), commentId)
		if err != nil {
			return "", errors.WithStack(err)
		}
		if !isAgentDiscussion && !strings.Contains(cast.ToString(note), fmt.Sprintf("@%s", idInfo.PlatformUsername)) {
			ctx.GetLogger().Info("not agent discussion, ignore it", "note", note)
			return "", nil
		}
	} else if !strings.Contains(cast.ToString(note), fmt.Sprintf("@%s", idInfo.PlatformUsername)) {
		ctx.GetLogger().Info("bot not mentioned, ignore it", "note", note)
		return "", nil
	}
	projectUrl, err := url.JoinPath(idInfo.PlatformEndpoint, fmt.Sprintf("%s.git", payload.Project.PathWithNamespace))
	if err != nil {
		return "", errors.WithStack(err)
	}
	mrId := payload.MergeRequest.Iid
	params := map[string]any{
		"event":           "MERGE_REQUEST_COMMENT",
		"project_url":     projectUrl,
		"comment_id":      commentId,
		"bot_name":        idInfo.PlatformUsername,
		"platform_type":   identity.SourceGitLab,
		"project_web_url": payload.Project.WebUrl, // 这个地址是web地址（不带.git后缀），非可以clone的地址。用于评论内的链接跳转使用
	}
	sessionInfo, err := core.GetCoreService().GetSessionService().CreateOrLoadForCodeReview(ctx, idInfo.IdentityId, cast.ToString(mrId), cast.ToString(payload.Project.Id))
	if err != nil {
		return "", errors.WithStack(err)
	}
	if payload.MergeRequestClosed() {
		ctx.GetLogger().Info("Reply comment by Controller due to MergeRequest state is closed", "commentId", commentId)
		// MR关闭的场景下，由程序直接响应
		replyContent := config.GetOrDefault(config.KeyCodeReviewCommentReplyContent, "当前合并请求已关闭，如需请重新打开合并请求。")
		comment := &codeplatform.CreateMergeRequestCommentOpts{
			Content: replyContent,
		}
		if strings.Contains(commentId, "-") {
			comment.ReplyToCommentID = &commentId
		}
		if _, err := core.GetCoreService().GetCodePlatformService().CreateMergeRequestComment(ctx, sessionInfo.SessionId, comment); err != nil {
			ctx.GetLogger().Error("CreateMergeRequestComment failed", "err", err)
			return "", errors.WithStack(err)
		}
		return sessionInfo.SessionId, nil
	}
	agentRule, err := core.GetCoreService().GetSessionService().GetCodeReviewProperty(ctx, sessionInfo.SessionId)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if !agentRule.ReviewRules.Enable {
		ctx.GetLogger().Info("CodeReview is disabled", "sessionId", sessionInfo.SessionId)
		return "", nil
	}
	rule := agentRule.ReviewRules.CodeReviewProperty

	for k, v := range rule.ToCodeReviewTaskParameters() {
		params[k] = v
	}
	cfg := &agentruntime.TaskConfig{
		Parameters: params,
	}
	ctx.GetLogger().Info("Trigger CodeReview for comment", "taskConfig", cfg)
	if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, sessionInfo.SessionId, cfg); err != nil {
		return "", errors.WithStack(err)
	}
	return sessionInfo.SessionId, nil
}
