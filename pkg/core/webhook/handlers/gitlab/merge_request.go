package gitlab

import (
	"fmt"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	errors2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/common"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
	"net/url"
)

const (
	actionReview = "review"
	actionClose  = "close"
)

func GitlabMergeRequestHandler(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.GitlabWebhookPayload) (string, error) {
	action := actionReview
	switch payload.ObjectAttributes["action"] {
	case "open", "reopen":
		action = actionReview
	case "close", "merge":
		action = actionClose
	default:
		ctx.GetLogger().Info("ignore action", "action", payload.ObjectAttributes["action"])
		return "", nil
	}
	mrId := payload.ObjectAttributes["iid"]

	agentInfo, err := core.GetCoreService().GetAgentService().GetAgent(ctx, idInfo.AgentId)
	if err != nil {
		return "", errors.WithStack(err)
	}
	if !agentInfo.Enabled() {
		ctx.GetLogger().Info("Agent is disabled, skip CodeReview Task", "agentId", idInfo.AgentId)
		return "", nil
	}

	sessionInfo, err := core.GetCoreService().GetSessionService().CreateOrLoadForCodeReview(ctx, idInfo.IdentityId, cast.ToString(mrId), cast.ToString(payload.Project.Id))
	if err != nil {
		return "", errors.WithStack(err)
	}
	repoInfo, err := core.GetCoreService().GetRepositoryService().EnsureRepository(ctx, identity.SourceGitLab, payload.Project.Name, payload.Project.Homepage)
	if err != nil {
		return "", errors.WithStack(err)
	}
	saveMergeRequestInfo(ctx, agentInfo, sessionInfo, payload, repoInfo)

	if idInfo.Source == identity.SourceAgentConnect {
		ctx.GetLogger().Info("webhook invocation from agent connect")
		// 如果是agent-connect进来的请求，这里需要先把生成的sessionId返回出去，等agent-connect建立连接，否则后续的处理会卡住
		go util.SafeGoroutineFunc(ctx, func() {
			err = doHandleMergeRequest(ctx, idInfo, payload, sessionInfo, action)
			if err != nil {
				ctx.GetLogger().Error("fail to handle merge request from agent connect", "err", err)
			}
		})
	} else {
		err = doHandleMergeRequest(ctx, idInfo, payload, sessionInfo, action)
		if err != nil {
			ctx.GetLogger().Error("fail to handle merge request", "err", err)
			return "", err
		}
	}
	return sessionInfo.SessionId, nil
}

func doHandleMergeRequest(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.GitlabWebhookPayload, sessionInfo *session.Info, action string) error {
	switch action {
	case actionReview:
		return doCodeReview(ctx, idInfo, payload, sessionInfo)
	case actionClose:
		return doAbort(ctx, idInfo, payload, sessionInfo)
	}
	return nil
}

func getProjectUrl(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.GitlabWebhookPayload) (string, error) {
	endpoint, err := idInfo.GetSandboxEntrypoint()
	if err != nil {
		return "", errors.WithStack(err)
	}
	subPath := fmt.Sprintf("%s.git", payload.Project.PathWithNamespace)
	projectUrl, err := url.JoinPath(endpoint, subPath)
	if err != nil {
		ctx.GetLogger().Error("url.JoinPath failed", "base", endpoint, "subpath", subPath, "err", err)
		return "", errors.WithStack(err)
	}
	return projectUrl, nil
}

func sendSkipReviewComment(ctx base.Context, sessionId string, reason *agent.SkipReason) error {
	content := generateSkipComment(reason)
	if _, err := core.GetCoreService().GetCodePlatformService().CreateMergeRequestComment(ctx, sessionId, &codeplatform.CreateMergeRequestCommentOpts{
		Content: content,
	}); err != nil {
		ctx.GetLogger().Error("failed to send skip review comment", "err", err)
		return errors.WithStack(err)
	}
	return nil
}

func generateSkipComment(skipReason *agent.SkipReason) string {
	comment := "### ⚠️ 智能评审已跳过\n\n"
	comment += "---\n\n"
	comment += "<blockquote>\n"
	comment += fmt.Sprintf("<h4>%s</h4>\n\n", skipReason.Reason)
	comment += skipReason.Description + "\n\n"

	if len(skipReason.Details) > 0 {
		for _, group := range skipReason.Details {
			comment += "<details><summary>" + group.Title + "</summary>\n\n"
			for _, item := range group.Items {
				comment += "- " + item + "\n\n"
			}
			comment += "</details>\n"
		}
	}
	comment += "</blockquote>"

	return comment
}

// saveMergeRequestInfo 保存 merge request 信息同时更新 会话元数据
func saveMergeRequestInfo(ctx base.Context, agentInfo *agent.AgentInfo, sessionInfo *session.Info, payload *webhook.GitlabWebhookPayload, repoInfo *repository.RepositoryInfo) {
	info := &session.MergeRequestProperty{
		Source:          identity.SourceGitLab,
		ProjectId:       cast.ToString(payload.Project.Id),
		MergeRequestId:  cast.ToString(payload.ObjectAttributes["iid"]),
		Title:           cast.ToString(payload.ObjectAttributes["title"]),
		State:           cast.ToString(payload.ObjectAttributes["state"]),
		Description:     cast.ToString(payload.ObjectAttributes["description"]),
		SourceProjectId: "",
		TargetProjectId: "",
		SourceBranch:    cast.ToString(payload.ObjectAttributes["source_branch"]),
		SourceRef:       cast.ToString(payload.ObjectAttributes["source_branch"]),
		TargetBranch:    cast.ToString(payload.ObjectAttributes["target_branch"]),
		Author:          payload.User.Username,
		Url:             cast.ToString(payload.ObjectAttributes["url"]),
		CreateTimestamp: 0,
		UpdateTimestamp: 0,
	}
	if v, ok := payload.ObjectAttributes["source"].(map[string]any); ok && v != nil {
		if v, ok := v["id"]; ok {
			info.SourceProjectId = cast.ToString(v)
		}
	}
	if v, ok := payload.ObjectAttributes["target"].(map[string]any); ok && v != nil {
		if v, ok := v["id"]; ok {
			info.TargetProjectId = cast.ToString(v)
		}
	}
	if t, err := util.ParseDateTimeWithZone(cast.ToString(payload.ObjectAttributes["created_at"])); err == nil {
		info.CreateTimestamp = t.UnixMilli()
	}
	if t, err := util.ParseDateTimeWithZone(cast.ToString(payload.ObjectAttributes["updated_at"])); err == nil {
		info.UpdateTimestamp = t.UnixMilli()
	}

	if err := core.GetCoreService().GetSessionService().RecordMergeRequestInfo(ctx, sessionInfo.SessionId, repoInfo.RepositoryId, info); err != nil {
		errors2.AlertWarning(ctx, errors2.AlertScopeCodeReview, []string{sessionInfo.SessionId, "gitlab", payload.Project.Name}, "Record MergeRequest Info Failed", err)
	}

	sessionMetadata := &session.Metadata{
		AgentId:      agentInfo.AgentId,
		AgentName:    agentInfo.AgentName,
		Author:       info.Author,
		RepositoryId: repoInfo.RepositoryId,
		SessionName:  cast.ToString(payload.ObjectAttributes["title"]),
	}
	if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{
		Metadata: sessionMetadata,
	}); err != nil {
		errors2.AlertWarning(ctx, errors2.AlertScopeCodeReview, []string{sessionInfo.SessionId, "gitlab", payload.Project.Name}, "Update Session Metadata Failed", err)
	}
}

func getGitlabVersion(ctx base.Context, sessionId string) string {
	v, err := core.GetCoreService().GetCodePlatformService().GetVersion(ctx, sessionId)
	if err != nil {
		ctx.GetLogger().Warn("failed to get gitlab version, step cr agent with empty version", "err", err)
		return ""
	}
	return v
}

func doCodeReview(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.GitlabWebhookPayload, sessionInfo *session.Info) error {
	projectUrl, err := getProjectUrl(ctx, idInfo, payload)
	if err != nil {
		return errors.WithStack(err)
	}
	params := map[string]any{
		"event":            "MERGE_REQUEST",
		"project_url":      projectUrl, // 支持clone的地址
		"bot_name":         idInfo.PlatformUsername,
		"platform_type":    identity.SourceGitLab,
		"platform_version": getGitlabVersion(ctx, sessionInfo.SessionId), // ar 需要通过 gitlab 版本定制流程
		"head_commit_id":   payload.GetHeadCommitId(),
		"project_web_url":  payload.Project.WebUrl, // 这个地址是web地址（不带.git后缀），非可以clone的地址。用于评论内的链接跳转使用
	}
	ctx.GetLogger().Info("Receive CodeReview Task", "basicParams", params)
	agentRule, err := core.GetCoreService().GetSessionService().GetCodeReviewProperty(ctx, sessionInfo.SessionId)
	if err != nil {
		return errors.WithStack(err)
	}
	if !agentRule.ReviewRules.Enable {
		ctx.GetLogger().Info("review disabled by repository rules")
		_ = core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Skipped)})
		return nil
	}
	rule := agentRule.ReviewRules.CodeReviewProperty
	if pass, err := core.GetCoreService().GetSessionService().ProcessCodeReviewEligibility(ctx, ptr.To(sessionInfo.SessionId), nil, rule); err != nil {
		errors2.AlertError(ctx, errors2.AlertScopeCodeReview, []string{sessionInfo.SessionId}, "Check CodeReview Eligibility Failed", err)
		// 检查失败就放过
	} else if !pass {
		ctx.GetLogger().Info("Skip CodeReview task due to session rules", "sessionId", sessionInfo.SessionId)
		return nil
	}

	// todo -------------移除这面这个跳过逻辑---------------
	skip, reason, err := core.GetCoreService().GetAgentService().ShouldSkip(ctx, idInfo.AgentId, sessionInfo.SessionId)
	if err != nil {
		ctx.GetLogger().Error("failed to check should skip", "err", err.Error())
		// 不要阻塞，默认都不跳过
	}
	if skip {
		ctx.GetLogger().Info("skip CodeReview Task", "reason", reason)
		// 跳过本次 review，发送 skip comment
		if err := sendSkipReviewComment(ctx, sessionInfo.SessionId, reason); err != nil {
			return errors.WithStack(err)
		}
		return nil
	}
	// ----------end--------------
	for k, v := range rule.ToCodeReviewTaskParameters() {
		params[k] = v
	}
	cfg := &agentruntime.TaskConfig{
		Parameters: params,
	}
	ctx.GetLogger().Info("Trigger CodeReview Task", "taskConfig", cfg)
	if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, sessionInfo.SessionId, cfg); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func doAbort(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.GitlabWebhookPayload, sessionInfo *session.Info) error {
	ctx.GetLogger().Info("Close CodeReview Task")
	backgroundCtx := base.NewBackgroundContext(ctx)
	go util.SafeGoroutineFunc(backgroundCtx, func() {
		if err := core.GetCoreService().GetSessionService().ScanMergeRequestMetrics(backgroundCtx, sessionInfo.SessionId, true); err != nil {
			errors2.AlertWarning(ctx, errors2.AlertScopeCodeReview, []string{sessionInfo.SessionId}, "Scan CodeReview Metrics Failed", err)
		}
	})
	if err := common.AbortCodeReviewTask(ctx, sessionInfo.SessionId); err != nil {
		return errors.WithStack(err)
	}
	if err := common.CloseSessionForMergeRequest(ctx, sessionInfo); err != nil {
		return errors.WithStack(err)
	}
	return nil
}
