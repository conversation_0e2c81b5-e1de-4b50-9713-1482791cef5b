package gitlab

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
)

// GitlabWebhookHandler 处理Webhook数据
type GitlabWebhookHandler func(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.GitlabWebhookPayload) (string, error)

func GetGitlabWebhookHandler(event webhook.GitlabEvent) GitlabWebhookHandler {
	switch event {
	case webhook.GitlabMergeRequestEvent:
		return GitlabMergeRequestHandler
	case webhook.GitlabCommentEvent:
		return GitlabCommentHandler
	default:
		return nil
	}
}
