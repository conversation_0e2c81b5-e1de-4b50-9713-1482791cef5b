package common

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"k8s.io/utils/ptr"
)

func CloseSessionForMergeRequest(ctx base.Context, sessionInfo *session.Info) error {
	if sessionInfo.State == session.Skipped {
		return nil
	}
	if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Finished)}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}
