package common

import (
	"github.com/pkg/errors"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
)

const (
	CodeReview        = "MERGE_REQUEST"
	IncrementalReview = "MERGE_REQUEST_INCREMENTAL_REVIEW"
)

func AbortCodeReviewTask(ctx base.Context, sessionId string) error {
	return AbortCodeReviewTaskWithCallbacks(ctx, sessionId, []agentruntime.AbortSessionCallbackFn{CreateCommentForCodeReviewAborted})
}

func AbortCodeReviewTaskWithCallbacks(ctx base.Context, sessionId string, callbacks []agentruntime.AbortSessionCallbackFn) error {
	if err := core.GetCoreService().GetAgentRuntimeService().AbortSession(ctx, sessionId, &agentruntime.AbortSessionOptions{
		MatchAgentName: "CodeReviewAgent",
		MatchTaskParametersAnyValue: map[string][]any{
			"event": {CodeReview, IncrementalReview},
		},
		CallbackOnSuccessFns: callbacks,
	}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func CreateCommentForCodeReviewAborted(ctx base.Context, sessionId string) error {
	ctx.GetLogger().Info("CodeReview task is abort, create comment to notify user")
	// MR关闭的场景下，由程序直接响应
	replyContent := config.GetOrDefault(config.KeyCodeReviewAbortMessage, "CodeReview流程已终止")
	if _, err := core.GetCoreService().GetCodePlatformService().CreateMergeRequestComment(ctx, sessionId, &codeplatform.CreateMergeRequestCommentOpts{
		Content: replyContent,
	}); err != nil {
		ctx.GetLogger().Error("CreateMergeRequestComment failed", "err", err)
		return errors.WithStack(err)
	}
	return nil
}
