package common

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

// GetLastCodeReviewCommitId 获取最近一次的CodeReview时的CommitId，如果找不到则返回空字符串
func GetLastCodeReviewCommitId(ctx base.Context, sessionId string) (string, error) {
	tasks, err := core.GetCoreService().GetAgentService().ListAgentTasks(ctx, &agent.ListAgentTaskOptions{
		States:    []agent.TaskState{agent.TaskStateTriggered},
		SessionId: sessionId,
		WithTaskParameterValues: map[string][]any{
			"event": {"MERGE_REQUEST", "MERGE_REQUEST_INCREMENTAL_REVIEW"},
		},
		PagesParams: page.PagesParams{PageNumber: 1, PageSize: 1},
		WithIdDesc:  true,
	})
	if err != nil {
		return "", err
	}
	if len(tasks.Items) == 0 {
		return "", nil
	}
	return tasks.Items[0].TaskConfig.GetCodeReviewHeadCommitId(), nil
}
