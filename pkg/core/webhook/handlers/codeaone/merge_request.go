package codeaone

import (
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerror "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/common"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
	"net/url"
)

const (
	actionReview = "review"
	actionAbort  = "abort"
)

func CodeAoneMergeRequestHandler(ctx base.Context, idInfo *identity.IdentityInfo, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.CodeaoneMergeRequestWebhookPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	action := actionReview
	switch payload.ObjectAttributes.Action {
	case "open", "reopen":
		action = actionReview
	case "close", "merge":
		action = actionAbort
	default:
		ctx.GetLogger().Info("ignore action", "action", action)
		return nil
	}
	agentInfo, err := core.GetCoreService().GetAgentService().GetAgent(ctx, idInfo.AgentId)
	if err != nil {
		return errors.WithStack(err)
	}
	sessionInfo, err := core.GetCoreService().GetSessionService().CreateOrLoadSession(ctx, &session.CreateSessionRequest{
		IdentityId: idInfo.IdentityId,
		Property: &session.Property{
			Type: session.CodeaoneCodeReview,
			CodeAoneCodeReview: &session.CodeAoneCodeReviewProperty{
				MergeRequestId: int64(payload.ObjectAttributes.Id),
				ProjectId:      int64(payload.ObjectAttributes.TargetProjectId),
			},
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	repoInfo, err := core.GetCoreService().GetRepositoryService().EnsureRepository(ctx, identity.SourceCodeaone, payload.Repository.Name, payload.Repository.Homepage)
	if err != nil {
		return errors.WithStack(err)
	}
	saveMergeRequestInfo(ctx, agentInfo, sessionInfo, payload, repoInfo)
	err = doHandleMergeRequest(ctx, idInfo, payload, sessionInfo, action)
	if err != nil {
		ctx.GetLogger().Error("fail to handle merge request", "err", err)
		return err
	}
	return nil
}

func doHandleMergeRequest(ctx base.Context, idInfo *identity.IdentityInfo, payload *webhook.CodeaoneMergeRequestWebhookPayload, sessionInfo *session.Info, action string) error {
	// payload.MergeRequest.Target.HttpUrl中的域名替换为idInfo.PlatformEndpoint
	projectUrl, err := url.Parse(payload.ObjectAttributes.Target.HttpUrl)
	if err != nil {
		return errors.WithStack(err)
	}
	platformEndpoint, err := url.Parse(idInfo.PlatformEndpoint)
	if err != nil {
		return errors.WithStack(err)
	}
	projectUrl.Host = platformEndpoint.Host
	params := map[string]any{
		"event":           "MERGE_REQUEST",
		"project_url":     projectUrl.String(),
		"bot_name":        idInfo.PlatformUsername,
		"platform_type":   identity.SourceCodeaone,
		"project_web_url": payload.ObjectAttributes.Source.WebUrl, // 这个地址是web地址（不带.git后缀），非可以clone的地址。用于评论内的链接跳转使用
	}

	switch action {
	case actionReview:
		ctx.GetLogger().Info("Receive CodeReview Task", "basicParam", params)
		agentRule, err := core.GetCoreService().GetSessionService().GetCodeReviewProperty(ctx, sessionInfo.SessionId)
		if err != nil {
			return errors.WithStack(err)
		}
		if !agentRule.ReviewRules.Enable {
			ctx.GetLogger().Info("review disabled by repository rules")
			_ = core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Skipped)})
			return nil
		}
		rule := agentRule.ReviewRules.CodeReviewProperty
		if pass, err := core.GetCoreService().GetSessionService().ProcessCodeReviewEligibility(ctx, ptr.To(sessionInfo.SessionId), nil, rule); err != nil {
			commonerror.AlertError(ctx, commonerror.AlertScopeCodeReview, []string{sessionInfo.SessionId}, "Check CodeReview Eligibility Failed", err)
			// 检查失败就放过
		} else if !pass {
			ctx.GetLogger().Info("Skip CodeReview task due to session rules", "sessionId", sessionInfo.SessionId)
			return nil
		}
		for k, v := range rule.ToCodeReviewTaskParameters() {
			params[k] = v
		}
		cfg := &agentruntime.TaskConfig{
			Parameters: params,
		}
		ctx.GetLogger().Info("Trigger CodeReview Task", "taskConfig", cfg)
		if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, sessionInfo.SessionId, cfg); err != nil {
			return errors.WithStack(err)
		}
	case actionAbort:
		ctx.GetLogger().Info("Abort CodeReview Task")
		backgroundCtx := base.NewBackgroundContext(ctx)
		go util.SafeGoroutineFunc(backgroundCtx, func() {
			if err := core.GetCoreService().GetSessionService().ScanMergeRequestMetrics(backgroundCtx, sessionInfo.SessionId, true); err != nil {
				commonerror.AlertWarning(ctx, commonerror.AlertScopeCodeReview, []string{sessionInfo.SessionId}, "Scan CodeReview Metrics Failed", err)
			}
		})
		if err := common.AbortCodeReviewTask(ctx, sessionInfo.SessionId); err != nil {
			return errors.WithStack(err)
		}
		if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Finished)}); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

// saveMergeRequestInfo 保存 merge request 信息同时更新 会话元数据
func saveMergeRequestInfo(ctx base.Context, agentInfo *agent.AgentInfo, sessionInfo *session.Info, payload *webhook.CodeaoneMergeRequestWebhookPayload, repoInfo *repository.RepositoryInfo) {
	info := &session.MergeRequestProperty{
		Source:          identity.SourceGitLab,
		ProjectId:       cast.ToString(payload.ObjectAttributes.TargetProjectId),
		MergeRequestId:  cast.ToString(payload.ObjectAttributes.Id),
		Title:           cast.ToString(payload.ObjectAttributes.Title),
		State:           cast.ToString(payload.ObjectAttributes.State),
		Description:     cast.ToString(payload.ObjectAttributes.Description),
		SourceProjectId: cast.ToString(payload.ObjectAttributes.SourceProjectId),
		TargetProjectId: cast.ToString(payload.ObjectAttributes.TargetProjectId),
		SourceBranch:    payload.ObjectAttributes.SourceBranch,
		SourceRef:       payload.ObjectAttributes.SourceBranch,
		TargetBranch:    payload.ObjectAttributes.TargetBranch,
		Author:          payload.User.Username,
		Url:             payload.ObjectAttributes.URL,
		CreateTimestamp: 0,
		UpdateTimestamp: 0,
	}

	if t, err := util.ParseDateTimeWithZone(payload.ObjectAttributes.CreatedAt); err == nil {
		info.CreateTimestamp = t.UnixMilli()
	}
	if t, err := util.ParseDateTimeWithZone(payload.ObjectAttributes.UpdatedAt); err == nil {
		info.UpdateTimestamp = t.UnixMilli()
	}

	if err := core.GetCoreService().GetSessionService().RecordMergeRequestInfo(ctx, sessionInfo.SessionId, repoInfo.RepositoryId, info); err != nil {
		commonerror.AlertWarning(ctx, commonerror.AlertScopeCodeReview, []string{sessionInfo.SessionId, "codeaone", payload.Repository.Name}, "Record MergeRequest Info Failed", err)
	}

	sessionMetadata := &session.Metadata{
		AgentId:      agentInfo.AgentId,
		AgentName:    agentInfo.AgentName,
		Author:       info.Author,
		RepositoryId: repoInfo.RepositoryId,
		SessionName:  payload.ObjectAttributes.Title,
	}
	if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{
		Metadata: sessionMetadata,
	}); err != nil {
		commonerror.AlertWarning(ctx, commonerror.AlertScopeCodeReview, []string{sessionInfo.SessionId, "codeaone", payload.Repository.Name}, "Update Session Metadata Failed", err)
	}
}
