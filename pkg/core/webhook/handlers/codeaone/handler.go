package codeaone

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
)

type CodeaoneWebhookHandler func(ctx base.Context, idInfo *identity.IdentityInfo, payload map[string]any) error

func GetCodeaoneWebhookHandler(event webhook.CodeaoneEvent) CodeaoneWebhookHandler {
	switch event {
	case webhook.CodeaoneMergeRequestEvent:
		return CodeAoneMergeRequestHandler
	case webhook.CodeaoneCommentEvent:
		return CodeAoneCommentHandler
	default:
		return nil
	}
}
