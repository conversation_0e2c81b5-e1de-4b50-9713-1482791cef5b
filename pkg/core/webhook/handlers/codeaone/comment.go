package codeaone

import (
	"fmt"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"net/url"
	"strings"
)

func CodeAoneCommentHandler(ctx base.Context, idInfo *identity.IdentityInfo, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.CodeaoneCommentWebhookPayload](ctx, data)
	if err != nil {
		ctx.GetLogger().Error("failed to convert map to CodeaoneCommentWebhookPayload", "err", err)
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	userName := payload.User.Username
	if userName == idInfo.PlatformUsername {
		return nil
	}
	note := payload.ObjectAttributes.Note
	commentId, err := cast.ToStringE(payload.ObjectAttributes.Id)
	if err != nil {
		ctx.GetLogger().Error("failed to get note id", "err", err)
		return errors.WithStack(err)
	}
	rootCommentId, err := cast.ToStringE(payload.ObjectAttributes.ParentNoteId)
	if err != nil {
		ctx.GetLogger().Error("failed to get parent note id", "err", err)
		return errors.WithStack(err)
	}
	sessionInfo, err := ensureSession(ctx, idInfo, int64(payload.MergeRequest.Id), int64(payload.ProjectId))
	if err != nil {
		return errors.WithStack(err)
	}
	doReply := false
	if botMentioned(note, idInfo.PlatformUsername) {
		doReply = true
	} else {
		ok, err := shouldProcessComment(ctx, idInfo.PlatformUsername, sessionInfo.SessionId, commentId, rootCommentId)
		if err != nil {
			ctx.GetLogger().Error("shouldProcessComment failed", "sessionId", sessionInfo.SessionId, "commentId", payload.ObjectAttributes.Id, "err", err)
			return errors.WithStack(err)
		}
		doReply = ok
	}
	if !doReply {
		ctx.GetLogger().Info("ignore comment", "comment", note)
		return nil
	}

	// 如果是回复评论，则将根评论的id作为commentId
	if payload.ObjectAttributes.ParentNoteId != 0 {
		commentId = rootCommentId
	}

	if payload.MergeRequestClosed() {
		ctx.GetLogger().Info("Reply comment by Controller due to MergeRequest state is closed", "commentId", commentId)
		// MR关闭的场景下，由程序直接响应
		replyContent := config.GetOrDefault(config.KeyCodeReviewCommentReplyContent, "当前合并请求已关闭，如需请重新打开合并请求。")
		commentOpt := &codeplatform.CreateMergeRequestCommentOpts{
			Content:          replyContent,
			ReplyToCommentID: &commentId,
		}
		if _, err := core.GetCoreService().GetCodePlatformService().CreateMergeRequestComment(ctx, sessionInfo.SessionId, commentOpt); err != nil {
			ctx.GetLogger().Error("CreateMergeRequestComment failed", "err", err)
			return errors.WithStack(err)
		}
		return nil
	}
	// payload.MergeRequest.Target.HttpUrl中的域名替换为idInfo.PlatformEndpoint
	projectUrl, err := url.Parse(payload.MergeRequest.Target.HttpUrl)
	if err != nil {
		return errors.WithStack(err)
	}
	platformEndpoint, err := url.Parse(idInfo.PlatformEndpoint)
	if err != nil {
		return errors.WithStack(err)
	}
	projectUrl.Host = platformEndpoint.Host

	params := map[string]any{
		"event":         "MERGE_REQUEST_COMMENT",
		"project_url":   projectUrl.String(),
		"comment_id":    commentId,
		"bot_name":      idInfo.PlatformUsername,
		"platform_type": identity.SourceCodeaone,
	}
	cfg := &agentruntime.TaskConfig{
		Parameters: params,
	}
	ctx.GetLogger().Info("Trigger Comment Reply", "taskConfig", cfg)
	if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, sessionInfo.SessionId, cfg); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func botMentioned(comment string, botName string) bool {
	return strings.Contains(comment, fmt.Sprintf("@%s", botName))
}

// shouldProcessComment 判断评论是否需要回复，要求条件，当前评论非Bot创建，根评论是Bot创建
func shouldProcessComment(ctx base.Context, botName string, sessionId string, commentId string, rootCommentId string) (bool, error) {
	comments, err := core.GetCoreService().GetCodePlatformService().ListMergeRequestComments(ctx, sessionId, &codeplatform.ListMergeRequestCommentsRequest{CommentId: commentId})
	if err != nil {
		return false, errors.WithStack(err)
	}
	if len(comments.Comments) == 0 {
		return false, nil
	}
	comment := comments.Comments[0]
	if comment.Username == botName {
		return false, nil
	}

	rootComments, err := core.GetCoreService().GetCodePlatformService().ListMergeRequestComments(ctx, sessionId, &codeplatform.ListMergeRequestCommentsRequest{CommentId: rootCommentId})
	if err != nil {
		return false, errors.WithStack(err)
	}
	if len(rootComments.Comments) == 0 {
		return false, nil
	}
	rootComment := rootComments.Comments[0]
	if rootComment.Username != botName {
		return false, nil
	}

	return true, nil
}

func ensureSession(ctx base.Context, idInfo *identity.IdentityInfo, mergeRequestId int64, projectId int64) (*session.Info, error) {
	sessionInfo, err := core.GetCoreService().GetSessionService().CreateOrLoadSession(ctx, &session.CreateSessionRequest{
		IdentityId: idInfo.IdentityId,
		Property: &session.Property{
			Type: session.CodeaoneCodeReview,
			CodeAoneCodeReview: &session.CodeAoneCodeReviewProperty{
				MergeRequestId: mergeRequestId,
				ProjectId:      projectId,
			},
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return sessionInfo, nil
}
