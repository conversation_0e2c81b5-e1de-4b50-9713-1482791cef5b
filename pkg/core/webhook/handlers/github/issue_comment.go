package github

import (
	"fmt"
	"github.com/pkg/errors"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	codeplatform2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/codeplatform/provider"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"strings"
)

func issueCommentHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.GithubIssueCommentPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	switch payload.Action {
	case "created":
	default:
		ctx.GetLogger().Info("issue comment event ignored", "action", payload.Action)
		return nil
	}
	// find identity
	installationId := payload.Installation.Id
	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId)
	if err != nil {
		return errors.WithStack(err)
	}
	if payload.Comment.User.Login == fmt.Sprintf("%s[bot]", idInfo.PlatformUsername) {
		ctx.GetLogger().Info("bot comment, ignore it", "owner", payload.Repository.Owner.Login, "repo", payload.Repository.Name, "number", payload.Issue.Number)
		return nil
	}
	// check if mentioned
	comment := payload.Comment.Body
	if !strings.Contains(comment, fmt.Sprintf("@%s", idInfo.PlatformUsername)) {
		ctx.GetLogger().Info("bot not mentioned, ignore it", "comment", comment)
		return nil
	}
	// find session
	sessionsResp, err := core.GetCoreService().GetSessionService().ListSessions(ctx, &session.ListSessionsRequest{
		GithubOwner:             payload.Repository.Owner.Login,
		GithubPullRequestNumber: payload.Issue.Number,
		GithubRepo:              payload.Repository.Name,
		PagesParams:             page.PagesParams{PageNumber: 1, PageSize: 1},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(sessionsResp.Items) == 0 {
		ctx.GetLogger().Info("no session found, ignore it", "owner", payload.Repository.Owner.Login, "repo", payload.Repository.Name, "number", payload.Issue.Number)
		return nil
	}
	s := sessionsResp.Items[0]

	commentIdStr, err := provider.GenerateGithubCommentIdStr(ctx, payload.Comment.Id, codeplatform2.CommentTypeCommon)
	if err != nil {
		return errors.WithStack(err)
	}
	params := map[string]any{
		"event":       "MERGE_REQUEST_COMMENT",
		"project_url": payload.Repository.CloneUrl,
		// fixme issue comment 没有回复能力
		"comment_id":      commentIdStr,
		"bot_name":        idInfo.PlatformUsername,
		"platform_type":   identity.SourceGithub,
		"project_web_url": payload.Repository.HtmlUrl, // 这个地址是web地址（不带.git后缀），非可以clone的地址。用于评论内的链接跳转使用
	}
	cfg := &agentruntime.TaskConfig{
		Parameters: params,
	}
	ctx.GetLogger().Info("Trigger CodeReviewAgent for github issue comment", "taskConfig", cfg)
	if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, s.SessionId, cfg); err != nil {
		return errors.WithStack(err)
	}
	return nil
}
