package github

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
)

type GithubWebhookHandler func(ctx base.Context, payload map[string]any) error

func GetGithubWebhookHandler(event webhook.GithubEvent) GithubWebhookHandler {
	switch event {
	case webhook.GithubPingEvent:
		return pingHandler
	case webhook.GithubInstallationEvent:
		return installationHandler
	case webhook.GithubPullRequestEvent:
		return pullRequestHandler
	case webhook.GithubIssueCommentEvent:
		return issueCommentHandler
	case webhook.GithubPullRequestReviewCommentEvent:
		return pullRequestReviewCommentHandler
	default:
		return nil
	}
}
