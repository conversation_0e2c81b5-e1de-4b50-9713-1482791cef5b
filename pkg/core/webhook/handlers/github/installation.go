package github

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/github"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
)

func installationHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.GithubInstallationPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	appId := payload.Installation.AppId
	action := payload.Action

	// 检查是否为传统GitHub App配置
	appConfig := config.GetGithubAppConfig(appId)
	isTraditionalApp := appConfig != ""

	// 检查是否为OIDC专用GitHub App配置
	oidcAppConfig := config.GetGithubOIDCAppConfig(appId)
	isOIDCApp := oidcAppConfig != ""

	// 必须是已配置的GitHub App（传统或OIDC）
	if !isTraditionalApp && !isOIDCApp {
		ctx.GetLogger().Error("GitHub App not configured", "appId", appId)
		return commonerrors.New(codes.ErrInvalidParameter, "appId", appId)
	}

	// 记录App类型用于日志
	appType := "traditional"
	if isOIDCApp {
		appType = "oidc"
	}
	ctx.GetLogger().Info("processing GitHub App installation", "appId", appId, "appType", appType, "action", action)
	// fixme 应该调用github API来验证installation_id和account_id数据是正确的，防止伪造的请求
	installationId := payload.Installation.Id
	accountId := payload.Installation.Account.Id
	username := payload.Installation.Account.Login
	accountType := payload.Installation.Account.Type
	userId, err := core.GetCoreService().GetCallbackService().GetUserIdByGithubInstallationId(ctx, installationId)
	if err != nil && !commonerrors.Is(err, codes.ErrRecordNotFound) {
		return errors.WithStack(err)
	}
	if userId != nil {
		ctx = base.NewContextWithUserOverride(ctx, *userId, "")
	}
	switch action {
	case "created":
		if _, err = core.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId); err != nil {
			if !commonerrors.Is(err, codes.ErrRecordNotFound) {
				return errors.WithStack(err)
			}

			// 对于OIDC专用App，采用不同的处理策略
			if isOIDCApp {
				ctx.GetLogger().Info("OIDC GitHub App installation created",
					"appId", appId, "installationId", installationId, "username", username)

				// 创建OIDC安装记录
				req := &github.CreateOIDCInstallationRequest{
					AppId:          appId,
					InstallationId: installationId,
					AccountId:      accountId,
					AccountLogin:   username,
					AccountType:    accountType,
					AppSlug:        payload.Installation.AppSlug,
				}

				// 如果payload包含仓库和权限信息，也一并记录
				if payload.Repositories != nil {
					repositories := make(github.RepositoryList, len(payload.Repositories))
					for i, repo := range payload.Repositories {
						repositories[i] = github.RepositoryInfo{
							ID:       repo.Id,
							Name:     repo.Name,
							FullName: repo.FullName,
							Private:  repo.Private,
						}
						if repo.Owner != nil {
							repositories[i].Owner.Login = repo.Owner.Login
							repositories[i].Owner.ID = repo.Owner.Id
							repositories[i].Owner.Type = repo.Owner.Type
						}
					}
					req.Repositories = &repositories
				}

				if _, err := dao.CreateOIDCInstallation(ctx, req); err != nil {
					ctx.GetLogger().Error("failed to create OIDC installation record", "err", err)
					return errors.WithStack(err)
				}

				ctx.GetLogger().Info("OIDC App installation recorded successfully")

			} else {
				// 传统GitHub App的处理逻辑保持不变
				ctx.GetLogger().Info("traditional GitHub App installation, creating agent and identity")

				// create agent
				agentInfo, err := core.GetCoreService().GetAgentService().CreateAgent(ctx, &agent.CreateAgentRequest{AgentName: agent.CodeReviewAgent})
				if err != nil {
					return errors.WithStack(err)
				}
				// create identity
				req := &identity.CreateIdentityRequest{
					AgentId:          agentInfo.AgentId,
					PlatformEndpoint: "https://api.github.com",
					PlatformProperty: &identity.PlatformProperty{
						GithubAppId:          appId,
						GithubInstallationId: installationId,
						GithubUsername:       username,
						GithubUserId:         accountId,
						GithubAppSlug:        payload.Installation.AppSlug,
						GithubAccountType:    accountType,
					},
					PlatformToken: "",
					Source:        identity.SourceGithub,
				}
				if _, err := core.GetCoreService().GetIdentityService().CreateIdentity(ctx, req); err != nil {
					return errors.WithStack(err)
				}
			}
		} else {
			// installation_id 已经存在，理论上需要校验一下数据对不对，或者更新。 现在先do nothing
			ctx.GetLogger().Info("installation already exists", "installation_id", installationId, "account_id", accountId, "username", username, "appType", appType)
		}
	case "deleted":
		if isOIDCApp {
			ctx.GetLogger().Info("OIDC GitHub App installation deleted",
				"appId", appId, "installationId", installationId, "username", username)

			// 软删除OIDC安装记录
			if err := dao.DeleteOIDCInstallation(ctx, installationId); err != nil {
				ctx.GetLogger().Error("failed to delete OIDC installation record", "err", err)
				return errors.WithStack(err)
			}

			ctx.GetLogger().Info("OIDC App installation record deleted successfully")
		} else {
			// 传统GitHub App的删除逻辑保持不变
			ctx.GetLogger().Info("traditional GitHub App installation deleted, cleaning up agent and identity")

			idInfo, err := core.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId)
			if err != nil && !commonerrors.Is(err, codes.ErrRecordNotFound) {
				return errors.WithStack(err)
			}
			if idInfo != nil {
				ctx.GetLogger().Info("delete agent and identity", "identityId", idInfo.IdentityId, "agentId", idInfo.AgentId)
				if err = core.GetCoreService().GetAgentService().DeleteAgent(ctx, idInfo.AgentId); err != nil {
					return errors.WithStack(err)
				}
				if err = core.GetCoreService().GetIdentityService().DeleteIdentity(ctx, idInfo.IdentityId); err != nil {
					return errors.WithStack(err)
				}
			}
		}

		// 清理安装绑定关系（对所有类型的App都适用）
		if err := core.GetCoreService().GetCallbackService().DeleteInstallationBindingIfExists(ctx, installationId); err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}
