package github

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
)

func installationHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.GithubInstallationPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	appId := payload.Installation.AppId
	appConfig := config.GetGithubAppConfig(appId)
	action := payload.Action
	if appConfig == "" {
		return commonerrors.New(codes.ErrInvalidParameter, "appId", appId)
	}
	// fixme 应该调用github API来验证installation_id和account_id数据是正确的，防止伪造的请求
	installationId := payload.Installation.Id
	accountId := payload.Installation.Account.Id
	username := payload.Installation.Account.Login
	accountType := payload.Installation.Account.Type
	userId, err := core.GetCoreService().GetCallbackService().GetUserIdByGithubInstallationId(ctx, installationId)
	if err != nil && !commonerrors.Is(err, codes.ErrRecordNotFound) {
		return errors.WithStack(err)
	}
	if userId != nil {
		ctx = base.NewContextWithUserOverride(ctx, *userId, "")
	}
	switch action {
	case "created":
		if _, err = core.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId); err != nil {
			if !commonerrors.Is(err, codes.ErrRecordNotFound) {
				return errors.WithStack(err)
			}
			// todo 当前需要先为这个identity创建一个agent，在后续设计中先不创建agent，在setupURL中找到installationId和user_id关联关系。 每个user_id应该有默认的Agent。 此时做关联。
			// create agent
			agentInfo, err := core.GetCoreService().GetAgentService().CreateAgent(ctx, &agent.CreateAgentRequest{AgentName: agent.CodeReviewAgent})
			if err != nil {
				return errors.WithStack(err)
			}
			// create identity
			req := &identity.CreateIdentityRequest{
				AgentId:          agentInfo.AgentId,
				PlatformEndpoint: "https://api.github.com",
				PlatformProperty: &identity.PlatformProperty{
					GithubAppId:          appId,
					GithubInstallationId: installationId,
					GithubUsername:       username,
					GithubUserId:         accountId,
					GithubAppSlug:        payload.Installation.AppSlug,
					GithubAccountType:    accountType,
				},
				PlatformToken: "",
				Source:        identity.SourceGithub,
			}
			if _, err := core.GetCoreService().GetIdentityService().CreateIdentity(ctx, req); err != nil {
				return errors.WithStack(err)
			}
		} else {
			// installation_id 已经存在，理论上需要校验一下数据对不对，或者更新。 现在先do nothing
			ctx.GetLogger().Info("installation already exists", "installation_id", installationId, "account_id", accountId, "username", username)
		}
	case "deleted":
		idInfo, err := core.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId)
		if err != nil && !commonerrors.Is(err, codes.ErrRecordNotFound) {
			return errors.WithStack(err)
		}
		if idInfo != nil {
			ctx.GetLogger().Info("delete agent and identity", "identityId", idInfo.IdentityId, "agentId", idInfo.AgentId)
			if err = core.GetCoreService().GetAgentService().DeleteAgent(ctx, idInfo.AgentId); err != nil {
				return errors.WithStack(err)
			}
			if err = core.GetCoreService().GetIdentityService().DeleteIdentity(ctx, idInfo.IdentityId); err != nil {
				return errors.WithStack(err)
			}
		}
		if err := core.GetCoreService().GetCallbackService().DeleteInstallationBindingIfExists(ctx, installationId); err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}
