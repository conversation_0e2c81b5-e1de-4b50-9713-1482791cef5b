package github

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
)

func pingHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.GithubPingPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	appId := payload.Hook.AppId
	appConfig := config.GetGithubAppConfig(appId)
	if appConfig == "" {
		return commonerrors.New(codes.ErrInvalidParameter, "appId", appId)
	}
	return nil
}
