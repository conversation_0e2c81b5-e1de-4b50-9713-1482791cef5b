package github

import (
	"fmt"
	githubsdk "github.com/google/go-github/v69/github"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	extgithub "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/github"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	platformprovider "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/codeplatform/provider"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/common"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
	"strings"
)

const (
	actionReview = "review"
	actionClose  = "close"
)

func IsValidGithubUser(userId string) bool {
	switch userId {
	case "mock", "Github webhook":
		return false
	default:
		return true
	}
}

func pullRequestHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.GithubPullRequestPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	action := actionReview
	switch strings.ToLower(payload.Action) {
	// github merged与closed事件 action都是closed，根据payload中merged字段判断是否是merge事件
	case "opened", "reopened":
		action = actionReview
	case "closed":
		action = actionClose
	default:
		ctx.GetLogger().Info("pull request event ignored", "action", payload.Action)
		return nil
	}

	installationId := payload.Installation.Id
	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId)
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("pull request event received", "action", action, "uid", ctx.GetUid(), "idInfo", idInfo)
	if !IsValidGithubUser(ctx.GetUid()) && action == actionReview {
		//没有阿里云身份的github安装事件，评论提示关联身份
		ctx.GetLogger().Warn("Github PR event from installation not bind to aliyun account , send bind account message comment")
		owner := payload.Repository.Owner.Login
		repo := payload.Repository.Name
		number := payload.Number
		githubAppId := idInfo.PlatformProperty.GithubAppId
		clientId := config.GetGithubAppClientId(githubAppId)
		callbackUrl := "https://github.com/login/oauth/authorize"
		callbackUrl = fmt.Sprintf("%s?client_id=%s&state=%d", callbackUrl, clientId, installationId)
		bindAccountMessage := config.GetOrDefault(config.KeyGithubBindAccountMessage, "Github应用安装未绑定到阿里云账号, 未对本次合并请求进行代码评审, 请访问 %s 绑定账号 ")
		replyContent := fmt.Sprintf(bindAccountMessage, callbackUrl)
		comment := &githubsdk.IssueComment{
			Body: &replyContent,
		}
		if _, err := extgithub.CreateIssueComment(ctx, idInfo, owner, repo, number, comment); err != nil {
			ctx.GetLogger().Error("CreateMergeRequestComment failed", "err", err)
			return errors.WithStack(err)
		}
		return nil
	}

	sessionInfo, err := core.GetCoreService().GetSessionService().CreateOrLoadSession(ctx, &session.CreateSessionRequest{
		IdentityId: idInfo.IdentityId,
		Property: &session.Property{
			Type: session.GithubCodeReview,
			GithubCodeReview: &session.GithubCodeReviewProperty{
				Owner:  payload.Repository.Owner.Login,
				Repo:   payload.Repository.Name,
				Number: payload.Number,
			},
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	taskConfig := &agentruntime.TaskConfig{
		Parameters: map[string]any{
			"event":           "MERGE_REQUEST",
			"project_url":     payload.Repository.CloneUrl,
			"bot_name":        idInfo.PlatformUsername,
			"platform_type":   identity.SourceGithub,
			"head_commit_id":  payload.PullRequest.Head.Sha,
			"project_web_url": payload.Repository.HtmlUrl, // 这个地址是web地址（不带.git后缀），非可以clone的地址。用于评论内的链接跳转使用
		},
	}
	switch action {
	case actionReview:
		ctx.GetLogger().Info("Trigger CodeReviewAgent for github pull request", "taskConfig", taskConfig)
		if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, sessionInfo.SessionId, taskConfig); err != nil {
			return errors.WithStack(err)
		}
	case actionClose:
		ctx.GetLogger().Info("Close CodeReviewAgent for github pull request", "taskConfig", taskConfig)
		if err := common.AbortCodeReviewTask(ctx, sessionInfo.SessionId); err != nil {
			return errors.WithStack(err)
		}
		if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Finished)}); err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func pullRequestReviewCommentHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.GithubPullRequestReviewCommentPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	switch payload.Action {
	case "created":
	default:
		ctx.GetLogger().Info("pull request comment event ignored", "action", payload.Action)
		return nil
	}
	// find identity
	installationId := payload.Installation.Id
	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId)
	if err != nil {
		return errors.WithStack(err)
	}
	// check if mentioned
	comment := payload.Comment.Body
	sessionInfo, err := core.GetCoreService().GetSessionService().CreateOrLoadSession(ctx, &session.CreateSessionRequest{
		IdentityId: idInfo.IdentityId,
		Property: &session.Property{
			Type: session.GithubCodeReview,
			GithubCodeReview: &session.GithubCodeReviewProperty{
				Owner:  payload.Repository.Owner.Login,
				Repo:   payload.Repository.Name,
				Number: payload.PullRequest.Number,
			},
		},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("debug comment reply", "payload.Comment.User", payload.Comment.User, "idInfo.PlatformUsername", idInfo.PlatformUsername)
	if payload.Comment.User.Login == fmt.Sprintf("%s[bot]", idInfo.PlatformUsername) {
		ctx.GetLogger().Info("bot comment, ignore it", "owner", payload.Repository.Owner.Login, "repo", payload.Repository.Name, "number", payload.PullRequest.Number)
		return nil
	}
	isAgentDiscussion, err := isAgentDiscussion(ctx, sessionInfo.SessionId, payload.Comment.Id)
	if err != nil {
		return errors.WithStack(err)
	}
	if !strings.Contains(comment, fmt.Sprintf("@%s", idInfo.PlatformUsername)) && !isAgentDiscussion {
		ctx.GetLogger().Info("bot not mentioned, ignore it", "comment", comment)
		return nil
	}
	// find session
	sessionsResp, err := core.GetCoreService().GetSessionService().ListSessions(ctx, &session.ListSessionsRequest{
		GithubOwner:             payload.Repository.Owner.Login,
		GithubPullRequestNumber: payload.PullRequest.Number,
		GithubRepo:              payload.Repository.Name,
		PagesParams:             page.PagesParams{PageNumber: 1, PageSize: 1},
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if len(sessionsResp.Items) == 0 {
		ctx.GetLogger().Info("no session found, ignore it", "owner", payload.Repository.Owner.Login, "repo", payload.Repository.Name, "number", payload.PullRequest.Number)
		return nil
	}
	s := sessionsResp.Items[0]

	commentIdStr, err := util.GenerateGithubCommentIdStr(ctx, payload.Comment.Id, codeplatform.CommentTypeDiscussionNote)
	if err != nil {
		return errors.WithStack(err)
	}
	params := map[string]any{
		"event":           "MERGE_REQUEST_COMMENT",
		"project_url":     payload.Repository.CloneUrl,
		"comment_id":      commentIdStr,
		"bot_name":        idInfo.PlatformUsername,
		"platform_type":   identity.SourceGithub,
		"project_web_url": payload.Repository.HtmlUrl, // 这个地址是web地址（不带.git后缀），非可以clone的地址。用于评论内的链接跳转使用
	}
	cfg := &agentruntime.TaskConfig{
		Parameters: params,
	}
	ctx.GetLogger().Info("Trigger CodeReviewAgent for github issue comment", "taskConfig", cfg)
	if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, s.SessionId, cfg); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func isAgentDiscussion(ctx base.Context, sessionId string, commentId int64) (bool, error) {
	sessionInfo, err := core.GetCoreService().GetSessionService().GetSession(ctx, sessionId)
	if err != nil {
		return false, errors.WithStack(err)
	}
	owner := sessionInfo.Property.GithubCodeReview.Owner
	repo := sessionInfo.Property.GithubCodeReview.Repo
	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentity(ctx, sessionInfo.IdentityId)
	if err != nil {
		return false, errors.WithStack(err)
	}
	topCommentId, err := platformprovider.GetTopCommentId(ctx, &platformprovider.Config{
		Identity: idInfo,
		ProjectConfig: &platformprovider.ProjectConfig{
			GithubProjectConfig: &platformprovider.GithubProjectConfig{
				Owner: owner,
				Repo:  repo,
			},
		},
	}, commentId)
	if err != nil {
		ctx.GetLogger().Error("GetTopCommentId failed", "err", err)
		return false, errors.WithStack(err)
	}
	topCommentIdSInt, err := cast.ToInt64E(topCommentId)
	if err != nil {
		return false, errors.WithStack(err)
	}
	prComment, err := extgithub.GetPRComment(ctx, idInfo, owner, repo, topCommentIdSInt)
	if err != nil {
		return false, errors.WithStack(err)
	}
	ctx.GetLogger().Info("isAgentDiscussion", "commentId", commentId, "topCommentId", topCommentId, "prComment.User", prComment.User, "idInfo.PlatformUsername", idInfo.PlatformUsername)
	//prComment的User的login带[bot]后缀
	botName := fmt.Sprintf("%s[bot]", idInfo.PlatformUsername)
	if prComment.User.GetLogin() == botName {
		return true, nil
	}
	return false, nil
}
