package codeup

import (
	"slices"
	"strconv"

	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	errors2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook/handlers/common"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
)

var interestedActions = []string{"open", "reopen", "close", "merge", "review", "abort"}

func codeupMergeRequestHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.CodeupMergeRequestPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	if !slices.Contains(interestedActions, payload.Data.ObjectAttributes.Action) {
		ctx.GetLogger().Info("ignore action for codeup merge request", "action", payload.Data.ObjectAttributes.Action)
		return nil
	}
	orgId, err := payload.GetOrganizationId()
	if err != nil {
		return errors.WithStack(err)
	}
	// 目前使用全局APP ID
	appId := config.FormatCodeupAppId("")
	idInfo, err := ensureIdentity(ctx, orgId, appId)
	if err != nil {
		return errors.WithStack(err)
	}
	sessionInfo, err := ensureSession(ctx, idInfo, orgId, strconv.Itoa(payload.Data.ObjectAttributes.ProjectId), payload.Data.ObjectAttributes.LocalId)
	if err != nil {
		return errors.WithStack(err)
	}
	agentInfo, err := core.GetCoreService().GetAgentService().GetAgent(ctx, idInfo.AgentId)
	if err != nil {
		return errors.WithStack(err)
	}
	repoInfo, err := core.GetCoreService().GetRepositoryService().EnsureRepository(ctx, identity.SourceCodeup, payload.Data.Repository.Name, payload.Data.Repository.Homepage)
	if err != nil {
		return errors.WithStack(err)
	}
	saveMergeRequestInfo(ctx, agentInfo, sessionInfo, payload, repoInfo)

	switch payload.Data.ObjectAttributes.Action {
	case "open", "review":
		return doCodeReview(ctx, sessionInfo, idInfo, payload)
	case "reopen":
		// 按云效侧要求，不自动进行代码评审，由他们控制
		return core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Idle)})
	case "close", "merge":
		return abortCodeReviewToFinished(ctx, sessionInfo, payload)
	case "abort":
		return abortCodeReviewByManual(ctx, sessionInfo, payload)
	}
	return nil
}

func doCodeReview(ctx base.Context, sessionInfo *session.Info, idInfo *identity.IdentityInfo, payload *webhook.CodeupMergeRequestPayload) error {
	params := map[string]any{
		"event":           common.CodeReview,
		"project_url":     payload.Data.Repository.GitHttpUrl,
		"bot_name":        idInfo.PlatformUsername,
		"platform_type":   identity.SourceCodeup,
		"head_commit_id":  payload.Data.ObjectAttributes.LastCommit.Id,
		"project_web_url": payload.Data.Repository.Homepage,
	}
	if lastCommitId, err := common.GetLastCodeReviewCommitId(ctx, sessionInfo.SessionId); err != nil {
		ctx.GetLogger().Error("get last code review commit id failed", "error", err)
		return errors.WithStack(err)
	} else if lastCommitId != "" {
		params["event"] = common.IncrementalReview
		params["last_review_commit_sha"] = lastCommitId
		params["head_review_commit_sha"] = params["head_commit_id"]
	}

	if rule, err := payload.LoadCodeReviewProperty(ctx); err != nil {
		ctx.GetLogger().Error("load code review property failed", "error", err)
		return errors.WithStack(err)
	} else {
		if pass, err := core.GetCoreService().GetSessionService().ProcessCodeReviewEligibility(ctx, ptr.To(sessionInfo.SessionId), nil, rule); err != nil {
			errors2.AlertError(ctx, errors2.AlertScopeCodeReview, []string{sessionInfo.SessionId}, "Check CodeReview Eligibility Failed", err)
			// 检查失败就放过
		} else if !pass {
			ctx.GetLogger().Info("Skip CodeReview task due to session rules", "sessionId", sessionInfo.SessionId)
			return nil
		}
		for k, v := range rule.ToCodeReviewTaskParameters() {
			params[k] = v
		}
	}
	cfg := &agentruntime.TaskConfig{
		Parameters: params,
	}
	ctx.GetLogger().Info("Trigger CodeReview task for codeup", "taskConfig", cfg)
	return core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, sessionInfo.SessionId, cfg)
}

func abortCodeReviewToFinished(ctx base.Context, sessionInfo *session.Info, payload *webhook.CodeupMergeRequestPayload) error {
	ctx.GetLogger().Info("abort CodeReview task for codeup", "sessionId", sessionInfo.SessionId)
	// todo 支持codeup 指标扫描
	//backgroundCtx := base.NewBackgroundContext(ctx)
	//go util.SafeGoroutineFunc(backgroundCtx, func() {
	//	if err := core.GetCoreService().GetSessionService().ScanMergeRequestMetrics(backgroundCtx, sessionInfo.SessionId, true); err != nil {
	//		commonerrors.AlertWarning(ctx, commonerrors.AlertScopeCodeReview, []string{sessionInfo.SessionId}, "Scan CodeReview Metrics Failed", err)
	//	}
	//})
	if err := common.AbortCodeReviewTask(ctx, sessionInfo.SessionId); err != nil {
		return errors.WithStack(err)
	}
	if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Finished)}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func abortCodeReviewByManual(ctx base.Context, sessionInfo *session.Info, payload *webhook.CodeupMergeRequestPayload) error {
	ctx.GetLogger().Info("abort CodeReview task for codeup by manual", "sessionId", sessionInfo.SessionId)
	if err := common.AbortCodeReviewTaskWithCallbacks(ctx, sessionInfo.SessionId, nil); err != nil {
		return errors.WithStack(err)
	}
	if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Idle)}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// saveMergeRequestInfo 保存 merge request 信息同时更新 会话元数据
func saveMergeRequestInfo(ctx base.Context, agentInfo *agent.AgentInfo, sessionInfo *session.Info, payload *webhook.CodeupMergeRequestPayload, repoInfo *repository.RepositoryInfo) {
	info := &session.MergeRequestProperty{
		Source:          identity.SourceGitLab,
		ProjectId:       cast.ToString(payload.Data.ObjectAttributes.TargetProjectId),
		MergeRequestId:  cast.ToString(payload.Data.ObjectAttributes.LocalId),
		Title:           cast.ToString(payload.Data.ObjectAttributes.Title),
		State:           cast.ToString(payload.Data.ObjectAttributes.State),
		Description:     cast.ToString(payload.Data.ObjectAttributes.Description),
		SourceProjectId: cast.ToString(payload.Data.ObjectAttributes.SourceProjectId),
		TargetProjectId: cast.ToString(payload.Data.ObjectAttributes.TargetProjectId),
		SourceBranch:    payload.Data.ObjectAttributes.SourceBranch,
		SourceRef:       payload.Data.ObjectAttributes.SourceCommitId,
		TargetBranch:    payload.Data.ObjectAttributes.TargetBranch,
		Author:          payload.Data.User.Name,
		Url:             payload.Data.ObjectAttributes.Url,
		CreateTimestamp: payload.Data.ObjectAttributes.CreatedAt,
		UpdateTimestamp: payload.Data.ObjectAttributes.UpdatedAt,
	}

	if err := core.GetCoreService().GetSessionService().RecordMergeRequestInfo(ctx, sessionInfo.SessionId, repoInfo.RepositoryId, info); err != nil {
		errors2.AlertWarning(ctx, errors2.AlertScopeCodeReview, []string{sessionInfo.SessionId, "codeup", payload.Data.Repository.Name}, "Record MergeRequest Info Failed", err)
	}

	sessionMetadata := &session.Metadata{
		AgentId:      agentInfo.AgentId,
		AgentName:    agentInfo.AgentName,
		Author:       info.Author,
		RepositoryId: repoInfo.RepositoryId,
		SessionName:  payload.Data.ObjectAttributes.Title,
	}
	if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, sessionInfo.SessionId, &session.UpdateSessionRequest{
		Metadata: sessionMetadata,
	}); err != nil {
		errors2.AlertWarning(ctx, errors2.AlertScopeCodeReview, []string{sessionInfo.SessionId, "codeup", payload.Data.Repository.Name}, "Update Session Metadata Failed", err)
	}
}
