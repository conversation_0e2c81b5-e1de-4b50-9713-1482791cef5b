package codeup

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
)

func codeupMergeCommentHandler(ctx base.Context, data map[string]any) error {
	payload, err := util.ConvertMapTo[webhook.CodeupCommentPayload](ctx, data)
	if err != nil {
		return errors.WithStack(err)
	}
	if err := payload.Validate(); err != nil {
		return errors.WithStack(err)
	}
	if payload.Event != "codeup.note.create" {
		ctx.GetLogger().Info("ignore comment event", "event", payload.Event)
		return nil
	}
	orgId, err := payload.GetOrganizationId()
	if err != nil {
		return errors.WithStack(err)
	}
	// 目前使用全局APP ID
	appId := config.FormatCodeupAppId("")
	idInfo, err := ensureIdentity(ctx, orgId, appId)
	if err != nil {
		return errors.WithStack(err)
	}
	sessionInfo, err := ensureSession(ctx, idInfo, orgId, strconv.FormatInt(payload.Data.MergeRequest.ProjectId, 10), payload.Data.MergeRequest.LocalId)
	if err != nil {
		return errors.WithStack(err)
	}
	comment := payload.Data.ObjectAttributes.Note
	botName := idInfo.PlatformUsername
	commentId := payload.Data.ObjectAttributes.NoteBizId
	projectUrl := payload.Data.Repository.GitHttpUrl

	doReply := false
	if botMentioned(comment, botName) {
		doReply = true
	} else {
		ok, err := shouldProcessComment(ctx, botName, sessionInfo.SessionId, commentId)
		if err != nil {
			ctx.GetLogger().Error("shouldProcessComment failed", "sessionId", sessionInfo.SessionId, "commentId", payload.Data.ObjectAttributes.NoteBizId, "err", err)
			return errors.WithStack(err)
		}
		doReply = ok
	}

	if !doReply {
		ctx.GetLogger().Info("ignore comment", "comment", comment)
		return nil
	}

	if payload.MergeRequestClosed() {
		ctx.GetLogger().Info("Reply comment by Controller due to MergeRequest state is closed", "commentId", commentId)
		// MR关闭的场景下，由程序直接响应
		replyContent := config.GetOrDefault(config.KeyCodeReviewCommentReplyContent, "当前合并请求已关闭，如需请重新打开合并请求。")
		commentOpt := &codeplatform.CreateMergeRequestCommentOpts{
			Content:          replyContent,
			ReplyToCommentID: &commentId,
		}
		if _, err := core.GetCoreService().GetCodePlatformService().CreateMergeRequestComment(ctx, sessionInfo.SessionId, commentOpt); err != nil {
			ctx.GetLogger().Error("CreateMergeRequestComment failed", "err", err)
			return errors.WithStack(err)
		}
		return nil
	}

	params := map[string]any{
		"event":           "MERGE_REQUEST_COMMENT",
		"project_url":     projectUrl,
		"comment_id":      commentId,
		"bot_name":        idInfo.PlatformUsername,
		"platform_type":   identity.SourceCodeup,
		"project_web_url": payload.Data.Repository.Homepage, // 这个地址是web地址（不带.git后缀），非可以clone的地址。用于评论内的链接跳转使用
	}
	cfg := &agentruntime.TaskConfig{
		Parameters: params,
	}
	ctx.GetLogger().Info("Trigger Comment Reply", "taskConfig", cfg)
	if err := core.GetCoreService().GetAgentService().Step(ctx, idInfo.AgentId, sessionInfo.SessionId, cfg); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func botMentioned(comment string, botName string) bool {
	return strings.Contains(comment, fmt.Sprintf("@%s", botName))
}

// shouldProcessComment 判断评论是否需要回复，要求条件，当前评论非Bot创建，根评论是Bot创建
func shouldProcessComment(ctx base.Context, botName string, sessionId string, commentId string) (bool, error) {
	comments, err := core.GetCoreService().GetCodePlatformService().ListMergeRequestComments(ctx, sessionId, &codeplatform.ListMergeRequestCommentsRequest{CommentId: commentId})
	if err != nil {
		return false, errors.WithStack(err)
	}
	if len(comments.Comments) == 0 {
		return false, nil
	}
	comment := comments.Comments[0]
	if comment.Username != botName {
		return false, nil
	}
	for _, c := range comments.Comments {
		if c.ID == commentId && c.Username != botName {
			// 当前评论不是BOT产生的
			return true, nil
		}
	}

	return false, nil
}

func ensureIdentity(ctx base.Context, orgId, appId string) (*identity.IdentityInfo, error) {
	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentityByCodeupOrganizationIdAndAppId(ctx, orgId, appId)
	if err != nil {
		if !commonerrors.Is(err, codes.ErrRecordNotFound) {
			return nil, errors.WithStack(err)
		}
		if v, err := core.GetCoreService().GetIdentityService().CreateIdentity(ctx, &identity.CreateIdentityRequest{
			AgentId:          "",
			Source:           identity.SourceCodeup,
			PlatformEndpoint: config.Get(config.KeyCodeupEndpointPrefix),
			PlatformToken:    "",
			PlatformProperty: &identity.PlatformProperty{
				CodeupOrganizationId: orgId,
				CodeupAppId:          appId,
			},
		}); err != nil {
			return nil, errors.WithStack(err)
		} else {
			idInfo = v
		}
	}
	return idInfo, nil
}

func ensureSession(ctx base.Context, idInfo *identity.IdentityInfo, orgId string, projectId string, localId int) (*session.Info, error) {
	sessionInfo, err := core.GetCoreService().GetSessionService().CreateOrLoadSession(ctx, &session.CreateSessionRequest{
		IdentityId: idInfo.IdentityId,
		Property: &session.Property{
			Type: session.CodeupCodeReview,
			CodeupCodeReview: &session.CodeupCodeReviewProperty{
				OrganizationId: orgId,
				RepositoryId:   projectId,
				LocalId:        localId,
			},
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return sessionInfo, nil
}
