package codeup

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
)

type CodeupWebhookHandler func(ctx base.Context, payload map[string]any) error

func GetCodeupWebhookHandler(event webhook.CodeupEvent) CodeupWebhookHandler {
	switch event {

	case webhook.CodeupMergeRequestEvent:
		return codeupMergeRequestHandler
	case webhook.CodeupCommentEvent:
		return codeupMergeCommentHandler
	default:
		return nil
	}
}
