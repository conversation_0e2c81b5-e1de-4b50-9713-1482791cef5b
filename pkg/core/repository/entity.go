package repository

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
	"time"
)

func withRepoName(name string) dao.DbWhereOption {
	return dao.WithKeyValues("repo_name", name)
}
func withRepoUrl(url string) dao.DbWhereOption {
	return dao.WithKeyValues("repo_url", url)
}

func withSources(sources ...identity.Source) dao.DbWhereOption {
	return dao.WithKeyValues("source", sources...)
}
func withRepoIds(repoIds []string) dao.DbWhereOption {
	return dao.WithKeyValues("repo_id", repoIds...)
}

func withRepoId(repoId string) dao.DbWhereOption {
	return dao.WithKeyValues("repo_id", repoId)
}

type repoEntity struct {
	ID             int64                         `gorm:"column:id;primaryKey"`
	RepoId         string                        `gorm:"column:repo_id"`
	UserId         string                        `gorm:"column:user_id"`
	Source         identity.Source               `gorm:"column:source"`
	RepoUrl        string                        `gorm:"column:repo_url"`
	RepoName       string                        `gorm:"column:repo_name"`
	Setting        *repository.RepositorySetting `gorm:"column:setting;serializer:json"`
	LastReviewTime time.Time                     `gorm:"column:last_review_time"`
	GmtCreate      time.Time                     `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified    time.Time                     `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (re *repoEntity) TableName() string {
	return "t_repository"
}

func (re *repoEntity) ToRepoInfo() *repository.RepositoryInfo {
	return &repository.RepositoryInfo{
		RepositoryId:        re.RepoId,
		Name:                re.RepoName,
		Setting:             re.Setting,
		Source:              re.Source,
		Url:                 re.RepoUrl,
		CreateTimestamp:     re.GmtCreate.UnixMilli(),
		LastReviewTimestamp: re.LastReviewTime.UnixMilli(),
	}
}
