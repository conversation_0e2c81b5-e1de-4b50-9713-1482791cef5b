package repository

import (
	"context"
	errors2 "github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commoncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"log/slog"
	"time"
)

var _ commoncore.RepositoryService = (*repoService)(nil)

type repoService struct {
	core    commoncore.Core
	started chan struct{}
	ready   bool
}

func (s *repoService) ListRepositories(ctx base.Context, options *repository.ListRepositoryOptions) (*page.PagedItems[*repository.RepositoryInfo], error) {
	if err := options.Validate(); err != nil {
		return nil, err
	}
	opt := dao.NewDbOptions(dao.WithUserId(ctx.GetUid()))
	if len(options.Sources) > 0 {
		opt.AddWhere(withSources(options.Sources...))
	}
	if len(options.RepositoryIds) > 0 {
		opt.AddWhere(withRepoIds(options.RepositoryIds))
	}
	if options.NameAndUrlOnly {
		opt.AddWhere(dao.WithSelect("repo_id,repo_name,repo_url"))
	}
	opt.AddPage(dao.WithPage(options.PagesParams.PageNumber, options.PagesParams.PageSize))
	results, total, err := dao.ListEntitiesWithPage(ctx, &repoEntity{}, opt)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	return &page.PagedItems[*repository.RepositoryInfo]{
		Items:      page.TransferSlice(results, func(entity *repoEntity) *repository.RepositoryInfo { return entity.ToRepoInfo() }),
		PageNumber: options.PagesParams.PageNumber,
		PageSize:   options.PagesParams.PageSize,
		TotalSize:  total,
	}, nil
}

func (s *repoService) EnsureRepository(ctx base.Context, source identity.Source, repoName string, repoUrl string) (*repository.RepositoryInfo, error) {
	opt := dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withSources(source), withRepoUrl(repoUrl))

	now := time.Now()
	entity := &repoEntity{
		RepoId:         helper.NewIDWithPrefix("r-"),
		RepoName:       repoName,
		RepoUrl:        repoUrl,
		Source:         source,
		UserId:         ctx.GetUid(),
		Setting:        &repository.RepositorySetting{},
		LastReviewTime: now,
		GmtCreate:      now,
		GmtModified:    now,
	}

	if existEntity, err := dao.GetEntity(ctx, &repoEntity{}, opt); err == nil {
		entity = existEntity
	} else if !errors.Is(err, codes.ErrRecordNotFound) {
		return nil, errors2.WithStack(err)
	}

	entity.LastReviewTime = now

	if err := dao.Save(ctx, entity); err != nil {
		return nil, errors2.WithStack(err)
	}
	return entity.ToRepoInfo(), nil
}

func (s *repoService) GetRepository(ctx base.Context, repoId string) (*repository.RepositoryInfo, error) {
	opt := dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withRepoId(repoId))
	entity, err := dao.GetEntity(ctx, &repoEntity{}, opt)
	if err != nil {
		return nil, err
	}
	return entity.ToRepoInfo(), nil
}

func (s *repoService) GetRepositoryByUrl(ctx base.Context, repoUrl string) (*repository.RepositoryInfo, error) {
	//TODO implement me
	panic("implement me")
}

func (s *repoService) UpdateRepository(ctx base.Context, repositoryId string, req *repository.UpdateRepositoryRequest) error {
	if err := req.Validate(); err != nil {
		return err
	}
	update := repoEntity{Setting: req.Setting}
	if err := dao.UpdateEntity(ctx, &update, dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withRepoId(repositoryId))); err != nil {
		return errors2.WithStack(err)
	}
	return nil
}

func (s *repoService) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *repoService) Started() <-chan struct{} {
	return s.started
}

func (s *repoService) IsReady() bool {
	return s.ready
}

func (s *repoService) Stop() {
	slog.Info("service is stopped", "service", s.GetName())
}

func (s *repoService) Metrics() []prometheus.Collector {
	return nil
}

func (s *repoService) GetName() string {
	return "repoService"
}

func (s *repoService) Register(core commoncore.Core) {
	s.core = core
}

func New() commoncore.RepositoryService {
	return &repoService{
		started: make(chan struct{}),
		ready:   false,
	}
}
