package agent_connect

import (
	"context"
	"encoding/json"
	"github.com/gorilla/websocket"
	"github.com/prometheus/client_golang/prometheus"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/agent-connect/message"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
)

var _ comncore.AgentConnectService = (*service)(nil)

type service struct {
	started    chan struct{}
	ready      bool
	core       comncore.Core
	sessionMgr *sessionManager
}

func (s *service) Serve(ctx base.Context, conn *http.SafeConn) error {
	agentId := conn.IdInfo.AgentId
	l := ctx.GetLogger().With("agentId", agentId, "connectId", conn.ConnectId)
	// 一条连接上可能有多个会话，一个会话可能多个连接并行，connId用来单独对连接进行管理
	defer s.sessionMgr.DetachSessionOfConnect(ctx, conn.ConnectId)

	s.sessionMgr.SendToConn(ctx, conn, message.NewConnectionEstablishedMessage(agentId, conn.ConnectId))
	for {
		msgType, rawMsg, err := conn.Read()
		if err != nil {
			l.Error("fail to read message from agent connect, disconnecting", "err", err)
			break
		}

		// 暂不处理二进制消息
		if msgType == websocket.BinaryMessage {
			l.Error("unsupported binary message")
			s.sessionMgr.SendToConn(ctx, conn, agent_connect.NewClientCommonError("unsupported binary message"))
			continue
		}

		msg, err := agent_connect.ParseMessage(rawMsg)
		if err != nil {
			l.Error("malformed message from client, skipped", "message", string(rawMsg))
			s.sessionMgr.SendToConn(ctx, conn, agent_connect.NewClientCommonError(err.Error()))
			continue
		}

		switch msg.GetType() {
		//case agent_connect.CreateSession:
		//	// todo 客户端创建会话
		//	s.CreateSession(ctx, conn, msg)
		case agent_connect.RestoreSession:
			// todo 客户端恢复会话监听
			go util.SafeGoroutineFunc(ctx, func() { s.handleRestoreSession(ctx, conn, agentId, msg) })
		case agent_connect.ListSessions:
			// todo 客户端查询会话列表
			s.ListSessions(ctx, conn, msg)
		case agent_connect.Chat, agent_connect.SessionControl:
			// todo 客户端对话，
			s.Chat(ctx, conn, msg)
		case agent_connect.WebhookInvocation:
			// 代码平台的 webhook 回调
			go util.SafeGoroutineFunc(ctx, func() { s.handleWebhookInvocation(ctx, conn, agentId, msg) })
		case agent_connect.SyncUsername:
			// 从代码平台同步用户名
			go util.SafeGoroutineFunc(ctx, func() { s.handleSyncUsername(ctx, conn, agentId, msg) })
		case agent_connect.ClientError, agent_connect.GetMergeRequest, agent_connect.GetMergeRequestChanges,
			agent_connect.CreateMergeRequestDiscussion, agent_connect.AddMergeRequestDiscussionNote, agent_connect.GetMergeRequestDiscussion,
			agent_connect.ListMergeRequestDiscussions, agent_connect.GetCommitDiff, agent_connect.GetMergeRequestCommits,
			agent_connect.ResolveMergeRequestDiscussion, agent_connect.GetFile, agent_connect.CreateMergeRequestNote,
			agent_connect.GetMergeRequestNote, agent_connect.UpdateMergeRequestNote, agent_connect.UpdateMergeRequestDiscussionNote,
			agent_connect.GetVersion, agent_connect.ListMergeRequestNotes, agent_connect.CreateDraftNotes, agent_connect.PublishAllDraftNotes:
			go util.SafeGoroutineFunc(ctx, func() { s.handleResponse(ctx, conn, agentId, msg) })
		default:
			l.Error("unsupported message type", "messageType", msg.GetType())
			s.sessionMgr.SendToConn(ctx, conn, agent_connect.NewClientRequestError(msg.GetRequestId(), "unsupported message type: "+string(msg.GetType())))
			continue
		}
	}
	return nil
}

func (s *service) Detach(ctx base.Context, agentId string) error {
	return nil
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "AgentConnectService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func (s *service) CreateSession(ctx base.Context, conn *http.SafeConn, msg agent_connect.Message) error {
	// todo @清矢
	// 调用会话服务，创建会话
	agentId := conn.IdInfo.AgentId
	l := ctx.GetLogger().With("agentId", agentId)
	//只创建，不加载
	sessionInfo, err := comncore.GetCoreService().GetSessionService().CreateOrLoadSession(ctx, &session.CreateSessionRequest{
		IdentityId: conn.IdInfo.IdentityId,
		Property: &session.Property{
			Type: session.AiDeveloper,
		},
	})
	if err != nil {
		l.Error("fail to create session", "err", err, "message", msg)
		s.sessionMgr.SendToConn(ctx, conn, message.NewCreateSessionResponse(msg.GetRequestId(), nil, "fail to create session"))
		return err
	}
	s.sessionMgr.AddSession(ctx, agentId, sessionInfo.SessionId, conn)
	msg.SetSessionId(sessionInfo.SessionId)
	return nil
}

func (s *service) RestoreSession(ctx base.Context, conn *http.SafeConn, msg agent_connect.Message) {
	agentId := conn.IdInfo.AgentId
	l := ctx.GetLogger().With("agentId", agentId)
	request := &message.RestoreSessionRequest{}
	err := json.Unmarshal(msg.GetPayload(), request)
	if err != nil {
		l.Error("malformed restore session message, skipped", "message", msg.GetPayload())
		s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), nil, nil, "malformed restore session message message"))
		return
	}

	if len(request.Sessions) == 0 {
		s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), nil, nil, "no session to restore"))
		return
	}
	var iSessions, rSessions []string
	for _, sid := range request.Sessions {
		sess, err := comncore.GetCoreService().GetSessionService().GetSession(ctx, sid)
		if err != nil {
			l.Error("fail to get session", "sessionId", sid)
			iSessions = append(iSessions, sid)
		} else if sess == nil {
			iSessions = append(iSessions, sid)
		} else {
			if sess.State == session.Abort || sess.State == session.Finished {
				iSessions = append(iSessions, sid)
			} else {
				s.sessionMgr.AddSession(ctx, agentId, sid, conn)
				rSessions = append(rSessions, sid)
				l.Info("session restored", "sessionId", sid, "connectId", conn.ConnectId)
			}
		}
	}
	s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), rSessions, iSessions, ""))
}

func (s *service) ListSessions(ctx base.Context, conn *http.SafeConn, msg agent_connect.Message) {
	// todo @清矢
	// 向前端返回用户相关的会话列表，这里建议做分页实现，后续可能会有查询需求
	agentId := conn.IdInfo.AgentId
	l := ctx.GetLogger().With("agentId", agentId)
	req := &message.ListSessionsRequest{}
	err := json.Unmarshal(msg.GetPayload(), req)
	if err != nil {
		l.Error("malformed list sessions message, skipped", "message", msg.GetPayload())
		s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), nil, nil, "malformed restore session message message"))
		return
	}
	idInfo := conn.IdInfo
	request := &session.ListSessionsRequest{
		WithoutUserId: true,
		IdentityId:    idInfo.IdentityId,
		PagesParams: page.PagesParams{
			PageNumber: req.PageNumber,
			PageSize:   req.PageSize,
		},
	}
	pagedSessions, err := comncore.GetCoreService().GetSessionService().ListSessions(ctx, request)
	if err != nil {
		l.Error("fail to list sessions", "err", err)
		s.sessionMgr.SendToConn(ctx, conn, message.NewListSessionsResponse(msg.GetRequestId(), msg.GetSessionId(), nil, "fail to list sessions"))
		return
	}

	s.sessionMgr.SendToConn(ctx, conn, message.NewListSessionsResponse(msg.GetRequestId(), msg.GetSessionId(), pagedSessions, ""))
}

func (s *service) Chat(ctx base.Context, conn *http.SafeConn, msg agent_connect.Message) {
	// todo @清矢
	// 接收前端的聊天请求，并调用 Agent Runtime 的 Step 接口
	// 接收 Agent Runtime 的响应，并将其投递到对应会话的消息队列中
	agentId := conn.IdInfo.AgentId
	l := ctx.GetLogger().With("agentId", agentId)
	if msg.GetRequestId() == "" {
		l.Error("missing requestId")
		s.sessionMgr.SendToConn(ctx, conn, message.NewChatResponse(msg.GetRequestId(), msg.GetSessionId(), "missing requestId"))
		return
	}
	if msg.GetSessionId() != "" {
		sessionInfo, err := comncore.GetCoreService().GetSessionService().GetSession(ctx, msg.GetSessionId())
		if err != nil {
			ctx.GetLogger().Error("fail to get session", "err", err, "sessionId", msg.GetSessionId())
			s.sessionMgr.SendToConn(ctx, conn, message.NewChatResponse(msg.GetRequestId(), msg.GetSessionId(), "fail to get session"))
			return
		}
		if conn.IdInfo.IdentityId != sessionInfo.IdentityId {
			l.Error("identityId not match", "identityId", conn.IdInfo.IdentityId, "sessionInfo", sessionInfo)
			s.sessionMgr.SendToConn(ctx, conn, message.NewChatResponse(msg.GetRequestId(), msg.GetSessionId(), "identityId not match"))
			return
		}
		s.sessionMgr.AddSession(ctx, agentId, msg.GetSessionId(), conn)
	} else {
		l.Info("session id is empty, create new session")
		if err := s.CreateSession(ctx, conn, msg); err != nil {
			return
		}
		l.Info("create session success", "sessionId", msg.GetSessionId())
	}

	params := map[string]any{
		"message": msg.ToString(),
	}
	taskConfig := &agentruntime.TaskConfig{
		Parameters: params,
	}
	if err := comncore.GetCoreService().GetAgentService().Step(ctx, agentId, msg.GetSessionId(), taskConfig); err != nil {
		l.Error("fail to step session", "err", err, "sessionId", msg.GetSessionId(), "taskConfig", taskConfig)
		s.sessionMgr.SendToConn(ctx, conn, message.NewChatResponse(msg.GetRequestId(), msg.GetSessionId(), "fail to send message to agent"))
		return
	}
	if err := s.core.GetSessionService().UpdateSession(ctx, msg.GetSessionId(), &session.UpdateSessionRequest{State: ptr.To(session.Idle)}); err != nil {
		ctx.GetLogger().Error("update session state failed", "sessionId", msg.GetSessionId(), "err", err)
		s.sessionMgr.SendToConn(ctx, conn, message.NewChatResponse(msg.GetRequestId(), msg.GetSessionId(), "fail to change agent state to idle"))
		return
	}
	s.sessionMgr.SendToConn(ctx, conn, message.NewChatResponse(msg.GetRequestId(), msg.GetSessionId(), ""))
}

func (s *service) Callback(ctx base.Context, conn *http.SafeConn, msg agent_connect.Message) error {
	// todo @清矢
	// 接收 Agent Runtime 的响应，并将其投递到对应会话的消息队列中
	if msg.GetSessionId() == "" {
		ctx.GetLogger().Error("missing sessionId")
		return errors.New(codes.ErrSessionIdMissingMessage)
	}

	if msg.GetRequestId() == "" {
		ctx.GetLogger().Error("missing requestId")
		return errors.New(codes.ErrRequestIdMissingMessage)
	}

	l := ctx.GetLogger().With("requestId", msg.GetRequestId(), "messageType", msg.GetType())

	// todo 要检查对应的会话有没有活跃的agent-connect监听
	// 将请求推送到会话的消息队列，由clientSession订阅到以后转发给agent-connect
	r := helper.GetRedis().LPush(ctx, getSessionReqQueue(msg.GetSessionId()), msg.ToString())
	if r.Err() != nil {
		l.Error("fail to send message", "err", r.Err())
		return errors.New(codes.ErrFailToSendMessage)
	} else {
		l.Info("session msg added", "sessionId", msg.GetSessionId(), "payload", msg.ToString())
	}
	return nil
}

func New() comncore.AgentConnectService {
	return &service{
		started:    make(chan struct{}),
		ready:      false,
		sessionMgr: newSessionManager(),
	}
}
