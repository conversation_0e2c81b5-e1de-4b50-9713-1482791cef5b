package message

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
)

type ListSessionsRequest struct {
	page.PagesParams
}

type ListSessionsResponse struct {
	agent_connect.BaseResponse
	Data ListSessionsResponseData `json:"data"`
}

type ListSessionsResponseData struct {
	PagedSessions *page.PagedItems[*session.Info] `json:"pagedSessions"`
}

func NewListSessionsResponse(requestId string, sessionId string, pagedSessions *page.PagedItems[*session.Info], errMsg string) agent_connect.Message {
	payload := &ListSessionsResponse{}
	if errMsg == "" {
		payload.Success = true
		payload.Data = ListSessionsResponseData{
			PagedSessions: pagedSessions,
		}
	} else {
		payload.Success = false
		payload.ErrorMessage = errMsg
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.ListSessions,
		RequestId: requestId,
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
