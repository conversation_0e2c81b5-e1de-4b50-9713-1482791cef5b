package message

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
)

type CreateSessionResponse struct {
	agent_connect.BaseResponse
	SessionInfo *session.Info
}

func NewCreateSessionResponse(requestId string, sessionInfo *session.Info, errMsg string) agent_connect.Message {
	payload := CreateSessionResponse{}
	if errMsg == "" {
		payload.Success = true
		payload.SessionInfo = sessionInfo
	} else {
		payload.Success = false
		payload.ErrorMessage = errMsg
	}
	p, _ := json.Marshal(payload)

	return &agent_connect.BaseMessage{
		Type:      agent_connect.CreateSession,
		RequestId: requestId,
		SessionId: "",
		Serial:    0,
		Payload:   p,
	}

}
