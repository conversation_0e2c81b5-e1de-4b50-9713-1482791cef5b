package message

import (
	"encoding/json"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
)

type ConnectionResponse struct {
	agent_connect.BaseResponse
	Data ConnectionData `json:"data"`
}

type ConnectionData struct {
	Message   string `json:"message"`
	AgentId   string `json:"agentId"`
	ConnectId string `json:"connectId"`
}

func NewConnectionEstablishedMessage(agentId string, connectId string) agent_connect.Message {
	payload := ConnectionResponse{}
	payload.Success = true
	payload.Data = ConnectionData{
		Message:   "NewConnectionEstablished",
		AgentId:   agentId,
		ConnectId: connectId,
	}

	p, _ := json.Marshal(payload)

	return &agent_connect.BaseMessage{
		Type:      agent_connect.Connection,
		RequestId: "",
		SessionId: "",
		Serial:    0,
		Payload:   p,
	}
}
