package gitlab

import (
	"encoding/json"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
)

type GetVersionRequest struct {
}

type GetVersionResponse struct {
	agent_connect.BaseResponse
	Version string `json:"version"`
}

func NewGetVersionRequest(sessionId string) agent_connect.Message {
	payload := &GetVersionRequest{}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetVersion,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
