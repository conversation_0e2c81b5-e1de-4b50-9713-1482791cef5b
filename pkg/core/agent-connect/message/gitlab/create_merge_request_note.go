package gitlab

import (
	"encoding/json"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
	"k8s.io/utils/ptr"
)

type CreateMergeRequestNoteRequest struct {
	ProjectId      string                                `json:"projectId"`
	MergeRequestId string                                `json:"mergeRequestId"`
	Options        *gitlab.CreateMergeRequestNoteOptions `json:"options"`
}

type CreateMergeRequestNoteResponse struct {
	agent_connect.BaseResponse
	Note *gitlab.Note `json:"note"`
}

func NewCreateMergeRequestNoteRequest(sessionId, projectId, mergeRequestId string, opts *codeplatform.CreateMergeRequestCommentOpts) agent_connect.Message {
	payload := &CreateMergeRequestNoteRequest{
		ProjectId:      projectId,
		MergeRequestId: mergeRequestId,
		Options: &gitlab.CreateMergeRequestNoteOptions{
			Body: ptr.To(opts.Content),
		},
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.CreateMergeRequestNote,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
