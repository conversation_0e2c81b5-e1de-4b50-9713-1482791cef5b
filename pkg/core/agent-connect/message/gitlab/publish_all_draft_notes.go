package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
)

type PublishAllDraftNotesRequest struct {
	ProjectId      string `json:"projectId"`
	MergeRequestId string `json:"mergeRequestId"`
}

type PublishAllDraftNotesResponse struct {
	agent_connect.BaseResponse
}

func NewPublishAllDraftNotesRequest(sessionId, projectId, mrId string) agent_connect.Message {
	payload := &PublishAllDraftNotesRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.PublishAllDraftNotes,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
