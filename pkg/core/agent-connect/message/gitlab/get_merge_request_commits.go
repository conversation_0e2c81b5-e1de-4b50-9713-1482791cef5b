package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type GetMergeRequestCommitsRequest struct {
	ProjectId      string `json:"projectId"`
	MergeRequestId string `json:"mergeRequestId"`
}

type GetMergeRequestCommitsResponse struct {
	agent_connect.BaseResponse
	Commits []*gitlab.Commit `json:"commits"`
}

func NewGetMergeRequestCommitsRequest(sessionId, projectId, mrId string) agent_connect.Message {
	payload := &GetMergeRequestCommitsRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetMergeRequestCommits,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
