package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
	"k8s.io/utils/ptr"
)

type AddMergeRequestDiscussionNoteRequest struct {
	ProjectId      string                                       `json:"projectId"`
	MergeRequestId string                                       `json:"mergeRequestId"`
	DiscussionId   string                                       `json:"discussionId"`
	NoteOptions    *gitlab.AddMergeRequestDiscussionNoteOptions `json:"noteOptions"`
}

type AddMergeRequestDiscussionNoteResponse struct {
	agent_connect.BaseResponse
	Note *gitlab.Note `json:"note"`
}

func NewAddMergeRequestDiscussionNoteRequest(sessionId, projectId, mrId, discussionId, content string) agent_connect.Message {
	noteOpts := &gitlab.AddMergeRequestDiscussionNoteOptions{
		Body: ptr.To(content),
	}

	payload := &AddMergeRequestDiscussionNoteRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
		DiscussionId:   discussionId,
		NoteOptions:    noteOpts,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.AddMergeRequestDiscussionNote,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
