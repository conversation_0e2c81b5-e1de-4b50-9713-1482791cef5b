package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type GetCommitDiffRequest struct {
	ProjectId string `json:"projectId"`
	Sha       string `json:"sha"`
}

type GetCommitDiffResponse struct {
	agent_connect.BaseResponse
	Diff []*gitlab.Diff `json:"diff"`
}

func NewGetCommitDiffRequest(sessionId, projectId, sha string) agent_connect.Message {
	payload := &GetCommitDiffRequest{
		ProjectId: projectId,
		Sha:       sha,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetCommitDiff,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
