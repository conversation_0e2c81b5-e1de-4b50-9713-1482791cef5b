package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type CreateDraftNotesRequest struct {
	ProjectId      string                         `json:"projectId"`
	MergeRequestId string                         `json:"mergeRequestId"`
	Options        *gitlab.CreateDraftNoteOptions `json:"options"`
}

type CreateDraftNotesResponse struct {
	agent_connect.BaseResponse
}

func NewCreateDraftNotesRequest(sessionId, projectId, mrId string, options *gitlab.CreateDraftNoteOptions) agent_connect.Message {
	payload := &CreateDraftNotesRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
		Options:        options,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.CreateDraftNotes,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
