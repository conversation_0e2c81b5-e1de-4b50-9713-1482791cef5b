package gitlab

import (
	"encoding/json"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type UpdateMergeRequestNoteRequest struct {
	ProjectId      string                                `json:"projectId"`
	MergeRequestId string                                `json:"mergeRequestId"`
	NoteId         string                                `json:"noteId"`
	Options        *gitlab.UpdateMergeRequestNoteOptions `json:"options"`
}

type UpdateMergeRequestNoteResponse struct {
	agent_connect.BaseResponse
	Note *gitlab.Note `json:"note"`
}

func NewUpdateMergeRequestNoteRequest(sessionId, projectId, mergeRequestId, noteId string, opts *codeplatform.UpdateMergeRequestCommentOpts) agent_connect.Message {
	payload := &UpdateMergeRequestNoteRequest{
		ProjectId:      projectId,
		MergeRequestId: mergeRequestId,
		NoteId:         noteId,
		Options: &gitlab.UpdateMergeRequestNoteOptions{
			Body: opts.Content,
		},
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.UpdateMergeRequestNote,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
