package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
)

type WebhookInvocationRequest struct {
	Token        string                `json:"token"`
	CodePlatform codeplatform.Platform `json:"codePlatform"`
	Payload      map[string]any        `json:"payload"`
}

type WebhookInvocationResponse struct {
	SessionId string `json:"sessionId"`
	agent_connect.BaseResponse
}

func NewWebhookInvocationResponse(requestId, sessionId, errMsg string) agent_connect.Message {
	payload := &WebhookInvocationResponse{SessionId: sessionId}
	if errMsg == "" {
		payload.Success = true
	} else {

		payload.Success = false
		payload.ErrorMessage = errMsg
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.WebhookInvocation,
		RequestId: requestId,
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
