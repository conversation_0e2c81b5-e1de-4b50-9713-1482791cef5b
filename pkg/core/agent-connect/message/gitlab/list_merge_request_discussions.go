package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type ListMergeRequestDiscussionsRequest struct {
	ProjectId      string `json:"projectId"`
	MergeRequestId string `json:"mergeRequestId"`
}

type ListMergeRequestDiscussionsResponse struct {
	agent_connect.BaseResponse
	Discussions []*gitlab.Discussion `json:"discussions"`
}

func NewListMergeRequestDiscussionsRequest(sessionId, projectId, mrId string) agent_connect.Message {
	payload := &ListMergeRequestDiscussionsRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.ListMergeRequestDiscussions,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
