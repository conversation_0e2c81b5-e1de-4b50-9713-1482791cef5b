package gitlab

import (
	"encoding/json"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type GetMergeRequestNoteRequest struct {
	ProjectId      string `json:"projectId"`
	MergeRequestId string `json:"mergeRequestId"`
	NoteId         string `json:"noteId"`
}

type GetMergeRequestNoteResponse struct {
	agent_connect.BaseResponse
	Note *gitlab.Note `json:"note"`
}

func NewGetMergeRequestNoteRequest(sessionId, projectId, mergeRequestId, noteId string) agent_connect.Message {
	payload := &GetMergeRequestNoteRequest{
		ProjectId:      projectId,
		MergeRequestId: mergeRequestId,
		NoteId:         noteId,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetMergeRequestNote,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
