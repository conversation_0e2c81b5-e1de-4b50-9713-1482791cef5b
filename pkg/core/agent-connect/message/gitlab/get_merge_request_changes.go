package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type GetMergeRequestChangesRequest struct {
	ProjectId      string `json:"projectId"`
	MergeRequestId string `json:"mergeRequestId"`
}

type GetMergeRequestChangesResponse struct {
	agent_connect.BaseResponse
	MergeRequest *gitlab.MergeRequest `json:"mergeRequest"`
}

func NewGetMergeRequestChangesRequest(sessionId, projectId, mrId string) agent_connect.Message {
	payload := &GetMergeRequestChangesRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetMergeRequestChanges,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
