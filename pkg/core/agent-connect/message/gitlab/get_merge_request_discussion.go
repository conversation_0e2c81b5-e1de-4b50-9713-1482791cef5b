package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type GetMergeRequestDiscussionRequest struct {
	ProjectId      string `json:"projectId"`
	MergeRequestId string `json:"mergeRequestId"`
	CommentId      string `json:"commentId"`
}

type GetMergeRequestDiscussionResponse struct {
	agent_connect.BaseResponse
	Discussion *gitlab.Discussion `json:"discussion"`
}

func NewGetMergeRequestDiscussionRequest(sessionId, projectId, mrId, commentId string) agent_connect.Message {
	payload := &GetMergeRequestDiscussionRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
		CommentId:      commentId,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetMergeRequestDiscussion,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
