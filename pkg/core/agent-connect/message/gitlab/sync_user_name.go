package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
)

type SyncUsernameRequest struct {
	Token    string `json:"token"`
	Username string `json:"username"`
}

type SyncUsernameResponse struct {
	agent_connect.BaseResponse
}

func NewSyncUsernameResponse(requestId, sessionId string, err error) agent_connect.Message {
	payload := &SyncUsernameResponse{}
	if err == nil {
		payload.Success = true
	} else {
		payload.Success = false
		payload.ErrorMessage = err.Error()
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.SyncUsername,
		RequestId: requestId,
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
