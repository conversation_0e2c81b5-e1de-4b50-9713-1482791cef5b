package gitlab

import (
	"encoding/json"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type UpdateMergeRequestDiscussionNoteRequest struct {
	ProjectId      string                                          `json:"projectId"`
	MergeRequestId string                                          `json:"mergeRequestId"`
	DiscussionId   string                                          `json:"discussionId"`
	NoteId         string                                          `json:"noteId"`
	Options        *gitlab.UpdateMergeRequestDiscussionNoteOptions `json:"options"`
}

type UpdateMergeRequestDiscussionNoteResponse struct {
	agent_connect.BaseResponse
	Note *gitlab.Note `json:"note"`
}

func NewUpdateMergeRequestDiscussionNoteRequest(sessionId, projectId, mergeRequestId, discussionId, noteId string, opts *codeplatform.UpdateMergeRequestCommentOpts) agent_connect.Message {
	payload := &UpdateMergeRequestDiscussionNoteRequest{
		ProjectId:      projectId,
		MergeRequestId: mergeRequestId,
		DiscussionId:   discussionId,
		NoteId:         noteId,
		Options: &gitlab.UpdateMergeRequestDiscussionNoteOptions{
			Body:     opts.Content,
			Resolved: opts.Resolved,
		},
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.UpdateMergeRequestDiscussionNote,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
