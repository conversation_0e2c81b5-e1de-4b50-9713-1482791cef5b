package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type GetMergeRequestRequest struct {
	ProjectId      string `json:"projectId"`
	MergeRequestId string `json:"mergeRequestId"`
}

type GetMergeRequestResponse struct {
	agent_connect.BaseResponse
	MergeRequest *gitlab.MergeRequest `json:"mergeRequest"`
}

func NewGetMergeRequestRequest(sessionId, projectId, mrId string) agent_connect.Message {
	payload := &GetMergeRequestRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetMergeRequest,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
