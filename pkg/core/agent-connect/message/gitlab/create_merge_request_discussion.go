package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type CreateMergeRequestDiscussionRequest struct {
	ProjectId         string                                      `json:"projectId"`
	MergeRequestId    string                                      `json:"mergeRequestId"`
	DiscussionOptions *gitlab.CreateMergeRequestDiscussionOptions `json:"discussionOptions"`
}

type CreateMergeRequestDiscussionResponse struct {
	agent_connect.BaseResponse
	Discussion *gitlab.Discussion `json:"discussion"`
}

func NewCreateMergeRequestDiscussionRequest(sessionId, projectId, mrId string, discussionOptions *gitlab.CreateMergeRequestDiscussionOptions) agent_connect.Message {
	payload := &CreateMergeRequestDiscussionRequest{
		ProjectId:         projectId,
		MergeRequestId:    mrId,
		DiscussionOptions: discussionOptions,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.CreateMergeRequestDiscussion,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
