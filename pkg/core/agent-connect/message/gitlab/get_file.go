package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type GetFileRequest struct {
	ProjectId string                 `json:"projectId"`
	Path      string                 `json:"path"`
	Options   *gitlab.GetFileOptions `json:"options"`
}

type GetFileResponse struct {
	agent_connect.BaseResponse
	File *gitlab.File `json:"file"`
}

func NewGetFileRequest(sessionId, projectId string, opts *codeplatform.GetRepositoryFileOpts) agent_connect.Message {
	payload := &GetFileRequest{
		ProjectId: projectId,
		Path:      opts.Path,
		Options: &gitlab.GetFileOptions{
			Ref: opts.Ref,
		},
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.GetFile,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
