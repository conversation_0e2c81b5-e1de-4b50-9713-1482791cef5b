package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

type ListMergeRequestNotesRequest struct {
	ProjectId      string                               `json:"projectId"`
	MergeRequestId string                               `json:"mergeRequestId"`
	Options        *gitlab.ListMergeRequestNotesOptions `json:"options"`
}

type ListMergeRequestNotesResponse struct {
	agent_connect.BaseResponse
	Notes []*gitlab.Note `json:"notes"`
}

func NewListMergeRequestNotesRequest(sessionId, projectId, mrId string, options *gitlab.ListMergeRequestNotesOptions) agent_connect.Message {
	payload := &ListMergeRequestNotesRequest{
		ProjectId:      projectId,
		MergeRequestId: mrId,
		Options:        options,
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.ListMergeRequestNotes,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
