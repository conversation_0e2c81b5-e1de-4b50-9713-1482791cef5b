package gitlab

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	gitlab "gitlab.com/gitlab-org/api/client-go"
	"k8s.io/utils/ptr"
)

type ResolveMergeRequestDiscussionRequest struct {
	ProjectId      string                                       `json:"projectId"`
	MergeRequestId string                                       `json:"mergeRequestId"`
	CommentId      string                                       `json:"commentId"`
	Options        *gitlab.ResolveMergeRequestDiscussionOptions `json:"options"`
}

type ResolveMergeRequestDiscussionResponse struct {
	agent_connect.BaseResponse
}

func NewResolveMergeRequestDiscussionRequest(sessionId, projectId, mrId string, opts *codeplatform.ResolveMergeRequestCommentOpts) agent_connect.Message {
	payload := &ResolveMergeRequestDiscussionRequest{
		ProjectId:      projectId,
		CommentId:      opts.CommentId,
		MergeRequestId: mrId,
		Options: &gitlab.ResolveMergeRequestDiscussionOptions{
			Resolved: ptr.To(opts.Resolved),
		},
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.ResolveMergeRequestDiscussion,
		RequestId: helper.NewIDWithPrefix("req-"),
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
