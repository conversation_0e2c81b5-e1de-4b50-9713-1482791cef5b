package message

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
)

type RestoreSessionRequest struct {
	Sessions []string `json:"sessions"`
}

type RestoreSessionResponse struct {
	agent_connect.BaseResponse
	RestoredSessions []string
	InvalidSessions  []string
}

func NewRestoreSessionResponse(requestId string, rSessions []string, iSessions []string, errMsg string) agent_connect.Message {
	payload := &RestoreSessionResponse{}
	if errMsg == "" {
		payload.Success = true
		payload.RestoredSessions = rSessions
		payload.InvalidSessions = iSessions
	} else {
		payload.Success = false
		payload.ErrorMessage = errMsg
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.RestoreSession,
		RequestId: requestId,
		SessionId: "",
		Serial:    0,
		Payload:   p,
	}
}
