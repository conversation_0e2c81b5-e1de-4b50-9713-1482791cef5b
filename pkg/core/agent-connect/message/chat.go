package message

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
)

type ChatResponse struct {
	agent_connect.BaseResponse
}

func NewChatResponse(requestId string, sessionId string, errMsg string) agent_connect.Message {
	payload := &ChatResponse{}
	if errMsg == "" {
		payload.Success = true
	} else {
		payload.Success = false
		payload.ErrorMessage = errMsg
	}

	p, _ := json.Marshal(payload)
	return &agent_connect.BaseMessage{
		Type:      agent_connect.Chat,
		RequestId: requestId,
		SessionId: sessionId,
		Serial:    0,
		Payload:   p,
	}
}
