package agent_connect

import (
	"encoding/json"
	errors2 "errors"
	"github.com/redis/go-redis/v9"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"sync"
	"time"
)

type sessionManager struct {
	sessionMap map[string]*clientSession
	lock       *sync.RWMutex
}

// AddSession 增加一个Session监听，幂等
func (s *sessionManager) AddSession(ctx base.Context, agentId, sessionId string, conn *http.SafeConn) {
	if sessionId == "" {
		return
	}

	s.lock.Lock()
	defer s.lock.Unlock()

	if cs, ok := s.sessionMap[sessionId]; ok {
		if cs.Conn.ConnectId == conn.ConnectId {
			return
		}

		ctx.GetLogger().Info("session updated", "sessionId", sessionId)
		// 进入这个分支，表明同一个session可能被两个连接监听，要停止掉原来的监听，避免goroutine泄漏
		cs.Stop()
	}

	cs := &clientSession{
		AgentId:   agentId,
		SessionId: sessionId,
		Conn:      conn,
		lock:      &sync.Mutex{},
	}

	s.sessionMap[sessionId] = cs
	cs.Start(ctx)
	ctx.GetLogger().Info("session added", "sessionId", sessionId)
	for k, v := range s.sessionMap {
		ctx.GetLogger().Info("sessionId: " + k + " -> connectId: " + v.Conn.ConnectId)
	}
}

func (s *sessionManager) startSessionTracking() {
	ctx := base.NewContextForAdmin(helper.NewTraceId(), "sessionTracker")
	go util.SafeGoroutineFunc(ctx, func() {
		for {
			select {
			case <-time.Tick(10 * time.Second):
				s.scanAndRemoveStoppedSession(ctx)
			}
		}
	})
}

func (s *sessionManager) scanAndRemoveStoppedSession(ctx base.Context) {
	s.lock.Lock()
	var allSessions []string
	for sid := range s.sessionMap {
		allSessions = append(allSessions, sid)
	}
	s.lock.Unlock()

	ctx.GetLogger().Info("scanning all active session", "total", len(allSessions))
	removed := 0
	for _, sid := range allSessions {
		sess, err := core.GetCoreService().GetSessionService().GetSession(ctx, sid)
		if err != nil {
			if errors.Is(err, codes.ErrRecordNotFound) {
				ctx.GetLogger().Error("session not exist, removing it", "sessionId", sid)
				s.StopAndRemoveSession(ctx, sid)
				removed++
			} else {
				ctx.GetLogger().Error("fail to get session", "error", err, "sessionId", sid)
				continue
			}
		} else {
			if sess.State == session.Finished || sess.State == session.Abort {
				ctx.GetLogger().Error("session stopped, removing it", "sessionId", sid)
				s.StopAndRemoveSession(ctx, sid)
				removed++
			}
		}
	}

	ctx.GetLogger().Info("session scan completed", "removed", removed)
}

func (s *sessionManager) DetachSessionOfConnect(ctx base.Context, connId string) {
	if connId == "" {
		return
	}

	ctx.GetLogger().Info("stopping client session of connect", "connectId", connId)

	s.lock.Lock()
	defer s.lock.Unlock()
	var sessionToRemove []string
	for k, v := range s.sessionMap {
		if v.Conn.ConnectId == connId {
			v.Stop()
			sessionToRemove = append(sessionToRemove, k)
			ctx.GetLogger().Info("client session closed", "sessionId", k, "connectId", connId)
		}
	}

	for _, sid := range sessionToRemove {
		delete(s.sessionMap, sid)
		ctx.GetLogger().Info("client session removed", "sessionId", sid)
	}
}

func (s *sessionManager) StopAndRemoveSession(ctx base.Context, sessionId string) {
	if sessionId == "" {
		return
	}

	ctx.GetLogger().Info("stopping client session", "sessionId", sessionId)

	s.lock.Lock()
	defer s.lock.Unlock()

	if v, ok := s.sessionMap[sessionId]; ok {
		v.Stop()
		delete(s.sessionMap, sessionId)
		ctx.GetLogger().Info("client session stopped and removed", "sessionId", sessionId)
	}
}

// SendToConn 向指定连接发送一条消息，失败了不重试
func (s *sessionManager) SendToConn(ctx base.Context, conn *http.SafeConn, msg agent_connect.Message) {
	err := conn.WriteText(msg.ToString())
	if err != nil {
		ctx.GetLogger().Error("fail to write message", "err", err)
	}
}

// Request 同步发送请求，并同步等待结果
func (s *sessionManager) Request(ctx base.Context, msg agent_connect.Message, timeout time.Duration) (agent_connect.Message, error) {
	if msg.GetSessionId() == "" {
		ctx.GetLogger().Error("missing sessionId")
		return nil, errors.New(codes.ErrSessionIdMissingMessage)
	}

	if msg.GetRequestId() == "" {
		ctx.GetLogger().Error("missing requestId")
		return nil, errors.New(codes.ErrRequestIdMissingMessage)
	}

	l := ctx.GetLogger().With("sessionId", msg.GetSessionId(), "requestId", msg.GetRequestId(), "messageType", msg.GetType())

	// 将请求推送到会话的消息队列，由clientSession订阅到以后转发给agent-connect
	rc := helper.GetRedis()
	sq := getSessionReqQueue(msg.GetSessionId())
	llenRes := rc.LLen(ctx, sq)

	// 判断 session 队列是否满了，如果满了就返回失败，避免大量消息堆积
	if llenRes.Err() != nil {
		l.Error("fail to get length of session queue", "err", llenRes.Err())
		return nil, errors.New(codes.ErrFailToSendMessage)
	} else if llenRes.Val() > 100 {
		l.Error("session queue is full")
		return nil, errors.New(codes.ErrFailToSendMessage)
	}

	// 推送消息
	r := rc.LPush(ctx, sq, msg.ToString())
	if r.Err() != nil {
		l.Error("fail to send message", "err", r.Err())
		return nil, errors.New(codes.ErrFailToSendMessage)
	} else {
		l.Info("session msg added", "payload", msg.ToString())
	}

	// 等待结果
	rq := getRequestResQueue(msg.GetRequestId())
	l.Info("start to waiting response from queue", "queue", rq)

	st := time.Now()
	for {
		pr := rc.RPop(ctx, rq)
		if pr.Err() != nil {
			if errors2.Is(pr.Err(), redis.Nil) {
				time.Sleep(time.Millisecond * 50)
			} else {
				l.Error("fail to waiting response", "err", pr.Err())
				return nil, errors.New(codes.ErrFailToRequestMessage)
			}
		} else {
			l.Info("request response got", "requestId", msg.GetRequestId(), "payload", pr.Val())
			return agent_connect.ParseMessage([]byte(pr.Val()))
		}

		if time.Since(st) > timeout {
			l.Error("fail to waiting response with timeout")
			return nil, errors.New(codes.ErrFailToRequestMessage)
		}
	}
}

func (s *sessionManager) RequestToFill(ctx base.Context, msg agent_connect.Message, timeout time.Duration, resp agent_connect.Response) error {
	respMsg, err := s.Request(ctx, msg, timeout)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(respMsg.GetPayload(), resp); err != nil {
		ctx.GetLogger().Error("fail to unmarshal response", "messageType", msg.GetType(), "payload", string(respMsg.GetPayload()))
		return errors.New(codes.ErrFailToRequestMessage)
	}

	if !resp.IsSuccess() {
		ctx.GetLogger().Error("agent connect returned error", "errMsg", resp.GetErrorMessage())
		return errors.New(codes.ErrResponseWithDetailMessage, resp.GetErrorMessage())
	}

	return nil
}

func newSessionManager() *sessionManager {
	s := &sessionManager{
		lock:       &sync.RWMutex{},
		sessionMap: map[string]*clientSession{},
	}

	s.startSessionTracking()
	return s
}

func getSessionReqQueue(sessionId string) string {
	return "sq-" + sessionId
}

func getRequestResQueue(requestId string) string {
	return "rq-" + requestId
}
