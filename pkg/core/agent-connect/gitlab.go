package agent_connect

import (
	"crypto/sha1"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/agent-connect/message/gitlab"
	sdk "gitlab.com/gitlab-org/api/client-go"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// GetMergeRequest 这个响应是不带Changes的，如果需要Changes，需要使用GetMergeRequestChanges
func (s *service) GetMergeRequest(ctx base.Context, sessionId, projectId, mrId string) (*sdk.MergeRequest, error) {
	resp := &gitlab.GetMergeRequestResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetMergeRequestRequest(sessionId, projectId, mrId),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.MergeRequest, nil
}

func (s *service) GetMergeRequestChanges(ctx base.Context, sessionId, projectId, mrId string) (*sdk.MergeRequest, error) {
	resp := &gitlab.GetMergeRequestChangesResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetMergeRequestChangesRequest(sessionId, projectId, mrId),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.MergeRequest, nil
}

func (s *service) AddMergeRequestDiscussionNote(ctx base.Context, sessionId, projectId, mrId, discussionId, content string) (*sdk.Note, error) {
	resp := &gitlab.AddMergeRequestDiscussionNoteResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewAddMergeRequestDiscussionNoteRequest(sessionId, projectId, mrId, discussionId, content),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Note, nil
}

func (s *service) CreateMergeRequestDiscussion(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.CreateMergeRequestCommentOpts) (*sdk.Discussion, error) {
	discussionOpts := &sdk.CreateMergeRequestDiscussionOptions{
		Body: sdk.Ptr(opts.Content),
	}

	mr, err := s.GetMergeRequestChanges(ctx, sessionId, projectId, mrId)
	if err != nil {
		ctx.GetLogger().Error("failed to get merge request", "projectId", projectId, "mrId", mrId, "err", err.Error())
		return nil, errors.WithStack(err)
	}

	position, err := convertPositionOption(ctx, &codeplatform.CodePosition{
		StartLine: opts.StartLine,
		EndLine:   opts.EndLine,
		LineType:  opts.LineType,
		Path:      opts.Path,
	}, mr)
	if err != nil {
		ctx.GetLogger().Error("failed to convert position option", "err", err.Error())
		return nil, errors.WithStack(err)
	}

	discussionOpts.Position = position

	resp := &gitlab.CreateMergeRequestDiscussionResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewCreateMergeRequestDiscussionRequest(sessionId, projectId, mrId, discussionOpts),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Discussion, nil
}

func (s *service) ListMergeRequestComments(ctx base.Context, sessionId, projectId, mrId string, request *codeplatform.ListMergeRequestCommentsRequest) (*sdk.Discussion, error) {
	resp := &gitlab.GetMergeRequestDiscussionResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetMergeRequestDiscussionRequest(sessionId, projectId, mrId, request.CommentId),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Discussion, nil
}

func (s *service) ListMergeRequestAllComments(ctx base.Context, sessionId, projectId, mrId string) ([]*sdk.Discussion, error) {
	resp := &gitlab.ListMergeRequestDiscussionsResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewListMergeRequestDiscussionsRequest(sessionId, projectId, mrId),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Discussions, nil
}

func (s *service) GetCommitDiff(ctx base.Context, sessionId, projectId, sha string) ([]*sdk.Diff, error) {
	resp := &gitlab.GetCommitDiffResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetCommitDiffRequest(sessionId, projectId, sha),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Diff, nil
}

func (s *service) ListMergeRequestCommits(ctx base.Context, sessionId, projectId, mrId string) ([]*sdk.Commit, error) {
	resp := &gitlab.GetMergeRequestCommitsResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetMergeRequestCommitsRequest(sessionId, projectId, mrId),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Commits, nil
}

func (s *service) ResolveMergeRequestDiscussion(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.ResolveMergeRequestCommentOpts) error {
	resp := &gitlab.ResolveMergeRequestDiscussionResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewResolveMergeRequestDiscussionRequest(sessionId, projectId, mrId, opts),
		time.Second*5,
		resp,
	); err != nil {
		return err
	}
	return nil
}

func (s *service) GetRepositoryFile(ctx base.Context, sessionId, projectId string, opts *codeplatform.GetRepositoryFileOpts) (*sdk.File, error) {
	resp := &gitlab.GetFileResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetFileRequest(sessionId, projectId, opts),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.File, nil
}

func (s *service) CreateMergeRequestNote(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.CreateMergeRequestCommentOpts) (*sdk.Note, error) {
	resp := &gitlab.CreateMergeRequestNoteResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewCreateMergeRequestNoteRequest(sessionId, projectId, mrId, opts),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Note, nil
}

func (s *service) GetMergeRequestNote(ctx base.Context, sessionId, projectId, mrId, noteId string) (*sdk.Note, error) {
	resp := &gitlab.GetMergeRequestNoteResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetMergeRequestNoteRequest(sessionId, projectId, mrId, noteId),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Note, nil
}
func (s *service) UpdateMergeRequestNote(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.UpdateMergeRequestCommentOpts) (*sdk.Note, error) {
	resp := &gitlab.UpdateMergeRequestNoteResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewUpdateMergeRequestNoteRequest(sessionId, projectId, mrId, opts.CommentId, opts),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Note, nil
}
func (s *service) UpdateMergeRequestDiscussionNote(ctx base.Context, sessionId, projectId, mrId, discussionId string, opts *codeplatform.UpdateMergeRequestCommentOpts) (*sdk.Note, error) {
	resp := &gitlab.UpdateMergeRequestDiscussionNoteResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewUpdateMergeRequestDiscussionNoteRequest(sessionId, projectId, mrId, discussionId, opts.CommentId, opts),
		time.Second*5,
		resp,
	); err != nil {
		return nil, err
	}
	return resp.Note, nil
}

func (s *service) GetVersion(ctx base.Context, sessionId string) (string, error) {
	resp := &gitlab.GetVersionResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewGetVersionRequest(sessionId),
		time.Second*5,
		resp,
	); err != nil {
		return "", err
	}
	return resp.Version, nil
}

func (s *service) CreateDraftNotes(ctx base.Context, sessionId, projectId, mergeRequestId string, draftNotes []codeplatform.ReviewComment) error {
	ctx.GetLogger().Info("start to create draft note", "count", len(draftNotes), "mrId", mergeRequestId)
	mr, err := s.GetMergeRequestChanges(ctx, sessionId, projectId, mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("failed to get mr", "err", err.Error())
		return errors.WithStack(err)
	}

	for _, note := range draftNotes {
		position, err := convertPositionOption(ctx, note.Position, mr)
		if err != nil {
			ctx.GetLogger().Error("failed to convert position option", "err", err.Error(), "draftNote", note)
			// 不要影响发送其他评论
			continue
		}

		resp := &gitlab.CreateDraftNotesResponse{}
		if err := s.sessionMgr.RequestToFill(
			ctx,
			gitlab.NewCreateDraftNotesRequest(sessionId, projectId, mergeRequestId, &sdk.CreateDraftNoteOptions{
				Note:     sdk.Ptr(note.Content),
				Position: position,
			}),
			time.Second*5,
			resp,
		); err != nil {
			ctx.GetLogger().Error("failed to create draft note", "draftNote", note, "err", err.Error(), "resp", resp, "draftNote", note)
			//return errors.WithStack(err)
			// 不要影响发送其他评论
			continue
		}
		ctx.GetLogger().Info("successfully create draft note", "resp", resp)
	}
	return nil
}

func (s *service) PublishAllDraftNotes(ctx base.Context, sessionId, projectId, mergeRequestId string) error {
	ctx.GetLogger().Info("start to publish draft notes", "mrId", mergeRequestId, "projectId", projectId)
	resp := &gitlab.PublishAllDraftNotesResponse{}
	if err := s.sessionMgr.RequestToFill(
		ctx,
		gitlab.NewPublishAllDraftNotesRequest(sessionId, projectId, mergeRequestId),
		time.Second*5,
		resp,
	); err != nil {
		ctx.GetLogger().Error("failed to publish draft notes", "err", err.Error(), "resp", resp)
		return err
	}

	ctx.GetLogger().Info("successfully publish all draft notes", "mrId", mergeRequestId, "projectId", projectId)
	return nil
}

func (s *service) ListNotes(ctx base.Context, sessionId, projectId, mergeRequestId string) ([]*sdk.Note, error) {
	ctx.GetLogger().Info("start to list notes", "mrId", mergeRequestId, "projectId", projectId)
	var allNotes []*sdk.Note
	page := 1
	perPage := 100 // GitLab API 每页最多返回 100 条记录

	// 按创建时间升序排列，确保分页稳定性
	opts := &sdk.ListMergeRequestNotesOptions{
		ListOptions: sdk.ListOptions{
			Page:    page,
			PerPage: perPage,
		},
		OrderBy: sdk.Ptr("created_at"), // 按创建时间排序
		Sort:    sdk.Ptr("asc"),        // 升序排列（从旧到新）
	}

	for {
		ctx.GetLogger().Info("fetching notes", "page", opts.Page, "perPage", opts.PerPage)
		resp := &gitlab.ListMergeRequestNotesResponse{}
		if err := s.sessionMgr.RequestToFill(
			ctx,
			gitlab.NewListMergeRequestNotesRequest(sessionId, projectId, mergeRequestId, opts),
			time.Second*5,
			resp,
		); err != nil {
			ctx.GetLogger().Error("failed to list notes", "err", err.Error(), "resp", resp)
			return nil, err
		}

		allNotes = append(allNotes, resp.Notes...)

		// 如果当前页返回数量小于请求量，说明已获取全部数据
		if len(resp.Notes) < perPage {
			break
		}

		// 更新下一页参数
		opts.Page++
	}

	ctx.GetLogger().Info("successfully listed all notes", "mrId", mergeRequestId, "projectId", projectId, "count", len(allNotes))
	return allNotes, nil
}

func convertPositionOption(ctx base.Context, position *codeplatform.CodePosition, mr *sdk.MergeRequest) (*sdk.PositionOptions, error) {
	opts := &sdk.PositionOptions{
		PositionType: sdk.Ptr("text"), // 仅支持代码块评论，不支持代码文件评论，有版本兼容问题
		BaseSHA:      &mr.DiffRefs.BaseSha,
		StartSHA:     &mr.DiffRefs.StartSha,
		HeadSHA:      &mr.DiffRefs.HeadSha,
	}

	if position.Path == nil || position.EndLine == nil {
		ctx.GetLogger().Error("path or endLine is nil", "position", position)
		return nil, fmt.Errorf("path or endLine is nil")
	}

	for _, c := range mr.Changes {
		if c.NewPath == *position.Path {
			opts.NewPath = &c.NewPath
			break
		}
	}
	// 生成的path不对
	if opts.NewPath == nil {
		ctx.GetLogger().Error("failed to find target path", "path", *position.Path)
		return nil, fmt.Errorf(fmt.Sprintf("failed to find target path: %s", *position.Path))
	}

	if position.StartLine == nil {
		// 如果 start line 为空，默认为 end line
		position.StartLine = position.EndLine
	}
	opts.NewLine = position.EndLine

	// 评论到多行，需要指定 LineRange
	if *position.StartLine != *position.EndLine {

		var fileDiff string
		for _, change := range mr.Changes {
			if change.NewPath == *position.Path {
				fileDiff = change.Diff
				break
			}
		}
		// 建立新行到旧行的映射
		lineMapping := parseDiffForLineMapping(fileDiff)
		fileNameHash := getSHA1Hash(*position.Path)

		opts.LineRange = &sdk.LineRangeOptions{
			Start: &sdk.LinePositionOptions{
				Type:     sdk.Ptr("new"), // 只支持评论新行
				LineCode: sdk.Ptr(generateLineCode(lineMapping, fileNameHash, *position.StartLine)),
			},
			End: &sdk.LinePositionOptions{
				Type:     sdk.Ptr("new"),
				LineCode: sdk.Ptr(generateLineCode(lineMapping, fileNameHash, *position.EndLine)),
			},
		}
	}
	return opts, nil
}

// parseDiffForLineMapping 解析git diff内容，生成新行到旧行的映射关系
func parseDiffForLineMapping(diff string) map[int]int {
	lineMapping := make(map[int]int)
	re := regexp.MustCompile(`(?m)^@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@.*\n([^@]*)`)

	matches := re.FindAllStringSubmatch(diff, -1)
	for _, match := range matches {
		oldStart, _ := strconv.Atoi(match[1])
		newStart, _ := strconv.Atoi(match[2])
		lines := strings.Split(match[3], "\n")

		currentOld := oldStart
		currentNew := newStart
		var lastDeleted *int

		for _, line := range lines {
			if line == "" {
				continue
			}

			switch line[0] {
			case ' ':
				lineMapping[currentNew] = currentOld
				currentOld++
				currentNew++
				lastDeleted = nil
			case '+':
				if lastDeleted != nil {
					lineMapping[currentNew] = *lastDeleted
				} else {
					lineMapping[currentNew] = 0
				}
				currentNew++
			case '-':
				lastDeleted = &currentOld
				currentOld++
			}
		}
	}
	return lineMapping
}

// generateLineCode A line code is of the form <SHA>_<old>_<new>, like this: adc83b19e793491b1c6ea0fd8b46cd9f32e292fc_5_5
func generateLineCode(lineMapping map[int]int, fileSha string, newLine int) string {
	var lineCode string
	if oldLine, exists := lineMapping[newLine]; exists {
		lineCode = fmt.Sprintf("%s_%d_%d", fileSha, oldLine, newLine)
	} else {
		// 新文件的新增行，oldLine 为 0
		lineCode = fmt.Sprintf("%s_0_%d", fileSha, newLine)
	}
	return lineCode
}

func getSHA1Hash(input string) string {
	hasher := sha1.New()
	hasher.Write([]byte(input))

	return fmt.Sprintf("%x", hasher.Sum(nil))
}
