package agent_connect

import (
	"context"
	"errors"
	"github.com/redis/go-redis/v9"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"sync"
	"time"
)

type clientSession struct {
	AgentId   string
	SessionId string
	Conn      *http.SafeConn

	started  bool
	stopChan chan bool
	lock     *sync.Mutex
}

func (c *clientSession) Start(ctx base.Context) {
	c.lock.Lock()
	defer c.lock.Unlock()
	c.started = true
	c.stopChan = make(chan bool)
	l := ctx.GetLogger().With("sessionId", c.SessionId, "agentId", c.SessionId, "connectId", c.Conn.ConnectId)
	redisCtx, cancel := context.WithCancel(ctx)

	go func() {
		select {
		case <-c.stopChan:
			cancel()
		}
	}()

	// 监听循环
	go util.SafeGoroutineFunc(ctx, func() {
		for {
			if !c.started {
				l.Error("client session has been stopped")
				return
			}

			// 这里不用 BRPop 长连接，节省连接池开销
			res := helper.GetRedis().RPop(redisCtx, getSessionReqQueue(c.SessionId))
			//res := helper.GetRedis().BRPop(redisCtx, 1*time.Second, getSessionReqQueue(c.SessionId))
			if res.Err() != nil {
				if errors.Is(res.Err(), redis.Nil) {
					time.Sleep(time.Millisecond * 100)
					continue
				}
				l.Error("fail to pull message from redis", "err", res.Err())
				time.Sleep(3 * time.Second)
				continue
			}
			err := c.Conn.WriteText(res.Val())
			if err != nil {
				l.Error("fail to write message", "err", err, "message", res.Val()[1])
			}
		}
	})
}

func (c *clientSession) Stop() {
	c.lock.Lock()
	defer c.lock.Unlock()
	if c.started {
		c.started = false
		close(c.stopChan)
	}
}
