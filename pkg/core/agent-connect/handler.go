package agent_connect

import (
	"encoding/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/agent-connect/message"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/agent-connect/message/gitlab"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
)

func (s *service) handleResponse(ctx base.Context, conn *http.SafeConn, agentId string, msg agent_connect.Message) {
	pr := helper.GetRedis().LPush(ctx, getRequestResQueue(msg.GetRequestId()), msg.ToString())
	if pr.Err() != nil {
		ctx.GetLogger().Error("fail to send response to request queue", "err", pr.Err())
	}
}
func (s *service) handleRestoreSession(ctx base.Context, conn *http.SafeConn, agentId string, msg agent_connect.Message) {
	l := ctx.GetLogger().With("agentId", agentId)
	request := &message.RestoreSessionRequest{}
	err := json.Unmarshal(msg.GetPayload(), request)
	if err != nil {
		l.Error("malformed restore session message, skipped", "message", msg.GetPayload())
		s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), nil, nil, "malformed restore session message message"))
		return
	}

	if len(request.Sessions) == 0 {
		s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), nil, nil, "no session to restore"))
		return
	}

	var iSessions, rSessions []string
	for _, sid := range request.Sessions {
		sess, err := comncore.GetCoreService().GetSessionService().GetSession(ctx, sid)
		if err != nil {
			l.Error("fail to get session", "sessionId", sid)
			iSessions = append(iSessions, sid)
		} else if sess == nil {
			iSessions = append(iSessions, sid)
		} else {
			idInfo, err := comncore.GetCoreService().GetIdentityService().GetIdentity(ctx, sess.IdentityId)
			if err != nil {
				l.Error("fail to fetch session", "err", err)
				s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), nil, nil, "fail to fetch session"))
				return
			}

			if idInfo.AgentId != agentId {
				l.Error("attempt to restore session which is not belong to the agent, ignored")
				iSessions = append(iSessions, sid)
			} else if sess.State == session.Abort || sess.State == session.Finished {
				iSessions = append(iSessions, sid)
			} else {
				s.sessionMgr.AddSession(ctx, agentId, sid, conn)
				rSessions = append(rSessions, sid)
				l.Info("session restored", "sessionId", sid, "connectId", conn.ConnectId)
			}
		}
	}

	s.sessionMgr.SendToConn(ctx, conn, message.NewRestoreSessionResponse(msg.GetRequestId(), rSessions, iSessions, ""))
}

func (s *service) handleWebhookInvocation(ctx base.Context, conn *http.SafeConn, agentId string, msg agent_connect.Message) {
	l := ctx.GetLogger().With("agentId", agentId)
	request := &gitlab.WebhookInvocationRequest{}
	err := json.Unmarshal(msg.GetPayload(), request)
	if err != nil {
		l.Error("malformed webhook message, skipped", "message", msg.GetPayload())
		s.sessionMgr.SendToConn(ctx, conn, gitlab.NewWebhookInvocationResponse(msg.GetRequestId(), "", "malformed webhook message"))
		return
	}

	switch request.CodePlatform {
	case codeplatform.PlatformGitlab:
		sid, err := comncore.GetCoreService().GetWebhookService().GitlabWebhook(ctx, request.Token, request.Payload)
		if err != nil {
			l.Error("fail to trigger gitlab webhook", "err", err)
			s.sessionMgr.SendToConn(ctx, conn, gitlab.NewWebhookInvocationResponse(msg.GetRequestId(), "", "fail to trigger webhook"))
			return
		}
		// webhook 会产生新的 session
		if sid != "" {
			s.sessionMgr.AddSession(ctx, agentId, sid, conn)
		}
		s.sessionMgr.SendToConn(ctx, conn, gitlab.NewWebhookInvocationResponse(msg.GetRequestId(), sid, ""))
	default:
		l.Error("unsupported code platform", "platform", request.CodePlatform)
		s.sessionMgr.SendToConn(ctx, conn, gitlab.NewWebhookInvocationResponse(msg.GetRequestId(), "", "unsupported code platform: "+string(request.CodePlatform)))
	}
}

func (s *service) handleSyncUsername(ctx base.Context, conn *http.SafeConn, agentId string, msg agent_connect.Message) {
	l := ctx.GetLogger().With("agentId", agentId)
	request := &gitlab.SyncUsernameRequest{}
	err := json.Unmarshal(msg.GetPayload(), request)
	if err != nil {
		l.Error("malformed sync username message, skipped", "message", msg.GetPayload())
		return
	}

	if request.Token == "" || request.Username == "" {
		l.Error("token or username is missing for sync username")
		s.sessionMgr.SendToConn(ctx, conn, agent_connect.NewClientRequestError(msg.GetRequestId(), "token is missing"))
		return
	}

	idSrv := comncore.GetCoreService().GetIdentityService()

	idInfo, err := idSrv.GetIdentityByToken(ctx, request.Token)
	if err != nil {
		l.Error("fail to find id with token", "err", err)
		s.sessionMgr.SendToConn(ctx, conn, agent_connect.NewClientRequestError(msg.GetRequestId(), "fail to find token"))
		return
	}

	if idInfo.PlatformUsername != request.Username {
		if err = idSrv.UpdateIdentity(ctx, idInfo.IdentityId, &identity.UpdateIdentityRequest{PlatformUsername: &request.Username}); err != nil {
			l.Error("fail to find id with token", "err", err)
			s.sessionMgr.SendToConn(ctx, conn, agent_connect.NewClientRequestError(msg.GetRequestId(), "fail to sync username"))
			return
		}
	}

	s.sessionMgr.SendToConn(ctx, conn, gitlab.NewSyncUsernameResponse(msg.GetRequestId(), msg.GetSessionId(), nil))
}
