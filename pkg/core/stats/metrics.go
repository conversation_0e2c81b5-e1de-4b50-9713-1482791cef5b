package stats

import (
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/sql"
)

var (
	metrics = []prometheus.Collector{
		SessionStatusGauge,
		AgentTaskStatusGauge,
		AgentTypeGauge,
		IdentitySourceGauge,
		WebhookAccessGauge,
	}
)

var (
	SessionStatusGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "session",
			Name:      "status_count",
			Help:      "session status",
		},
		[]string{"status"},
	)
	AgentTaskStatusGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "agent",
			Name:      "task_status_count",
			Help:      "agent task status",
		},
		[]string{"status"},
	)
	AgentTypeGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "agent",
			Name:      "count",
			Help:      "agent count",
		},
		[]string{"type"},
	)
	IdentitySourceGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "identity",
			Name:      "source_count",
			Help:      "identity source count",
		},
		[]string{"source"},
	)
	WebhookAccessGauge = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: "webhook",
			Name:      "access_count",
			Help:      "webhook access count",
		},
		[]string{"source", "event_type"},
	)
)

func singleLabelMetricScanFunc(name string, sql sql.Template[[]singleLabelCount], metric *prometheus.GaugeVec) func(base.Context) error {
	return func(ctx base.Context) error {
		return scanSingleLabelMetrics(ctx, name, sql, metric)
	}
}
func twoLabelMetricScanFunc(name string, sql sql.Template[[]twoLabelCount], metric *prometheus.GaugeVec) func(base.Context) error {
	return func(ctx base.Context) error {
		return scanTwoLabelMetrics(ctx, name, sql, metric)
	}
}

func scanSingleLabelMetrics(ctx base.Context, name string, sql sql.Template[[]singleLabelCount], metric *prometheus.GaugeVec) error {
	results, err := dao.RawScan(ctx, sql)
	if err != nil {
		errors.AlertError(ctx, errors.AlertScopeMetrics, []string{name}, "scan stats error", err)
		return err
	}
	for _, r := range results {
		metric.WithLabelValues(r.Label).Set(float64(r.Count))
	}
	return nil
}

func scanTwoLabelMetrics(ctx base.Context, name string, sql sql.Template[[]twoLabelCount], metric *prometheus.GaugeVec) error {
	results, err := dao.RawScan(ctx, sql)
	if err != nil {
		errors.AlertError(ctx, errors.AlertScopeMetrics, []string{name}, "scan stats error", err)
		return err
	}
	for _, r := range results {
		metric.WithLabelValues(r.Label1, r.Label2).Set(float64(r.Count))
	}
	return nil
}
