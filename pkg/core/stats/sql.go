package stats

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/sql"

type singleLabelCount struct {
	Label string `gorm:"column:label"`
	Count int    `gorm:"column:count"`
}

type twoLabelCount struct {
	Label1 string `gorm:"column:label1"`
	Label2 string `gorm:"column:label2"`
	Count  int    `gorm:"column:count"`
}

var (
	SqlSessionStatusCount = sql.Template[[]singleLabelCount]{
		Sql: "select count(*) as \"count\", \"session_state\" as \"label\" from t_session group by \"session_state\" ",
	}
	SqlAgentTaskStateCount = sql.Template[[]singleLabelCount]{
		Sql: "select count(*) as \"count\", \"state\" as \"label\" from t_agent_task group by \"state\"",
	}
	SqlAgentTypeCount = sql.Template[[]singleLabelCount]{
		Sql: "select count(*) as \"count\", \"agent_name\" as \"label\" from t_agent group by \"agent_name\"",
	}
	SqlIdentitySourceCount = sql.Template[[]singleLabelCount]{
		Sql: "SELECT count(*) as \"count\", \"source\" as \"label\" FROM \"t_identity\" group by \"source\" ",
	}
	SqlWebhookAccessCount = sql.Template[[]twoLabelCount]{
		Sql: "select count(*), \"source\" as label1, \"event_type\" as label2 from \"t_webhook_access\"  group by \"source\", \"event_type\"",
	}
)
