package stats

import (
	"context"
	errorswrapper "github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commoncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/cronjob"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"log/slog"
	"time"
)

var _ commoncore.StatsService = (*statsService)(nil)

type statsService struct {
	core    commoncore.Core
	started chan struct{}
	ready   bool
}

func (s *statsService) Start(ctx context.Context) error {
	defer close(s.started)
	<-s.core.GetCronjobService().Started()

	// 每分钟执行一次统计任务
	if err := s.core.GetCronjobService().RegisterJob(cronjob.NewJob(
		cronjob.WithSingletonMode(false),
		cronjob.WithName("stats.GatherStats"),
		cronjob.WithCronExpression("0 * * * * *"),
		cronjob.WithTask(s.GatherAllStats),
		cronjob.WithLockerRetries(0),
		cronjob.WithLockerTimeout(5*time.Minute),
	)); err != nil {
		return errorswrapper.WithStack(err)
	}

	s.ready = true
	return nil
}

func (s *statsService) Started() <-chan struct{} {
	return s.started
}

func (s *statsService) IsReady() bool {
	return s.ready
}

func (s *statsService) Stop() {
	slog.Info("service is stopped", "service", s.GetName())
}

func (s *statsService) Metrics() []prometheus.Collector {
	return metrics
}

func (s *statsService) GetName() string {
	return "statsService"
}

func (s *statsService) Register(core commoncore.Core) {
	s.core = core
}

func (s *statsService) GatherAllStats() error {
	ctx := base.NewContextForAdmin(helper.NewIDWithPrefix("GatherStats-"), "internal|GatherStats")
	ctx.GetLogger().Info("start to gather stats")
	funcs := []func(ctx base.Context) error{
		singleLabelMetricScanFunc("session-status", SqlSessionStatusCount, SessionStatusGauge),
		singleLabelMetricScanFunc("agent-task-status", SqlAgentTaskStateCount, AgentTaskStatusGauge),
		singleLabelMetricScanFunc("agent-type", SqlAgentTypeCount, AgentTypeGauge),
		singleLabelMetricScanFunc("identity-source", SqlIdentitySourceCount, IdentitySourceGauge),
		twoLabelMetricScanFunc("webhook-access", SqlWebhookAccessCount, WebhookAccessGauge),
	}

	for _, f := range funcs {
		_ = f(ctx)
	}

	ctx.GetLogger().Info("gather stats finished")
	return nil
}

func New() commoncore.StatsService {
	return &statsService{
		started: make(chan struct{}),
		ready:   false,
	}
}
