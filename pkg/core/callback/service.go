package callback

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
	agent2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"strconv"
	"strings"
	"time"

	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	extgithub "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/github"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/ram_oauth"
	redishelper "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
)

var _ comncore.CallbackService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}
func (s *service) GetName() string {
	return "CallbackService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func (s *service) GithubOAuthCallback(ctx base.Context, appId int64, code string, installationId int64, setupAction string, state string) (string, error) {
	ctx.GetLogger().Info("GithubOAuthCallback", "code", code, "installationId", installationId, "setupAction", setupAction, "state", state)
	//清矢
	//后续如果产品交互改变，身份关联不通过ram Oauth的话， 要从identity表查有效安装数
	validInstallationNumber, err := s.GetValidInstallationCount(ctx)
	if err != nil {
		return "", errors.WithStack(err)
	}
	installationLimit := config.GetOrDefaultInt64(config.KeyGithubAppInstallationLimit, 100)
	if validInstallationNumber >= installationLimit {
		ctx.GetLogger().Error("installation number exceeds limit", "installation number", validInstallationNumber, "limit", installationLimit)
		return "", commonerrors.New(codes.ErrInstallationNumberExceedsLimit)
	}
	if setupAction == "" && installationId == 0 && state != "" {
		ctx.GetLogger().Info("user link installation to aliyun identity")
		var err error
		installationId, err = cast.ToInt64E(state)
		if err != nil {
			ctx.GetLogger().Error("state convert to installationId failed", "state", state, "err", err)
			return "", errors.WithStack(err)
		}
	} else if setupAction != "install" {
		ctx.GetLogger().Info("setupAction is not installation, ignore")
		return "", nil
	}

	user, err := extgithub.GetGithubUserInfoByAuthCode(ctx, appId, code)
	if err != nil {
		ctx.GetLogger().Error("GetGithubUserInfoByAuthCode failed", "authorization code", code, "err", err)
		return "", errors.WithStack(err)
	}
	installationOwner, err := extgithub.GetGithubUserInfoByInstallationId(ctx, appId, installationId)
	if err != nil {
		ctx.GetLogger().Error("GetGithubUserInfoByInstallationId failed", "installationId", installationId, "err", err)
		return "", errors.WithStack(err)
	}
	switch *installationOwner.Type {
	case "User":
		if *installationOwner.ID != *user.ID {
			//请求者不是安装拥有者，拒绝关联
			ctx.GetLogger().Error("caller is not the owner of  installation", "caller", user, "installation owner id", installationOwner)
			return "", commonerrors.New(codes.ErrInvalidParameter, "installationId", installationId)
		}
	case "Organization":
		match, err := extgithub.UserIsOrganizationMember(ctx, appId, installationId, *user.Login, *installationOwner.Login)
		if err != nil {
			ctx.GetLogger().Error("check UserIsOrganizationMember failed", "installationOwner", installationOwner, "user", user, "err", err)
			return "", commonerrors.New(codes.ServerInternalError)
		}
		if !match {
			ctx.GetLogger().Error("caller is not the member of  installation owner organization", "caller", user, "installation owner id", installationOwner)
			return "", commonerrors.New(codes.ErrInvalidParameter, "installationId", installationId)
		}
	}

	// 生成随机字符串作为 key
	randomKey := uuid.New().String()
	key := "RamOauth-" + randomKey
	// 将随机字符串存储到 Redis 中
	redisClient := redishelper.GetRedis()
	if redisClient == nil {
		ctx.GetLogger().Error("Redis client is not initialized")
		return "", errors.New("redis client is not initialized")
	}
	value := s.generateInstallationInfo(*installationOwner.Type, *installationOwner.Login, installationId)
	err = redisClient.Set(ctx, key, value, 10*time.Minute).Err()
	if err != nil {
		ctx.GetLogger().Error("Failed to store key in Redis", "key", key, "err", err)
		return "", errors.WithStack(err)
	}
	ctx.GetLogger().Info("redis store Key", "key", key, "installationId:", installationId)
	callbackURL := ram_oauth.GetRamCallbackUrl(ctx, key)
	ctx.GetLogger().Info("Generated callback URL", "callbackURL", callbackURL)
	return callbackURL, nil
}

func (s *service) generateInstallationInfo(accountType string, name string, installationId int64) string {
	// 使用 "-" 连接账号类型、名字和安装 ID
	return fmt.Sprintf("%s-%s-%d", accountType, name, installationId)
}

func (s *service) parseInstallationInfo(ctx base.Context, installationInfo string) (string, string, int64, error) {
	// 使用 "-" 分割字符串
	parts := strings.Split(installationInfo, "-")
	if len(parts) < 3 {
		ctx.GetLogger().Error("invalid installation info format", "installationInfo", installationInfo)
		return "", "", 0, errors.New("invalid installation info format")
	}

	// 解析账号类型（第一段）
	accountType := parts[0]

	// 解析安装 ID（最后一段）
	installationId, err := strconv.ParseInt(parts[len(parts)-1], 10, 64)
	if err != nil {
		ctx.GetLogger().Error("failed to parse installation ID", "installationInfo", installationInfo, "err", err)
		return "", "", 0, errors.WithStack(err)
	}

	// 解析名字（中间部分，可能包含多个 "-"）
	name := strings.Join(parts[1:len(parts)-1], "-")

	return accountType, name, installationId, nil
}

func (s *service) RamOAuthCallback(ctx base.Context, ramOauthAppId string, code string, state string, errorMessage string) (string, error) {
	redisClient := redishelper.GetRedis()
	if redisClient == nil {
		ctx.GetLogger().Error("Redis client is not initialized")
		return "", errors.New("redis client is not initialized")
	}
	ctx.GetLogger().Info("state:", state)
	typeAndInstallationId, err := redisClient.Get(ctx, state).Result()
	if err == redis.Nil {
		ctx.GetLogger().Error("Key does not exist", "key", state, "err", err)
		return "", errors.WithStack(err)
	} else if err != nil {
		ctx.GetLogger().Error("Failed to query key in Redis", "key", state, "err", err)
		return "", errors.WithStack(err)
	}
	accountType, name, installationId, err := s.parseInstallationInfo(ctx, typeAndInstallationId)
	if err != nil {
		ctx.GetLogger().Error("Failed to parse installation info", "installationInfo", typeAndInstallationId, "err", err)
		return "", errors.WithStack(err)
	}
	if errorMessage == "access_denied" {
		ctx.GetLogger().Info("User denied access", "installationId", installationId)
		res := fmt.Sprintf("https://github.com/settings/installations/%d", installationId)
		if accountType == "Organization" {
			res = fmt.Sprintf("https://github.com/organizations/%s/settings/installations/%d", name, installationId)
		}
		return res, nil
	} else if errorMessage != "" {
		ctx.GetLogger().Error("RamOauthCallback failed", "errorMessage", errorMessage, "installationId", installationId)
		return "", errors.New(fmt.Sprintf("RamOauthCallback failed, errorMessage: %s", errorMessage))
	}
	user, err := ram_oauth.QueryUserInfoByAuthCode(ctx, ramOauthAppId, code)
	userId := user.Aid
	loginUserId := user.Uid
	if loginUserId == "" {
		loginUserId = userId
	}
	loginUserType := user.Type
	entity, err := dao.GetEntity(ctx, &GithubInstallationIdentityEntity{}, dao.NewDbOptions(withInstallationId(installationId)))
	if commonerrors.Is(err, codes.ErrRecordNotFound) {
		ctx.GetLogger().Info("GithubInstallationIdentityEntity not found, create new one", "installationId", installationId)
		entity = &GithubInstallationIdentityEntity{
			InstallationId: installationId,
			UserId:         userId,
			LoginUserId:    loginUserId,
			LoginUserType:  loginUserType,
		}
		err = dao.InsertEntity(ctx, entity)
		if err != nil {
			ctx.GetLogger().Error("Insert GithubInstallationIdentityEntity failed", "err", err)
			return "", errors.WithStack(err)
		}
	} else if err != nil {
		ctx.GetLogger().Error("Get GithubInstallationIdentityEntity failed", "err", err)
		return "", errors.WithStack(err)
	} else {
		entity.UserId = userId
		entity.LoginUserType = loginUserType
		entity.LoginUserId = loginUserId
		err = dao.UpdateEntity(ctx, entity, dao.NewDbOptions(withInstallationId(installationId)))
		if err != nil {
			ctx.GetLogger().Error("Update GithubInstallationIdentityEntity failed", "err", err)
			return "", errors.WithStack(err)
		}
	}
	idInfo, err := comncore.GetCoreService().GetIdentityService().GetIdentityByGithubInstallationId(ctx, installationId)
	if err != nil && commonerrors.Is(err, codes.ErrRecordNotFound) {
		ctx.GetLogger().Error("identity has not created, ignore", "installationId", installationId, "userId", userId)
	} else if err != nil {
		ctx.GetLogger().Error("GetIdentityByGithubInstallationId failed", "installationId", installationId, "userId", userId, "err", err)
		return "", errors.WithStack(err)
	} else {
		if idInfo.UserId == "" {
			return "", commonerrors.New(codes.ErrInvalidParameter, "user_id is required")
		}
		err = comncore.GetCoreService().GetAgentService().UpdateAgent(ctx, idInfo.AgentId, &agent2.UpdateAgentRequest{UserId: &userId})
		if err != nil {
			ctx.GetLogger().Error("UpdateAgent failed", "installationId", installationId, "agentId", idInfo.AgentId, "userId", userId, "err", err)
			return "", errors.WithStack(err)
		}
		err = comncore.GetCoreService().GetIdentityService().UpdateIdentityByGithubInstallationId(ctx, installationId, &identity.UpdateIdentityRequest{UserId: &userId})
		if err != nil {
			ctx.GetLogger().Error("UpdateIdentityByGithubInstallationId failed", "installationId", installationId, "userId", userId, "err", err)
			return "", errors.WithStack(err)
		}
	}
	ctx.GetLogger().Info("installation attach to user success ", "installationId", installationId, "userInfo", user)
	res := fmt.Sprintf("https://github.com/settings/installations/%d", installationId)
	if accountType == "Organization" {
		res = fmt.Sprintf("https://github.com/organizations/%s/settings/installations/%d", name, installationId)
	}
	return res, nil
}

func (s *service) GetUserIdByGithubInstallationId(ctx base.Context, installationId int64) (*string, error) {
	entity, err := dao.GetEntity(ctx, &GithubInstallationIdentityEntity{}, dao.NewDbOptions(withInstallationId(installationId)))
	if err != nil && commonerrors.Is(err, codes.ErrRecordNotFound) {
		return nil, errors.WithStack(err)
	}
	return &entity.UserId, nil
}

func (s *service) GetValidInstallationCount(ctx base.Context) (int64, error) {
	count, err := dao.Count(ctx, &GithubInstallationIdentityEntity{}, &dao.DbOptions{})
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return count, nil
}

func (s *service) DeleteInstallationBindingIfExists(ctx base.Context, installationId int64) error {
	err := dao.DeleteEntities(ctx, &GithubInstallationIdentityEntity{}, dao.NewDbOptions(withInstallationId(installationId)))
	if err != nil && commonerrors.Is(err, codes.ErrRecordNotFound) {
		return nil // 记录不存在，无需处理
	}
	return errors.WithStack(err)
}

func New() comncore.CallbackService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
