package callback

import "time"

type GithubInstallationIdentityEntity struct {
	ID             int64     `gorm:"column:id;primaryKey"`
	InstallationId int64     `gorm:"column:installation_id"`
	UserId         string    `gorm:"column:user_id"`
	LoginUserType  string    `gorm:"column:login_user_type"`
	LoginUserId    string    `gorm:"column:login_user_id"`
	GmtCreate      time.Time `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified    time.Time `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (*GithubInstallationIdentityEntity) TableName() string {
	return "t_github_installation_identity"
}
