package identity

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	event2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/code_aone"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/codeup"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/gitlab"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/lock"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
	"time"
)

var _ comncore.IdentityService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) GetIdentity(ctx base.Context, identityId string) (*identity.IdentityInfo, error) {
	entity, err := dao.GetEntity(ctx, &identityEntity{}, dao.NewDbOptions(withIdentityId(identityId)))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToIdentityInfo(), nil
}

func (s *service) GetGitToken(ctx base.Context, identityId *string, idInfo *identity.IdentityInfo) (string, error) {
	if idInfo == nil && identityId == nil {
		return "", commonerrors.New(codes.ErrInvalidParameterWithEmpty, "identityId or idInfo")
	}
	if idInfo == nil {
		if info, err := s.GetIdentity(ctx, *identityId); err != nil {
			return "", errors.WithStack(err)
		} else {
			idInfo = info
		}
	}
	switch idInfo.Source {
	case identity.SourceGitLab:
		return idInfo.PlatformToken, nil
	case identity.SourceCodeup:
		cli := codeup.NewClient(idInfo.PlatformProperty.CodeupAppId)
		resp, err := cli.GetAppToken(ctx, idInfo.PlatformProperty.CodeupOrganizationId)
		if err != nil {
			return "", errors.WithStack(err)
		} else {
			return resp.Token, nil
		}
	}
	return idInfo.PlatformToken, nil
}

func (s *service) CreateIdentity(ctx base.Context, req *identity.CreateIdentityRequest) (*identity.IdentityInfo, error) {
	username := ""
	switch req.Source {
	case identity.SourceAgentConnect, identity.SourceAiDeveloper:
		break
	case identity.SourceGitLab:
		user, err := gitlab.GetCurrentUser(ctx, req.PlatformEndpoint, req.PlatformToken, req.PlatformProperty)
		if err != nil {
			ctx.GetLogger().Error("failed to get gitlab current user", "endpoint", req.PlatformEndpoint, "token", req.PlatformToken, "err", err)
			return nil, errors.WithStack(err)
		}
		username = user.Username
	case identity.SourceGithub:
		// fixme 同下方改造，创建Agent流程内聚到CreateIdentity里，并且考虑防并发创建（github 通过installation事件，不太可能出现并发问题）
		username = req.PlatformProperty.GithubAppSlug
	case identity.SourceCodeup:
		// codeup 身份系统由webhook事件驱动创建，可能存在并发问题，需要进入临界区后判断是否存在
		orgId := req.PlatformProperty.CodeupOrganizationId
		appId := req.PlatformProperty.CodeupAppId
		lockName := fmt.Sprintf("create-identity-codeup-org-%s-%d", appId, orgId)
		lockFunc, unlockFunc := lock.NewLockFunc(ctx, lockName)
		if err := lockFunc(); err != nil {
			return nil, errors.WithStack(err)
		} else {
			defer unlockFunc()
		}
		if idInfo, err := s.GetIdentityByCodeupOrganizationIdAndAppId(ctx, orgId, appId); err == nil {
			return idInfo, nil
		} else if !commonerrors.Is(err, codes.ErrRecordNotFound) {
			return nil, errors.WithStack(err)
		}
		if req.AgentId == "" {
			// 创建Agent
			if agentInfo, err := s.core.GetAgentService().CreateAgent(ctx, &agent.CreateAgentRequest{AgentName: agent.CodeReviewAgent}); err != nil {
				return nil, errors.WithStack(err)
			} else {
				req.AgentId = agentInfo.AgentId
			}
		}
		// bot name
		username = config.GetCodeupAppName(appId)
	case identity.SourceCodeaone:
		user, err := code_aone.GetAuthenticatedUser(ctx, req.PlatformEndpoint, req.PlatformToken)
		if err != nil {
			ctx.GetLogger().Error("failed to get codeaone current user", "endpoint", req.PlatformEndpoint, "token", req.PlatformToken, "err", err)
			return nil, commonerrors.New(codes.ErrInvalidParameterWithDetail, "codeaone endpoint and access token", "get current user info failed")
		}
		username = user.UserName
	}

	if _, err := s.core.GetAgentService().GetAgent(ctx, req.AgentId); err != nil {
		return nil, errors.WithStack(err)
	}

	entity := &identityEntity{
		IdentityId:       helper.NewIDWithPrefix("id-"),
		PlatformEndpoint: req.PlatformEndpoint,
		PlatformToken:    dao.AESStringField(req.PlatformToken),
		PlatformUsername: username,
		PlatformProperty: req.PlatformProperty,
		Source:           req.Source,
		UserId:           ctx.GetUid(),
		AgentId:          req.AgentId,
		WebhookToken:     helper.NewIDWithPrefix("tkn-"),
		Description:      req.Description,
	}
	if req.Source == identity.SourceAiDeveloper {
		entity.UserId = ctx.GetLoginUid()
	}

	if err := dao.InsertEntity(ctx, entity); err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToIdentityInfo(), nil
}

func (s *service) DeleteIdentity(ctx base.Context, identityId string) error {
	if err := dao.DeleteEntities(ctx, &identityEntity{}, dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withIdentityId(identityId))); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) UpdateIdentity(ctx base.Context, identityId string, req *identity.UpdateIdentityRequest) error {
	entity, err := dao.GetEntity(ctx, &identityEntity{}, dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withIdentityId(identityId)))
	if err != nil {
		return errors.WithStack(err)
	}
	identityIds := []string{identityId}
	if util.GetBoolOrDefault(req.SyncAssociated, false) {
		// 获取同一个Agent下的所有Identity记录，然后用AccessToken匹配
		listOpt := dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), dao.WithKV("agent_id", entity.AgentId), dao.WithSelect("identity_id,platform_token"))
		idAndTokenOnly, _, err := dao.ListEntitiesAutoPage(ctx, &identityEntity{}, listOpt)
		if err != nil {
			return errors.WithStack(err)
		}
		for _, e := range idAndTokenOnly {
			if e.PlatformToken != entity.PlatformToken {
				continue
			}
			if e.IdentityId == identityId {
				continue
			}
			identityIds = append(identityIds, e.IdentityId)
		}
	}

	update := &identityEntity{GmtModified: time.Now()}
	if req.PlatformToken != nil {
		switch entity.Source {
		// 校验Token有效性
		case identity.SourceGitLab:
			user, err := gitlab.GetCurrentUser(ctx, entity.PlatformEndpoint, *req.PlatformToken, entity.PlatformProperty)
			if err != nil {
				ctx.GetLogger().Error("failed to get gitlab current user", "endpoint", entity.PlatformEndpoint, "token", *req.PlatformToken, "err", err)
				return commonerrors.New(codes.ErrInvalidParameterWithDetail, "PlatformToken", "fail to request Gitlab/GetCurrentUser")
			}
			req.PlatformUsername = ptr.To(user.Username)
		default:
			return commonerrors.New(codes.ErrInvalidParameterWithDetail, "identityId", "This identity can not change access token")
		}
		update.PlatformToken = dao.AESStringField(*req.PlatformToken)
	}

	if req.PlatformUsername != nil {
		update.PlatformUsername = *req.PlatformUsername
	}
	if req.Description != nil {
		update.Description = req.Description
	}

	if err := dao.UpdateEntity(ctx, update, dao.NewDbOptions(dao.WithKeyValues("identity_id", identityIds...))); err != nil {
		return errors.WithStack(err)
	}

	if len(identityIds) > 0 && req.PlatformToken != nil {
		for _, id := range identityIds {
			event := event2.NewIdentityUpdatedEvent(ctx, id,
				&event2.IdentityProperty{IdentityId: id, PlatformToken: util.MaskSecret(*req.PlatformToken)},
				&event2.IdentityChanges{PlatformToken: &event2.StringDiff{Before: util.MaskSecret(string(entity.PlatformToken)), After: util.MaskSecret(*req.PlatformToken)}})
			s.core.GetEventService().Audit(ctx, event)
		}
	}

	return nil
}

func (s *service) UpdateIdentityByGithubInstallationId(ctx base.Context, installationId int64, req *identity.UpdateIdentityRequest) error {
	entity, err := dao.GetEntity(ctx, &identityEntity{}, dao.NewDbOptions(withGithubInstallationId(installationId)))
	if err != nil {
		return errors.WithStack(err)
	}

	if req.UserId != nil {
		entity.UserId = *req.UserId
		if err = dao.UpdateEntity(ctx, entity, dao.NewDbOptions(withGithubInstallationId(installationId))); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

func (s *service) ListIdentities(ctx base.Context, options *identity.ListIdentityOptions) (*page.PagedItems[identity.IdentityInfo], error) {
	if options == nil {
		return nil, commonerrors.New(codes.ErrInvalidParameter, "options")
	}
	opt := dao.NewDbOptions(dao.WithUserId(ctx.GetUid()))
	pager := options.PagesParams
	opt.AddPage(dao.WithPage(pager.PageNumber, pager.PageSize))
	opt.AddPage(dao.WithFieldOrderDesc("id"))

	if options.AgentId != "" {
		opt.AddWhere(dao.WithKV("agent_id", options.AgentId))
	}
	if len(options.SourceTypes) > 0 {
		opt.AddWhere(dao.WithKeyValues("source", options.SourceTypes...))
	}

	results, total, err := dao.ListEntitiesWithPage(ctx, &identityEntity{}, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	items := page.TransferSlice(results, func(entity *identityEntity) identity.IdentityInfo {
		return *entity.ToIdentityInfo()
	})
	return &page.PagedItems[identity.IdentityInfo]{
		Items:      items,
		PageNumber: options.PagesParams.PageNumber,
		PageSize:   options.PagesParams.PageSize,
		TotalSize:  total,
	}, nil
}

func (s *service) GetIdentityByToken(ctx base.Context, token string) (*identity.IdentityInfo, error) {
	entity, err := dao.GetEntity(ctx, &identityEntity{}, dao.NewDbOptions(withWebhookToken(token)))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToIdentityInfo(), nil
}

func (s *service) GetIdentityByGithubInstallationId(ctx base.Context, installationId int64) (*identity.IdentityInfo, error) {
	entity, err := dao.GetEntity(ctx, &identityEntity{}, dao.NewDbOptions(withGithubInstallationId(installationId)))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToIdentityInfo(), nil
}

func (s *service) GetIdentityByCodeupOrganizationIdAndAppId(ctx base.Context, orgId, appId string) (*identity.IdentityInfo, error) {
	source := string(identity.SourceCodeup)
	entity, err := dao.GetEntity(ctx, &identityEntity{}, dao.NewDbOptions(withCodeupOrganizationIdId(orgId), withSource(source), withCodeupAppId(appId)))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToIdentityInfo(), nil
}

func (s *service) GetAiDeveloperIdentityByLoginUserId(ctx base.Context) (*identity.IdentityInfo, error) {
	source := string(identity.SourceAiDeveloper)
	entity, err := dao.GetEntity(ctx, &identityEntity{}, dao.NewDbOptions(withUserId(ctx.GetLoginUid()), withSource(source)))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToIdentityInfo(), nil
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "IdentityService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.IdentityService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
