package identity

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"

func withIdentityId(id string) dao.DbWhereOption {
	return dao.WithKV("identity_id", id)
}

func withGithubInstallationId(installationId int64) dao.DbWhereOption {
	return dao.WithKV("platform_property->'githubInstallationId'", installationId)
}

func withUserId(userId string) dao.DbWhereOption {
	return dao.WithKV("user_id", userId)
}
func withSource(source string) dao.DbWhereOption {
	return dao.WithKV("source", source)
}

func withCodeupOrganizationIdId(orgId string) dao.DbWhereOption {
	return dao.WithKV("platform_property->>'codeupOrganizationId'", orgId)
}

func withCodeaoneUserId(orgId string) dao.DbWhereOption {
	return dao.WithKV("platform_property->>'codeaoneUserId'", orgId)
}

func withCodeupAppId(appId string) dao.DbWhereOption {
	return dao.WithKV("platform_property->>'codeupAppId'", appId)
}
