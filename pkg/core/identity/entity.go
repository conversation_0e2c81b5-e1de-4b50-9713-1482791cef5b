package identity

import (
	"fmt"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"net/url"
	"time"
)

type identityEntity struct {
	ID               int64                      `gorm:"column:id;primaryKey"`
	IdentityId       string                     `gorm:"column:identity_id"`
	PlatformEndpoint string                     `gorm:"column:platform_endpoint"`
	PlatformUsername string                     `gorm:"column:platform_username"`
	PlatformToken    dao.AESStringField         `gorm:"column:platform_token"`
	PlatformProperty *identity.PlatformProperty `gorm:"column:platform_property;serializer:json"`
	Source           identity.Source            `gorm:"column:source"`
	UserId           string                     `gorm:"column:user_id"`
	WebhookToken     string                     `gorm:"column:webhook_token"`
	AgentId          string                     `gorm:"column:agent_id"`
	GmtCreate        time.Time                  `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified      time.Time                  `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
	Description      *string                    `gorm:"column:description"`
}

func (*identityEntity) TableName() string {
	return "t_identity"
}

func (ie *identityEntity) ToIdentityInfo() *identity.IdentityInfo {
	info := &identity.IdentityInfo{
		AgentId:          ie.AgentId,
		CreateTimestamp:  ie.GmtCreate.UnixMilli(),
		IdentityId:       ie.IdentityId,
		PlatformEndpoint: ie.PlatformEndpoint,
		PlatformUsername: ie.PlatformUsername,
		PlatformToken:    string(ie.PlatformToken),
		UserId:           ie.UserId,
		Source:           ie.Source,
		WebhookToken:     ie.WebhookToken,
		PlatformProperty: ie.PlatformProperty,
		Description:      util.GetString(ie.Description),
	}
	path := ""
	switch info.Source {
	case identity.SourceGitLab:
		path = fmt.Sprintf("/v1/gitlab/webhook/%s", info.WebhookToken)
	case identity.SourceCodeaone:
		path = fmt.Sprintf("/v1/codeaone/webhook/%s", info.WebhookToken)
	}
	link, _ := url.JoinPath(config.GetSystemEndpoint(), path)
	info.WebhookUrl = link
	return info
}

func withWebhookToken(webhookToken string) dao.DbWhereOption {
	return dao.WithKV("webhook_token", webhookToken)
}
