package codeplatform

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"time"
)

type codeReviewFeedbackEntity struct {
	ID              int64                     `gorm:"column:id;primaryKey"`
	UserId          string                    `gorm:"column:user_id"`
	SessionId       string                    `gorm:"column:session_id"`
	SuggestionId    string                    `gorm:"column:suggestion_id"`
	FeedbackType    codeplatform.FeedbackType `gorm:"column:feedback_type"`
	FeedbackContent *string                   `gorm:"column:feedback_content"`
	GmtCreate       time.Time                 `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified     time.Time                 `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (c *codeReviewFeedbackEntity) TableName() string {
	return "t_code_review_feedback"
}

type codeReviewCommentEntity struct {
	ID          int64     `gorm:"column:id;primaryKey"`
	CommentId   string    `gorm:"column:comment_id"`
	SessionId   string    `gorm:"column:session_id"`
	Content     string    `gorm:"column:content"`
	CommitId    string    `gorm:"column:commit_id"`
	GmtCreate   time.Time `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified time.Time `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (c *codeReviewCommentEntity) TableName() string {
	return "t_code_review_comment"
}
