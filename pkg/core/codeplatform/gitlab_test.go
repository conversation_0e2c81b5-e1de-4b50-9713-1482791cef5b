package codeplatform

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/codeplatform/provider"
	"testing"
)

func Test_splitGitlabLineCode(t *testing.T) {
	type args struct {
		lineCode string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 int
		want2 int
	}{
		{
			name: "测试正常case",
			args: args{
				lineCode: "f0c0e0f0_0_1",
			},
			want:  "f0c0e0f0",
			want1: 0,
			want2: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2 := provider.splitGitlabLineCode(tt.args.lineCode)
			if got != tt.want {
				t.<PERSON><PERSON>rf("splitGitlabLineCode() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.<PERSON><PERSON><PERSON>("splitGitlabLineCode() got1 = %v, want %v", got1, tt.want1)
			}
			if got2 != tt.want2 {
				t.<PERSON><PERSON>("splitGitlabLineCode() got2 = %v, want %v", got2, tt.want2)
			}
		})
	}
}
