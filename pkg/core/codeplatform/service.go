package codeplatform

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	comncodepl "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/codeplatform/provider"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"strings"
	"time"
)

var _ comncore.CodePlatformService = (*codePlatformService)(nil)

type injectCommitIdFn func(ctx base.Context, sessionId string, discussions []*comncodepl.Discussion) error

type codePlatformService struct {
	started                 chan struct{}
	ready                   bool
	core                    comncore.Core
	platformsProviders      map[comncodepl.Platform]provider.Provider
	platformInjectCommitFns map[comncodepl.Platform]injectCommitIdFn
}

func New() comncore.CodePlatformService {
	return &codePlatformService{
		started:            make(chan struct{}),
		ready:              false,
		platformsProviders: make(map[comncodepl.Platform]provider.Provider),
		platformInjectCommitFns: map[comncodepl.Platform]injectCommitIdFn{
			comncodepl.PlatformGitlab:       injectGitlabCommitId,
			comncodepl.PlatformGithub:       injectCommitId,
			comncodepl.PlatformCodeup:       injectCommitId,
			comncodepl.PlatformCodeaone:     injectCommitId,
			comncodepl.PlatformAgentConnect: injectGitlabCommitId, // connect模式 只支持gitlab平台
		},
	}
}

func (c *codePlatformService) Start(ctx context.Context) error {
	defer close(c.started)
	if err := c.initializePlatformProvider(); err != nil {
		return fmt.Errorf("failed to initialize platformsProviders: %w", err)
	}

	c.ready = true
	return nil
}

func (c *codePlatformService) initializePlatformProvider() error {
	platforms := map[comncodepl.Platform]provider.Provider{
		comncodepl.PlatformGitlab:       provider.NewGitlab(),
		comncodepl.PlatformGithub:       provider.NewGithub(),
		comncodepl.PlatformAgentConnect: provider.NewAgentConnect(),
		comncodepl.PlatformCodeup:       provider.NewCodeup(),
		comncodepl.PlatformCodeaone:     provider.NewCodeAone(),
		// 未来扩展其他平台
	}

	// 注册并验证
	for p, impl := range platforms {
		c.platformsProviders[p] = impl
		if impl == nil {
			return fmt.Errorf("platform %s implementation is nil", p)
		}
	}

	return nil
}

func (c *codePlatformService) Started() <-chan struct{} {
	return c.started

}

func (c *codePlatformService) IsReady() bool {
	return c.ready
}

func (c *codePlatformService) Stop() {
	return
}

func (c *codePlatformService) Metrics() []prometheus.Collector {
	return nil
}

func (c *codePlatformService) GetName() string {
	return "CodePlatformService"
}

func (c *codePlatformService) Register(core comncore.Core) {
	c.core = core
}

func (c *codePlatformService) GetMergeRequest(ctx base.Context, sessionId string, opts *comncodepl.GetMergeRequestOptions) (*comncodepl.MergeRequest, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	mr, err := p.GetMergeRequest(ctx, config)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if opts.WithCommits {
		//
		commits, err := p.ListMergeRequestCommits(ctx, config)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		mr.CommitRanges = commits
	}
	return mr, nil
}

func (c *codePlatformService) GetIncrementalMergeRequest(ctx base.Context, sessionId string, opts *comncodepl.GetIncrementalMergeRequestOptions) (*comncodepl.IncrementalMergeRequest, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	compareRes, err := p.CompareCommits(ctx, config, opts.BaseSha, opts.HeadSha)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	result := &comncodepl.IncrementalMergeRequest{
		CommitDiffs: compareRes.CommitDiffs,
	}
	if opts.WithCommits {
		commits, err := p.ListMergeRequestCommits(ctx, config)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(commits) < 1 {
			return result, nil
		}

		headFound := false
		for _, commit := range commits {
			if commit.Sha == opts.HeadSha {
				headFound = true
			}

			if commit.Sha == opts.BaseSha {
				break
			}

			if headFound {
				result.CommitRanges = append(result.CommitRanges, commit)
			}

		}
	}

	return result, nil
}

func (c *codePlatformService) CreateMergeRequestComment(ctx base.Context, sessionId string, opts *comncodepl.CreateMergeRequestCommentOpts) (*comncodepl.Comment, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp, err := p.CreateMergeRequestComment(ctx, config, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp.CommitId = util.GetString(opts.CommitId)
	now := time.Now()
	entity := &codeReviewCommentEntity{
		CommentId:   resp.ID,
		SessionId:   sessionId,
		Content:     opts.Content,
		CommitId:    util.GetString(opts.CommitId),
		GmtCreate:   now,
		GmtModified: now,
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		commonerrors.AlertError(ctx, commonerrors.AlertScopeCodePlatform, []string{sessionId, resp.ID}, "failed to save merge request comment", err)
		// 不阻塞
	}
	return resp, nil
}

func (c *codePlatformService) ListMergeRequestComments(ctx base.Context, sessionId string, req *comncodepl.ListMergeRequestCommentsRequest) (*comncodepl.ListMergeRequestCommentsResponse, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return p.ListMergeRequestComments(ctx, config, req)
}

func (c *codePlatformService) ListMergeRequestAllComments(ctx base.Context, sessionId string, req *comncodepl.ListMergeRequestAllCommentsRequest) ([]*comncodepl.Discussion, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	discussions, err := p.ListMergeRequestAllComments(ctx, config)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if fn, ok := c.platformInjectCommitFns[p.Platform()]; ok {
		if err := fn(ctx, sessionId, discussions); err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		ctx.GetLogger().Warn("platform not support inject commit id", "platform", p.Platform())
	}

	return discussions, nil
}

func injectCommitIdCommon(ctx base.Context, sessionId string, discussions []*comncodepl.Discussion, includeNoteId bool) error {
	commentIds := extractCommentIds(discussions, includeNoteId, ctx)
	if len(commentIds) == 0 {
		return nil
	}

	commentId2CommitId, err := fetchCommitIdMapping(ctx, sessionId, commentIds)
	if err != nil {
		return err
	}

	applyCommitIdToDiscussions(ctx, discussions, commentId2CommitId, includeNoteId)
	return nil
}

// 提取评论ID
func extractCommentIds(discussions []*comncodepl.Discussion, includeNoteId bool, ctx base.Context) []string {
	var commentIds []string
	for _, d := range discussions {
		for _, com := range d.Comments {
			commentIds = append(commentIds, com.ID)

			if includeNoteId {
				_, noteId, _ := provider.ParseCommentId(ctx, &com.ID)
				if noteId != nil {
					commentIds = append(commentIds, *noteId)
				}
			}
		}
	}
	return commentIds
}

// 从数据库获取 commitId 映射
func fetchCommitIdMapping(ctx base.Context, sessionId string, commentIds []string) (map[string]string, error) {
	opt := dao.NewDbOptions(
		dao.WithKV("session_id", sessionId),
		dao.WithKeyValues("comment_id", commentIds...),
		dao.WithSelect("comment_id,commit_id"),
	)

	entities, _, err := dao.ListEntitiesAutoPage(ctx, &codeReviewCommentEntity{}, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	commentId2CommitId := make(map[string]string, len(entities))
	for _, e := range entities {
		commentId2CommitId[e.CommentId] = e.CommitId
	}

	return commentId2CommitId, nil
}

// 将 commitId 应用到讨论中
func applyCommitIdToDiscussions(ctx base.Context, discussions []*comncodepl.Discussion, commentId2CommitId map[string]string, includeNoteId bool) {
	for _, d := range discussions {
		for _, com := range d.Comments {
			// 尝试直接匹配 comment ID
			if commitId, ok := commentId2CommitId[com.ID]; ok && commitId != "" {
				com.CommitId = commitId
			}

			// 如果需要，尝试匹配 noteId
			if includeNoteId {
				_, noteId, _ := provider.ParseCommentId(ctx, &com.ID)
				if noteId != nil {
					if commitId, ok := commentId2CommitId[*noteId]; ok && commitId != "" {
						com.CommitId = commitId
					}
				}
			}
		}
	}
}

func injectCommitId(ctx base.Context, sessionId string, discussions []*comncodepl.Discussion) error {
	return injectCommitIdCommon(ctx, sessionId, discussions, false)
}

func injectGitlabCommitId(ctx base.Context, sessionId string, discussions []*comncodepl.Discussion) error {
	return injectCommitIdCommon(ctx, sessionId, discussions, true)
}

func (c *codePlatformService) ListAllMergeRequestCommitDiffs(ctx base.Context, sessionId string, req *comncodepl.ListAllMergeRequestCommitDiffsRequest) ([]*comncodepl.CommitDiff, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return p.ListAllMergeRequestCommitDiffs(ctx, config, req)
}

func (c *codePlatformService) ResolveMergeRequestComment(ctx base.Context, sessionId string, opts *comncodepl.ResolveMergeRequestCommentOpts) error {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return errors.WithStack(err)
	}
	return p.ResolveMergeRequestComment(ctx, config, opts)
}

func (c *codePlatformService) UpdateMergeRequestComment(ctx base.Context, sessionId string, opts *comncodepl.UpdateMergeRequestCommentOpts) (*comncodepl.Comment, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return p.UpdateMergeRequestComment(ctx, config, opts)
}

func (c *codePlatformService) DeleteMergeRequestComment(ctx base.Context, sessionId string, opts *comncodepl.DeleteMergeRequestCommentOpts) error {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return errors.WithStack(err)
	}

	return p.DeleteMergeRequestComment(ctx, config, opts.CommentId)
}

func (c *codePlatformService) GetRepositoryFile(ctx base.Context, sessionId string, opts *comncodepl.GetRepositoryFileOpts) (*comncodepl.RepositoryFile, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	file, err := p.GetRepositoryFile(ctx, config, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 如果指定了 lineRange 参数，把文件content字段裁剪为指定行内容
	if opts.LineRange != nil {
		// 确保内容编码是base64
		if file.Encoding != "base64" {
			return nil, errors.Errorf("line range is only supported for base64 encoded content, current encoding: %s", file.Encoding)
		}

		// 解码Base64内容
		decodedContent, err := base64.StdEncoding.DecodeString(file.Content)
		if err != nil {
			return nil, errors.Wrap(err, "failed to decode base64 content")
		}

		contentStr := string(decodedContent)
		lines := strings.Split(contentStr, "\n")
		totalLines := len(lines)

		var start, end int

		if opts.LineRange.Start != nil {
			start = *opts.LineRange.Start
		} else {
			start = 1 // 未指定时从第一行开始
		}

		if opts.LineRange.End != nil {
			end = *opts.LineRange.End
		} else {
			end = totalLines // 未指定时到文件末尾
		}

		// 参数有效性验证
		if start < 1 || start > totalLines {
			return nil, errors.Errorf("invalid start line: %d (valid range 1-%d)", start, totalLines)
		}
		if end < 1 {
			return nil, errors.Errorf("end line cannot be less than 1")
		}
		if end < start {
			return nil, errors.Errorf("end line (%d) cannot be less than start line (%d)", end, start)
		}
		// end 不超过总行数
		if end > totalLines {
			end = totalLines
		}

		selectedLines := lines[start-1 : end]

		newContent := strings.Join(selectedLines, "\n")
		file.Content = base64.StdEncoding.EncodeToString([]byte(newContent))

	}
	return file, nil
}

// CreateReview 创建并 submit 多个评论
func (c *codePlatformService) CreateReview(ctx base.Context, sessionId string, opts *comncodepl.CreateReviewOpts) (*comncodepl.CreateReviewResponse, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp, err := p.CreateReview(ctx, config, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	now := time.Now()
	var entities []*codeReviewCommentEntity
	for _, com := range resp.Comments {
		entities = append(entities, &codeReviewCommentEntity{
			CommentId:   com.ID,
			SessionId:   sessionId,
			Content:     com.Content,
			CommitId:    opts.CommitId,
			GmtCreate:   now,
			GmtModified: now,
		})
	}

	if len(entities) > 0 {
		if err := dao.InsertEntities(ctx, entities); err != nil {
			commonerrors.AlertError(ctx, commonerrors.AlertScopeCodePlatform, []string{sessionId}, "failed to insert code review comments", err)
		}
	}
	return resp, nil
}

// UpdateReview 只有 github 会用到，更新 review 内容。AR 会把最终的评审报告更新到 review 里面。而 gitlab 评审报告是更新初始评论。
func (c *codePlatformService) UpdateReview(ctx base.Context, sessionId string, opts *comncodepl.UpdateReviewOpts) error {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return errors.WithStack(err)
	}
	return p.UpdateReview(ctx, config, opts)
}

func (c *codePlatformService) CreateCheckRun(ctx base.Context, sessionId string, opts *comncodepl.CreateCheckRunOpts) (*comncodepl.CreateCheckRunResponse, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return p.CreateCheckRun(ctx, config, opts)
}

func (c *codePlatformService) UpdateCheckRun(ctx base.Context, sessionId string, opts *comncodepl.UpdateCheckRunOpts) error {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return errors.WithStack(err)
	}
	return p.UpdateCheckRun(ctx, config, opts)
}

// GetVersion 获取平台版本，目前只有 gitlab 用到
func (c *codePlatformService) GetVersion(ctx base.Context, sessionId string) (string, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return "", errors.WithStack(err)
	}
	return p.GetVersion(ctx, config)
}

func (c *codePlatformService) ListMergeRequestCommits(ctx base.Context, sessionId string) ([]*comncodepl.Commit, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return p.ListMergeRequestCommits(ctx, config)
}

func (c *codePlatformService) Feedback(ctx base.Context, opts *comncodepl.CreateFeedbackOpts) error {
	ctx.GetLogger().Info("start to add feedback", "opts", opts)
	session, err := c.core.GetSessionService().GetSession(ctx, opts.SessionId)
	if err != nil {
		return errors.WithStack(err)
	}

	entity, err := dao.GetEntity(ctx, &codeReviewFeedbackEntity{}, dao.NewDbOptions(
		dao.WithKV("session_id", opts.SessionId),
		dao.WithKV("suggestion_id", opts.SuggestionId),
	))
	if err != nil {
		if !commonerrors.Is(err, codes.ErrRecordNotFound) {
			return errors.WithStack(err)
		}
		if err = dao.InsertEntity(ctx, &codeReviewFeedbackEntity{
			SessionId:    opts.SessionId,
			UserId:       session.UserId,
			SuggestionId: opts.SuggestionId,
			FeedbackType: opts.FeedbackType,
		}); err != nil {
			ctx.GetLogger().Error("failed to save feedback", "err", err, "feedback", opts)
			return errors.WithStack(err)
		}
		ctx.GetLogger().Info("success save feedback", "type", opts.FeedbackType)
		feedbackCounter.WithLabelValues(string(opts.FeedbackType)).Inc()
		return nil
	}
	ctx.GetLogger().Warn("comment feedback already exists", "oldFeedback", entity, "newFeedback", opts)
	return nil
}

func (c *codePlatformService) UpdateFeedback(ctx base.Context, opts *comncodepl.UpdateFeedbackOpts) error {
	ctx.GetLogger().Info("start to update feedback", "opts", opts)
	if opts.FeedbackContent == nil {
		return nil
	}

	feedbackEntity, err := dao.GetEntity(ctx, &codeReviewFeedbackEntity{}, dao.NewDbOptions(
		dao.WithKV("session_id", opts.SessionId),
		dao.WithKV("suggestion_id", opts.SuggestionId),
	))

	if err != nil {
		ctx.GetLogger().Error("failed to get feedback", "err", err)
		return errors.WithStack(err)
	}
	feedbackEntity.FeedbackContent = opts.FeedbackContent
	if err := dao.UpdateEntity(ctx, feedbackEntity, dao.NewDbOptions(dao.WithKV("id", feedbackEntity.ID))); err != nil {
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("successfully update feedback", "opts", opts)
	return nil
}

func (c *codePlatformService) CompareCommits(ctx base.Context, sessionId string, opts *comncodepl.CompareCommitsOpts) (*comncodepl.CompareCommitResult, error) {
	p, config, err := c.initializeProvider(ctx, sessionId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return p.CompareCommits(ctx, config, opts.Base, opts.Head)
}

func (c *codePlatformService) initializeProvider(ctx base.Context, sessionId string) (provider.Provider, *provider.Config, error) {
	sessionInfo, err := c.core.GetSessionService().GetSession(ctx, sessionId)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	idInfo, err := c.core.GetIdentityService().GetIdentity(ctx, sessionInfo.IdentityId)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	config := &provider.Config{
		Identity: idInfo,
	}

	switch comncodepl.Platform(idInfo.Source) {
	case comncodepl.PlatformGitlab:
		config.ProjectConfig = &provider.ProjectConfig{
			GitlabProjectConfig: &provider.GitlabProjectConfig{
				ProjectId: sessionInfo.Property.CodeReview.ProjectId,
			},
		}
		config.ReviewConfig = &provider.ReviewConfig{
			GitlabReviewConfig: &provider.GitlabReviewConfig{
				MergeRequestId: sessionInfo.Property.CodeReview.MergeRequestId,
			},
		}
	case comncodepl.PlatformGithub:
		config.ProjectConfig = &provider.ProjectConfig{
			GithubProjectConfig: &provider.GithubProjectConfig{
				Owner: sessionInfo.Property.GithubCodeReview.Owner,
				Repo:  sessionInfo.Property.GithubCodeReview.Repo,
			},
		}
		config.ReviewConfig = &provider.ReviewConfig{
			GithubReviewConfig: &provider.GithubReviewConfig{
				PullNumber: sessionInfo.Property.GithubCodeReview.Number,
			},
		}
	case comncodepl.PlatformCodeup:
		config.ProjectConfig = &provider.ProjectConfig{
			CodeupProjectConfig: &provider.CodeupProjectConfig{
				OrganizationId: sessionInfo.Property.CodeupCodeReview.OrganizationId,
				RepositoryId:   sessionInfo.Property.CodeupCodeReview.RepositoryId,
			},
		}
		config.ReviewConfig = &provider.ReviewConfig{
			CodeupReviewConfig: &provider.CodeupReviewConfig{
				LocalId: sessionInfo.Property.CodeupCodeReview.LocalId,
			},
		}
	case comncodepl.PlatformCodeaone:
		config.ProjectConfig = &provider.ProjectConfig{
			CodeAoneProjectConfig: &provider.CodeAoneProjectConfig{
				ProjectId: sessionInfo.Property.CodeAoneCodeReview.ProjectId,
			},
		}
		config.ReviewConfig = &provider.ReviewConfig{
			CodeAoneReviewConfig: &provider.CodeAoneReviewConfig{
				MergeRequestId: sessionInfo.Property.CodeAoneCodeReview.MergeRequestId,
			},
		}
	case comncodepl.PlatformAgentConnect:
		config.ConnectConfig = &provider.ConnectConfig{
			SessionId: sessionId,
		}
		config.ProjectConfig = &provider.ProjectConfig{
			GitlabProjectConfig: &provider.GitlabProjectConfig{
				ProjectId: sessionInfo.Property.CodeReview.ProjectId,
			},
		}
		config.ReviewConfig = &provider.ReviewConfig{
			GitlabReviewConfig: &provider.GitlabReviewConfig{
				MergeRequestId: sessionInfo.Property.CodeReview.MergeRequestId,
			},
		}
	default:
		return nil, nil, commonerrors.New(codes.ErrInvalidParameter, "source", idInfo.Source)
	}
	return c.platformsProviders[comncodepl.Platform(idInfo.Source)], config, nil
}
