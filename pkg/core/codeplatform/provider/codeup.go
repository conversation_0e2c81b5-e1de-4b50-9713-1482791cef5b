package provider

import (
	"fmt"
	errors2 "github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	codeup2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/codeup"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
	"strconv"
	"strings"
)

type codeup struct{}

func NewCodeup() Provider {
	return &codeup{}
}

func validateCodeupConfig(config *Config) error {
	if config == nil {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config"))
	}
	if config.Identity == nil {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.Identity"))
	}
	if config.ProjectConfig == nil {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig"))
	}
	if config.ProjectConfig.CodeupProjectConfig == nil {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig.CodeupProjectConfig"))
	}
	if config.ProjectConfig.CodeupProjectConfig.OrganizationId == "" {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig.CodeupProjectConfig.OrganizationId"))
	}
	if config.ProjectConfig.CodeupProjectConfig.RepositoryId == "" {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig.CodeupProjectConfig.RepositoryId"))
	}

	return nil
}
func validateCodeReviewConfig(config *Config) error {
	if config.ReviewConfig == nil {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig"))
	}
	if config.ReviewConfig.CodeupReviewConfig == nil {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig.CodeupReviewConfig"))
	}
	if config.ReviewConfig.CodeupReviewConfig.LocalId < 1 {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig.CodeupReviewConfig.LocalId"))
	}
	return nil
}
func (c *codeup) GetMergeRequest(ctx base.Context, config *Config) (*codeplatform.MergeRequest, error) {
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId
	localId := config.ReviewConfig.CodeupReviewConfig.LocalId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)
	mr, err := cli.GetChangeRequest(ctx, orgId, repoId, localId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	commitInfo, err := cli.GetChangeRequestLatestFromToCommitId(ctx, orgId, repoId, localId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	changes, err := cli.GetCompare(ctx, orgId, repoId, commitInfo.FromCommitId, commitInfo.ToCommitId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	var sourceRef string
	if mr.SourceBranch != nil {
		sourceRef = *mr.SourceBranch
	} else if mr.SourceCommitId != nil {
		sourceRef = *mr.SourceCommitId
	} else {
		return nil, errors2.WithStack(errors.New(codes.ErrCodeUpUnexpectedMRInfo, "SourceBranch and SourceCommitId are both empty"))
	}

	return &codeplatform.MergeRequest{
		ID:              strconv.Itoa(localId),
		ProjectId:       repoId,
		SourceBranch:    sourceRef,
		SourceRef:       sourceRef,
		TargetBranch:    mr.TargetBranch,
		Title:           mr.Title,
		Description:     mr.Description,
		WebURL:          mr.WebUrl,
		State:           mr.Status,
		CreatedAt:       mr.CreateTime,
		UpdatedAt:       mr.UpdateTime,
		SourceProjectID: mr.SourceProjectId,
		TargetProjectID: mr.TargetProjectId,
		SHA:             commitInfo.FromCommitId,
		MergeCommitSHA:  "",
		SquashCommitSHA: "",
		Changes:         convertChanges(changes),
		AuthorName:      mr.Author.Username,
		DiffRefs: &codeplatform.DiffRefs{
			BaseSHA: commitInfo.FromCommitId,
			HeadSHA: commitInfo.ToCommitId,
		},
	}, nil
}

func convertChanges(resp *codeup2.GetCompareResponse) []*codeplatform.MergeRequestDiff {
	var result []*codeplatform.MergeRequestDiff
	for _, diff := range resp.Diffs {
		content := diff.Diff
		split := strings.Split(content, "\n")
		var filtered []string
		for _, line := range split {
			if strings.HasPrefix(line, "---") || strings.HasPrefix(line, "+++") {
				continue
			}
			filtered = append(filtered, line)
		}
		filteredDiff := strings.Join(filtered, "\n")
		result = append(result, &codeplatform.MergeRequestDiff{
			OldPath:     diff.OldPath,
			NewPath:     diff.NewPath,
			AMode:       diff.AMode,
			BMode:       diff.BMode,
			Diff:        filteredDiff,
			NewFile:     diff.NewFile,
			RenamedFile: diff.RenamedFile,
			DeletedFile: diff.DeletedFile,
		})
	}
	return result
}

func (c *codeup) CreateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.CreateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId
	localId := config.ReviewConfig.CodeupReviewConfig.LocalId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)

	req := &codeup2.CreateChangeRequestCommentRequest{
		CommentType: codeup2.GlobalComment,
		Content:     opts.Content,
		Draft:       util.GetBoolOrDefault(opts.Draft, false),
		Resolved:    util.GetBoolOrDefault(opts.Resolved, false),
	}
	if opts.Path != nil && *opts.Path != "" {
		req.CommentType = codeup2.InlineComment
		req.FilePath = opts.Path
		req.LineNumber = opts.StartLine
		if mrCommitInfo, err := cli.GetChangeRequestLatestFromToCommitId(ctx, orgId, repoId, localId); err != nil {
			return nil, errors2.WithStack(err)
		} else {
			req.PatchSetBizId = ptr.To(mrCommitInfo.ToPatchSetBizId)
			req.FromPatchSetBizId = ptr.To(mrCommitInfo.FromPatchSetBizId)
			req.ToPatchSetBizId = ptr.To(mrCommitInfo.ToPatchSetBizId)
		}
	}
	if opts.ReplyToCommentID != nil && *opts.ReplyToCommentID != "" {
		req.ParentCommentBizId = opts.ReplyToCommentID
		parentComment, err := cli.GetMergeRequestComment(ctx, orgId, repoId, localId, *opts.ReplyToCommentID)
		if err != nil {
			return nil, errors2.WithStack(err)
		}
		req.CommentType = parentComment.CommentType
		req.PatchSetBizId = ptr.To(parentComment.RelatedPatchset.PatchSetBizId)
		req.FilePath = ptr.To(parentComment.FilePath)
		req.LineNumber = ptr.To(parentComment.LineNumber)
		req.FromPatchSetBizId = ptr.To(parentComment.FromPatchsetBizId)
		req.ToPatchSetBizId = ptr.To(parentComment.ToPatchsetBizId)
	}
	resp, err := cli.CreateChangeRequestComment(ctx, orgId, repoId, localId, req)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	return &codeplatform.Comment{
		ID:              resp.CommentBizId,
		Content:         resp.Content,
		Username:        resp.Author.Username,
		CreateTimestamp: resp.CommentTime.Unix(),
		CommitId:        resp.RelatedPatchSet.CommitId,
	}, nil
}

func (c *codeup) ListMergeRequestComments(ctx base.Context, config *Config, req *codeplatform.ListMergeRequestCommentsRequest) (*codeplatform.ListMergeRequestCommentsResponse, error) {
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId
	localId := config.ReviewConfig.CodeupReviewConfig.LocalId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)

	comments, err := cli.ListMergeRequestComments(ctx, orgId, repoId, localId, &codeup2.ListMergeRequestCommentsRequest{CommentIds: []string{req.CommentId}})
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	resp := &codeplatform.ListMergeRequestCommentsResponse{
		// 先假设是全局评论
		Type:     codeplatform.CommentTypeCommon,
		Comments: make([]*codeplatform.Comment, 0),
	}
	for _, comment := range *comments {
		if comment.CommentType == codeup2.InlineComment {
			resp.Type = codeplatform.CommentTypeDiscussionNote
			resp.CodePosition = &codeplatform.CodePosition{
				Path:      ptr.To(comment.FilePath),
				StartLine: ptr.To(comment.LineNumber),
				EndLine:   ptr.To(comment.LineNumber),
				LineType:  ptr.To("new"),
			}
		}
		convertedComments := recursiveConvertComment(&comment)
		resp.Comments = append(resp.Comments, convertedComments...)

	}
	return resp, nil
}

func recursiveConvertComment(comment *codeup2.MergeRequestComment) []*codeplatform.Comment {
	result := make([]*codeplatform.Comment, 0)
	result = append(result, &codeplatform.Comment{
		ID:              comment.CommentBizId,
		Content:         comment.Content,
		Username:        comment.Author.Name,
		CreateTimestamp: comment.CommentTime.Unix(),
		CommitId:        comment.RelatedPatchset.CommitId,
	})
	for i := range comment.ChildCommentsList {
		child := &comment.ChildCommentsList[i]
		result = append(result, recursiveConvertComment(child)...)
	}
	return result
}

func (c *codeup) ResolveMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.ResolveMergeRequestCommentOpts) error {
	return errors.New(codes.ErrUnsupportedPlatform, "ResolveMergeRequestComment", "codeup")
}

func (c *codeup) UpdateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.UpdateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId
	localId := config.ReviewConfig.CodeupReviewConfig.LocalId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)
	if err := cli.UpdateMergeRequestComment(ctx, orgId, repoId, localId, opts); err != nil {
		return nil, errors2.WithStack(err)
	}
	comment, err := cli.GetMergeRequestComment(ctx, orgId, repoId, localId, opts.CommentId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	return &codeplatform.Comment{
		ID:              comment.CommentBizId,
		Content:         comment.Content,
		Username:        comment.Author.Name,
		CreateTimestamp: comment.CommentTime.Unix(),
		CommitId:        comment.RelatedPatchset.CommitId,
	}, nil
}

func (c *codeup) DeleteMergeRequestComment(ctx base.Context, config *Config, commentId string) error {
	if err := validateCodeupConfig(config); err != nil {
		return errors2.WithStack(err)
	}
	if err := validateCodeReviewConfig(config); err != nil {
		return errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId
	localId := config.ReviewConfig.CodeupReviewConfig.LocalId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)
	return cli.DeleteChangeRequestComment(ctx, orgId, repoId, localId, commentId)
}

func (c *codeup) ListMergeRequestAllComments(ctx base.Context, config *Config) ([]*codeplatform.Discussion, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "ListMergeRequestAllComments", "codeup")
}

func (c *codeup) ListAllMergeRequestCommitDiffs(ctx base.Context, config *Config, req *codeplatform.ListAllMergeRequestCommitDiffsRequest) ([]*codeplatform.CommitDiff, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "ListAllMergeRequestCommitDiffs", "codeup")
}

func (c *codeup) ListMergeRequestCommits(ctx base.Context, config *Config) ([]*codeplatform.Commit, error) {
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId
	localId := config.ReviewConfig.CodeupReviewConfig.LocalId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)
	commitInfo, err := cli.GetChangeRequestLatestFromToCommitId(ctx, orgId, repoId, localId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	compare, err := cli.GetCompare(ctx, orgId, repoId, commitInfo.FromCommitId, commitInfo.ToCommitId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	var commits []*codeplatform.Commit
	for _, com := range compare.Commits {
		commits = append(commits, &codeplatform.Commit{
			Sha:              com.Id,
			Title:            com.Title,
			AuthorName:       com.AuthorName,
			AuthorEmail:      com.AuthorEmail,
			AuthoredDate:     &com.AuthoredDate,
			CommitterName:    com.CommitterName,
			CommitterEmail:   com.CommitterEmail,
			CommittedDate:    &com.CommittedDate,
			CreatedAt:        &com.CommittedDate,
			Message:          com.Message,
			ParentIDs:        com.ParentIds,
			ProjectID:        cast.ToInt(repoId),
			Trailers:         nil,
			ExtendedTrailers: nil,
			URL:              "",
		})
	}

	return commits, nil
}

func (c *codeup) CreateReview(ctx base.Context, config *Config, opts *codeplatform.CreateReviewOpts) (*codeplatform.CreateReviewResponse, error) {
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId
	localId := config.ReviewConfig.CodeupReviewConfig.LocalId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)

	if opts == nil {
		return nil, errors.New(codes.ErrInvalidParameterWithEmpty, "body")
	}
	if len(opts.Comments) == 0 {
		return nil, errors.New(codes.ErrInvalidParameterWithEmpty, "comments")
	}
	var commentIds []string
	var commentResults []*codeplatform.Comment
	for _, comment := range opts.Comments {
		commentOpt := &codeplatform.CreateMergeRequestCommentOpts{
			Content: comment.Content,
			Draft:   ptr.To(true),
		}
		if comment.Position != nil {
			commentOpt.Path = comment.Position.Path
			commentOpt.StartLine = comment.Position.StartLine
			commentOpt.EndLine = comment.Position.EndLine
		}
		commentResp, err := c.CreateMergeRequestComment(ctx, config, commentOpt)
		if err != nil {
			errors.AlertError(ctx, errors.AlertScopeCodeReview, []string{"codeup", "CreateReview"}, "CreateMergeRequestComment failed", err)
			continue
		}
		commentIds = append(commentIds, commentResp.ID)
		commentResults = append(commentResults, commentResp)
	}
	if len(commentIds) == 0 {
		return nil, errors.New(codes.ServerInternalError, "all comments failed")
	}
	reviewResp, err := cli.ReviewChangeRequest(ctx, orgId, repoId, localId, &codeup2.ReviewChangeRequestRequest{
		SubmitDraftCommentIds: commentIds,
	})
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	if !reviewResp.Result {
		return nil, errors.New(codes.ServerInternalError, "review failed")
	}
	return &codeplatform.CreateReviewResponse{
		Comments: commentResults,
	}, nil
}

func (c *codeup) UpdateReview(ctx base.Context, config *Config, opts *codeplatform.UpdateReviewOpts) error {
	return errors.New(codes.ErrUnsupportedPlatform, "UpdateReview", "codeup")
}

func (c *codeup) CreateCheckRun(ctx base.Context, config *Config, opts *codeplatform.CreateCheckRunOpts) (*codeplatform.CreateCheckRunResponse, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "CreateCheckRun", "codeup")
}

func (c *codeup) UpdateCheckRun(ctx base.Context, config *Config, opts *codeplatform.UpdateCheckRunOpts) error {
	return errors.New(codes.ErrUnsupportedPlatform, "UpdateCheckRun", "codeup")
}

func (c *codeup) GetRepositoryFile(ctx base.Context, config *Config, opts *codeplatform.GetRepositoryFileOpts) (*codeplatform.RepositoryFile, error) {
	if opts.Ref == nil {
		return nil, errors.New(codes.ErrInvalidParameterWithEmpty, "ref")
	}
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)
	resp, err := cli.GetFileBlobs(ctx, orgId, repoId, opts.Path, *opts.Ref)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	if resp.Encoding != "base64" {
		// 目前是AR层面解码文件，AR层面暂时只支持base64编码，其他编码方式暂不支持
		return nil, errors.New(codes.ServerInternalError, fmt.Sprintf("unsupported encoding %s", resp.Encoding))
	}
	return &codeplatform.RepositoryFile{
		BlobID:          resp.BlobId,
		CommitID:        resp.CommitId,
		Content:         resp.Content,
		Encoding:        resp.Encoding,
		ExecuteFileMode: false,
		FileName:        resp.FileName,
		FilePath:        resp.FilePath,
		LastCommitID:    resp.LastCommitId,
		Ref:             resp.Ref,
		SHA256:          "",
		Size:            cast.ToInt(resp.Size),
	}, nil
}

func (c *codeup) GetVersion(ctx base.Context, config *Config) (string, error) {
	return "", errors.New(codes.ErrUnsupportedPlatform, "GetVersion", "codeup")
}

func (c *codeup) CompareCommits(ctx base.Context, config *Config, baseSha, headSha string) (*codeplatform.CompareCommitResult, error) {
	if err := validateCodeupConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	orgId := config.ProjectConfig.CodeupProjectConfig.OrganizationId
	repoId := config.ProjectConfig.CodeupProjectConfig.RepositoryId

	cli := codeup2.NewClient(config.Identity.PlatformProperty.CodeupAppId)
	compares, err := cli.GetCompare(ctx, orgId, repoId, baseSha, headSha)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	var commitDiffs []*codeplatform.MergeRequestDiff
	for _, diff := range compares.Diffs {
		commitDiffs = append(commitDiffs, &codeplatform.MergeRequestDiff{
			Diff:        diff.Diff,
			NewPath:     diff.NewPath,
			OldPath:     diff.OldPath,
			NewFile:     diff.NewFile,
			DeletedFile: diff.DeletedFile,
			RenamedFile: diff.RenamedFile,
			AMode:       diff.AMode,
			BMode:       diff.BMode,
		})
	}
	return &codeplatform.CompareCommitResult{
		CommitDiffs: commitDiffs,
	}, nil
}

func (c *codeup) Platform() codeplatform.Platform {
	return codeplatform.PlatformCodeup
}
