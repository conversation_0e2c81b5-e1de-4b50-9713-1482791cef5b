package provider

import (
	"fmt"
	"github.com/spf13/cast"
	"k8s.io/utils/ptr"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	extgitlab "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/gitlab"
	sdk "gitlab.com/gitlab-org/api/client-go"
)

var _ Provider = (*gitlab)(nil)

type gitlab struct {
}

func NewGitlab() Provider {
	return &gitlab{}
}

func validateGitlabConfig(config *Config) error {
	if config == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config")
	}
	if config.Identity == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.Identity")
	}

	if config.ProjectConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig")
	}
	if config.ProjectConfig.GitlabProjectConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig.GitlabProjectConfig")
	}
	if config.ProjectConfig.GitlabProjectConfig.ProjectId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig.GitlabProjectConfig.ProjectId")
	}
	return nil
}
func validateGitlabReviewConfig(config *Config) error {
	if config.ReviewConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig")
	}
	if config.ReviewConfig.GitlabReviewConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig.GitlabReviewConfig")
	}
	if config.ReviewConfig.GitlabReviewConfig.MergeRequestId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig.GitlabReviewConfig.MergeRequestId")
	}
	return nil
}
func (g *gitlab) GetMergeRequest(ctx base.Context, config *Config) (*codeplatform.MergeRequest, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId
	var mr *sdk.MergeRequest
	mr, err := extgitlab.GetMergeRequest(ctx, config.Identity, projectId, mrId)

	if err != nil {
		ctx.GetLogger().Error("failed to get merge request", "projectId", config.ReviewConfig.GitlabReviewConfig.ProjectId, "mrId", mrId, "err", err.Error())
		return nil, err
	}
	ctx.GetLogger().Info("success to get merge request", "projectId", projectId, "mrId", mrId)

	mergeRequestInfo := &codeplatform.MergeRequest{
		ID:              strconv.Itoa(mr.IID),
		ProjectId:       strconv.Itoa(mr.ProjectID),
		SourceBranch:    mr.SourceBranch,
		SourceRef:       mr.SourceBranch,
		TargetBranch:    mr.TargetBranch,
		Title:           mr.Title,
		Description:     mr.Description,
		WebURL:          mr.WebURL,
		State:           mr.State,
		CreatedAt:       mr.CreatedAt,
		UpdatedAt:       mr.UpdatedAt,
		SourceProjectID: mr.SourceProjectID,
		TargetProjectID: mr.TargetProjectID,
		SHA:             mr.SHA,
		MergeCommitSHA:  mr.MergeCommitSHA,
		SquashCommitSHA: mr.SquashCommitSHA,
		Changes:         convertMergeRequestDiff(mr.Changes),
		AuthorName:      mr.Author.Username,
		DiffRefs: &codeplatform.DiffRefs{
			BaseSHA: mr.DiffRefs.BaseSha,
			HeadSHA: mr.DiffRefs.HeadSha,
		},
	}

	return mergeRequestInfo, nil
}

func convertMergeRequestDiff(diffs []*sdk.MergeRequestDiff) []*codeplatform.MergeRequestDiff {
	var result []*codeplatform.MergeRequestDiff
	for _, diff := range diffs {
		result = append(result, &codeplatform.MergeRequestDiff{
			AMode:       diff.AMode,
			BMode:       diff.BMode,
			DeletedFile: diff.DeletedFile,
			Diff:        diff.Diff,
			NewFile:     diff.NewFile,
			NewPath:     diff.NewPath,
			OldPath:     diff.OldPath,
		})
	}
	return result
}

func (g *gitlab) CreateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.CreateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	ctx.GetLogger().Info("start to CreateMergeRequestComment", "projectId", projectId, "mrId", mrId, "opts", opts)
	// 将 string 转换为 int
	mrIDInt, err := strconv.Atoi(mrId)
	if err != nil {
		ctx.GetLogger().Error("invalid merge request id", "mrId", mrId, "err", err.Error())
		return nil, fmt.Errorf("invalid merge request id: %s", mrId)
	}
	discussionId, _, err := ParseCommentId(ctx, opts.ReplyToCommentID)
	if opts.ReplyToCommentID != nil && err != nil {
		ctx.GetLogger().Warn("parse commentId fail", "discussionId", opts.ReplyToCommentID, "err", err.Error())
	}
	if opts.ReplyToCommentID != nil && discussionId != nil {
		note, err := extgitlab.AddMergeRequestDiscussionNote(ctx, config.Identity, projectId, mrIDInt, *discussionId, opts.Content)
		if err != nil {
			ctx.GetLogger().Error("failed to add merge request discussion note",
				"projectId", projectId,
				"mrId", mrId,
				"discussionId", *discussionId,
				"err", err.Error(),
			)
			return nil, err
		}
		commentId := strings.Join([]string{*discussionId, strconv.Itoa(note.ID)}, "-")
		return &codeplatform.Comment{ID: commentId, Content: opts.Content}, nil
	}
	if opts.Path != nil {
		discussion, err := extgitlab.CreateMergeRequestDiscussion(ctx, config.Identity, projectId, mrIDInt, opts)
		if err != nil {
			ctx.GetLogger().Error("failed to create merge request discussion",
				"projectId", projectId,
				"mrId", mrId,
				"err", err.Error(),
			)
			return nil, err
		}
		commentId, err := generateCommentIdFromDiscussion(ctx, discussion)
		return &codeplatform.Comment{ID: commentId, Content: opts.Content}, nil
	}

	note, err := extgitlab.CreateMergeRequestNote(ctx, config.Identity, projectId, mrIDInt, opts)
	if err != nil {
		ctx.GetLogger().Error("failed to create merge request note",
			"projectId", projectId,
			"mrId", mrId,
			"err", err.Error(),
		)
		return nil, err
	}
	commentId := strconv.Itoa(note.ID)
	return &codeplatform.Comment{ID: commentId, Content: opts.Content}, nil
}

func (g *gitlab) ListMergeRequestComments(ctx base.Context, config *Config, req *codeplatform.ListMergeRequestCommentsRequest) (*codeplatform.ListMergeRequestCommentsResponse, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	discussionId, _, err := ParseCommentId(ctx, &req.CommentId)
	if err != nil {
		ctx.GetLogger().Warn("parse commentId fail", "discussionId", req.CommentId, "err", err.Error())
		return nil, errors.WithStack(err)
	}
	if discussionId == nil {
		ctx.GetLogger().Info("start to GetMergeRequestNote", "req", req)
		note, err := extgitlab.GetMergeRequestNote(ctx, config.Identity, projectId, mrId, req.CommentId)
		if err != nil {
			if commonerrors.Is(err, codes.ErrMergeRequestCommentNotFound) {
				return &codeplatform.ListMergeRequestCommentsResponse{
					Type:     codeplatform.CommentTypeCommon,
					Comments: []*codeplatform.Comment{},
				}, nil
			}
			ctx.GetLogger().Error("failed to get merge request note",
				"projectId", projectId,
				"mrId", mrId,
				"commentId", req.CommentId,
				"err", err.Error(),
			)
			return nil, errors.WithStack(err)
		}
		commentId := strconv.Itoa(note.ID)
		comment := &codeplatform.Comment{
			ID:              commentId,
			Content:         note.Body,
			CreateTimestamp: note.CreatedAt.Unix(),
			Username:        note.Author.Username,
		}
		res := &codeplatform.ListMergeRequestCommentsResponse{
			Type:     codeplatform.CommentTypeCommon,
			Comments: []*codeplatform.Comment{comment},
		}
		return res, nil
	}
	req.CommentId = *discussionId
	ctx.GetLogger().Info("start to ListMergeRequestComments", "req", req)

	var discussion *sdk.Discussion
	discussion, err = extgitlab.ListMergeRequestComments(ctx, config.Identity, projectId, mrId, req)

	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp := &codeplatform.ListMergeRequestCommentsResponse{
		Type:     codeplatform.CommentTypeDiscussionNote,
		Comments: make([]*codeplatform.Comment, 0),
	}
	for _, note := range discussion.Notes {
		un := note.Author.Username
		createTime := note.CreatedAt
		content := note.Body
		commentId := strings.Join([]string{*discussionId, strconv.Itoa(note.ID)}, "-")
		resp.Comments = append(resp.Comments, &codeplatform.Comment{
			ID:              commentId,
			Content:         content,
			CommitId:        getCommentCommitId(note),
			CreateTimestamp: createTime.Unix(),
			Username:        un,
		})
		if resp.CodePosition == nil &&
			note.Position != nil {
			if note.Position.LineRange != nil &&
				note.Position.LineRange.StartRange != nil &&
				note.Position.LineRange.EndRange != nil {

				startLineCode := note.Position.LineRange.StartRange.LineCode
				endLineCode := note.Position.LineRange.EndRange.LineCode
				// 以新代码的行号为准
				_, _, startLine := splitGitlabLineCode(startLineCode)
				_, _, endLine := splitGitlabLineCode(endLineCode)

				resp.CodePosition = &codeplatform.CodePosition{
					Path:      ptr.To(note.Position.NewPath),
					StartLine: ptr.To(startLine),
					EndLine:   ptr.To(endLine),
					LineType:  ptr.To("new"),
				}
			} else {
				// 单行评论先以new_line为准
				resp.CodePosition = &codeplatform.CodePosition{
					Path:      ptr.To(note.Position.NewPath),
					StartLine: ptr.To(note.Position.NewLine),
					EndLine:   ptr.To(note.Position.NewLine),
					LineType:  ptr.To("new"),
				}
			}
		}
	}

	return resp, nil

}

func (g *gitlab) UpdateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.UpdateMergeRequestCommentOpts) (*codeplatform.Comment, error) {

	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	if opts.Content == nil && opts.Resolved == nil {
		ctx.GetLogger().Warn("updateMergeRequestComment fail", "opts", opts)
		return nil, commonerrors.New(codes.ErrInvalidParameterWithBothEmpty, "resolved", "content")
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	discussionId, noteId, err := ParseCommentId(ctx, &opts.CommentId)
	if err != nil {
		ctx.GetLogger().Warn("parse commentId fail", "discussionId", opts.CommentId, "err", err.Error())
		return nil, errors.WithStack(err)
	}
	opts.CommentId = *noteId
	var note *sdk.Note
	commentId := *noteId
	if discussionId == nil {
		note, err = extgitlab.UpdateMergeRequestNote(ctx, config.Identity, projectId, mrId, opts)
		if err != nil {
			ctx.GetLogger().Error("failed to update merge request note",
				"projectId", projectId,
				"mrId", mrId,
				"err", err.Error(),
			)
			return nil, errors.WithStack(err)
		}
		commentId = *noteId
	} else {
		note, err = extgitlab.UpdateMergeRequestDiscussionNote(ctx, config.Identity, projectId, mrId, *discussionId, opts)
		if err != nil {
			ctx.GetLogger().Error("failed to update merge request discussion",
				"projectId", projectId,
				"mrId", mrId,
				"err", err.Error(),
			)
			return nil, errors.WithStack(err)
		}
	}
	if discussionId != nil {
		commentId = *discussionId + "-" + commentId
	}
	return &codeplatform.Comment{
		ID:      commentId,
		Content: note.Body,
	}, nil
}

func (g *gitlab) DeleteMergeRequestComment(ctx base.Context, config *Config, commentId string) error {
	if err := validateGitlabConfig(config); err != nil {
		return errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	discussionId, noteId, err := ParseCommentId(ctx, ptr.To(commentId))
	if err != nil {
		ctx.GetLogger().Warn("parse commentId fail", "discussionId", commentId, "err", err.Error())
		return errors.WithStack(err)
	}
	if discussionId == nil {
		err = extgitlab.DeleteMergeRequestNote(ctx, config.Identity, projectId, mrId, *noteId)
		if err != nil {
			ctx.GetLogger().Error("failed to delete merge request note",
				"projectId", projectId,
				"mrId", mrId,
				"err", err.Error(),
			)
			return errors.WithStack(err)
		}
	} else {
		err = extgitlab.DeleteMergeRequestDiscussionNote(ctx, config.Identity, projectId, mrId, *discussionId, *noteId)
		if err != nil {
			ctx.GetLogger().Error("failed to delete merge request discussion note",
				"projectId", projectId,
				"mrId", mrId,
				"err", err.Error(),
			)
			return errors.WithStack(err)
		}
	}
	return nil
}

func (g *gitlab) ResolveMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.ResolveMergeRequestCommentOpts) error {
	if err := validateGitlabConfig(config); err != nil {
		return errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	return extgitlab.ResolveMergeRequestDiscussion(ctx, config.Identity, projectId, mrId, opts)
}

func (g *gitlab) ListMergeRequestAllComments(ctx base.Context, config *Config) ([]*codeplatform.Discussion, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	ctx.GetLogger().Debug("start to ListMergeRequestAllComments")

	var discussions []*sdk.Discussion
	discussions, err := extgitlab.ListMergeRequestAllComments(ctx, config.Identity, projectId, mrId)

	if err != nil {
		return nil, errors.WithStack(err)
	}
	var resp []*codeplatform.Discussion
	for _, d := range discussions {
		t := codeplatform.CommentTypeCommon
		discuss := &codeplatform.Discussion{
			Type:     t,
			Comments: make([]*codeplatform.Comment, 0),
		}
		for _, note := range d.Notes {
			if note.Type == sdk.DiffNote {
				t = codeplatform.CommentTypeDiscussionNote
			}
			un := note.Author.Username
			createTime := note.CreatedAt
			content := note.Body
			discuss.Comments = append(discuss.Comments, &codeplatform.Comment{
				ID:              ToCommentId(d.ID, note.ID),
				CommitId:        getCommentCommitId(note),
				Content:         content,
				CreateTimestamp: createTime.Unix(),
				Username:        un,
			})
			if discuss.CodePosition == nil &&
				note.Position != nil {
				if note.Position.LineRange != nil &&
					note.Position.LineRange.StartRange != nil &&
					note.Position.LineRange.EndRange != nil {
					startLineCode := note.Position.LineRange.StartRange.LineCode
					endLineCode := note.Position.LineRange.EndRange.LineCode
					// 以新代码的行号为准
					_, _, startLine := splitGitlabLineCode(startLineCode)
					_, _, endLine := splitGitlabLineCode(endLineCode)

					discuss.CodePosition = &codeplatform.CodePosition{
						Path:      ptr.To(note.Position.NewPath),
						StartLine: ptr.To(startLine),
						EndLine:   ptr.To(endLine),
						LineType:  ptr.To("new"),
					}
				} else {
					// 单行评论先以new_line为准
					discuss.CodePosition = &codeplatform.CodePosition{
						Path:      ptr.To(note.Position.NewPath),
						StartLine: ptr.To(note.Position.NewLine),
						EndLine:   ptr.To(note.Position.NewLine),
						LineType:  ptr.To("new"),
					}
				}
			}
			if note.Resolvable && note.Resolved {
				discuss.Resolved = true
			}
		}
		discuss.Type = t
		resp = append(resp, discuss)
	}
	// TODO 需要挪走
	//if err := g.injectCommitId(ctx, sessionId, resp); err != nil {
	//	return nil, errors.WithStack(err)
	//}
	return resp, nil
}

func (g *gitlab) ListAllMergeRequestCommitDiffs(ctx base.Context, config *Config, req *codeplatform.ListAllMergeRequestCommitDiffsRequest) ([]*codeplatform.CommitDiff, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	var commits []*sdk.Commit
	commits, err := extgitlab.ListMergeRequestCommits(ctx, config.Identity, projectId, mrId, req)

	if err != nil {
		return nil, errors.WithStack(err)
	}
	var result []*codeplatform.CommitDiff
	for _, c := range commits {
		var diffs []*sdk.Diff
		diffs, err = extgitlab.GetCommitDiff(ctx, config.Identity, projectId, c.ID)

		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, d := range diffs {
			result = append(result, &codeplatform.CommitDiff{
				CommitId:    c.ID,
				Diff:        d.Diff,
				NewPath:     d.NewPath,
				OldPath:     d.OldPath,
				NewFile:     d.NewFile,
				DeletedFile: d.DeletedFile,
				RenamedFile: d.RenamedFile,
			})
		}
	}
	return result, nil
}

func (g *gitlab) GetRepositoryFile(ctx base.Context, config *Config, opts *codeplatform.GetRepositoryFileOpts) (*codeplatform.RepositoryFile, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId

	file, err := extgitlab.GetRepositoryFile(ctx, config.Identity, projectId, opts)

	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &codeplatform.RepositoryFile{
		FileName:        file.FileName,
		FilePath:        file.FilePath,
		Size:            file.Size,
		Encoding:        file.Encoding,
		Content:         file.Content,
		ExecuteFileMode: file.ExecuteFilemode,
		Ref:             file.Ref,
		BlobID:          file.BlobID,
		CommitID:        file.CommitID,
		SHA256:          file.SHA256,
		LastCommitID:    file.LastCommitID,
	}, nil
}

// CreateReview 创建多个 draft notes，一次性 publish 这些 draft notes，并且把发送出去的评论 ID 返回给 Agent。
func (g *gitlab) CreateReview(ctx base.Context, config *Config, opts *codeplatform.CreateReviewOpts) (*codeplatform.CreateReviewResponse, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	ctx.GetLogger().Info("start to create gitlab review", "commentCount", len(opts.Comments))

	// 1. 创建 draft notes
	if err := extgitlab.CreateDraftNotes(ctx, config.Identity, projectId, mrId, opts.Comments); err != nil {
		return nil, errors.WithStack(err)
	}

	// 2. publish draft notes
	if err := extgitlab.********************(ctx, config.Identity, projectId, mrId); err != nil {
		return nil, errors.WithStack(err)
	}
	// 3. 捞出所有已发送的 note，比对 draftNotes 内容，把 publish 出去的 note ID 记下来。Agent 需要评论 ID，拼接 overall report。
	notes, err := extgitlab.ListNotes(ctx, config.Identity, projectId, mrId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var comments []*codeplatform.Comment
	for _, draftNote := range opts.Comments {
		for _, note := range notes {
			// 预设不会有内容一模一样的多条评论
			if strings.TrimSpace(note.Body) == strings.TrimSpace(draftNote.Content) {
				comments = append(comments, &codeplatform.Comment{
					ID:              fmt.Sprintf("%d", note.ID),
					Content:         note.Body,
					CreateTimestamp: note.CreatedAt.Unix(),
					Username:        note.Author.Username,
				})
				break
			}
		}
	}
	ctx.GetLogger().Info("successfully create review", "commentCount", len(comments))

	return &codeplatform.CreateReviewResponse{Comments: comments}, nil
}

func (g *gitlab) UpdateReview(ctx base.Context, config *Config, opts *codeplatform.UpdateReviewOpts) error {
	return commonerrors.New(codes.ErrUnsupportedPlatform, "UpdateReview", "gitlab")
}

func (g *gitlab) CreateCheckRun(ctx base.Context, config *Config, opts *codeplatform.CreateCheckRunOpts) (*codeplatform.CreateCheckRunResponse, error) {
	return nil, commonerrors.New(codes.ErrUnsupportedPlatform, "CreateCheckRun", "gitlab")
}

func (g *gitlab) UpdateCheckRun(ctx base.Context, config *Config, opts *codeplatform.UpdateCheckRunOpts) error {
	return commonerrors.New(codes.ErrUnsupportedPlatform, "UpdateCheckRun", "gitlab")

}

func (g *gitlab) GetVersion(ctx base.Context, config *Config) (string, error) {
	if config == nil {
		return "", commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config")
	}
	if config.Identity == nil {
		return "", commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.Identity")
	}

	return extgitlab.GetVersion(ctx, config.Identity)
}

func (g *gitlab) ListMergeRequestCommits(ctx base.Context, config *Config) ([]*codeplatform.Commit, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mrId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId

	commits, err := extgitlab.GetMergeRequestCommits(ctx, config.Identity, projectId, mrId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var result []*codeplatform.Commit
	for _, c := range commits {
		result = append(result, &codeplatform.Commit{
			Sha:           c.ID,
			AuthorName:    c.AuthorName,
			AuthoredDate:  c.AuthoredDate,
			CommittedDate: c.CommittedDate,
			URL:           c.WebURL,
			Message:       c.Message,
		})
	}
	return result, nil
}

func (g *gitlab) CompareCommits(ctx base.Context, config *Config, baseSha, headSha string) (*codeplatform.CompareCommitResult, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	compares, err := extgitlab.Compare(ctx, config.Identity, projectId, baseSha, headSha)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(compares.Diffs) < 1 {
		ctx.GetLogger().Warn("no compared files", "projectId", projectId, "from", headSha, "to", baseSha)
		return &codeplatform.CompareCommitResult{
			CommitDiffs: []*codeplatform.MergeRequestDiff{},
		}, nil
	}

	return &codeplatform.CompareCommitResult{
		CommitDiffs: convertGitlabDiffsToMergeRequestDiffs(compares.Diffs),
	}, nil
}
func (g *gitlab) Platform() codeplatform.Platform {
	return codeplatform.PlatformGitlab
}

func ToCommentId(discussionId string, noteId int) string {
	if noteId == 0 {
		return discussionId
	}
	if discussionId == "" {
		return fmt.Sprintf("%d", noteId)
	}
	return fmt.Sprintf("%s-%d", discussionId, noteId)
}

func splitGitlabLineCode(lineCode string) (string, int, int) {
	sha := ""
	oldCodeLine := 0
	newCodeLine := 0

	splits := strings.Split(lineCode, "_")
	if len(splits) == 3 {
		sha = splits[0]
		oldCodeLine = cast.ToInt(splits[1])
		newCodeLine = cast.ToInt(splits[2])
	}

	return sha, oldCodeLine, newCodeLine
}

func convertGitlabDiffsToMergeRequestDiffs(diffs []*sdk.Diff) []*codeplatform.MergeRequestDiff {
	var result []*codeplatform.MergeRequestDiff
	for _, diff := range diffs {
		result = append(result, &codeplatform.MergeRequestDiff{
			OldPath:     diff.OldPath,
			NewPath:     diff.NewPath,
			NewFile:     diff.NewFile,
			DeletedFile: diff.DeletedFile,
			Diff:        diff.Diff,
			AMode:       diff.AMode,
			BMode:       diff.BMode,
			RenamedFile: diff.RenamedFile,
		})
	}
	return result
}
