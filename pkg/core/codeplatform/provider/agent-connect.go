package provider

import (
	"fmt"
	errors2 "github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	sdk "gitlab.com/gitlab-org/api/client-go"
	"log/slog"
	"strconv"
	"strings"
)

var _ Provider = (*agentConnect)(nil)

type agentConnect struct {
}

func NewAgentConnect() Provider {
	return &agentConnect{}
}

func validateConnectConfig(config *Config) error {
	if config.ConnectConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameter, "connectConfig", "nil")
	}
	if config.ConnectConfig.SessionId == "" {
		return commonerrors.New(codes.ErrInvalidParameter, "connectConfig.SessionId", "empty")
	}
	return nil
}
func (a *agentConnect) GetMergeRequest(ctx base.Context, config *Config) (*codeplatform.MergeRequest, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateConnectConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	l = l.With("platform", "agent-connect")

	l.Info("start to get merge request")
	mr, err := core.GetCoreService().GetAgentConnectService().GetMergeRequestChanges(ctx, config.ConnectConfig.SessionId, projectId, mrId)
	if err != nil {
		l.Error("failed to get merge request", "err", err.Error())
		return nil, err
	}
	return convertGitlabMergeRequest(mr), nil
}
func (a *agentConnect) CreateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.CreateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	l = l.With("platform", "agent-connect")

	l.Info("start to create merge request comment", "opts", opts)
	var discussionId *string = nil
	if opts.ReplyToCommentID != nil {
		discussionId, _, err = ParseCommentId(ctx, opts.ReplyToCommentID)
		if err != nil {
			ctx.GetLogger().Warn("parse commentId fail", "discussionId", opts.ReplyToCommentID, "err", err.Error())
		}
	}
	if opts.ReplyToCommentID != nil && discussionId != nil {
		note, err := core.GetCoreService().GetAgentConnectService().AddMergeRequestDiscussionNote(ctx, config.ConnectConfig.SessionId, projectId, mrId, *discussionId, opts.Content)
		if err != nil {
			l.Error("failed to add merge request discussion note", "discussionId", *discussionId, "err", err.Error())
			return nil, err
		}
		commentId := strings.Join([]string{*discussionId, strconv.Itoa(note.ID)}, "-")
		return &codeplatform.Comment{ID: commentId, Content: opts.Content}, nil
	}
	if opts.Path != nil {
		discussion, err := core.GetCoreService().GetAgentConnectService().CreateMergeRequestDiscussion(ctx, config.ConnectConfig.SessionId, projectId, mrId, opts)
		if err != nil {
			l.Error("failed to create merge request discussion", "err", err.Error())
			return nil, err
		}
		commentId, err := generateCommentIdFromDiscussion(ctx, discussion)
		return &codeplatform.Comment{ID: commentId, Content: opts.Content}, nil
	}

	note, err := core.GetCoreService().GetAgentConnectService().CreateMergeRequestNote(ctx, config.ConnectConfig.SessionId, projectId, mrId, opts)
	if err != nil {
		l.Error("failed to create merge request note", "err", err.Error())
		return nil, err
	}
	commentId := strconv.Itoa(note.ID)
	return &codeplatform.Comment{ID: commentId, Content: opts.Content}, nil
}

func (a *agentConnect) ListMergeRequestComments(ctx base.Context, config *Config, req *codeplatform.ListMergeRequestCommentsRequest) (*codeplatform.ListMergeRequestCommentsResponse, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateConnectConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	l = l.With("platform", "agent-connect")

	discussionId, _, err := ParseCommentId(ctx, &req.CommentId)
	if err != nil {
		l.Warn("parse commentId fail", "discussionId", req.CommentId, "err", err.Error())
		return nil, errors2.WithStack(err)
	}
	if discussionId == nil {
		l.Info("start to get merge request note", "req", req)
		note, err := core.GetCoreService().GetAgentConnectService().GetMergeRequestNote(ctx, config.ConnectConfig.SessionId, projectId, mrId, req.CommentId)
		if err != nil {
			l.Error("failed to get merge request note", "commentId", req.CommentId, "err", err.Error())
			return nil, errors2.WithStack(err)
		}
		commentId := strconv.Itoa(note.ID)
		comment := &codeplatform.Comment{
			ID:              commentId,
			Content:         note.Body,
			CreateTimestamp: note.CreatedAt.Unix(),
			Username:        note.Author.Username,
			CommitId:        getCommentCommitId(note),
		}
		res := &codeplatform.ListMergeRequestCommentsResponse{
			Type:     codeplatform.CommentTypeCommon,
			Comments: []*codeplatform.Comment{comment},
		}
		return res, nil
	}
	req.CommentId = *discussionId
	l.Info("start to list merge request comments", "req", req)
	discussion, err := core.GetCoreService().GetAgentConnectService().ListMergeRequestComments(ctx, config.ConnectConfig.SessionId, projectId, mrId, req)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	return convertGitlabDiscussion(discussion), nil

}

func (a *agentConnect) UpdateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.UpdateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateConnectConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	l = l.With("platform", "agent-connect")

	if opts.Content == nil && opts.Resolved == nil {
		l.Warn("update merge request comment fail", "opts", opts)
		return nil, commonerrors.New(codes.ErrInvalidParameterWithBothEmpty, "resolved", "content")
	}

	discussionId, noteId, err := ParseCommentId(ctx, &opts.CommentId)
	if err != nil {
		l.Warn("parse commentId fail", "discussionId", opts.CommentId, "err", err.Error())
		return nil, errors2.WithStack(err)
	}
	opts.CommentId = *noteId
	var note *sdk.Note
	commentId := *noteId
	if discussionId == nil {
		note, err = core.GetCoreService().GetAgentConnectService().UpdateMergeRequestNote(ctx, config.ConnectConfig.SessionId, projectId, mrId, opts)
		if err != nil {
			l.Error("failed to update merge request note", "err", err.Error())
			return nil, errors2.WithStack(err)
		}
		commentId = *noteId
	} else {
		note, err = core.GetCoreService().GetAgentConnectService().UpdateMergeRequestDiscussionNote(ctx, config.ConnectConfig.SessionId, projectId, mrId, *discussionId, opts)
		if err != nil {
			l.Error("failed to update merge request discussion", "err", err.Error())
			return nil, errors2.WithStack(err)
		}
	}
	if discussionId != nil {
		commentId = *discussionId + "-" + commentId
	}
	return &codeplatform.Comment{
		ID:      commentId,
		Content: note.Body,
	}, nil
}

func (a *agentConnect) ResolveMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.ResolveMergeRequestCommentOpts) error {
	if err := validateGitlabConfig(config); err != nil {
		return errors2.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return errors2.WithStack(err)
	}
	if err := validateConnectConfig(config); err != nil {
		return errors2.WithStack(err)
	}

	projectId, mrId, _, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return errors2.WithStack(err)
	}

	return core.GetCoreService().GetAgentConnectService().ResolveMergeRequestDiscussion(ctx, config.ConnectConfig.SessionId, projectId, mrId, opts)
}

func (a *agentConnect) ListMergeRequestAllComments(ctx base.Context, config *Config) ([]*codeplatform.Discussion, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateConnectConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	l = l.With("platform", "agent-connect")

	l.Info("start to list merge request all comments")
	discussions, err := core.GetCoreService().GetAgentConnectService().ListMergeRequestAllComments(ctx, config.ConnectConfig.SessionId, projectId, mrId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	return convertGitlabDiscussions(discussions), nil
}

func (a *agentConnect) ListAllMergeRequestCommitDiffs(ctx base.Context, config *Config, req *codeplatform.ListAllMergeRequestCommitDiffsRequest) ([]*codeplatform.CommitDiff, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateConnectConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	l = l.With("platform", "agent-connect")

	commits, err := core.GetCoreService().GetAgentConnectService().ListMergeRequestCommits(ctx, config.ConnectConfig.SessionId, projectId, mrId)

	if err != nil {
		return nil, errors2.WithStack(err)
	}
	var result []*codeplatform.CommitDiff
	for _, c := range commits {
		diffs, err := core.GetCoreService().GetAgentConnectService().GetCommitDiff(ctx, config.ConnectConfig.SessionId, projectId, c.ID)
		if err != nil {
			return nil, errors2.WithStack(err)
		}
		for _, d := range diffs {
			result = append(result, &codeplatform.CommitDiff{
				CommitId:    c.ID,
				Diff:        d.Diff,
				NewPath:     d.NewPath,
				OldPath:     d.OldPath,
				NewFile:     d.NewFile,
				DeletedFile: d.DeletedFile,
				RenamedFile: d.RenamedFile,
			})
		}
	}
	return result, nil
}

func (a *agentConnect) GetRepositoryFile(ctx base.Context, config *Config, opts *codeplatform.GetRepositoryFileOpts) (*codeplatform.RepositoryFile, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	projectId := config.ReviewConfig.GitlabReviewConfig.ProjectId

	file, err := core.GetCoreService().GetAgentConnectService().GetRepositoryFile(ctx, config.ConnectConfig.SessionId, projectId, opts)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	return convertGitlabFile(file), nil
}

func (a *agentConnect) DeleteMergeRequestComment(ctx base.Context, config *Config, commentId string) error {
	return commonerrors.New(codes.ErrUnsupportedPlatform, "DeleteMergeRequestComment", "agent-connect")
}

func (a *agentConnect) CreateReview(ctx base.Context, config *Config, opts *codeplatform.CreateReviewOpts) (*codeplatform.CreateReviewResponse, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateConnectConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	l.Info("start to create gitlab review", "commentCount", len(opts.Comments))

	// 1. 创建 draft notes
	err = core.GetCoreService().GetAgentConnectService().CreateDraftNotes(ctx, config.ConnectConfig.SessionId, projectId, mrId, opts.Comments)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	// 2. publish draft notes
	err = core.GetCoreService().GetAgentConnectService().PublishAllDraftNotes(ctx, config.ConnectConfig.SessionId, projectId, mrId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	// 3. 捞出所有已发送的 note，比对 draftNotes 内容，把 publish 出去的 note ID 记下来。Agent 需要评论 ID，拼接 overall report。
	notes, err := core.GetCoreService().GetAgentConnectService().ListNotes(ctx, config.ConnectConfig.SessionId, projectId, mrId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	var comments []*codeplatform.Comment
	for _, draftNote := range opts.Comments {
		for _, note := range notes {
			// 预设不会有内容一模一样的多条评论
			if strings.TrimSpace(note.Body) == strings.TrimSpace(draftNote.Content) {
				comments = append(comments, &codeplatform.Comment{
					ID:              fmt.Sprintf("%d", note.ID),
					Content:         note.Body,
					CreateTimestamp: note.CreatedAt.Unix(),
					Username:        note.Author.Username,
				})
				break
			}
		}
	}
	l.Info("successfully create review", "commentCount", len(comments))

	return &codeplatform.CreateReviewResponse{Comments: comments}, nil
}

func (a *agentConnect) UpdateReview(ctx base.Context, config *Config, opts *codeplatform.UpdateReviewOpts) error {
	return commonerrors.New(codes.ErrUnsupportedPlatform, "UpdateReview", "agent-connect")
}

func (a *agentConnect) CreateCheckRun(ctx base.Context, config *Config, opts *codeplatform.CreateCheckRunOpts) (*codeplatform.CreateCheckRunResponse, error) {
	return nil, commonerrors.New(codes.ErrUnsupportedPlatform, "CreateCheckRun", "agent-connect")
}

func (a *agentConnect) UpdateCheckRun(ctx base.Context, config *Config, opts *codeplatform.UpdateCheckRunOpts) error {
	return commonerrors.New(codes.ErrUnsupportedPlatform, "UpdateCheckRun", "agent-connect")
}

func (a *agentConnect) GetVersion(ctx base.Context, config *Config) (string, error) {
	if config == nil {
		return "", commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config")
	}
	if err := validateConnectConfig(config); err != nil {
		return "", errors2.WithStack(err)
	}

	return core.GetCoreService().GetAgentConnectService().GetVersion(ctx, config.ConnectConfig.SessionId)
}

func (a *agentConnect) ListMergeRequestCommits(ctx base.Context, config *Config) ([]*codeplatform.Commit, error) {
	if err := validateGitlabConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	if err := validateGitlabReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	if err := validateConnectConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	projectId, mrId, l, err := getProjectAndMergeRequestIdBySession(ctx, config)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	l.Info("start to list merge request commits", "projectId", projectId, "mrId", mrId)

	commits, err := core.GetCoreService().GetAgentConnectService().ListMergeRequestCommits(ctx, config.ConnectConfig.SessionId, projectId, mrId)
	if err != nil {
		return nil, errors2.WithStack(err)
	}

	var result []*codeplatform.Commit
	for _, c := range commits {
		result = append(result, &codeplatform.Commit{
			Sha:           c.ID,
			AuthorName:    c.AuthorName,
			AuthoredDate:  c.AuthoredDate,
			CommittedDate: c.CommittedDate,
			URL:           c.WebURL,
			Message:       c.Message,
		})
	}

	l.Info("successfully list merge request commits", "commitsCount", len(result))

	return result, nil
}

func (a *agentConnect) CompareCommits(ctx base.Context, config *Config, baseSha, headSha string) (*codeplatform.CompareCommitResult, error) {
	return nil, commonerrors.New(codes.ErrUnsupportedPlatform, "CompareCommits", "agent-connect")
}

func (a *agentConnect) Platform() codeplatform.Platform {
	return codeplatform.PlatformAgentConnect
}

// connect内部使用
func getProjectAndMergeRequestIdBySession(ctx base.Context, config *Config) (string, string, *slog.Logger, error) {
	projectId := config.ProjectConfig.GitlabProjectConfig.ProjectId
	mergeRequestId := config.ReviewConfig.GitlabReviewConfig.MergeRequestId
	return projectId,
		mergeRequestId,
		ctx.GetLogger().With(
			"projectId", projectId,
			"mergeRequestId", mergeRequestId),
		nil
}
