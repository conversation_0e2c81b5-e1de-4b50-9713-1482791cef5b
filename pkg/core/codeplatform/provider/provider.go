package provider

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
)

// Config 定义了代码平台提供者的配置信息
// 包含身份认证、项目配置、评审配置和连接配置
type Config struct {
	Identity      *identity.IdentityInfo // 身份认证信息
	ProjectConfig *ProjectConfig         // 项目配置
	ReviewConfig  *ReviewConfig          // 评审配置
	ConnectConfig *ConnectConfig         // 连接配置
}

// ProjectConfig 定义了不同代码平台的项目配置
// 支持 Gitlab、Github、Codeup 和 CodeAone 平台
type ProjectConfig struct {
	GitlabProjectConfig   *GitlabProjectConfig   // Gitlab 项目配置
	GithubProjectConfig   *GithubProjectConfig   // Github 项目配置
	CodeupProjectConfig   *CodeupProjectConfig   // Codeup 项目配置
	CodeAoneProjectConfig *CodeAoneProjectConfig // CodeAone 项目配置
}

// GitlabProjectConfig 定义了 Gitlab 项目的配置结构
// 包含项目 ID
type GitlabProjectConfig struct {
	ProjectId string `json:"projectId"` // Gitlab 项目唯一标识
}

// GithubProjectConfig 定义了 Github 项目的配置结构
// 包含所有者和仓库名称
type GithubProjectConfig struct {
	Owner string `json:"owner"` // 仓库所有者
	Repo  string `json:"repo"`  // 仓库名称
}

// CodeupProjectConfig 定义了 Codeup 项目的配置结构
// 包含组织 ID 和 仓库 ID
type CodeupProjectConfig struct {
	OrganizationId string `json:"organizationId"` // 组织唯一标识
	RepositoryId   string `json:"repositoryId"`   // 仓库唯一标识
}

// CodeAoneProjectConfig 定义了 CodeAone 项目的配置结构
// 包含项目 ID
type CodeAoneProjectConfig struct {
	ProjectId int64 `json:"projectId"` // CodeAone 项目唯一标识
}

// ReviewConfig 定义了不同代码平台的评审配置
// 支持 Gitlab、Github、Codeup 和 CodeAone 平台
type ReviewConfig struct {
	GitlabReviewConfig   *GitlabReviewConfig   // Gitlab 评审配置
	GithubReviewConfig   *GithubReviewConfig   // Github 评审配置
	CodeupReviewConfig   *CodeupReviewConfig   // Codeup 评审配置
	CodeAoneReviewConfig *CodeAoneReviewConfig // CodeAone 评审配置
}

// GitlabReviewConfig 定义了 Gitlab 评审的配置结构
// 包含合并请求 ID 和 项目 ID
type GitlabReviewConfig struct {
	MergeRequestId string // 合并请求唯一标识
	ProjectId      string // 项目唯一标识
}

// GithubReviewConfig 定义了 Github 评审的配置结构
// 包含拉取请求编号
type GithubReviewConfig struct {
	PullNumber int `json:"pullNumber"` // 拉取请求编号
}

// CodeupReviewConfig 定义了 Codeup 评审的配置结构
// 包含本地 ID
type CodeupReviewConfig struct {
	LocalId int `json:"localId"` // 本地唯一标识
}

// CodeAoneReviewConfig 定义了 CodeAone 评审的配置结构
// 包含合并请求 ID
type CodeAoneReviewConfig struct {
	MergeRequestId int64 `json:"mergeRequestId"` // 合并请求唯一标识
}

// ConnectConfig 定义了连接配置
// 目前仅包含会话 ID
type ConnectConfig struct {
	SessionId string // 会话唯一标识
}

// Provider 定义了代码平台提供者的基本行为接口
// 实现了合并请求管理、评论管理、文件获取、版本查询等功能
// 所有方法都需要上下文和配置参数
type Provider interface {

	// Platform 返回代码平台标识
	Platform() codeplatform.Platform

	// GetMergeRequest 获取指定配置的合并请求
	// ctx: 上下文信息
	// config: 配置信息
	// 返回值: 合并请求对象和错误信息
	GetMergeRequest(ctx base.Context, config *Config) (*codeplatform.MergeRequest, error)

	// CreateMergeRequestComment 创建合并请求评论
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 创建评论的选项
	// 返回值: 创建的评论对象和错误信息
	CreateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.CreateMergeRequestCommentOpts) (*codeplatform.Comment, error)

	// ListMergeRequestComments 列出合并请求评论
	// ctx: 上下文信息
	// config: 配置信息
	// req: 列出评论的请求参数
	// 返回值: 评论列表响应和错误信息
	ListMergeRequestComments(ctx base.Context, config *Config, req *codeplatform.ListMergeRequestCommentsRequest) (*codeplatform.ListMergeRequestCommentsResponse, error)

	// ResolveMergeRequestComment 解决合并请求评论
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 解决评论的选项
	// 返回值: 错误信息
	ResolveMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.ResolveMergeRequestCommentOpts) error

	// UpdateMergeRequestComment 更新合并请求评论
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 更新评论的选项
	// 返回值: 更新后的评论对象和错误信息
	UpdateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.UpdateMergeRequestCommentOpts) (*codeplatform.Comment, error)

	// DeleteMergeRequestComment 删除合并请求评论
	// ctx: 上下文信息
	// config: 配置信息
	// commentId: 评论唯一标识
	// 返回值: 错误信息
	DeleteMergeRequestComment(ctx base.Context, config *Config, commentId string) error

	// ListMergeRequestAllComments 列出所有合并请求评论
	// ctx: 上下文信息
	// config: 配置信息
	// 返回值: 讨论列表和错误信息
	ListMergeRequestAllComments(ctx base.Context, config *Config) ([]*codeplatform.Discussion, error)

	// ListAllMergeRequestCommitDiffs 列出所有提交差异
	// ctx: 上下文信息
	// config: 配置信息
	// req: 列出差异的请求参数
	// 返回值: 提交差异列表和错误信息
	ListAllMergeRequestCommitDiffs(ctx base.Context, config *Config, req *codeplatform.ListAllMergeRequestCommitDiffsRequest) ([]*codeplatform.CommitDiff, error)

	// ListMergeRequestCommits 列出合并请求提交
	// ctx: 上下文信息
	// config: 配置信息
	// 返回值: 提交列表和错误信息
	ListMergeRequestCommits(ctx base.Context, config *Config) ([]*codeplatform.Commit, error)

	// GetRepositoryFile 获取仓库文件
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 获取文件的选项
	// 返回值: 文件对象和错误信息
	GetRepositoryFile(ctx base.Context, config *Config, opts *codeplatform.GetRepositoryFileOpts) (*codeplatform.RepositoryFile, error)

	// GetVersion 获取代码平台版本
	// ctx: 上下文信息
	// config: 配置信息
	// 返回值: 版本字符串和错误信息
	GetVersion(ctx base.Context, config *Config) (string, error)

	// CompareCommits 比较两个提交
	// ctx: 上下文信息
	// config: 配置信息
	// baseSha: 基础提交哈希
	// headSha: 头部提交哈希
	// 返回值: 提交比较结果和错误信息
	CompareCommits(ctx base.Context, config *Config, baseSha, headSha string) (*codeplatform.CompareCommitResult, error)

	// CreateReview 创建评审
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 创建评审的选项
	// 返回值: 创建评审的响应和错误信息
	CreateReview(ctx base.Context, config *Config, opts *codeplatform.CreateReviewOpts) (*codeplatform.CreateReviewResponse, error)

	// UpdateReview 更新评审
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 更新评审的选项
	// 返回值: 错误信息
	UpdateReview(ctx base.Context, config *Config, opts *codeplatform.UpdateReviewOpts) error

	// CreateCheckRun 创建检查运行
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 创建检查运行的选项
	// 返回值: 创建检查运行的响应和错误信息
	CreateCheckRun(ctx base.Context, config *Config, opts *codeplatform.CreateCheckRunOpts) (*codeplatform.CreateCheckRunResponse, error)

	// UpdateCheckRun 更新检查运行
	// ctx: 上下文信息
	// config: 配置信息
	// opts: 更新检查运行的选项
	// 返回值: 错误信息
	UpdateCheckRun(ctx base.Context, config *Config, opts *codeplatform.UpdateCheckRunOpts) error
}
