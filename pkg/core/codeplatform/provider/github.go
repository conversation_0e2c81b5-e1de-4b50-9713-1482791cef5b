package provider

import (
	"encoding/base64"
	"fmt"
	githubsdk "github.com/google/go-github/v69/github"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	extgithub "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/github"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
	"strings"

	"strconv"
)

const (
	IssueCommentPrefix string = "issueComment-"
	PrCommentPrefix    string = "prComment-"
)

var _ Provider = (*github)(nil)

type github struct {
}

func NewGithub() Provider {
	return &github{}
}

func ParseGithubCommentIdStr(ctx base.Context, commentIdStr string) (int64, codeplatform.CommentType, error) {
	var commentType codeplatform.CommentType
	if strings.HasPrefix(commentIdStr, IssueCommentPrefix) {
		commentType = codeplatform.CommentTypeCommon
		commentIdStr = strings.TrimPrefix(commentIdStr, IssueCommentPrefix)
	} else if strings.HasPrefix(commentIdStr, PrCommentPrefix) {
		commentType = codeplatform.CommentTypeDiscussionNote
		commentIdStr = strings.TrimPrefix(commentIdStr, PrCommentPrefix)
	} else {
		ctx.GetLogger().Error("cannot resolve the comment id", "commentIdStr", commentIdStr)
		return int64(-1), "", commonerrors.New(codes.ErrInvalidParameter, "commentIdStr", commentIdStr)
	}
	commentId, err := cast.ToInt64E(commentIdStr)
	if err != nil {
		return int64(-1), "", errors.WithStack(err)
	}
	return commentId, commentType, nil
}

func GenerateGithubCommentIdStr(ctx base.Context, commentId int64, commentType codeplatform.CommentType) (string, error) {
	switch commentType {
	case codeplatform.CommentTypeDiscussionNote:
		return fmt.Sprintf("%s%s", PrCommentPrefix, strconv.FormatInt(commentId, 10)), nil
	case codeplatform.CommentTypeCommon:
		return fmt.Sprintf("%s%s", IssueCommentPrefix, strconv.FormatInt(commentId, 10)), nil
	default:
		ctx.GetLogger().Error("Invalid comment type", "comment type", commentType)
		return "", commonerrors.New(codes.ServerInternalError)
	}
}

func validateGithubConfig(config *Config) error {
	if config == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config")
	}
	if config.Identity == nil {
		return commonerrors.New(codes.ErrInvalidParameter, "config.Identity")
	}

	if config.ProjectConfig.GithubProjectConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameter, "config.GithubProjectConfig")
	}
	if config.ProjectConfig.GithubProjectConfig.Owner == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.GithubProjectConfig.Owner")
	}
	if config.ProjectConfig.GithubProjectConfig.Repo == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.GithubProjectConfig.Repo")
	}
	return nil
}

func validateGithubReviewConfig(config *Config) error {
	if config.ReviewConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig")
	}
	if config.ReviewConfig.GithubReviewConfig == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.GithubReviewConfig")
	}

	if config.ReviewConfig.GithubReviewConfig.PullNumber < 0 {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "config.GithubReviewConfig.Number")
	}
	return nil
}

func (g *github) GetMergeRequest(ctx base.Context, config *Config) (*codeplatform.MergeRequest, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo
	prId := config.ReviewConfig.GithubReviewConfig.PullNumber

	pr, err := extgithub.GetMergeRequest(ctx, config.Identity, owner, repo, prId)
	if err != nil {
		ctx.GetLogger().Error("failed to get merge request", "owner", owner, "repo", repo, "prId", prId, "err", err.Error())
		return nil, errors.WithStack(err)
	}

	files, err := extgithub.ListPullRequestAllFiles(ctx, config.Identity, owner, repo, prId)
	if err != nil {
		ctx.GetLogger().Error("failed to list pull request all files", "owner", owner, "repo", repo, "prId", prId, "err", err)
		return nil, errors.WithStack(err)
	}

	createAt := pr.GetCreatedAt()
	updateAt := pr.GetUpdatedAt()
	return &codeplatform.MergeRequest{
		ID:              strconv.Itoa(prId),
		ProjectId:       repo,
		SourceBranch:    pr.GetHead().GetRef(),
		SourceRef:       pr.GetHead().GetRef(),
		TargetBranch:    pr.GetBase().GetRef(),
		Title:           pr.GetTitle(),
		Description:     pr.GetBody(),
		WebURL:          pr.GetHTMLURL(),
		State:           pr.GetState(),
		CreatedAt:       createAt.GetTime(),
		UpdatedAt:       updateAt.GetTime(),
		SourceProjectID: int(pr.GetHead().GetRepo().GetID()),
		TargetProjectID: int(pr.GetBase().GetRepo().GetID()),
		SHA:             pr.GetHead().GetSHA(),
		MergeCommitSHA:  pr.GetMergeCommitSHA(),
		SquashCommitSHA: "",
		Changes:         convertGithubFilesToChanges(files),
		AuthorName:      pr.GetUser().GetLogin(),
		DiffRefs: &codeplatform.DiffRefs{
			BaseSHA: pr.Base.GetSHA(),
			HeadSHA: pr.Head.GetSHA(),
		},
	}, nil
}

func (g *github) UpdateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.UpdateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	ctx.GetLogger().Info("UpdateMergeRequestComment", "opts", opts)
	if opts.Content == nil {
		return nil, commonerrors.New(codes.ErrInvalidParameterWithEmpty, "content")
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo

	commentId, commentType, err := ParseGithubCommentIdStr(ctx, opts.CommentId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	switch commentType {
	case codeplatform.CommentTypeCommon:
		comment := &githubsdk.IssueComment{
			Body: opts.Content,
		}
		comment, err = extgithub.UpdateIssueComment(ctx, config.Identity, owner, repo, commentId, comment)
		if err != nil {
			ctx.GetLogger().Error("failed to update issue comment", "owner", owner, "repo", repo, "commentId", commentId, "err", err)
			return nil, errors.WithStack(err)
		}
		commentIdStr, err := GenerateGithubCommentIdStr(ctx, comment.GetID(), commentType)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return &codeplatform.Comment{
			ID:      commentIdStr,
			Content: comment.GetBody(),
		}, nil
	case codeplatform.CommentTypeDiscussionNote:
		comment := &githubsdk.PullRequestComment{
			Body: opts.Content,
		}
		comment, err = extgithub.UpdatePullRequestComment(ctx, config.Identity, owner, repo, commentId, comment)
		if err != nil {
			ctx.GetLogger().Error("failed to update pull request comment", "owner", owner, "repo", repo, "commentId", commentId, "err", err)
			return nil, errors.WithStack(err)
		}
		commentIdStr, err := GenerateGithubCommentIdStr(ctx, comment.GetID(), commentType)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return &codeplatform.Comment{
			ID:      commentIdStr,
			Content: comment.GetBody(),
		}, nil
	default:
		return nil, commonerrors.New(codes.ErrInvalidParameter, "commentType", commentType)
	}
}

func (g *github) DeleteMergeRequestComment(ctx base.Context, config *Config, inCommentId string) error {
	ctx.GetLogger().Info("DeleteMergeRequestComment", "inCommentId", inCommentId)
	if err := validateGithubConfig(config); err != nil {
		return errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo

	commentId, commentType, err := ParseGithubCommentIdStr(ctx, inCommentId)
	if err != nil {
		return errors.WithStack(err)
	}
	switch commentType {
	case codeplatform.CommentTypeCommon:
		err = extgithub.DeleteIssueComment(ctx, config.Identity, owner, repo, commentId)
		if err != nil {
			ctx.GetLogger().Error("failed to delete issue comment", "owner", owner, "repo", repo, "commentId", commentId, "err", err.Error())
			return errors.WithStack(err)
		}
		return nil
	case codeplatform.CommentTypeDiscussionNote:
		err = extgithub.DeletePullRequestComment(ctx, config.Identity, owner, repo, commentId)
		if err != nil {
			ctx.GetLogger().Error("failed to delete pull request comment", "owner", owner, "repo", repo, "commentId", commentId, "err", err.Error())
			return errors.WithStack(err)
		}
	}
	return nil
}

func GetTopCommentId(ctx base.Context, config *Config, commentId int64) (*int64, error) {
	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo

	comment, err := extgithub.GetPRComment(ctx, config.Identity, owner, repo, commentId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if comment.InReplyTo == nil {
		ctx.GetLogger().Info("comment is top comment", "commentId", commentId)
		return comment.ID, nil
	} else {
		ctx.GetLogger().Info("comment is reply comment", "commentId", commentId, "topCommentId", *comment.InReplyTo)
		return comment.InReplyTo, nil
	}
}

func (g *github) CreateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.CreateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo
	prId := config.ReviewConfig.GithubReviewConfig.PullNumber

	issueComment := true
	if opts.ReplyToCommentID != nil {
		_, commentType, err := ParseGithubCommentIdStr(ctx, *opts.ReplyToCommentID)
		if err != nil {
			return nil, errors.WithStack(err)
		} else if commentType == codeplatform.CommentTypeDiscussionNote {
			issueComment = false
		}
	} else if opts.Path != nil {
		issueComment = false
	}
	prCommentFn := func() (*codeplatform.Comment, error) {
		pr, err := g.GetMergeRequest(ctx, config)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		comment := &githubsdk.PullRequestComment{
			Body:     ptr.To(opts.Content),
			CommitID: ptr.To(pr.SHA),
		}
		if opts.ReplyToCommentID != nil {
			ctx.GetLogger().Info("reply to comment", "commentId", *opts.ReplyToCommentID)
			commentId, _, err := ParseGithubCommentIdStr(ctx, *opts.ReplyToCommentID)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			ctx.GetLogger().Info("comment id", "commentId", commentId)
			topCommentId, err := GetTopCommentId(ctx, config, commentId)
			ctx.GetLogger().Info("top comment id", "topCommentId", *topCommentId)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			resp, err := extgithub.ReplyPullRequestComment(ctx, config.Identity, owner, repo, prId, *topCommentId, comment)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			commentStr, err := GenerateGithubCommentIdStr(ctx, resp.GetID(), codeplatform.CommentTypeDiscussionNote)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			return &codeplatform.Comment{
				ID:      commentStr,
				Content: resp.GetBody(),
			}, nil
		} else {
			comment.Path = opts.Path
			if opts.EndLine == nil {
				opts.EndLine = opts.StartLine
			}
			if opts.StartLine != nil && opts.EndLine != nil && *opts.StartLine < *opts.EndLine {
				comment.StartLine = opts.StartLine
			}
			comment.Line = opts.EndLine
		}
		resp, err := extgithub.CreatePullRequestComment(ctx, config.Identity, owner, repo, prId, comment)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		commentStr, err := GenerateGithubCommentIdStr(ctx, resp.GetID(), codeplatform.CommentTypeDiscussionNote)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		return &codeplatform.Comment{
			ID:      commentStr,
			Content: resp.GetBody(),
		}, nil
	}

	if !issueComment {
		if result, err := prCommentFn(); err != nil {
			ctx.GetLogger().Error("failed to create pull request comment", "owner", owner, "repo", repo, "prId", prId, "err", err.Error(), "comment", opts)
			//ctx.GetLogger().Error("failed to create pull request comment, try to create issue comment", "owner", owner, "repo", repo, "prId", prId, "err", err.Error())
			return nil, errors.WithStack(err)
		} else {
			return result, nil
		}
		// 失败了改成尝试发布IssueComment
	}
	comment := &githubsdk.IssueComment{
		Body: ptr.To(opts.Content),
	}

	resp, err := extgithub.CreateIssueComment(ctx, config.Identity, owner, repo, prId, comment)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commentStr, err := GenerateGithubCommentIdStr(ctx, resp.GetID(), codeplatform.CommentTypeCommon)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &codeplatform.Comment{
		ID:      commentStr,
		Content: resp.GetBody(),
	}, nil
}

func (g *github) ListMergeRequestComments(ctx base.Context, config *Config, req *codeplatform.ListMergeRequestCommentsRequest) (*codeplatform.ListMergeRequestCommentsResponse, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo
	number := config.ReviewConfig.GithubReviewConfig.PullNumber

	commentId, commentType, err := ParseGithubCommentIdStr(ctx, req.CommentId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	switch commentType {
	case codeplatform.CommentTypeCommon:
		issueComment, err := extgithub.GetIssueComment(ctx, config.Identity, owner, repo, commentId)
		if err != nil {
			if commonerrors.Is(err, codes.ErrMergeRequestCommentNotFound) {
				return &codeplatform.ListMergeRequestCommentsResponse{
					Type:     commentType,
					Comments: []*codeplatform.Comment{},
				}, nil
			}
			return nil, errors.WithStack(err)
		}
		commentIdStr, err := GenerateGithubCommentIdStr(ctx, issueComment.GetID(), commentType)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		comment := &codeplatform.Comment{
			ID:              commentIdStr,
			Content:         issueComment.GetBody(),
			Username:        issueComment.GetUser().GetLogin(),
			CreateTimestamp: issueComment.GetCreatedAt().Unix(),
		}
		return &codeplatform.ListMergeRequestCommentsResponse{
			Type:     commentType,
			Comments: []*codeplatform.Comment{comment},
		}, nil
	case codeplatform.CommentTypeDiscussionNote:
		topCommentId, err := GetTopCommentId(ctx, config, commentId)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		comments, err := extgithub.ListPullRequestComments(ctx, config.Identity, owner, repo, number)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		res := codeplatform.ListMergeRequestCommentsResponse{
			Type:     commentType,
			Comments: []*codeplatform.Comment{},
		}
		for _, comment := range comments {
			if comment.GetInReplyTo() == *topCommentId || comment.GetID() == *topCommentId {
				if res.CodePosition == nil && comment.Position != nil {
					res.CodePosition = &codeplatform.CodePosition{
						Path:      comment.Path,
						StartLine: comment.StartLine,
						EndLine:   comment.OriginalLine,
						LineType:  ptr.To("new"),
					}
				}
				commentIdStr, err := GenerateGithubCommentIdStr(ctx, comment.GetID(), commentType)
				if err != nil {
					return nil, errors.WithStack(err)
				}
				ctx.GetLogger().Info("list comments", "comment", comment)
				res.Comments = append(res.Comments, &codeplatform.Comment{
					ID:              commentIdStr,
					Content:         comment.GetBody(),
					Username:        comment.GetUser().GetLogin(),
					CreateTimestamp: comment.GetCreatedAt().Unix(),
					CommitId:        util.GetString(comment.CommitID),
				})
			}
		}
		return &res, nil

	default:
		return nil, commonerrors.New(codes.ErrInvalidParameter, "commentType", commentType)
	}
}

func (g *github) ResolveMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.ResolveMergeRequestCommentOpts) error {
	return commonerrors.New(codes.ErrUnsupportedPlatform, "ResolveMergeRequestComment", "github")
}

func (g *github) ListMergeRequestAllComments(ctx base.Context, config *Config) ([]*codeplatform.Discussion, error) {
	return nil, commonerrors.New(codes.ErrUnsupportedPlatform, "ListMergeRequestAllComments", "github")

}

func (g *github) ListAllMergeRequestCommitDiffs(ctx base.Context, config *Config, req *codeplatform.ListAllMergeRequestCommitDiffsRequest) ([]*codeplatform.CommitDiff, error) {
	return nil, commonerrors.New(codes.ErrUnsupportedPlatform, "ListAllMergeRequestCommitDiffs", "github")

}

func (g *github) GetRepositoryFile(ctx base.Context, config *Config, opts *codeplatform.GetRepositoryFileOpts) (*codeplatform.RepositoryFile, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo

	fileContent, err := extgithub.GetRepositoryFile(ctx, config.Identity, owner, repo, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	//content, err := decodeGithubFileContent(fileContent)
	//if err != nil {
	//	return nil, errors.WithStack(err)
	//}
	content := *fileContent.Content
	return &codeplatform.RepositoryFile{
		FileName:        fileContent.GetName(),
		FilePath:        fileContent.GetPath(),
		Size:            fileContent.GetSize(),
		Encoding:        fileContent.GetEncoding(),
		Content:         content,
		ExecuteFileMode: false,
		Ref:             util.GetString(opts.Ref),
		SHA256:          fileContent.GetSHA(),
	}, nil
}

func decodeGithubFileContent(r *githubsdk.RepositoryContent) (string, error) {
	var encoding string
	if r.Encoding != nil {
		encoding = *r.Encoding
	}

	switch encoding {
	case "base64":
		if r.Content == nil {
			return "", errors.New("malformed response: base64 encoding of null content")
		}
		var decodedStr []string
		split := strings.Split(*r.Content, "\n")
		for _, s := range split {
			c, err := base64.StdEncoding.DecodeString(s)
			if err != nil {
				return "", errors.WithStack(err)
			}
			decodedStr = append(decodedStr, string(c))
		}
		return strings.Join(decodedStr, ""), nil
	case "":
		if r.Content == nil {
			return "", nil
		}
		return *r.Content, nil
	case "none":
		return "", errors.New("unsupported content encoding: none, this may occur when file size > 1 MB, if that is the case consider using DownloadContents")
	default:
		return "", fmt.Errorf("unsupported content encoding: %v", encoding)
	}
}

func convertGithubFilesToChanges(files []*githubsdk.CommitFile) []*codeplatform.MergeRequestDiff {
	var changes []*codeplatform.MergeRequestDiff
	for _, file := range files {
		changes = append(changes, &codeplatform.MergeRequestDiff{
			OldPath:     file.GetPreviousFilename(),
			NewPath:     file.GetFilename(),
			AMode:       "",
			BMode:       "",
			Diff:        file.GetPatch(),
			NewFile:     file.GetStatus() == "added",
			RenamedFile: file.GetStatus() == "renamed",
			DeletedFile: file.GetStatus() == "removed",
		})
	}
	return changes
}

func (g *github) CreateReview(ctx base.Context, config *Config, opts *codeplatform.CreateReviewOpts) (*codeplatform.CreateReviewResponse, error) {

	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo
	prId := config.ReviewConfig.GithubReviewConfig.PullNumber

	// 获取PR信息用于获取提交SHA
	pr, err := g.GetMergeRequest(ctx, config)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 准备创建评审请求
	review := &githubsdk.PullRequestReviewRequest{
		CommitID: ptr.To(pr.SHA), // 设置整个评审的提交ID
	}

	// 如果提供了评论，添加到评审中
	if opts != nil && len(opts.Comments) > 0 {
		comments := make([]*githubsdk.DraftReviewComment, 0, len(opts.Comments))
		for _, comment := range opts.Comments {
			draftComment := &githubsdk.DraftReviewComment{
				Path: comment.Position.Path,
				Body: ptr.To(comment.Content),
			}

			switch {
			// 多行评论情况：start < end
			case comment.Position.StartLine != nil && comment.Position.EndLine != nil && *comment.Position.StartLine < *comment.Position.EndLine:
				draftComment.StartLine = comment.Position.StartLine
				draftComment.Line = comment.Position.EndLine // GitHub 要求 line 参数对应 end_line

			// 单行评论情况（优先使用 end_line）
			case comment.Position.EndLine != nil:
				draftComment.Line = comment.Position.EndLine

			// 单行评论回退（使用 start_line）
			case comment.Position.StartLine != nil:
				draftComment.Line = comment.Position.StartLine

			default:
				return nil, fmt.Errorf("comment must specify at least one line number")
			}

			comments = append(comments, draftComment)
		}
		review.Comments = comments
	}

	// 创建评审
	prReview, err := extgithub.CreateReview(ctx, config.Identity, owner, repo, prId, review)
	if err != nil {
		ctx.GetLogger().Error("failed to create PR review", "owner", owner, "repo", repo, "prId", prId, "err", err.Error())
		return nil, errors.WithStack(err)
	}

	comments, err := extgithub.ListReviewComments(ctx, config.Identity, owner, repo, prId, prReview.GetID())
	if err != nil {
		ctx.GetLogger().Error("failed to list review comments", "owner", owner, "repo", repo, "prId", prId, "err", err.Error())
		return nil, errors.WithStack(err)
	}
	result := &codeplatform.CreateReviewResponse{
		ReviewId: ptr.To(strconv.FormatInt(prReview.GetID(), 10)),
		Comments: []*codeplatform.Comment{},
	}
	for _, c := range comments {
		result.Comments = append(result.Comments, &codeplatform.Comment{
			ID:       fmt.Sprintf("%s%s", PrCommentPrefix, strconv.FormatInt(c.GetID(), 10)),
			Username: c.GetUser().GetLogin(),
			Content:  c.GetBody(),
		})
	}

	return result, nil
}

// UpdateReview 更新 review 的内容，AR 会把评审总结更新到 review 内容里面。
func (g *github) UpdateReview(ctx base.Context, config *Config, opts *codeplatform.UpdateReviewOpts) error {
	if err := validateGithubConfig(config); err != nil {
		return errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo
	prId := config.ReviewConfig.GithubReviewConfig.PullNumber

	reviewId, err := strconv.ParseInt(opts.ReviewId, 10, 64)
	if err != nil {
		ctx.GetLogger().Error("failed to parse review id", "err", err.Error(), "reviewId", opts.ReviewId)
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "sessionId")
	}
	return extgithub.UpdateReview(ctx, config.Identity, owner, repo, prId, reviewId, opts.Content)
}

func (g *github) CreateCheckRun(ctx base.Context, config *Config, opts *codeplatform.CreateCheckRunOpts) (*codeplatform.CreateCheckRunResponse, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	prInfo, err := g.GetMergeRequest(ctx, config)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo

	checkRun, err := extgithub.CreateCheckRun(ctx, config.Identity, owner, repo, prInfo.SHA, opts)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &codeplatform.CreateCheckRunResponse{
		Id:     strconv.FormatInt(checkRun.GetID(), 10),
		Status: ptr.To(checkRun.GetStatus()),
	}, nil

}

func (g *github) UpdateCheckRun(ctx base.Context, config *Config, opts *codeplatform.UpdateCheckRunOpts) error {
	if err := validateGithubConfig(config); err != nil {
		return errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo

	if err := extgithub.UpdateCheckRun(ctx, config.Identity, owner, repo, opts); err != nil {
		return errors.WithStack(err)
	}
	return nil

}

func (g *github) GetVersion(ctx base.Context, config *Config) (string, error) {
	ctx.GetLogger().Warn("unexpected to get github version")
	return "", nil
}

func (g *github) ListMergeRequestCommits(ctx base.Context, config *Config) ([]*codeplatform.Commit, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}
	if err := validateGithubReviewConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo
	prId := config.ReviewConfig.GithubReviewConfig.PullNumber

	var result []*codeplatform.Commit

	commits, err := extgithub.ListPullRequestCommits(ctx, config.Identity, owner, repo, prId)
	if err != nil {
		ctx.GetLogger().Error("failed to list pull request commits", "owner", owner, "repo", repo, "prId", prId, "err", err.Error())
		return nil, errors.WithStack(err)
	}
	for _, c := range commits {
		result = append(result, &codeplatform.Commit{
			Sha:          c.GetSHA(),
			AuthorEmail:  c.GetAuthor().GetEmail(),
			AuthorName:   c.GetAuthor().GetLogin(),
			AuthoredDate: ptr.To(c.GetCommit().GetAuthor().GetDate().Time),
			URL:          c.GetURL(),
			Message:      c.GetCommit().GetMessage(),
		})
	}
	return result, nil
}

func (g *github) CompareCommits(ctx base.Context, config *Config, baseSha, headSha string) (*codeplatform.CompareCommitResult, error) {
	if err := validateGithubConfig(config); err != nil {
		return nil, errors.WithStack(err)
	}

	owner := config.ProjectConfig.GithubProjectConfig.Owner
	repo := config.ProjectConfig.GithubProjectConfig.Repo

	compares, err := extgithub.Compare(ctx, config.Identity, owner, repo, baseSha, headSha)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	comparedFiles := compares.Files
	if len(comparedFiles) < 1 {
		ctx.GetLogger().Warn("no compared files", "owner", owner, "repo", repo, "baseSha", baseSha, "headSha", headSha)
		return &codeplatform.CompareCommitResult{
			CommitDiffs: []*codeplatform.MergeRequestDiff{},
		}, nil
	}
	return &codeplatform.CompareCommitResult{
		CommitDiffs: convertGithubFilesToChanges(compares.Files),
	}, nil
}

func (g *github) Platform() codeplatform.Platform {
	return codeplatform.PlatformGithub
}
