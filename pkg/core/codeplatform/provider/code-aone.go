package provider

import (
	"fmt"
	errors2 "github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	codeaone2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/code_aone"
	"strconv"
)

type codeAone struct{}

func NewCodeAone() Provider {
	return &codeAone{}
}
func validateCodeAoneConfig(config *Config) error {
	if config == nil {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "config")
	}

	if config.ProjectConfig == nil {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig")
	}
	if config.ProjectConfig.CodeAoneProjectConfig == nil {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig.CodeupProjectConfig"))
	}

	if config.ProjectConfig.CodeAoneProjectConfig.ProjectId < 1 {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ProjectConfig.CodeupProjectConfig.ProjectId"))
	}

	return nil
}

func validateCodeAoneReviewConfig(config *Config) error {
	if config.ReviewConfig == nil {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig")
	}
	if config.ReviewConfig.CodeAoneReviewConfig == nil {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig.CodeAoneReviewConfig")
	}

	if config.ReviewConfig.CodeAoneReviewConfig.MergeRequestId < 1 {
		return errors2.WithStack(errors.New(codes.ErrInvalidParameterWithEmpty, "config.ReviewConfig.CodeAoneReviewConfig.MergeRequestId"))
	}

	return nil
}
func (c *codeAone) GetMergeRequest(ctx base.Context, config *Config) (*codeplatform.MergeRequest, error) {
	if err := validateCodeAoneConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeAoneReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId := config.ProjectConfig.CodeAoneProjectConfig.ProjectId
	mrId := config.ReviewConfig.CodeAoneReviewConfig.MergeRequestId
	cli := codeaone2.NewClient()
	mr, err := cli.GetMergeRequest(ctx, projectId, mrId, config.Identity.PlatformToken)
	if err != nil {
		ctx.GetLogger().Error("failed to get merge request", "projectId", projectId, "mrId", mrId, "err", err)
		return nil, errors2.WithStack(err)
	}
	changes, err := cli.ListMergeRequestChanges(ctx, projectId, mrId, config.Identity.PlatformToken)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	diffs, err := convertChangesToDiffs(ctx, projectId, mrId, config.Identity.PlatformToken, changes)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	pushRecords, err := cli.ListMergeRequestPushRecords(ctx, projectId, mrId, config.Identity.PlatformToken)
	if err != nil {
		ctx.GetLogger().Error("failed to list merge request push records", "projectId", projectId, "mrId", mrId, "err", err)
		return nil, errors2.WithStack(err)
	}
	if len(*pushRecords) == 0 {
		ctx.GetLogger().Error("no push records found for merge request", "projectId", projectId, "mrId", mrId)
		return nil, errors.New(codes.ErrGetMergeRequestPushRecords, "no push records found")
	}

	commitInfo := (*pushRecords)[0]
	return &codeplatform.MergeRequest{
		ID:              strconv.Itoa(mr.IID),
		ProjectId:       strconv.Itoa(mr.ProjectID),
		SourceBranch:    mr.SourceBranch,
		SourceRef:       mr.SourceBranch,
		TargetBranch:    mr.TargetBranch,
		Title:           mr.Title,
		Description:     mr.Description,
		WebURL:          mr.DetailURL,
		State:           mr.State,
		CreatedAt:       mr.CreatedAt,
		UpdatedAt:       mr.UpdatedAt,
		SourceProjectID: mr.SourceProjectID,
		TargetProjectID: int(mr.TargetProjectID),
		SHA:             commitInfo.BaseRevision,
		MergeCommitSHA:  "",
		SquashCommitSHA: "",
		Changes:         diffs,
		AuthorName:      mr.Author.Username,
		DiffRefs: &codeplatform.DiffRefs{
			BaseSHA: commitInfo.OldRevision,
			HeadSHA: commitInfo.NewRevision,
		},
	}, nil
}

func convertChangesToDiffs(ctx base.Context, projectId, mrId int64, platformToken string, changes *codeaone2.CodeReviewChangeTreeRspDTO) ([]*codeplatform.MergeRequestDiff, error) {
	cli := codeaone2.NewClient()
	var result []*codeplatform.MergeRequestDiff
	for _, change := range changes.ChangesetDTOS {
		diff, err := cli.GetMergeRequestChangeDiff(ctx, projectId, mrId, change.Ref, platformToken)
		if err != nil {
			return nil, errors2.WithStack(err)
		}
		result = append(result, &codeplatform.MergeRequestDiff{
			OldPath:     change.OldPath,
			NewPath:     change.NewPath,
			AMode:       change.Amode,
			BMode:       change.Bmode,
			Diff:        diff.Diff,
			NewFile:     change.NewFile,
			RenamedFile: change.RenamedFile,
			DeletedFile: change.DeletedFile,
		})
	}
	return result, nil
}

func (c *codeAone) CreateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.CreateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if err := validateCodeAoneConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeAoneReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId := config.ProjectConfig.CodeAoneProjectConfig.ProjectId
	mrId := config.ReviewConfig.CodeAoneReviewConfig.MergeRequestId

	platformToken := config.Identity.PlatformToken
	cli := codeaone2.NewClient()
	// 如果没有Path 和 replyToCommentID，判断为全局评论。通过drafts规避Aone平台comment的长度限制3414
	if opts.Path == nil && opts.ReplyToCommentID == nil {
		preComments, err := c.ListMergeRequestComments(ctx, config, &codeplatform.ListMergeRequestCommentsRequest{})
		if err != nil {
			ctx.GetLogger().Error("failed to list merge request comments", "projectId", projectId, "mrId", mrId)
			return nil, errors2.WithStack(err)
		}
		preCommentsMap := make(map[string]*codeplatform.Comment)
		for _, comment := range preComments.Comments {
			preCommentsMap[comment.ID] = comment
		}
		req := codeaone2.MergeRequestDraftsParams{
			MergeRequestID: mrId,
			ProjectID:      projectId,
			CommitDraftsDTO: codeaone2.CommitDraftsDTO{
				LabelIds: "",
				Note:     opts.Content,
			},
		}

		_, err = cli.PostMergeRequestDrafts(ctx, &req, platformToken)
		if err != nil {
			ctx.GetLogger().Error("failed to post merge request drafts", "projectId", projectId, "mrId", mrId, "content", opts.Content)
			return nil, errors2.WithStack(err)
		}

		postComments, err := c.ListMergeRequestComments(ctx, config, &codeplatform.ListMergeRequestCommentsRequest{})
		if err != nil {
			return nil, errors2.WithStack(err)
		}
		for _, comment := range postComments.Comments {
			if _, ok := preCommentsMap[comment.ID]; !ok {
				return &codeplatform.Comment{
					ID:              comment.ID,
					Content:         comment.Content,
					Username:        comment.Username,
					CreateTimestamp: comment.CreateTimestamp,
				}, nil
			}
		}
	}

	// FIXME: Aone平台单一评论长度限制3414，需要做截断处理
	req := codeaone2.MergeRequestCommentParams{
		MergeRequestID: mrId,
		ProjectID:      projectId,
		InputDTO: codeaone2.MergeRequestCommentInputDTO{
			IsDraft: false,
			Note:    opts.Content,
		},
	}

	if opts.Draft != nil {
		req.InputDTO.IsDraft = *opts.Draft
	}

	if opts.Resolved != nil {
		if *opts.Resolved {
			req.InputDTO.Closed = 1
		} else {
			req.InputDTO.Closed = 0
		}
	}

	// 判断全局评论还是行间评论
	if opts.Path != nil {
		if opts.StartLine == nil || opts.EndLine == nil || opts.LineType == nil {
			return nil, errors.New(codes.ErrInvalidParameterWithEmpty, "start_line, end_line and line_type")
		}
		req.InputDTO.Path = *opts.Path
		req.InputDTO.Line = int64(*opts.StartLine)
		req.InputDTO.ToLine = int64(*opts.EndLine)
		if *opts.LineType == "new" {
			req.InputDTO.Side = "right"
		} else {
			req.InputDTO.Side = "left"
		}
	}
	if opts.ReplyToCommentID != nil {
		parentNoteId, err := strconv.Atoi(*opts.ReplyToCommentID)
		if err != nil {
			return nil, errors2.WithStack(err)
		}
		req.InputDTO.ParentNoteID = int64(parentNoteId)
	}
	comment, err := cli.CreateMergeRequestComment(ctx, &req, platformToken)
	if err != nil {
		ctx.GetLogger().Error("failed to create merge request comment", "projectId", projectId, "mrId", mrId, "comment", opts.Content)
		return nil, errors.New(codes.ErrCreateMergeRequestComment, err)
	}
	return &codeplatform.Comment{
		ID:              strconv.FormatInt(comment.ID, 10),
		Content:         comment.Note,
		Username:        comment.Author.Username,
		CreateTimestamp: comment.CreatedAt.Unix(),
	}, nil
}

func (c *codeAone) ListMergeRequestComments(ctx base.Context, config *Config, req *codeplatform.ListMergeRequestCommentsRequest) (*codeplatform.ListMergeRequestCommentsResponse, error) {
	if err := validateCodeAoneConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeAoneReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId := config.ProjectConfig.CodeAoneProjectConfig.ProjectId
	mrId := config.ReviewConfig.CodeAoneReviewConfig.MergeRequestId

	platformToken := config.Identity.PlatformToken
	cli := codeaone2.NewClient()
	resp, err := cli.ListMergeRequestAllComments(ctx, projectId, mrId, platformToken)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	if req.CommentId != "" {
		for _, comment := range *resp {
			if comment.ID == cast.ToInt64(req.CommentId) {
				return &codeplatform.ListMergeRequestCommentsResponse{
					Comments: []*codeplatform.Comment{
						{
							ID:              strconv.FormatInt(comment.ID, 10),
							Content:         comment.Note,
							Username:        comment.Author.Username,
							CreateTimestamp: comment.CreatedAt.Unix(),
						},
					},
				}, nil
			}
		}
	}
	return &codeplatform.ListMergeRequestCommentsResponse{
		Comments: convertComments(resp),
		Type:     codeplatform.CommentTypeCommon,
	}, nil
}

func convertComments(comments *[]codeaone2.MergeRequestCommentOutputDTO) []*codeplatform.Comment {
	var result []*codeplatform.Comment
	for _, comment := range *comments {
		result = append(result, &codeplatform.Comment{
			ID:              strconv.FormatInt(comment.ID, 10),
			Content:         comment.Note,
			Username:        comment.Author.Username,
			CreateTimestamp: comment.CreatedAt.Unix(),
		})
	}
	return result
}

func (c *codeAone) ResolveMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.ResolveMergeRequestCommentOpts) error {
	return errors.New(codes.ErrUnsupportedPlatform, "ResolveMergeRequestComment", "codeaone")
}

func (c *codeAone) UpdateMergeRequestComment(ctx base.Context, config *Config, opts *codeplatform.UpdateMergeRequestCommentOpts) (*codeplatform.Comment, error) {
	if opts.Content == nil {
		return nil, errors.New(codes.ErrInvalidParameterWithEmpty, "content")
	}

	if err := validateCodeAoneConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeAoneReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId := config.ProjectConfig.CodeAoneProjectConfig.ProjectId
	mrId := config.ReviewConfig.CodeAoneReviewConfig.MergeRequestId

	platformToken := config.Identity.PlatformToken
	cli := codeaone2.NewClient()
	if _, err := cli.UpdateMergeRequestComment(ctx, projectId, mrId, platformToken, opts); err != nil {
		return nil, errors2.WithStack(err)
	}
	return &codeplatform.Comment{
		ID:      opts.CommentId,
		Content: *opts.Content,
	}, nil
}

func (c *codeAone) DeleteMergeRequestComment(ctx base.Context, config *Config, commentId string) error {
	return errors.New(codes.ErrUnsupportedPlatform, "DeleteMergeRequestComment", "codeaone")
}

func (c *codeAone) ListMergeRequestAllComments(ctx base.Context, config *Config) ([]*codeplatform.Discussion, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "ListMergeRequestAllComments", "codeaone")
}

func (c *codeAone) ListAllMergeRequestCommitDiffs(ctx base.Context, config *Config, req *codeplatform.ListAllMergeRequestCommitDiffsRequest) ([]*codeplatform.CommitDiff, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "ListAllMergeRequestCommitDiffs", "codeaone")
}

func (c *codeAone) ListMergeRequestCommits(ctx base.Context, config *Config) ([]*codeplatform.Commit, error) {
	if err := validateCodeAoneConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}
	if err := validateCodeAoneReviewConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId := config.ProjectConfig.CodeAoneProjectConfig.ProjectId
	mrId := config.ReviewConfig.CodeAoneReviewConfig.MergeRequestId

	platformToken := config.Identity.PlatformToken
	cli := codeaone2.NewClient()
	pushRecords, err := cli.ListMergeRequestPushRecords(ctx, projectId, mrId, platformToken)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	var result []*codeplatform.Commit
	for _, pushRecord := range *pushRecords {
		commits := pushRecord.Commits
		for _, c := range commits {
			result = append(result, &codeplatform.Commit{
				Sha:           c.SHA,
				AuthorName:    c.Author.Name,
				AuthoredDate:  c.AuthoredDate,
				CommittedDate: c.CommittedDate,
				Message:       c.Message,
			})
		}
	}
	return result, nil
}

func (c *codeAone) CreateReview(ctx base.Context, config *Config, opts *codeplatform.CreateReviewOpts) (*codeplatform.CreateReviewResponse, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "CreateReview", "codeaone")
}

func (c *codeAone) UpdateReview(ctx base.Context, config *Config, opts *codeplatform.UpdateReviewOpts) error {
	return errors.New(codes.ErrUnsupportedPlatform, "UpdateReview", "codeaone")
}

func (c *codeAone) CreateCheckRun(ctx base.Context, config *Config, opts *codeplatform.CreateCheckRunOpts) (*codeplatform.CreateCheckRunResponse, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "CreateCheckRun", "codeaone")
}

func (c *codeAone) UpdateCheckRun(ctx base.Context, config *Config, opts *codeplatform.UpdateCheckRunOpts) error {
	return errors.New(codes.ErrUnsupportedPlatform, "UpdateCheckRun", "codeaone")
}

func (c *codeAone) GetRepositoryFile(ctx base.Context, config *Config, opts *codeplatform.GetRepositoryFileOpts) (*codeplatform.RepositoryFile, error) {
	if err := validateCodeAoneConfig(config); err != nil {
		return nil, errors2.WithStack(err)
	}

	projectId := config.ProjectConfig.CodeAoneProjectConfig.ProjectId
	platformToken := config.Identity.PlatformToken

	if opts.Ref == nil {
		return nil, errors.New(codes.ErrInvalidParameterWithEmpty, "ref")
	}
	cli := codeaone2.NewClient()
	getFileRequest := codeaone2.GetRepositoryFilesParams{
		ProjectID: projectId,
		FilePath:  opts.Path,
		Ref:       *opts.Ref,
	}

	resp, err := cli.GetRepositoryFile(ctx, &getFileRequest, platformToken)
	if err != nil {
		return nil, errors2.WithStack(err)
	}
	if resp.Encoding != "base64" {
		// 目前是AR层面解码文件，AR层面暂时只支持base64编码，其他编码方式暂不支持
		return nil, errors.New(codes.ServerInternalError, fmt.Sprintf("unsupported encoding %s", resp.Encoding))
	}
	return &codeplatform.RepositoryFile{
		BlobID:          resp.BlobId,
		CommitID:        resp.CommitId,
		Content:         resp.Content,
		Encoding:        resp.Encoding,
		ExecuteFileMode: false,
		FileName:        resp.FileName,
		FilePath:        resp.FilePath,
		LastCommitID:    resp.LastCommitId,
		Ref:             resp.Ref,
		SHA256:          "",
		Size:            cast.ToInt(resp.Size),
	}, nil
}

func (c *codeAone) GetVersion(ctx base.Context, config *Config) (string, error) {
	return "", errors.New(codes.ErrUnsupportedPlatform, "GetVersion", "codeaone")
}

func (c *codeAone) CompareCommits(ctx base.Context, config *Config, baseSha, headSha string) (*codeplatform.CompareCommitResult, error) {
	return nil, errors.New(codes.ErrUnsupportedPlatform, "CompareCommits", "codeaone")
}

func (c *codeAone) Platform() codeplatform.Platform {
	return codeplatform.PlatformCodeaone
}
