package provider

import (
	"fmt"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	sdk "gitlab.com/gitlab-org/api/client-go"
	"k8s.io/utils/ptr"
	"strconv"
	"strings"
)

func convertGitlabMergeRequest(mr *sdk.MergeRequest) *codeplatform.MergeRequest {
	return &codeplatform.MergeRequest{
		ID:              strconv.Itoa(mr.IID),
		ProjectId:       strconv.Itoa(mr.ProjectID),
		SourceBranch:    mr.SourceBranch,
		SourceRef:       mr.SourceBranch,
		TargetBranch:    mr.TargetBranch,
		Title:           mr.Title,
		Description:     mr.Description,
		WebURL:          mr.WebURL,
		State:           mr.State,
		CreatedAt:       mr.Created<PERSON>t,
		UpdatedAt:       mr.<PERSON>t,
		SourceProjectID: mr.SourceProjectID,
		TargetProjectID: mr.TargetProjectID,
		SHA:             mr.SHA,
		MergeCommitSHA:  mr.MergeCommitSHA,
		SquashCommitSHA: mr.SquashCommitSHA,
		Changes:         convertGitlabMergeRequestDiff(mr.Changes),
		AuthorName:      mr.Author.Username,
		DiffRefs: &codeplatform.DiffRefs{
			BaseSHA: mr.DiffRefs.BaseSha,
			HeadSHA: mr.DiffRefs.HeadSha,
		},
	}
}
func convertGitlabMergeRequestDiff(diffs []*sdk.MergeRequestDiff) []*codeplatform.MergeRequestDiff {
	var result []*codeplatform.MergeRequestDiff
	for _, diff := range diffs {
		result = append(result, &codeplatform.MergeRequestDiff{
			AMode:       diff.AMode,
			BMode:       diff.BMode,
			DeletedFile: diff.DeletedFile,
			Diff:        diff.Diff,
			NewFile:     diff.NewFile,
			NewPath:     diff.NewPath,
			OldPath:     diff.OldPath,
		})
	}
	return result
}
func convertGitlabDiscussion(discussion *sdk.Discussion) *codeplatform.ListMergeRequestCommentsResponse {
	resp := &codeplatform.ListMergeRequestCommentsResponse{
		Type:     codeplatform.CommentTypeDiscussionNote,
		Comments: make([]*codeplatform.Comment, 0),
	}
	for _, note := range discussion.Notes {
		un := note.Author.Username
		createTime := note.CreatedAt
		content := note.Body
		commentId := strings.Join([]string{discussion.ID, strconv.Itoa(note.ID)}, "-")
		resp.Comments = append(resp.Comments, &codeplatform.Comment{
			ID:              commentId,
			Content:         content,
			CreateTimestamp: createTime.Unix(),
			CommitId:        getCommentCommitId(note),
			Username:        un,
		})
		if resp.CodePosition == nil &&
			note.Position != nil {
			if note.Position.LineRange != nil &&
				note.Position.LineRange.StartRange != nil &&
				note.Position.LineRange.EndRange != nil {
				resp.CodePosition = &codeplatform.CodePosition{
					Path:      ptr.To(note.Position.NewPath),
					StartLine: ptr.To(note.Position.LineRange.StartRange.NewLine),
					EndLine:   ptr.To(note.Position.LineRange.EndRange.NewLine),
					LineType:  ptr.To("new"),
				}
			} else {
				// 单行评论先以new_line为准
				resp.CodePosition = &codeplatform.CodePosition{
					Path:      ptr.To(note.Position.NewPath),
					StartLine: ptr.To(note.Position.NewLine),
					EndLine:   ptr.To(note.Position.NewLine),
					LineType:  ptr.To("new"),
				}
			}
		}
	}
	return resp
}
func convertGitlabDiscussions(discussions []*sdk.Discussion) []*codeplatform.Discussion {
	var resp []*codeplatform.Discussion
	for _, d := range discussions {
		t := codeplatform.CommentTypeCommon
		discuss := &codeplatform.Discussion{
			Type:     t,
			Comments: make([]*codeplatform.Comment, 0),
		}
		for _, note := range d.Notes {
			if note.Type == sdk.DiffNote {
				t = codeplatform.CommentTypeDiscussionNote
			}
			un := note.Author.Username
			createTime := note.CreatedAt
			content := note.Body
			discuss.Comments = append(discuss.Comments, &codeplatform.Comment{
				CommitId:        getCommentCommitId(note),
				Content:         content,
				CreateTimestamp: createTime.Unix(),
				Username:        un,
			})
			if discuss.CodePosition == nil &&
				note.Position != nil {
				if note.Position.LineRange != nil &&
					note.Position.LineRange.StartRange != nil &&
					note.Position.LineRange.EndRange != nil {
					discuss.CodePosition = &codeplatform.CodePosition{
						Path:      ptr.To(note.Position.NewPath),
						StartLine: ptr.To(note.Position.LineRange.StartRange.NewLine),
						EndLine:   ptr.To(note.Position.LineRange.EndRange.NewLine),
						LineType:  ptr.To("new"),
					}
				} else {
					// 单行评论先以new_line为准
					discuss.CodePosition = &codeplatform.CodePosition{
						Path:      ptr.To(note.Position.NewPath),
						StartLine: ptr.To(note.Position.NewLine),
						EndLine:   ptr.To(note.Position.NewLine),
						LineType:  ptr.To("new"),
					}
				}
			}
			if note.Resolvable && note.Resolved {
				discuss.Resolved = true
			}
		}
		discuss.Type = t
		resp = append(resp, discuss)
	}
	return resp
}
func convertGitlabFile(file *sdk.File) *codeplatform.RepositoryFile {
	return &codeplatform.RepositoryFile{
		FileName:        file.FileName,
		FilePath:        file.FilePath,
		Size:            file.Size,
		Encoding:        file.Encoding,
		Content:         file.Content,
		ExecuteFileMode: file.ExecuteFilemode,
		Ref:             file.Ref,
		BlobID:          file.BlobID,
		CommitID:        file.CommitID,
		SHA256:          file.SHA256,
		LastCommitID:    file.LastCommitID,
	}

}
func ParseCommentId(ctx base.Context, commentId *string) (*string, *string, error) {
	if commentId == nil {
		return nil, nil, commonerrors.New(codes.ErrInvalidParameterWithEmpty, commentId)
	} else if strings.Count(*commentId, "-") > 1 {
		return nil, nil, commonerrors.New(codes.ErrInvalidParameter, "gitlab commentId", commentId)
	} else if strings.Contains(*commentId, "-") {
		parts := strings.Split(*commentId, "-")
		discussionId := parts[0]
		noteId := parts[1]
		return &discussionId, &noteId, nil
	}
	return nil, commentId, nil
}
func generateCommentIdFromDiscussion(ctx base.Context, discussion *sdk.Discussion) (string, error) {
	if discussion.Notes == nil {
		ctx.GetLogger().Error("discussion has no note", "gitlab discussion", discussion)
		return "", commonerrors.New(codes.ErrInvalidParameter, "gitlab discussion doesn't have note", discussion)
	}
	return fmt.Sprintf("%s-%d", discussion.ID, discussion.Notes[0].ID), nil
}

func getCommentCommitId(note *sdk.Note) string {
	if note == nil {
		return ""
	}
	if note.Position == nil {
		return ""
	}
	return note.Position.HeadSHA
}
