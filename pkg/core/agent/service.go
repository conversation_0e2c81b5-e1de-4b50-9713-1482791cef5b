package agent

import (
	"context"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/cronjob"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

var _ comncore.AgentService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func New() comncore.AgentService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
func (s *service) CreateAgentClass(ctx base.Context, agentClass *agent.Class) error {
	if err := createAgentClass(ctx, agentClass); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) ListAgentClasses(ctx base.Context, options *comncore.ListAgentClassOptions) (*page.PagedItems[agent.Class], error) {
	if result, err := listAgentClasses(ctx, options); err != nil {
		return nil, errors.WithStack(err)
	} else {
		return result, nil
	}
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	<-s.core.GetCronjobService().Started()
	if err := s.core.GetCronjobService().RegisterJob(cronjob.NewJob(
		cronjob.WithName("agent.task.trigger"),
		cronjob.WithCronExpression("*/1 * * * *"),
		cronjob.WithTask(s.triggerAgentTasks),
		cronjob.WithSingletonMode(true),
	)); err != nil {
		return errors.WithStack(err)
	}
	if err := s.core.GetCronjobService().RegisterJob(cronjob.NewJob(
		cronjob.WithName("agent.db.cleaner"),
		cronjob.WithCronExpression("0 0 * * *"),
		cronjob.WithTask(cleanDbRecord),
		cronjob.WithSingletonMode(true),
	)); err != nil {
		return errors.WithStack(err)
	}
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "AgentService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}
