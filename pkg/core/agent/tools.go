package agent

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

func (s *service) ListCodeReviewTools(ctx base.Context, agentId string, req *agent.ListToolDescriptionsRequest) (*page.PagedItems[*agent.CodeReviewTool], error) {
	opt := dao.NewDbOptions(dao.WithKV("enabled", true))
	opt.AddPage(dao.WithPage(req.PageNumber, req.PageSize))
	entities, total, err := dao.ListEntitiesWithPage(ctx, &codeReviewToolEntity{}, opt)
	if err != nil {
		return nil, err
	}
	results := page.TransferSlice(entities, func(e *codeReviewToolEntity) *agent.CodeReviewTool { return e.ToCodeReviewTool() })
	return &page.PagedItems[*agent.CodeReviewTool]{
		Items:      results,
		PageNumber: req.PageNumber,
		PageSize:   req.PageSize,
		TotalSize:  total,
	}, nil
}
