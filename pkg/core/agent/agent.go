package agent

import (
	"fmt"
	"github.com/pkg/errors"
	"github.com/sourcegraph/conc/pool"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/lock"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"k8s.io/utils/ptr"
	"time"
)

func (s *service) CreateAgent(ctx base.Context, agt *agent.CreateAgentRequest) (*agent.AgentInfo, error) {
	switch agt.AgentName {
	case agent.CodeReviewAgent:
		if agt.AgentProperty == nil {
			agt.AgentProperty = &agent.AgentProperty{Enable: true}
		}
		if agt.AgentProperty.CodeReview == nil {
			agt.AgentProperty.CodeReview = agent.NewDefaultCodeReviewProperty()
		}
		if agt.AgentProperty.GithubCodeReview == nil {
			agt.AgentProperty.GithubCodeReview = agent.NewDefaultCodeReviewProperty()
		}
	}

	entity := &agentEntity{
		AgentId:       helper.NewIDWithPrefix("a-"),
		AgentName:     agt.AgentName,
		AgentProperty: agt.AgentProperty,
		UserId:        ctx.GetUid(),
		GmtCreate:     time.Now(),
		GmtModified:   time.Now(),
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToAgentInfo(), nil
}

func (s *service) DeleteAgent(ctx base.Context, agentId string) error {
	if err := dao.DeleteEntities(ctx, &agentEntity{}, dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withAgentId(agentId))); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) UpdateAgent(ctx base.Context, agentId string, req *agent.UpdateAgentRequest) error {
	update := &agentEntity{}
	if req.UserId != nil {
		update.UserId = *req.UserId
	}
	if req.AgentProperty != nil {
		update.AgentProperty = req.AgentProperty
	}

	if err := dao.UpdateEntity(ctx, update, dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withAgentId(agentId))); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) GetAgent(ctx base.Context, agentId string) (*agent.AgentInfo, error) {
	entity, err := dao.GetEntity(ctx, &agentEntity{}, dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withAgentId(agentId)))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return entity.ToAgentInfo(), nil
}

func (s *service) ListAgents(ctx base.Context, options *agent.ListAgentOptions) (*page.PagedItems[*agent.AgentInfo], error) {
	if err := options.PagesParams.Validate(); err != nil {
		return nil, errors.WithStack(err)
	}
	opt := dao.NewDbOptions(dao.WithKV("user_id", ctx.GetUid()))
	if options.AgentName != "" {
		opt.AddWhere(dao.WithKV("agent_name", options.AgentName))
	}

	opt.AddPage(dao.WithPage(options.PagesParams.PageNumber, options.PagesParams.PageSize))
	entities, total, err := dao.ListEntitiesWithPage(ctx, &agentEntity{}, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	results := page.TransferSlice(entities, func(e *agentEntity) *agent.AgentInfo { return e.ToAgentInfo() })
	return &page.PagedItems[*agent.AgentInfo]{
		Items:      results,
		PageNumber: options.PagesParams.PageNumber,
		PageSize:   options.PagesParams.PageSize,
		TotalSize:  total,
	}, nil
}

func (s *service) ToggleAgent(ctx base.Context, agentId string, enable bool) error {
	opt := dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withAgentId(agentId))
	entity, err := dao.GetEntity(ctx, &agentEntity{}, opt)
	if err != nil {
		return errors.WithStack(err)
	}
	if entity.AgentProperty == nil {
		entity.AgentProperty = &agent.AgentProperty{}
	}
	entity.AgentProperty.Enable = enable
	if err := dao.UpdateEntity(ctx, entity, opt); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) PatchAgent(ctx base.Context, agentId string, req *agent.PatchAgentRequest) error {
	opt := dao.NewDbOptions(dao.WithUserId(ctx.GetUid()), withAgentId(agentId))
	entity, err := dao.GetEntity(ctx, &agentEntity{}, opt)
	if err != nil {
		return errors.WithStack(err)
	}
	if entity.AgentProperty == nil {
		entity.AgentProperty = &agent.AgentProperty{}
	}
	if req.Enable != nil {
		entity.AgentProperty.Enable = *req.Enable
	}
	if req.GithubCodeReview != nil {
		entity.AgentProperty.GithubCodeReview = req.GithubCodeReview
	}
	if req.CodeReview != nil {
		entity.AgentProperty.CodeReview = req.CodeReview
	}
	if err := dao.UpdateEntity(ctx, entity, opt); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *service) ListAgentTasks(ctx base.Context, options *agent.ListAgentTaskOptions) (*page.PagedItems[*agent.AgentTaskInfo], error) {
	opt := dao.NewDbOptions()
	opt.AddPage(dao.WithPage(options.PageNumber, options.PageSize))
	if options.WithIdDesc {
		opt.AddPage(dao.WithFieldOrderDesc("id"))
	} else {
		opt.AddPage(dao.WithFieldOrderAsc("id"))
	}
	if options.SessionId != "" {
		opt.AddWhere(dao.WithKV("session_id", options.SessionId))
	}
	if options.AgentId != "" {
		opt.AddWhere(dao.WithKV("request->>agent_id", options.AgentId))
	}
	if len(options.States) > 0 {
		opt.AddWhere(dao.WithKeyValues("state", options.States...))
	}
	if len(options.WithTaskParameterValues) > 0 {
		for k, v := range options.WithTaskParameterValues {
			key := fmt.Sprintf("request->'task_config'->'parameters'->>'%s'", k)
			opt.AddWhere(dao.WithKeyValues(key, v...))
		}
	}
	entities, total, err := dao.ListEntitiesWithPage(ctx, &taskEntity{}, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	items := page.TransferSlice(entities, func(entity *taskEntity) *agent.AgentTaskInfo {
		return entity.ToAgentTaskInfo()
	})
	return &page.PagedItems[*agent.AgentTaskInfo]{
		Items:      items,
		PageNumber: options.PageNumber,
		PageSize:   options.PageSize,
		TotalSize:  total,
	}, nil
}

func (s *service) Step(ctx base.Context, agentId string, sessionId string, taskConfig *agentruntime.TaskConfig) error {
	entity, err := dao.GetEntity(ctx, &agentEntity{}, dao.NewDbOptions(withAgentId(agentId)))
	if err != nil {
		return errors.WithStack(err)
	}

	req := &agentruntime.StepSessionRequest{
		AgentName:  string(entity.AgentName),
		AgentId:    entity.AgentId,
		TaskConfig: taskConfig,
	}

	// 保存t_agent_task
	task := &taskEntity{
		TaskId:    helper.NewIDWithPrefix("t-"),
		SessionId: sessionId,
		State:     agent.TaskStatePending,
		Request:   req,
	}
	if err := dao.InsertEntity(ctx, task); err != nil {
		return errors.WithStack(err)
	}
	// 获取当前Session状态，若空闲则尝试直接发起Task，否则等待定时任务下发Task
	if sessionInfo, err := s.core.GetSessionService().GetSession(ctx, sessionId); err != nil {
		return errors.WithStack(err)
	} else if sessionInfo.State.Busy() {
		ctx.GetLogger().Info("session busy, wait for cron job to trigger task", "sessionId", sessionId)
		return nil
	}
	go util.SafeGoroutineFunc(ctx, func() {
		l := lock.NewLock(lock.NewSessionLockName(sessionId))
		if err := l.TryLock(); err != nil {
			ctx.GetLogger().Warn("Try lock for session failed, wait for cron job to trigger task", "sessionId", sessionId, "err", err)
			return
		}
		defer func() {
			if _, err := l.Unlock(); err != nil {
				// 解锁失败忽略错误
				ctx.GetLogger().Error("unlock failed", "lockName", lock.NewSessionLockName(sessionId), "err", err)
			}
		}()

		// 临界区重新判断会话状态
		if sessInfo, err := s.core.GetSessionService().GetSession(ctx, sessionId); err != nil {
			ctx.GetLogger().Error("get session failed", "sessionId", sessionId, "err", err)
			return
		} else if sessInfo.State.Busy() {
			ctx.GetLogger().Warn("session busy, wait for cron job to trigger task", "sessionId", sessionId)
			return
		}

		if err := s.core.GetAgentRuntimeService().StepSession(ctx, sessionId, req); err != nil {
			ctx.GetLogger().Error("step session failed", "sessionId", sessionId, "req", req, "err", err)
			return
		}
		if err := s.core.GetSessionService().UpdateSession(ctx, sessionId, &session.UpdateSessionRequest{State: ptr.To(session.Running)}); err != nil {
			ctx.GetLogger().Error("update session state failed", "sessionId", sessionId, "err", err)
			return
		}
		if err := dao.UpdateEntity(ctx, &taskEntity{State: agent.TaskStateTriggered}, dao.NewDbOptions(withTaskId(task.TaskId))); err != nil {
			ctx.GetLogger().Error("update task state failed", "taskId", task.TaskId, "err", err)
			return
		}
		ctx.GetLogger().Info("trigger agent task", "taskId", task.TaskId, "sessionId", sessionId)
	})

	return nil
}

func (s *service) ListSnapshots(ctx base.Context, options *agent.ListSnapshotOptions) (*page.PagedItems[agent.Snapshot], error) {
	opt := dao.NewDbOptions()
	pageInfo := page.PagesParams{PageNumber: 1, PageSize: 10}
	if options != nil {
		if options.AgentId != "" {
			opt.AddWhere(withAgentId(options.AgentId))
		}
		if options.SessionId != "" {
			opt.AddWhere(withSessionId(options.SessionId))
		}
		pageInfo = options.Page
	}
	opt.AddPage(dao.WithPage(pageInfo.PageNumber, pageInfo.PageSize))

	entities, total, err := dao.ListEntitiesWithPage(ctx, &agentSnapshotEntity{}, opt)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var items []agent.Snapshot
	for _, entity := range entities {
		items = append(items, *entity.ToSnapshot())
	}
	return &page.PagedItems[agent.Snapshot]{
		Items:      items,
		PageNumber: pageInfo.PageNumber,
		PageSize:   pageInfo.PageSize,
		TotalSize:  total,
	}, nil
}

func (s *service) CreateOrUpdateSnapshot(ctx base.Context, agentId string, sessionId string, data map[string]any) error {
	entity, err := dao.GetEntity(ctx, &agentSnapshotEntity{}, dao.NewDbOptions(withAgentId(agentId), withSessionId(sessionId)))
	if err == nil {
		entity.Data = data
	} else if commonerrors.Is(err, codes.ErrRecordNotFound) {
		entity = &agentSnapshotEntity{
			AgentId:   agentId,
			SessionId: sessionId,
			Data:      data,
		}
	} else {
		return errors.WithStack(err)
	}

	return dao.Save(ctx, entity)
}

func (s *service) triggerAgentTasks() error {
	adminCtx := base.NewContextForAdmin(helper.NewIDWithPrefix("cron-agent-task-"), "triggerAgentTasks")
	taskIdInfos, err := dao.RawScan(adminCtx, pendingTaskTemplate)
	if err != nil {
		adminCtx.GetLogger().Error("scan pending task failed", "err", commonerrors.FormatFullStack(err))
		return errors.WithStack(err)
	}
	if len(taskIdInfos) == 0 {
		return nil
	}
	sessionId2TaskId := map[string]string{}
	for _, taskIdInfo := range taskIdInfos {
		sessionId := taskIdInfo.SessionId
		taskId := taskIdInfo.TaskId
		if _, ok := sessionId2TaskId[sessionId]; !ok {
			sessionId2TaskId[sessionId] = taskId
		}
	}
	var taskIds []string
	for _, v := range sessionId2TaskId {
		taskIds = append(taskIds, v)
	}

	taskInfos, _, err := dao.ListEntitiesAutoPage(adminCtx, &taskEntity{}, dao.NewDbOptions(dao.WithKeyValues("task_id", taskIds...)))
	if err != nil {
		commonerrors.AlertError(adminCtx, commonerrors.AlertScopeAgent, taskIds, "list pending task failed", err)
		return errors.WithStack(err)
	}
	p := pool.New().WithMaxGoroutines(100)
	for i := range taskInfos {
		t := taskInfos[i]
		// 一直失败提交的Agent任务 设置为失败，不会再触发提交任务
		if t.FailedCount > config.GetOrDefaultInt(config.KeyAgentTaskFailureThreshold, config.DefaultAgentTaskFailureThreshold) {
			if err := dao.UpdateEntity(adminCtx, &taskEntity{State: agent.TaskStateFailed}, dao.NewDbOptions(withTaskId(t.TaskId))); err != nil {
				adminCtx.GetLogger().Error("update task state failed", "taskId", t.TaskId, "err", commonerrors.FormatFullStack(err))
				commonerrors.AlertError(adminCtx, commonerrors.AlertScopeAgent, []string{t.SessionId, t.TaskId}, "agent task failed", err)
			}
			continue
		}
		p.Go(func() {
			lk := lock.NewLock(lock.NewSessionLockName(t.SessionId))
			if err := lk.TryLock(); err != nil {
				adminCtx.GetLogger().Warn("try lock failed, ignore it", "lockName", lock.NewSessionLockName(t.SessionId), "err", err)
				return
			}
			defer func() {
				if _, err := lk.Unlock(); err != nil {
					adminCtx.GetLogger().Error("unlock failed", "lockName", lock.NewSessionLockName(t.SessionId), "err", err)
				}
			}()

			sessionInfo, err := s.core.GetSessionService().GetSession(adminCtx, t.SessionId)
			if err != nil {
				adminCtx.GetLogger().Error("get session failed", "sessionId", t.SessionId, "err", err)
				return
			}
			if sessionInfo.State.Busy() {
				adminCtx.GetLogger().Warn("session busy, wait for cron job to trigger task", "sessionId", t.SessionId)
				return
			}

			// 重建CTX，注入用户身份
			ctx := base.NewContextForBareUser(sessionInfo.UserId, helper.NewTraceId(), adminCtx.GetTraceId(), base.SiteTypeUnknown)

			// 临界区重新判断会话状态
			if sessInfo, err := s.core.GetSessionService().GetSession(ctx, t.SessionId); err != nil {
				ctx.GetLogger().Error("get session failed", "sessionId", t.SessionId, "err", err)
				return
			} else if sessInfo.State.Busy() {
				ctx.GetLogger().Warn("session busy, wait for cron job to trigger task", "sessionId", t.SessionId)
				return
			}
			success := false
			defer func() {
				if !success {
					commonerrors.AlertWarning(ctx, commonerrors.AlertScopeAgent, []string{t.SessionId, t.TaskId}, "agent task submit failed, wait for retry", err)
					if err := dao.UpdateEntity(ctx, &taskEntity{FailedCount: t.FailedCount + 1}, dao.NewDbOptions(withTaskId(t.TaskId))); err != nil {
						ctx.GetLogger().Error("update task failed count failed", "err", commonerrors.FormatFullStack(err))
					}
				}
			}()
			if err := s.core.GetAgentRuntimeService().StepSession(ctx, t.SessionId, t.Request); err != nil {
				ctx.GetLogger().Error("step session failed", "err", commonerrors.FormatFullStack(err))
				return
			}
			if err := dao.UpdateEntity(ctx, &taskEntity{State: agent.TaskStateTriggered}, dao.NewDbOptions(withTaskId(t.TaskId))); err != nil {
				ctx.GetLogger().Error("update task state failed", "err", commonerrors.FormatFullStack(err))
				return
			}
			if err := core.GetCoreService().GetSessionService().UpdateSession(ctx, t.SessionId, &session.UpdateSessionRequest{State: ptr.To(session.Running)}); err != nil {
				ctx.GetLogger().Error("update session state failed", "err", commonerrors.FormatFullStack(err))
				return
			}
			ctx.GetLogger().Info("trigger task success", "taskId", t.TaskId, "sessionId", t.SessionId)
			success = true
		})
	}
	p.Wait()
	return nil
}

func (s *service) ShouldSkip(ctx base.Context, agentId, sessionId string) (bool, *agent.SkipReason, error) {
	agentInfo, err := s.GetAgent(ctx, agentId)
	if err != nil {
		return false, nil, errors.WithStack(err)
	}
	if agentInfo.AgentProperty == nil || agentInfo.AgentProperty.SkipConditions == nil {
		return false, nil, nil
	}

	return agentInfo.AgentProperty.SkipConditions.Check(ctx, sessionId)
}
