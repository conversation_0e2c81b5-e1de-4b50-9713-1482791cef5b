package agent

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"gorm.io/gorm/schema"
	"time"
)

func cleanDbRecord() error {
	if !config.GetOrDefaultBool(config.KeyAgentRecordAutoCleanEnabled, true) {
		// 已关闭
		return nil
	}
	// 删除旧数据，保持DB空间充足
	ctx := base.NewContextForAdmin(helper.NewTraceId(), "agent-db-cleaner")
	ctx.GetLogger().Info("start clean db record")

	now := time.Now()

	var cleanFuncs []func() error

	if agentKeepDays := config.GetOrDefaultInt(config.KeyAgentRecordKeepDays, 30); agentKeepDays > 0 {
		olderThan := now.AddDate(0, 0, -agentKeepDays)
		ctx.GetLogger().Info("clean agent record older than", "days", agentKeepDays, "olderThan", olderThan)
		cleanFuncs = append(cleanFuncs, func() error {
			return cleanRecordNotUpdatedSince(ctx, &agentSnapshotEntity{}, olderThan)
		})
	}
	if eventKeepDays := config.GetOrDefaultInt(config.KeyEventRecordKeepDays, 15); eventKeepDays > 0 {
		olderThan := now.AddDate(0, 0, -eventKeepDays)
		ctx.GetLogger().Info("clean event record older than", "days", eventKeepDays, "olderThan", olderThan)
		cleanFuncs = append(cleanFuncs, func() error {
			return cleanRecordOlderThan(ctx, &eventEntity{}, olderThan)
		})
	}

	for _, f := range cleanFuncs {
		if err := f(); err != nil {
			return errors.WithStack(err)
		}
	}
	ctx.GetLogger().Info("clean db record done")
	return nil
}

type eventEntity struct {
}

func (ee *eventEntity) TableName() string {
	return "t_event"
}

func cleanRecordOlderThan(ctx base.Context, table schema.Tabler, olderThan time.Time) error {
	opt := dao.NewDbOptions(dao.WithGmtCreateBefore(olderThan))
	if err := dao.DeleteEntities(ctx, table, opt); err != nil {
		ctx.GetLogger().Error("clean record older than failed", "olderThan", olderThan, "table", table.TableName(), "err", err)
		return errors.WithStack(err)
	}
	return nil
}

func cleanRecordNotUpdatedSince(ctx base.Context, table schema.Tabler, since time.Time) error {
	opt := dao.NewDbOptions(dao.WithGmtModifiedBefore(since))
	if err := dao.DeleteEntities(ctx, table, opt); err != nil {
		ctx.GetLogger().Error("clean record not updated since failed", "since", since, "table", table.TableName(), "err", err)
		return errors.WithStack(err)
	}
	return nil
}
