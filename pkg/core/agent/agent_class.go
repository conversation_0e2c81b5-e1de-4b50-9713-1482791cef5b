package agent

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

func createAgentClass(ctx base.Context, agentClass *agent.Class) error {
	entity := &agentClassEntity{
		AgentType: agentClass.AgentType,
		ClassName: agentClass.ClassName,
	}
	if err := dao.InsertEntity(ctx, entity); err != nil {
		return err
	}
	return nil
}

func listAgentClasses(ctx base.Context, options *comncore.ListAgentClassOptions) (*page.PagedItems[agent.Class], error) {
	dbOpts := dao.NewDbOptions()
	dbOpts.AddPage(dao.WithPage(options.PageNumber, options.PageSize), dao.WithFieldOrderDesc("id"))

	if options.Type != nil {
		dbOpts.AddWhere(dao.WithKV("agent_type", *options.Type))
	}

	entities, totalSize, err := dao.ListEntitiesWithPage[agentClassEntity](ctx, agentClassEntity{}, dbOpts)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := &page.PagedItems[agent.Class]{
		PageNumber: options.PageNumber,
		PageSize:   options.PageSize,
		TotalSize:  totalSize,
	}

	for _, entity := range entities {
		result.Items = append(result.Items, agent.Class{
			AgentType: entity.AgentType,
			ClassName: entity.ClassName,
		})
	}

	return result, nil
}
