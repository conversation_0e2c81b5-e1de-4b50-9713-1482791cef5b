package agent

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"time"
)

type agentClassEntity struct {

	// 自增主键
	Id int64 `gorm:"column:id;primaryKey"`

	// ClassName agentClass 名称
	ClassName string `gorm:"column:class_name"`

	// AgentType agentClass 类型
	AgentType agent.Type `gorm:"column:agent_type"`

	// GmtCreate agentClass 创建时间
	GmtCreate time.Time `gorm:"autoCreateTime:mills;comment:创建时间"`

	// GmtModified agentClass 更新时间
	GmtModified time.Time `gorm:"autoUpdateTime:mills;comment:更新时间"`
}

func (agentClassEntity) TableName() string {
	return "t_agent_class"
}

type agentEntity struct {
	Id            int64                `gorm:"column:id;primaryKey"`
	AgentId       string               `gorm:"column:agent_id"`
	AgentName     agent.AgentName      `gorm:"column:agent_name"`
	UserId        string               `gorm:"column:user_id"`
	AgentProperty *agent.AgentProperty `gorm:"column:agent_property;serializer:json"`
	GmtCreate     time.Time            `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified   time.Time            `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (*agentEntity) TableName() string {
	return "t_agent"
}

func (ae *agentEntity) ToAgentInfo() *agent.AgentInfo {
	return &agent.AgentInfo{
		AgentId:         ae.AgentId,
		AgentName:       ae.AgentName,
		AgentProperty:   ae.AgentProperty,
		CreateTimestamp: ae.GmtCreate.UnixMilli(),
		UpdateTimestamp: ae.GmtModified.UnixMilli(),
	}
}

type agentSnapshotEntity struct {
	Id          int64          `gorm:"column:id;primaryKey"`
	AgentId     string         `gorm:"column:agent_id"`
	SessionId   string         `gorm:"column:session_id"`
	Data        map[string]any `gorm:"column:data;serializer:json"`
	GmtCreate   time.Time      `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified time.Time      `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (ae *agentSnapshotEntity) TableName() string {
	return "t_agent_snapshot"
}

func (ae *agentSnapshotEntity) ToSnapshot() *agent.Snapshot {
	return &agent.Snapshot{
		AgentId:         ae.AgentId,
		SessionId:       ae.SessionId,
		Data:            ae.Data,
		CreateTimestamp: ae.GmtCreate.UnixMilli(),
		UpdateTimestamp: ae.GmtModified.UnixMilli(),
	}
}

type taskEntity struct {
	Id          int64                            `gorm:"column:id;primaryKey"`
	TaskId      string                           `gorm:"column:task_id"`
	SessionId   string                           `gorm:"column:session_id"`
	State       agent.TaskState                  `gorm:"column:state"`
	FailedCount int                              `gorm:"column:failed_count"`
	Request     *agentruntime.StepSessionRequest `gorm:"column:request;serializer:json"`
	GmtCreate   time.Time                        `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified time.Time                        `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (te *taskEntity) TableName() string {
	return "t_agent_task"
}

func (te *taskEntity) ToAgentTaskInfo() *agent.AgentTaskInfo {
	if te.Request == nil {
		te.Request = &agentruntime.StepSessionRequest{}
	}
	return &agent.AgentTaskInfo{
		TaskId:          te.TaskId,
		SessionId:       te.SessionId,
		AgentId:         te.Request.AgentId,
		State:           te.State,
		TaskConfig:      te.Request.TaskConfig,
		CreateTimestamp: te.GmtCreate.UnixMilli(),
		UpdateTimestamp: te.GmtModified.UnixMilli(),
	}
}

type codeReviewToolEntity struct {
	Id          int64                          `gorm:"column:id;primaryKey"`
	Name        string                         `gorm:"column:name"`
	Alias       string                         `gorm:"column:alias"`
	Description string                         `gorm:"column:description"`
	Type        agent.CodeReviewToolType       `gorm:"column:type"`
	Version     string                         `gorm:"column:version"`
	Properties  []agent.CodeReviewToolProperty `gorm:"column:properties;serializer:json"`
	Enabled     bool                           `gorm:"column:enabled"`
	GmtCreate   time.Time                      `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified time.Time                      `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (cte *codeReviewToolEntity) TableName() string {
	return "t_code_review_tool_schema"
}

func (cte *codeReviewToolEntity) ToCodeReviewTool() *agent.CodeReviewTool {
	return &agent.CodeReviewTool{
		Name:        cte.Name,
		Alias:       cte.Alias,
		Description: cte.Description,
		Type:        cte.Type,
		Version:     cte.Version,
		Properties:  cte.Properties,
	}
}
