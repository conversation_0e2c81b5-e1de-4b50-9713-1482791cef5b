package agent

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"testing"
)

func TestAgentProperty_JSONParsing(t *testing.T) {
	t.Run("full_json_parsing", func(t *testing.T) {
		data := []byte(`
            {
                "skipSandbox": true,
                "skipConditions": {
                    "type": "SkipReview",
                    "maxCommits": 5,
                    "titleKeywords": ["chore", "docs"],
                    "diffLines": 100
                }
            }
        `)

		var prop agent.AgentProperty
		err := json.Unmarshal(data, &prop)
		assert.Nil(t, err)

		// 验证基础字段
		assert.True(t, prop.SkipSandbox)

		// 验证 SkipConditions
		sc, ok := prop.SkipConditions.(*SkipReviewConditions)
		assert.True(t, ok)
		assert.Equal(t, agent.SkipConditionTypeSkipReview, sc.Type)
		assert.Equal(t, 5, *sc.MaxCommits)
		assert.Equal(t, []string{"chore", "docs"}, sc.TitleKeywords)
		assert.Equal(t, 100, *sc.DiffLines)
	})

	t.Run("skip_conditions_null", func(t *testing.T) {
		data := []byte(`
            {
                "skipSandbox": true
            }
        `)

		var prop agent.AgentProperty
		err := json.Unmarshal(data, &prop)
		assert.Nil(t, err)
		assert.Nil(t, prop.SkipConditions)
		assert.True(t, prop.SkipSandbox)
	})

	t.Run("skip_conditions_absent", func(t *testing.T) {
		data := []byte(`{}`)

		var prop agent.AgentProperty
		err := json.Unmarshal(data, &prop)
		assert.Nil(t, err)
		assert.Nil(t, prop.SkipConditions)
	})
}
