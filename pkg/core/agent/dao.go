package agent

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/sql"
)

func withAgentId(agentId string) dao.DbWhereOption {
	return dao.WithKV("agent_id", agentId)
}

func withSessionId(sessionId string) dao.DbWhereOption {
	return dao.WithKV("session_id", sessionId)
}

func withTaskId(taskId string) dao.DbWhereOption {
	return dao.WithKV("task_id", taskId)
}

type readySessionAndTask struct {
	SessionId string
	TaskId    string
}

var (
	pendingTaskTemplate = sql.Template[[]*readySessionAndTask]{
		Sql: "select t_agent_task.session_id, t_agent_task.task_id from t_session, t_agent_task where t_session.session_id = t_agent_task.session_id AND t_agent_task.state = 'pending' AND t_session.session_state in ('Finished', 'Abort', 'Init', 'Idle') order by t_agent_task.id ",
	}
)
