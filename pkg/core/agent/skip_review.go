package agent

import (
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"k8s.io/utils/ptr"
	"strings"
)

// SkipReviewConditions 跳过 CRAgent Review 的条件
type SkipReviewConditions struct {
	Type          agent.SkipConditionType `json:"type"`
	MaxCommits    *int                    `json:"maxCommits,omitempty"`    // Commit 数量阈值（超过则跳过）
	TitleKeywords []string                `json:"titleKeywords,omitempty"` // 标题关键词列表（任一匹配则跳过）
	DiffLines     *int                    `json:"diffLines,omitempty"`     // 改动行数
}

func (c *SkipReviewConditions) Check(ctx base.Context, sessionId string) (bool, *agent.SkipReason, error) {
	mr, err := core.GetCoreService().GetCodePlatformService().GetMergeRequest(ctx, sessionId, &codeplatform.GetMergeRequestOptions{
		WithCommits: false,
	})
	if err != nil {
		ctx.GetLogger().Error("failed to get merge request", "err", err.Error())
		return false, nil, errors.WithStack(err)
	}
	if c.DiffLines == nil {
		c.DiffLines = ptr.To(10000) // 默认过滤超过 10000 行改动的 mr
	}

	lines := 0
	for _, change := range mr.Changes {
		for _, line := range strings.Split(change.Diff, "\n") {
			// 统计以 '+' 或 '-' 开头的行（排除文件信息行）
			if strings.HasPrefix(line, "+") || strings.HasPrefix(line, "-") {
				lines++
			}
		}
	}

	if lines > *c.DiffLines {
		skipReason := &agent.SkipReason{
			Reason:      "跳过原因：改动行数超出限定",
			Description: "灵码智能体支持设置智能评审最大改动行数，超过限定数量自动跳过。如需触发智能评审，可联系管理员进行设置。",
			Details: []agent.DetailGroup{
				{
					Title: "改动行数",
					Items: []string{fmt.Sprintf("%d（限制最大行数 %d，默认值 10000）", lines, *c.DiffLines)},
				},
			},
		}
		return true, skipReason, nil
	}

	if c.MaxCommits != nil {
		commits, err := core.GetCoreService().GetCodePlatformService().ListMergeRequestCommits(ctx, sessionId)
		if err != nil {
			ctx.GetLogger().Error("failed to list merge request commits")
			return false, nil, errors.WithStack(err)
		}
		if len(commits) > *c.MaxCommits {
			skipReason := &agent.SkipReason{
				Reason:      "跳过原因：Commit 数量超出限定",
				Description: "灵码智能体支持设置智能评审最大Commit数，超过限定数量自动跳过。如需触发智能评审，可联系管理员进行设置。",
				Details: []agent.DetailGroup{
					{
						Title: "Commit 数",
						Items: []string{fmt.Sprintf("%d（限制最大数量 %d）", len(commits), *c.MaxCommits)},
					},
				},
			}
			return true, skipReason, nil
		}
	}

	if len(c.TitleKeywords) > 0 {
		mr, err := core.GetCoreService().GetCodePlatformService().GetMergeRequest(ctx, sessionId, &codeplatform.GetMergeRequestOptions{
			WithCommits: false,
		})
		if err != nil {
			ctx.GetLogger().Error("failed to get merge request")
			return false, nil, errors.WithStack(err)
		}
		matchedKeywords := []string{}
		for _, keyword := range c.TitleKeywords {
			if strings.Contains(mr.Title, keyword) {
				matchedKeywords = append(matchedKeywords, keyword)
			}
		}
		if len(matchedKeywords) > 0 {
			skipReason := &agent.SkipReason{
				Reason:      "跳过原因：标题包含关键词",
				Description: "灵码智能体支持设置标题关键词过滤，触发关键字自动跳过。如需触发智能评审，可联系管理员进行设置。",
				Details: []agent.DetailGroup{
					{
						Title: fmt.Sprintf("关键词 (%d)", len(matchedKeywords)),
						Items: matchedKeywords,
					},
				},
			}
			return true, skipReason, nil
		}
	}

	return false, nil, nil
}

// 注册到 SkipConditionsFactory
func init() {
	agent.RegisterSkipConditionType(agent.SkipConditionTypeSkipReview, func() agent.SkipConditions { return &SkipReviewConditions{} })
}
