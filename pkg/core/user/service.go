package user

import (
	"context"
	"github.com/prometheus/client_golang/prometheus"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/user"
	"time"
)

var _ comncore.UserService = (*service)(nil)

type service struct {
	started chan struct{}
	ready   bool
	core    comncore.Core
}

func (s *service) GetUserInfoById(ctx base.Context, userId string) (*user.UserInfo, error) {
	entity, err := getUserById(ctx, userId)
	if err != nil {
		return nil, err
	} else if entity == nil {
		return nil, commonerrors.New(codes.ErrRecordNotFound, userId)
	}
	return entity.ToUserInfo(), nil
}

// EnsureUser 根据用户id查询记录，如果不存在则创建entity，如果其他类型错误就返回错误，如果没错误直接返回
func (s *service) EnsureUser(ctx base.Context, userId string) error {
	// fixme 清矢合一下
	return nil
}

func (s *service) SaveUserIfNotExist(ctx base.Context, user *user.User) error {
	now := time.Now()
	entity := &userEntity{
		UserId:          user.UserId,
		UserName:        user.UserName,
		SiteType:        user.SiteType,
		AvatarUrl:       user.AvatarUrl,
		UserType:        string(user.UserType),
		IsInnerCustomer: user.IsInnerCustomer,
		GmtCreate:       now,
		GmtModified:     now,
	}

	if _, err := dao.GetEntity(ctx, &userEntity{}, dao.NewDbOptions(dao.WithUserId(user.UserId))); err != nil {
		if !commonerrors.Is(err, codes.ErrRecordNotFound) {
			return err
		}
		// 没有记录，后续新建
	} else {
		// 有记录
		return nil
	}
	return dao.Save(ctx, entity)
}

func (s *service) Start(ctx context.Context) error {
	defer close(s.started)
	s.ready = true
	return nil
}

func (s *service) Started() <-chan struct{} {
	return s.started
}

func (s *service) IsReady() bool {
	return s.ready
}

func (s *service) Stop() {
	return
}

func (s *service) Metrics() []prometheus.Collector {
	return nil
}

func (s *service) GetName() string {
	return "UserService"
}

func (s *service) Register(core comncore.Core) {
	s.core = core
}

func New() comncore.UserService {
	return &service{
		started: make(chan struct{}),
		ready:   false,
	}
}
