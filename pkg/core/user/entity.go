package user

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/user"
	"time"
)

type userEntity struct {
	ID              int64         `gorm:"column:id;primaryKey"`
	UserId          string        `gorm:"column:user_id"`
	UserName        string        `gorm:"column:user_name"`
	AvatarUrl       string        `gorm:"column:avatar_url"`
	UserType        string        `gorm:"column:user_type"`
	IsInnerCustomer bool          `gorm:"column:is_inner_customer;type:boolean;not null;default:false;comment:是否为内部用户"`
	SiteType        base.SiteType `gorm:"column:site_type;type:varchar(64);default:'domestic';comment:用户站点"`
	GmtCreate       time.Time     `gorm:"autoCreateTime;column:gmt_create;comment:创建时间"`
	GmtModified     time.Time     `gorm:"autoUpdateTime;column:gmt_modified;comment:修改时间"`
}

func (*userEntity) TableName() string {
	return "t_user"
}

func (ue *userEntity) ToUserInfo() *user.UserInfo {
	return &user.UserInfo{
		UserId:    ue.UserId,
		UserName:  ue.UserName,
		AvatarUrl: ue.AvatarUrl,
	}
}
