package user

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
)

func withUserId(userId string) dao.DbWhereOption {
	return dao.WithKV("user_id", userId)
}

func getUserById(ctx base.Context, userId string) (*userEntity, error) {
	if userId == "" {
		return nil, commonerrors.New(codes.ErrInvalidParameterWithEmpty, "userId")
	}

	entity, err := dao.GetEntity(ctx, &userEntity{}, dao.NewDbOptions(dao.WithUserId(userId)))
	if err != nil {
		if commonerrors.Is(err, codes.ErrRecordNotFound) {
			return nil, nil
		}
		ctx.GetLogger().Error("fail to get user", "userId", userId, "err", err)
		return nil, commonerrors.New(codes.ErrFailToQueryUser, userId)

	}
	return entity, nil
}

func saveUser(ctx base.Context, user *userEntity) error {
	if user == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "user")
	}
	if err := dao.Save(ctx, user); err != nil {
		ctx.GetLogger().Error("fail to save user", "userId", user.UserId, "err", err)
		return commonerrors.New(codes.ErrFailToSaveUser, user.UserId)
	}

	return nil
}
