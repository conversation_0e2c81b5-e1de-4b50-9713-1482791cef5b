package cronjob

import (
	"context"
	"fmt"
	gocron "github.com/go-co-op/gocron/v2"
	"github.com/pkg/errors"
	commoncronjob "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/cronjob"
	lk "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/lock"
	"golang.org/x/exp/slog"
	"strings"
	"sync"
	"time"
)

var schedulerInitOnce sync.Once

type cronJobScheduler struct {
	scheduler gocron.Scheduler
}

func (c *cronJobScheduler) Register(jobs ...*commoncronjob.Job) error {
	for _, rj := range jobs {

		if err := c.validateJob(rj); err != nil {
			return errors.WithStack(err)
		}

		var jobDefinition gocron.JobDefinition
		if rj.CronExpression != nil {
			cronLen := len(strings.Split(*rj.CronExpression, " "))
			isCronWithSeconds := cronLen == 6
			jobDefinition = gocron.CronJob(*rj.CronExpression, isCronWithSeconds)
		} else {
			jobDefinition = gocron.DurationJob(*rj.Duration)
		}

		jobOpts := []gocron.JobOption{
			gocron.WithName(rj.Name),
		}

		if rj.SingletonMode {
			jobOpts = append(jobOpts,
				gocron.WithSingletonMode(gocron.LimitModeReschedule),
				gocron.WithDistributedJobLocker(newLocker(rj.LockerRetryable, rj.LockerTimeout, rj.LockerRetryDelay, rj.LockerRetries)),
			)
		}
		if _, err := c.scheduler.NewJob(jobDefinition, gocron.NewTask(taskFn(rj.Name, rj.Task)), jobOpts...); err != nil {
			return errors.WithStack(err)
		}
	}
	//c.Start(context.Background()) // 确保cronjob服务启动后注册的任务能被调度
	return nil
}

func newCronJonScheduler() (*cronJobScheduler, error) {
	var (
		s   gocron.Scheduler
		err error
	)
	schedulerInitOnce.Do(func() {
		s, err = gocron.NewScheduler(gocron.WithLocation(time.Local))
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &cronJobScheduler{
		scheduler: s,
	}, nil
}
func (c *cronJobScheduler) Start(_ context.Context) {
	c.scheduler.Start()
}

func (c *cronJobScheduler) Stop() error {
	if c.scheduler != nil {
		if err := c.scheduler.Shutdown(); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

type locker struct {
	prefix     string
	retries    int
	expiry     time.Duration
	retryDelay time.Duration
	retryable  bool
}

func (l *locker) Lock(ctx context.Context, key string) (gocron.Lock, error) {
	jobLocker := lk.NewLock(key, lk.WithExpiry(l.expiry), lk.WithRetryDelay(l.retryDelay), lk.WithRetry(l.retries))
	var err error
	if l.retryable {
		err = jobLocker.Lock()
	} else {
		err = jobLocker.TryLock()
	}
	if err != nil {
		slog.Warn("cronjob cannot acquire lock. skip to execute.", "lockName", jobLocker.Name(), "err", err)
		return nil, errors.WithStack(err)
	}
	slog.Debug("cronjob lock acquired", "lockName", jobLocker.Name())

	return &lock{
		lock: jobLocker,
	}, nil
}

func newLocker(retryable bool, expiry, retryDelay time.Duration, retries int) *locker {
	return &locker{
		prefix:     "cronjob-",
		retries:    retries,
		expiry:     expiry,
		retryDelay: retryDelay,
		retryable:  retryable,
	}
}

type lock struct {
	lock lk.DistributedLock
}

func (l *lock) Unlock(ctx context.Context) error {
	//slog.Info("cronjob unlock")
	if ok, err := l.lock.Unlock(); !ok || err != nil {
		slog.Warn("cronjob unlock failed", "lockName", l.lock.Name())
		return errors.New("unlock failed")
	} else {
		slog.Debug("cronjob unlock success", "lockName", l.lock.Name())
	}
	return nil
}

func (c *cronJobScheduler) validateJob(job *commoncronjob.Job) error {
	if len(job.Name) < 1 {
		return errors.New("job name is empty")
	}

	if job.CronExpression == nil && job.Duration == nil {
		return errors.New("cronExpression and duration cannot be both empty")
	}

	if job.CronExpression != nil && job.Duration != nil {
		return errors.New("cronExpression and duration cannot be both set")
	}

	if job.CronExpression != nil {
		cronLen := len(strings.Split(*job.CronExpression, " "))
		if cronLen != 5 && cronLen != 6 {
			return errors.Errorf("cronExpression %s is illegal", *job.CronExpression)
		}
	}

	registeredJobs := c.scheduler.Jobs()
	if len(registeredJobs) > 0 {
		for _, j := range registeredJobs {
			if j.Name() == job.Name {
				return errors.Errorf("job name %s already exists", job.Name)
			}
		}
	}

	return nil
}

func taskFn(name string, fn func() error) func() error {
	return func() error {
		defer func() {
			if err := recover(); err != nil {
				if e, ok := err.(error); ok {
					slog.Error("Recovery panic", "err", fmt.Sprintf("%+v", errors.WithStack(e)))
				} else {
					slog.Error("cronjob panic", "name", name, "err", err)
				}
			}
		}()
		return fn()
	}
}
