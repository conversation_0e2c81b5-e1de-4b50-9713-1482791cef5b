package cronjob

import (
	"context"
	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	commoncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commoncronjob "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/cronjob"
	"log/slog"
	"sync"
)

var _ commoncore.CronjobService = new(cronjobService)

type cronjobService struct {
	core              commoncore.Core
	started           chan struct{}
	ready             bool
	jobScheduler      *cronJobScheduler
	jobRegistryLocker sync.Mutex
}

func (c *cronjobService) Start(ctx context.Context) error {
	defer close(c.started)
	js, err := newCronJonScheduler()
	if err != nil {
		return errors.WithStack(err)
	}
	c.jobScheduler = js
	//
	c.jobScheduler.Start(ctx)
	c.ready = true
	return nil
}

func (c *cronjobService) Started() <-chan struct{} {
	return c.started
}

func (c *cronjobService) IsReady() bool {
	return c.ready
}

func (c *cronjobService) Stop() {
	err := c.jobScheduler.Stop()
	if err != nil {
		slog.Error("cronjob stop error", "err", err)
		return
	}
}

func (c *cronjobService) Metrics() []prometheus.Collector {
	//TODO implement me
	return nil
}

func (c *cronjobService) GetName() string {
	return "cronjob"
}

func (c *cronjobService) Register(core commoncore.Core) {
	c.core = core
}

// RegisterJob 注册定时任务
func (c *cronjobService) RegisterJob(job *commoncronjob.Job) error {
	c.jobRegistryLocker.Lock()
	defer c.jobRegistryLocker.Unlock()

	if err := c.jobScheduler.Register(job); err != nil {
		return errors.WithStack(err)
	}
	return nil

}

// RegisterJobs 批量注册定时任务
func (c *cronjobService) RegisterJobs(jobs ...*commoncronjob.Job) (err error) {
	c.jobRegistryLocker.Lock()
	defer c.jobRegistryLocker.Unlock()

	return c.jobScheduler.Register(jobs...)
}

func New() commoncore.CronjobService {
	return &cronjobService{started: make(chan struct{})}
}
