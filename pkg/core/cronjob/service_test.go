package cronjob

import (
	"context"
	"fmt"
	"github.com/stretchr/testify/assert"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/cronjob"
	"testing"
	"time"
)

func TestCronjobServiceRegisterJob(t *testing.T) {
	svc := New()
	assert.Nil(t, svc.Start(context.TODO()))
	<-svc.Started()
	assert.True(t, svc.IsReady())
	assert.Nil(t, svc.RegisterJob(cronjob.NewJob(
		cronjob.WithName("test"),
		cronjob.WithCronExpression("* * * * * *"),
		cronjob.WithTask(func() error {
			fmt.Println("job")
			return nil
		}),
		cronjob.WithLockerRetries(2),
		cronjob.WithLockerTimeout(time.Hour),
		cronjob.WithSingletonMode(true))))

	svc.Stop()
}
