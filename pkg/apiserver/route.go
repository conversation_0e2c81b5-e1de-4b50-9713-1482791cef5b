package apiserver

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/internal/route"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"net/http"
)

func setupApiRoute(engine *gin.Engine, innerAccess bool) {
	route.SetAgentRoute(engine, innerAccess)
	route.SetupCodePlatformRoutes(engine, innerAccess)
	route.SetAgentClassRoute(engine, innerAccess)
	route.SetWebhookRoute(engine, innerAccess)
	route.SetSessionRoute(engine, innerAccess)
	route.SetEventRoutes(engine, innerAccess)
	route.SetIdentityRoutes(engine, innerAccess)
	route.SetCallbackRoute(engine, innerAccess)
	route.SetAgentConnectRoute(engine, innerAccess)
	route.SetAiDeveloperConnectRoute(engine, innerAccess)
	route.SetRepositoryRoute(engine, innerAccess)
	route.SetFeatureRoute(engine, innerAccess)
	route.SetOverviewRoute(engine, innerAccess)
	route.SetYunxiaoRoute(engine, innerAccess)
	route.SetUserRoute(engine, innerAccess)
	route.SetGitHubActionsRoute(engine, innerAccess)
}

func setupMainPage(engine *gin.Engine, innerAccess bool) {
	if innerAccess {
		setupOpsPage(engine)
	} else {
		setupYunxiaoPage(engine)
	}
	engine.Static("statics", "./statics")
	engine.GET("/favicon.ico", func(c *gin.Context) {
		c.File("./statics/favicon.ico")
	})
}

type opsDetailQuery struct {
	UserId    string `form:"user-id"`
	SessionId string `form:"session-id"`
}

func (q *opsDetailQuery) Validate() error {
	if q.UserId == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "user-id")
	}
	if q.SessionId == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "session-id")
	}
	return nil
}

func setupOpsPage(engine *gin.Engine) {
	engine.LoadHTMLGlob("templates/*")
	engine.GET("/admin/ops", func(c *gin.Context) {
		c.HTML(http.StatusOK, "ops.html", gin.H{})
	})
	engine.GET("/admin/ops-detail", func(c *gin.Context) {
		query := &opsDetailQuery{}
		if err := c.BindQuery(query); err != nil {
			_ = c.AbortWithError(http.StatusBadRequest, fmt.Errorf("InvalidParameter: %s", err.Error()))
			return
		}
		if err := query.Validate(); err != nil {
			_ = c.AbortWithError(http.StatusBadRequest, err)
			return
		}
		c.HTML(http.StatusOK, "ops-detail.html", gin.H{
			"UserId":    query.UserId,
			"SessionId": query.SessionId,
		})

	})
}

func setupYunxiaoPage(engine *gin.Engine) {
	engine.LoadHTMLGlob("templates/*")
	frontPages := []string{
		"",
		"/setting",
		"/agent",
		"/agent/configuration",
		"/code-base",
		"/code-base/configuration",
		"/code-base/configuration/:configurationId",
		"/agent-logs",
		"/audit-logs",
	}
	for _, fp := range frontPages {
		engine.GET(fp, frontPageHandler)
	}
}

func frontPageHandler(c *gin.Context) {
	nonce := generateNonce()
	cspKey, cspPolicy := getCspHeaderKeyAndValue(nonce)
	// 添加安全响应头
	c.Header(cspKey, cspPolicy)
	c.Header("X-Content-Type-Options", "nosniff")
	c.Header("X-Frame-Options", "DENY")
	c.Header("X-XSS-Protection", "1; mode=block")
	c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
	yunxiaoUser := base.GetYunxiaoUserFromGinContext(c)
	if yunxiaoUser != nil {
		domain := config.GetSystemDomain()
		cdnPath := config.Get(config.KeySystemYunxiaoCdnPath)
		if domain != "" {
			cdnPath = fmt.Sprintf("//%s/%s", domain, cdnPath)
		}
		c.HTML(http.StatusOK, "index.html", gin.H{
			"OrganizationId":       yunxiaoUser.GetUid(),
			"UserId":               yunxiaoUser.GetLoginUid(),
			"Name":                 yunxiaoUser.GetName(),
			"Username":             yunxiaoUser.GetUsername(),
			"Domain":               domain,
			"CDNPath":              cdnPath,
			"PageTitle":            config.Get(config.KeySystemAppTitle),
			"AppDesc":              config.Get(config.KeySystemAppDescription),
			"AppIcon":              config.Get(config.KeySystemAppIcon),
			"Env":                  config.Get(config.KeySystemEnv),
			"AppId":                config.Get(config.KeySystemAppId),
			"HomePageVersion":      config.Get(config.KeySystemHomePageVersion),
			"TeamixUIVersion":      config.Get(config.KeySystemTeamixUIVersion),
			"NavigationSDKVersion": config.Get(config.KeySystemNaviagtionSDKVersion),
			"CodeLibVersion":       config.Get(config.KeySystemCodeLibVersion),
			"Nonce":                nonce,
		})
		return
	}
	c.Redirect(http.StatusFound, base.GetYunxiaoLoginUri(c))
}

// 生成随机 nonce 值的函数
func generateNonce() string {
	return helper.NewUUIDLower()
}

func getCspHeaderKeyAndValue(nonce string) (string, string) {
	cspKey := "Content-Security-Policy-Report-Only"
	cspPolicy := fmt.Sprintf(`
            default-src 'self' 127.0.0.1:* localhost:*;
            script-src 'nonce-%s' 'strict-dynamic' 'unsafe-eval' 127.0.0.1:* localhost:*;
            style-src 'self' 'unsafe-inline' 127.0.0.1:* localhost:*;
            img-src 'self' data: 127.0.0.1:* localhost:*;
            font-src 'self' 127.0.0.1:* localhost:*;
            connect-src 'self' 127.0.0.1:* localhost:*;
            object-src 'none';
            base-uri 'self';
            report-uri /csp-report
        `, nonce)
	if config.GetOrDefaultBool(config.KeySystemFrontPageCSPEnabled, true) {
		cspKey = "Content-Security-Policy"
		cspPolicy = fmt.Sprintf(`
    default-src 'self';
    script-src 'nonce-%s' 'strict-dynamic';
	style-src 'self' 'unsafe-inline';
    object-src 'none';
    base-uri 'self'
`, nonce)
	}

	return cspKey, cspPolicy
}
