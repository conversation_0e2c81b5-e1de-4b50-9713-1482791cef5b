package apiserver

import (
	"context"
	"errors"
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/metrics"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/middleware"
	commcore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"os"
	"strings"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"log/slog"
	"net/http"
	_ "net/http/pprof"
	"time"
)

func init() {
	if os.Getenv("GIN_MODE") == "" {
		os.Setenv("GIN_MODE", "release")
	}
}

type WebServer struct {
	address         string
	innerAddress    string
	metricsAddress  string
	httpServer      *http.Server
	metricsServer   *http.Server
	innerHttpServer *http.Server
	engine          *gin.Engine
	innerEngine     *gin.Engine
	started         chan struct{}
	ready           bool
	core            commcore.Core
}

func New(address, innerAddress, metricsAddress, pprofAddress string) *WebServer {
	mode := config.GetOrDefault("GIN_MODE", "release")
	gin.SetMode(mode)
	if mode == "release" {
		gin.DisableConsoleColor()
	} else {
		gin.ForceConsoleColor()
	}

	if strings.EqualFold(os.Getenv("PPROF_ENABLE"), "true") {
		go func() {
			profAddress := os.Getenv("PPROF_ADDRESS")
			if len(profAddress) < 1 {
				profAddress = pprofAddress
			}
			if len(profAddress) < 1 {
				profAddress = "127.0.0.1:9091"
			}
			err := http.ListenAndServe(profAddress, nil)
			if err != nil {
				slog.Error("pprof server start error.", "error", err)
				return
			}
			slog.Info("pprof server start success. address: ")
		}()
	}

	envAddress := os.Getenv("SERVER_ADDRESS")
	if len(envAddress) > 0 {
		address = envAddress
	}

	envMetricsAddress := os.Getenv("METRICS_ADDRESS")
	if len(envMetricsAddress) > 0 {
		metricsAddress = envMetricsAddress
	}

	return &WebServer{
		address:        address,
		innerAddress:   innerAddress,
		metricsAddress: metricsAddress,
		engine:         newEngine(),
		innerEngine:    newInnerEngine(),
		started:        make(chan struct{}),
	}
}

func newInnerEngine() *gin.Engine {
	engine := gin.New()
	engine.UseH2C = true
	// ***** 健康检查接口 *****
	// 健康检查接口不增加任何中间件
	engine.GET("/healthz", healthCheck)
	// ***** 通用中间件 *****
	engine.Use(gin.LoggerWithWriter(gin.DefaultWriter))
	engine.Use(gin.CustomRecovery(middleware.HandleRecovery))
	engine.Use(middleware.WithRequestId())
	engine.Use(middleware.WithTraceId())
	engine.Use(middleware.WithAction())
	engine.Use(middleware.AccessLog())
	engine.Use(middleware.RequestLogger())
	engine.Use(middleware.ResponseBodyLogger())
	engine.Use(middleware.Cors())
	engine.Use(middleware.WithMetrics(nil))

	// ***** 业务接口，需要做用户认证 *****
	// 内部鉴权
	engine.Use(middleware.Authentication(allowMockUserHeader(), true))
	setupApiRoute(engine, true)
	setupMainPage(engine, true)

	return engine
}

func newEngine() *gin.Engine {
	engine := gin.New()
	engine.UseH2C = true
	// ***** 健康检查接口 *****
	// 健康检查接口不增加任何中间件
	engine.GET("/healthz", healthCheck)
	// ***** 通用中间件 *****
	engine.Use(gin.LoggerWithWriter(gin.DefaultWriter))
	engine.Use(gin.CustomRecovery(middleware.HandleRecovery))
	engine.Use(middleware.WithRequestId())
	engine.Use(middleware.WithTraceId())
	engine.Use(middleware.WithAction())
	engine.Use(middleware.AccessLog())
	engine.Use(middleware.RequestLogger())
	engine.Use(middleware.ResponseBodyLogger())
	engine.Use(middleware.Cors())
	engine.Use(middleware.WithMetrics(nil))

	// ***** 业务接口，需要做用户认证 *****
	engine.Use(middleware.Authentication(allowMockUserHeader(), false))

	// API
	setupApiRoute(engine, false)
	setupMainPage(engine, false)
	return engine
}

func allowMockUserHeader() bool {
	return strings.ToLower(os.Getenv("MOCK_USER_HEADER")) == "true"
}

func (server *WebServer) GetName() string {
	return "agent-controller"
}

func healthCheck(context *gin.Context) {
	context.JSON(http.StatusOK, gin.H{
		"status": "up",
	})
}

func (server *WebServer) Started() <-chan struct{} {
	return server.started
}

func (server *WebServer) IsReady() bool {
	return server.ready
}

func (server *WebServer) Stop() {
	slog.Info("Shutting down agent-controller server...")
	ctx, cancel := context.WithTimeout(context.TODO(), 5*time.Second)
	defer cancel()
	if server.httpServer != nil {
		if err := server.httpServer.Shutdown(ctx); err != nil {
			slog.Error("Shutting down error.", "error", err)
		}
	}
	if server.metricsServer != nil {
		if err := server.metricsServer.Shutdown(ctx); err != nil {
			slog.Error("Shutting down agent-controller metrics server error.", "error", err)
		}
	}
	if server.innerHttpServer != nil {
		if err := server.innerHttpServer.Shutdown(ctx); err != nil {
			slog.Error("Shutting down agent-controller inner server error.", "error", err)
		}
	}
}

func (server *WebServer) Metrics() []prometheus.Collector {
	return []prometheus.Collector{
		middleware.RespSizeBytes,
		middleware.ReqSizeBytes,
		middleware.ReqCount,
		middleware.Uptime,
		middleware.ReqDuration,
		metrics.ErrorCounter,
		metrics.ActionCostHistogram,
		metrics.SSEventGauge,
	}
}

func (server *WebServer) Register(core commcore.Core) {
	server.core = core
}

func (server *WebServer) Start(ctx context.Context) error {
	// api 服务最后启动

	server.httpServer = &http.Server{
		Addr:    server.address,
		Handler: server.engine,
	}
	server.innerHttpServer = &http.Server{
		Addr:    server.innerAddress,
		Handler: server.innerEngine,
	}
	server.metricsServer = &http.Server{
		Addr:    server.metricsAddress,
		Handler: promhttp.Handler(),
	}
	go func() {
		if err := server.httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			server.core.ErrorChan() <- err
		}
	}()
	go func() {
		if err := server.innerHttpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			server.core.ErrorChan() <- err
		}
	}()

	go func() {
		if err := server.metricsServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			server.core.ErrorChan() <- err
		}
	}()

	slog.Info("agent-controller server listening on " + server.address)
	slog.Info("agent-controller inner server listening on " + server.innerAddress)
	slog.Info("agent-controller metrics server listening on " + server.metricsAddress)

	server.ready = true
	close(server.started)
	return nil
}
