package route

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/yunxiao"
	"net/http"
)

func SetYunxiaoRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/organization", engine)

	// 获取当前用户云效组织信息
	r.Must<PERSON>eg<PERSON>(http.MethodGet, "/org-info", getOrgInfo)
}

type orgOwner struct {
	AvatarUrl string `json:"avatarUrl"`
	Id        string `json:"id"`
	Name      string `json:"name"`
}

func getOrgInfo(ctx base.Context, _ any, _ any, _ any) (any, error) {
	resp, err := yunxiao.GetOrganizationInfo(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var owners []orgOwner
	for _, owner := range resp.Owners {
		owners = append(owners, orgOwner{
			AvatarUrl: owner.Avatar,
			Id:        owner.UserId,
			Name:      owner.Name,
		})
	}
	owner := orgOwner{}
	if len(owners) > 0 {
		owner = owners[0]
	}

	result := map[string]any{
		"owner":      owner,
		"admins":     owners,
		"identifier": resp.Id,
		"name":       resp.Name,
		"gmtCreate":  resp.CreatedAt.UnixMilli(),
		// 就是为空
		"type": "",
	}
	return result, nil
}

/* 以下是lingma的API响应参考，前端依赖该API
{
    "isSuccessful": true,
    "result": {
        "admins": [
            {
                "avatarUrl": "/objects/ajax/default/avatars/cGFuanVuZmVuZy5wamY=",
                "id": "ffbb11f3-c29c-43a9-a769-6e8c24e58780",
                "name": "panjunfeng.pjf"
            }
        ],
        "gmtCreate": 1747018825944,
        "identifier": "4fdb8b3b-bf0a-43d4-a443-27ac7d324919",
        "isFirstLogin": "N",
        "name": "之卫自测",
        "owner": {
            "avatarUrl": "/objects/ajax/default/avatars/cGFuanVuZmVuZy5wamY=",
            "id": "ffbb11f3-c29c-43a9-a769-6e8c24e58780",
            "name": "panjunfeng.pjf"
        },
        "type": ""
    },
    "traceId": "b186048eb91649298f8b4987a1f48315.365.17471009890690015"
}
*/
