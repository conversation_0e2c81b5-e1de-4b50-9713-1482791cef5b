package route

import (
	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	http2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"net/http"
)

func SetAgentConnectRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/agent-connect/:agentId", engine)
	r.<PERSON>(http.MethodGet, "", connect)
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许跨域访问
	},
}

type agentConnectQueryParams struct {
	AgentConnectToken *string `form:"agentConnectToken"`
}

func connect(ctx base.Context, pathParams *agentPathParams, queryParams *agentConnectQueryParams, _ any) (any, error) {
	token := ctx.GetGinContext().GetHeader("x-agent-connect-token")
	if queryParams.AgentConnectToken != nil {
		ctx.GetLogger().Info("agent connect with token in query param", "agentId", pathParams.AgentId)
		token = *queryParams.AgentConnectToken
	}
	if token == "" {
		ctx.GetLogger().Error("unauthorized agent connect", "agentId", pathParams.AgentId)
		return nil, errors.New(codes.ErrAgentConnectUnauthorized)
	}

	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentityByToken(ctx, token)
	if err != nil || idInfo == nil {
		ctx.GetLogger().Error("unauthorized agent connect", "agentId", pathParams.AgentId)
		return nil, errors.New(codes.ErrAgentConnectUnauthorized)
	}
	ctx = base.NewContextWithUserOverride(ctx, idInfo.UserId, ctx.GetSiteType())
	c := ctx.GetGinContext()
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		ctx.GetLogger().Error("fail to upgrade to websocket connection", "agentId", pathParams.AgentId, "err", err)
		return nil, errors.New(codes.ErrFailToUpgrade)
	}
	defer conn.Close()
	service := core.GetCoreService().GetAgentConnectService()

	ctx.GetLogger().Info("agent connected", "agentId", pathParams.AgentId, "err", err)

	if err := service.Serve(
		ctx,
		http2.NewSafeConn(helper.NewIDWithPrefix("conn-"), conn, idInfo)); err != nil {
		return nil, err
	}

	return nil, service.Detach(ctx, pathParams.AgentId)
}
