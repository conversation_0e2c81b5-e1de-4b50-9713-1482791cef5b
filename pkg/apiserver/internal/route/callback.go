package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"net/http"
)

func SetCallbackRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1", engine)
	r.<PERSON>(http.MethodGet, "/github/callback/auth/:githubAppId", githubInstallationAuthCallback)
	r.<PERSON><PERSON><PERSON><PERSON>(http.MethodGet, "/github/callback/ram/:ramOauthAppId", githubRamAuthCallback)
}

type githubInstallCallbackQueryParams struct {
	Code           string `form:"code"`
	InstallationId int64  `form:"installation_id"`
	SetupAction    string `form:"setup_action"`
	State          string `form:"state"`
}

type githubRamCallbackQueryParams struct {
	Code  string `form:"code"`
	State string `form:"state"`
	Error string `form:"error"`
}

type githubInstallCallbackPathParams struct {
	AppId int64 `uri:"githubAppId"`
}

type ramOauthCallbackPathParams struct {
	AppId string `uri:"ramOauthAppId"`
}

func githubInstallationAuthCallback(ctx base.Context, pathParams *githubInstallCallbackPathParams, queryParams *githubInstallCallbackQueryParams, _ any) (any, error) {
	appId := pathParams.AppId
	code := queryParams.Code
	installationId := queryParams.InstallationId
	setupAction := queryParams.SetupAction
	state := queryParams.State
	url, err := core.GetCoreService().GetCallbackService().GithubOAuthCallback(ctx, appId, code, installationId, setupAction, state)
	if commonerrors.Is(err, codes.ErrInstallationNumberExceedsLimit) {
		return nil, err
	} else if err != nil {
		ctx.GetLogger().Error("GithubOAuthCallback error", "code", code, "installationId", installationId, "err", err)
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	if url != "" {
		return nil, commonerrors.New(codes.RedirectTo, url)
	}
	return nil, nil
}

func githubRamAuthCallback(ctx base.Context, pathParams *ramOauthCallbackPathParams, queryParams *githubRamCallbackQueryParams, _ any) (any, error) {
	url, err := core.GetCoreService().GetCallbackService().RamOAuthCallback(ctx, pathParams.AppId, queryParams.Code, queryParams.State, queryParams.Error)
	if err != nil {
		ctx.GetLogger().Error("RamOAuthCallback error", "code", queryParams.Code, "state", queryParams.State, "err", err)
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	if url != "" {
		return nil, commonerrors.New(codes.RedirectTo, url)
	}
	return nil, commonerrors.New(codes.ServerInternalError)

}
