package route

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/external/github"
)

// SetGitHubActionsRoute 设置GitHub Actions相关路由
func SetGitHubActionsRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/github-actions", engine)
	r.MustRegister(http.MethodPost, "/token-exchange", exchangeOIDCForInstallationToken)
	r.<PERSON><PERSON><PERSON><PERSON>(http.MethodGet, "/health", healthCheck)
	r.<PERSON><PERSON><PERSON><PERSON>(http.MethodGet, "/permissions", getSupportedPermissions)
	r.<PERSON><PERSON><PERSON><PERSON>(http.MethodPost, "/validate-oidc", validateOIDC)
}

// TokenExchangeRequest OIDC令牌交换请求
type TokenExchangeRequest struct {
	OIDCToken    string            `json:"oidc_token" binding:"required"`
	Repository   string            `json:"repository" binding:"required"`
	Permissions  map[string]string `json:"permissions,omitempty"`
	Repositories []string          `json:"repositories,omitempty"`
}

// TokenExchangeResponse OIDC令牌交换响应
type TokenExchangeResponse struct {
	Token        string            `json:"token"`
	ExpiresAt    string            `json:"expires_at"`
	Permissions  map[string]string `json:"permissions"`
	Repositories []RepositoryInfo  `json:"repositories,omitempty"`
}

// RepositoryInfo 仓库信息
type RepositoryInfo struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name"`
	Private  bool   `json:"private"`
}

// Validate 验证请求参数
func (r *TokenExchangeRequest) Validate() error {
	if strings.TrimSpace(r.OIDCToken) == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "oidc_token")
	}

	if strings.TrimSpace(r.Repository) == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "repository")
	}

	// 验证repository格式
	parts := strings.Split(r.Repository, "/")
	if len(parts) != 2 || parts[0] == "" || parts[1] == "" {
		return commonerrors.New(codes.ErrInvalidParameter, "repository", "must be in format 'owner/repo'")
	}

	// 验证权限设置
	validPermissions := map[string][]string{
		"contents":      {"read", "write"},
		"metadata":      {"read", "write"},
		"pull_requests": {"read", "write"},
		"issues":        {"read", "write"},
		"actions":       {"read", "write"},
		"checks":        {"read", "write"},
		"statuses":      {"read", "write"},
	}

	for perm, level := range r.Permissions {
		validLevels, exists := validPermissions[perm]
		if !exists {
			return commonerrors.New(codes.ErrInvalidParameter, "permissions", 
				"unknown permission: "+perm)
		}

		valid := false
		for _, validLevel := range validLevels {
			if level == validLevel {
				valid = true
				break
			}
		}
		if !valid {
			return commonerrors.New(codes.ErrInvalidParameter, "permissions", 
				"invalid level '"+level+"' for permission '"+perm+"'")
		}
	}

	return nil
}

// exchangeOIDCForInstallationToken 处理OIDC令牌交换Installation Token的请求
func exchangeOIDCForInstallationToken(ctx base.Context, _ any, _ any, body *TokenExchangeRequest) (any, error) {
	ctx.GetLogger().Info("GitHub Actions OIDC token exchange request", 
		"repository", body.Repository,
		"permissions", body.Permissions)

	// 验证请求参数
	if err := body.Validate(); err != nil {
		return nil, err
	}

	// 创建Token交换服务
	tokenService := github.NewTokenExchangeService()

	// 构造Installation Token请求
	installationReq := &github.InstallationTokenRequest{
		Repository:   body.Repository,
		Permissions:  body.Permissions,
		Repositories: body.Repositories,
	}

	// 执行令牌交换
	installationToken, err := tokenService.ExchangeOIDCForInstallationToken(ctx, body.OIDCToken, installationReq)
	if err != nil {
		ctx.GetLogger().Error("failed to exchange OIDC token for installation token", 
			"repository", body.Repository, "err", err)
		return nil, err
	}

	// 构造响应
	response := &TokenExchangeResponse{
		Token:       installationToken.Token,
		ExpiresAt:   installationToken.ExpiresAt.Format("2006-01-02T15:04:05Z"),
		Permissions: installationToken.Permissions,
	}

	// 转换仓库信息
	if len(installationToken.Repositories) > 0 {
		response.Repositories = make([]RepositoryInfo, len(installationToken.Repositories))
		for i, repo := range installationToken.Repositories {
			response.Repositories[i] = RepositoryInfo{
				ID:       repo.GetID(),
				Name:     repo.GetName(),
				FullName: repo.GetFullName(),
				Private:  repo.GetPrivate(),
			}
		}
	}

	ctx.GetLogger().Info("OIDC token exchange completed successfully", 
		"repository", body.Repository,
		"token_expires_at", response.ExpiresAt)

	return response, nil
}

// HealthCheckRequest 健康检查请求
type HealthCheckRequest struct{}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status    string `json:"status"`
	Timestamp string `json:"timestamp"`
	Version   string `json:"version,omitempty"`
}

// healthCheck 健康检查端点
func healthCheck(ctx base.Context, _ any, _ any, _ any) (any, error) {
	return &HealthCheckResponse{
		Status:    "ok",
		Timestamp: time.Now().Format("2006-01-02T15:04:05Z"),
		Version:   "1.0.0", // 可以从配置或构建信息获取
	}, nil
}

// 可以添加更多GitHub Actions相关的端点
// 比如：
// - 获取支持的权限列表
// - 验证OIDC令牌（不交换）
// - 获取仓库的安装信息等

// GetSupportedPermissionsRequest 获取支持的权限请求
type GetSupportedPermissionsRequest struct{}

// GetSupportedPermissionsResponse 获取支持的权限响应
type GetSupportedPermissionsResponse struct {
	Permissions map[string][]string `json:"permissions"`
}

// getSupportedPermissions 获取支持的权限列表
func getSupportedPermissions(ctx base.Context, _ any, _ any, _ any) (any, error) {
	permissions := map[string][]string{
		"contents":      {"read", "write"},
		"metadata":      {"read", "write"},
		"pull_requests": {"read", "write"},
		"issues":        {"read", "write"},
		"actions":       {"read", "write"},
		"checks":        {"read", "write"},
		"statuses":      {"read", "write"},
		"deployments":   {"read", "write"},
		"packages":      {"read", "write"},
		"pages":         {"read", "write"},
	}

	return &GetSupportedPermissionsResponse{
		Permissions: permissions,
	}, nil
}

// ValidateOIDCRequest 验证OIDC令牌请求（不交换Installation Token）
type ValidateOIDCRequest struct {
	OIDCToken  string `json:"oidc_token" binding:"required"`
	Repository string `json:"repository,omitempty"`
}

// ValidateOIDCResponse 验证OIDC令牌响应
type ValidateOIDCResponse struct {
	Valid      bool   `json:"valid"`
	Repository string `json:"repository,omitempty"`
	Actor      string `json:"actor,omitempty"`
	Workflow   string `json:"workflow,omitempty"`
	Ref        string `json:"ref,omitempty"`
	ExpiresAt  string `json:"expires_at,omitempty"`
}

// validateOIDC 验证OIDC令牌（不交换Installation Token）
func validateOIDC(ctx base.Context, _ any, _ any, body *ValidateOIDCRequest) (any, error) {
	if strings.TrimSpace(body.OIDCToken) == "" {
		return nil, commonerrors.New(codes.ErrInvalidParameterWithEmpty, "oidc_token")
	}

	// 创建OIDC验证器
	validator := github.NewOIDCValidator()

	// 验证OIDC令牌
	claims, err := validator.ValidateOIDCToken(ctx, body.OIDCToken, body.Repository)
	if err != nil {
		ctx.GetLogger().Warn("OIDC token validation failed", "err", err)
		return &ValidateOIDCResponse{Valid: false}, nil
	}

	// 构造响应
	response := &ValidateOIDCResponse{
		Valid:      true,
		Repository: claims.Repository,
		Actor:      claims.Actor,
		Workflow:   claims.Workflow,
		Ref:        claims.Ref,
		ExpiresAt:  claims.ExpiresAt.Format("2006-01-02T15:04:05Z"),
	}

	return response, nil
}
