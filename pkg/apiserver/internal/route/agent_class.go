package route

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"net/http"
)

func SetAgentClassRoute(engine *gin.Engine, innerAccess bool) {
	if innerAccess {
		r := router.NewApiRouter("v1/agent-classes", engine)
		r.<PERSON>(http.MethodPost, "", createAgentClass)
		r.<PERSON>(http.MethodGet, "", listAgentClasses)
	}
}

type createAgentClassRequest struct {
	AgentType agent.Type `json:"agentType" binding:"required"`
	ClassName string     `json:"className" binding:"required"`
}

func (r *createAgentClassRequest) Validate() error {
	if len(r.AgentType) < 1 {
		return comnerrors.New(codes.ErrInvalidParameterWithEmpty, "agentType")
	}

	if err := r.AgentType.Validate(); err != nil {
		return errors.WithStack(err)
	}
	if len(r.ClassName) < 1 {
		return comnerrors.New(codes.ErrInvalidParameterWithEmpty, "className")
	}
	return nil
}

func createAgentClass(ctx base.Context, _ any, _ any, request *createAgentClassRequest) (any, error) {
	if err := comncore.GetCoreService().GetAgentService().CreateAgentClass(ctx, &agent.Class{
		AgentType: request.AgentType,
		ClassName: request.ClassName,
	}); err != nil {
		return nil, errors.WithStack(err)
	}
	return nil, nil
}

type listAgentClassRequest struct {
	page.PagesParams
}

func (r *listAgentClassRequest) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func listAgentClasses(ctx base.Context, _ any, request *listAgentClassRequest, _ any) (any, error) {
	if result, err := comncore.GetCoreService().GetAgentService().ListAgentClasses(ctx, &comncore.ListAgentClassOptions{
		PagesParams: request.PagesParams,
	}); err != nil {
		return nil, errors.WithStack(err)
	} else {
		return result, nil
	}
}
