package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
	"net/http"
)

func SetWebhookRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1", engine)
	r.MustRegister(http.MethodPost, "/gitlab/webhook/:token", gitlabWebhook)
	r.MustRegister(http.MethodPost, "/github/webhook", githubWebhook)
	r.MustRegister(http.MethodPost, "/codeup/webhook", codeupWebhook)
	r.MustRegister(http.MethodPost, "/codeaone/webhook/:token", codeaoneWebhook)
}

type gitlabWebhookPathParams struct {
	Token string `uri:"token"`
}

type codeaoneWebhookPathParams struct {
	Token string `uri:"token"`
}

func gitlabWebhook(ctx base.Context, path *gitlabWebhookPathParams, _ any, body *map[string]any) (any, error) {
	_, err := core.GetCoreService().GetWebhookService().GitlabWebhook(ctx, path.Token, *body)
	return nil, err
}

func githubWebhook(ctx base.Context, _ any, _ any, body *map[string]any) (any, error) {
	ginCtx := ctx.GetGinContext()
	event := ginCtx.GetHeader("X-GitHub-Event")
	return nil, core.GetCoreService().GetWebhookService().GithubWebhook(ctx, webhook.GithubEvent(event), *body)
}

func codeupWebhook(ctx base.Context, _ any, _ any, body *map[string]any) (any, error) {
	return nil, core.GetCoreService().GetWebhookService().CodeupWebhook(ctx, *body)
}

func codeaoneWebhook(ctx base.Context, path *codeaoneWebhookPathParams, _ any, body *map[string]any) (any, error) {
	return nil, core.GetCoreService().GetWebhookService().CodeaoneWebhook(ctx, path.Token, *body)
}
