package route

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	commoncodeplatform "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"net/http"
	"strings"
)

func SetupCodePlatformRoutes(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/code-platform", engine)
	if innerAccess {
		r.MustRegister(http.MethodGet, "/sessions/:sessionId/merge-request", getMergeRequest)
		r.MustRegister(http.MethodPost, "/sessions/:sessionId/comments", createMergeRequestComment)
		r.MustRegister(http.MethodGet, "/sessions/:sessionId/comments/:commentId", listMergeRequestComments)
		r.MustRegister(http.MethodPut, "/sessions/:sessionId/comments/:commentId", updateMergeRequestComment)
		r.MustRegister(http.MethodDelete, "/sessions/:sessionId/comments/:commentId", deleteMergeRequestComment)

		r.MustRegister(http.MethodGet, "/sessions/:sessionId/repository/files/*filePath", getRepositoryFile)

		r.MustRegister(http.MethodPost, "/sessions/:sessionId/reviews", createReview)

		r.MustRegister(http.MethodGet, "/sessions/:sessionId/incremental-review-request", GetIncrementalMergeRequest)

		// 只有 github 用
		r.MustRegister(http.MethodPut, "/sessions/:sessionId/reviews/:reviewId", updateReview)
		r.MustRegister(http.MethodPost, "/sessions/:sessionId/check-runs", createCheckRun)
		r.MustRegister(http.MethodPut, "/sessions/:sessionId/check-runs/:checkRunId", updateCheckRun)
	}

	// 用户反馈。a 标签只能发送支持 Get 请求。需要返回特殊的 html 内容，所以用不了 MustRegister。
	engine.Group("v1/code-platform").GET("/sessions/:sessionId/feedback", feedback)
	r.MustRegister(http.MethodPut, "/sessions/:sessionId/feedback", updateFeedback)

}

type mergeRequestPathParam struct {
	SessionId string `uri:"sessionId"`
}

func (p *mergeRequestPathParam) Validate() error {
	if p.SessionId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "session_id")
	}
	return nil
}

type getMergeRequestQueryParams struct {
	WithCommits bool `form:"with_commits"`
}

func getMergeRequest(ctx base.Context, path *mergeRequestPathParam, query *getMergeRequestQueryParams, _ any) (any, error) {
	return core.GetCoreService().GetCodePlatformService().GetMergeRequest(ctx, path.SessionId, &commoncodeplatform.GetMergeRequestOptions{
		WithCommits: query.WithCommits,
	})
}

type createMergeRequestCommentRequest struct {
	*commoncodeplatform.CreateMergeRequestCommentOpts
}

func (r *createMergeRequestCommentRequest) Validate() error {
	return nil
}

func createMergeRequestComment(ctx base.Context, path *mergeRequestPathParam, _ any, body *createMergeRequestCommentRequest) (any, error) {
	return core.GetCoreService().GetCodePlatformService().CreateMergeRequestComment(ctx, path.SessionId, body.CreateMergeRequestCommentOpts)
}

type listMergeRequestCommentsRequest struct {
	SessionId string `form:"session_id"`
}

type commentPathParams struct {
	SessionId string `uri:"sessionId"`
	CommentId string `uri:"commentId"`
}

func (p *commentPathParams) Validate() error {
	if p.SessionId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "session_id")
	}
	if p.CommentId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "comment_id")
	}
	return nil
}

func listMergeRequestComments(ctx base.Context, path *commentPathParams, req *listMergeRequestCommentsRequest, _ any) (any, error) {
	return core.GetCoreService().GetCodePlatformService().ListMergeRequestComments(ctx, path.SessionId, &commoncodeplatform.ListMergeRequestCommentsRequest{
		CommentId: path.CommentId,
	})
}

type resolveMergeRequestCommentRequest struct {
	Resolved bool `json:"resolved"`
}

type updateMergeRequestCommentRequest struct {
	Content  *string `json:"content"`
	Resolved *bool   `json:"resolved"`
}

func updateMergeRequestComment(ctx base.Context, path *commentPathParams, _ any, body *updateMergeRequestCommentRequest) (any, error) {
	return core.GetCoreService().GetCodePlatformService().UpdateMergeRequestComment(ctx, path.SessionId, &commoncodeplatform.UpdateMergeRequestCommentOpts{
		CommentId: path.CommentId,
		Content:   body.Content,
		Resolved:  body.Resolved,
	})
}
func deleteMergeRequestComment(ctx base.Context, path *commentPathParams, _ any, _ any) (any, error) {
	return nil, core.GetCoreService().GetCodePlatformService().DeleteMergeRequestComment(ctx, path.SessionId, &commoncodeplatform.DeleteMergeRequestCommentOpts{
		CommentId: path.CommentId,
	})
}

func resolveMergeRequestComment(ctx base.Context, path *commentPathParams, _ any, body *resolveMergeRequestCommentRequest) (any, error) {
	return nil, core.GetCoreService().GetCodePlatformService().ResolveMergeRequestComment(ctx, path.SessionId, &commoncodeplatform.ResolveMergeRequestCommentOpts{
		CommentId: path.CommentId,
		Resolved:  body.Resolved,
	})
}

type getRepositoryFileRequest struct {
	StartLine *int    `form:"start_line"`
	EndLine   *int    `form:"end_line"`
	Ref       *string `form:"ref"`
}

type repositoryFilePathParam struct {
	SessionId string `uri:"sessionId"`
	FilePath  string `uri:"filePath"`
}

func (p *repositoryFilePathParam) Validate() error {
	if p.SessionId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "sessionId")
	}
	if p.FilePath == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "filePath")
	}
	return nil
}

func getRepositoryFile(ctx base.Context, path *repositoryFilePathParam, req *getRepositoryFileRequest, _ any) (any, error) {
	return core.GetCoreService().GetCodePlatformService().GetRepositoryFile(ctx, path.SessionId, &commoncodeplatform.GetRepositoryFileOpts{
		Path: strings.TrimPrefix(path.FilePath, "/"),
		Ref:  req.Ref,
		LineRange: &commoncodeplatform.LineRange{
			Start: req.StartLine,
			End:   req.EndLine,
		},
	})
}

type createReviewRequest struct {
	*commoncodeplatform.CreateReviewOpts
}

func (r *createReviewRequest) Validate() error {
	return nil
}

func createReview(ctx base.Context, path *mergeRequestPathParam, _ any, body *createReviewRequest) (any, error) {
	return core.GetCoreService().GetCodePlatformService().CreateReview(ctx, path.SessionId, body.CreateReviewOpts)
}

type reviewPathParam struct {
	SessionId string `uri:"sessionId"`
	ReviewId  string `uri:"reviewId"`
}

type updateReviewRequest struct {
	*commoncodeplatform.UpdateReviewOpts
}

func updateReview(ctx base.Context, path *reviewPathParam, _ any, body *updateReviewRequest) (any, error) {
	body.UpdateReviewOpts.ReviewId = path.ReviewId
	return nil, core.GetCoreService().GetCodePlatformService().UpdateReview(ctx, path.SessionId, body.UpdateReviewOpts)
}

type createCheckRunRequest struct {
	*commoncodeplatform.CreateCheckRunOpts
}

func createCheckRun(ctx base.Context, path *mergeRequestPathParam, _ any, body *createCheckRunRequest) (any, error) {
	return core.GetCoreService().GetCodePlatformService().CreateCheckRun(ctx, path.SessionId, body.CreateCheckRunOpts)
}

type checkRunPathParam struct {
	SessionId  string `uri:"sessionId"`
	CheckRunId string `uri:"checkRunId"`
}

type updateCheckRunRequest struct {
	*commoncodeplatform.UpdateCheckRunOpts
}

func updateCheckRun(ctx base.Context, path *checkRunPathParam, _ any, body *updateCheckRunRequest) (any, error) {
	body.Id = path.CheckRunId
	return nil, core.GetCoreService().GetCodePlatformService().UpdateCheckRun(ctx, path.SessionId, body.UpdateCheckRunOpts)
}

type updateFeedbackRequest struct {
	FeedbackContent *string `json:"feedback_content"`
	SuggestionId    string  `json:"suggestion_id"`
}

func updateFeedback(ctx base.Context, path *mergeRequestPathParam, _ any, body *updateFeedbackRequest) (any, error) {
	return nil, core.GetCoreService().GetCodePlatformService().UpdateFeedback(ctx, &commoncodeplatform.UpdateFeedbackOpts{
		SessionId:       path.SessionId,
		SuggestionId:    body.SuggestionId,
		FeedbackContent: body.FeedbackContent,
	})
}

func feedback(ctx *gin.Context) {
	baseCtx := base.NewContextFromRequest(ctx)
	sessionId := ctx.Param("sessionId")
	sgId := ctx.Query("suggestion_id")
	t := ctx.Query("feedback_type")

	err := core.GetCoreService().GetCodePlatformService().Feedback(baseCtx, &commoncodeplatform.CreateFeedbackOpts{
		SessionId:    sessionId,
		SuggestionId: sgId,
		FeedbackType: commoncodeplatform.FeedbackType(t),
	})
	if err != nil {
		ctx.Status(http.StatusInternalServerError)
		return
	}

	ctx.Data(http.StatusOK, "text/html; charset=utf-8", []byte(fmt.Sprintf(`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>反馈提交</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%%;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
            background-color: #edf2fb;
            color: #333;
        }

        body {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }

        .container {
            width: 100%%;
            max-width: 600px;
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
            padding: 40px;
            text-align: center;
        }

        .icon-container {
            display: flex;
            justify-content: center;
            margin-bottom: 24px;
        }

        .icon {
            width: 70px;
            height: 70px;
            border-radius: 50%%;
            background-color: #f0f4ff;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon svg {
            width: 32px;
            height: 32px;
            color: #7953FF;
        }

        h2 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #1a202c;
            text-align: center;
        }

        .description {
            color: #4a5568;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 12px;
            text-align: left;
        }

        textarea {
            width: 100%%;
            height: 180px;
            resize: none;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            font-size: 16px;
            font-family: inherit;
            margin-bottom: 32px;
            background-color: #f9fafc;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        textarea:focus {
            outline: none;
            border-color: #7953FF;
            box-shadow: 0 0 0 2px rgba(121, 83, 255, 0.1);
        }

        textarea::placeholder {
            color: #a0aec0;
        }

        button {
            background-color: #7953FF;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px 32px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
            min-width: 180px;
        }

        button:hover {
            background-color: #6942e0;
        }

        button svg {
            margin-right: 8px;
        }

        .footer-note {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 24px;
            color: #718096;
            font-size: 14px;
        }

        .footer-note svg {
            margin-right: 8px;
            width: 16px;
            height: 16px;
        }

        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
            }
            
            button {
                width: 100%%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon-container">
            <div class="icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
            </div>
        </div>
        
        <h2>请填写反馈</h2>
        <p class="description">为了进一步提升建议质量，请填写原因帮助我们更好的了解反馈内容：</p>
        
        <form id="feedbackForm">
            <textarea id="content" placeholder="请输入您的反馈..."></textarea>
            
            <button type="submit">
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
                提交并关闭
            </button>
        </form>
        
        <div class="footer-note">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="16" x2="12" y2="12"></line>
                <line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
            您的反馈将帮助我们改进服务
        </div>
    </div>

    <script>
        document.getElementById('feedbackForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const content = document.getElementById('content').value.trim();
            const isHelpful = "%s" === "helpful";

            if (!isHelpful && !content) {
                alert("请输入反馈内容");
                return;
            }

            const body = {
                suggestion_id: "%s",
                feedback_content: content
            };

            const url = window.location.origin + '/v1/code-platform/sessions/%s/feedback';

            fetch(url, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(body)
            }).then(() => {
                window.close();
            }).catch((err) => {
                // 失败也关闭窗口
                window.close();
            });
        });
    </script>
</body>
</html>
`,
		t,
		sgId,
		sessionId,
	)))
}

type getIncrementalMergeRequestParams struct {
	HeadSha     string `form:"head_sha"`
	BaseSha     string `form:"base_sha"`
	WithCommits bool   `form:"with_commits"`
}

func (p *getIncrementalMergeRequestParams) Validate() error {
	if len(p.BaseSha) < 1 {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "base_sha")
	}
	if len(p.HeadSha) < 1 {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "head_sha")
	}
	return nil
}
func GetIncrementalMergeRequest(ctx base.Context, path *sessionPathParams, query *getIncrementalMergeRequestParams, _ any) (any, error) {
	return core.GetCoreService().GetCodePlatformService().GetIncrementalMergeRequest(ctx, path.SessionId, &commoncodeplatform.GetIncrementalMergeRequestOptions{
		BaseSha:     query.BaseSha,
		HeadSha:     query.HeadSha,
		WithCommits: query.WithCommits,
	})

}
