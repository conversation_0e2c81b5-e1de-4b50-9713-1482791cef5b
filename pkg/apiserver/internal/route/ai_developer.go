package route

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"net/http"
)

func SetAiDeveloperConnectRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/ai-developer", engine)
	r.MustReg<PERSON>(http.MethodPost, "/token", createOrGetToken)
}

type createOrGetTokenResponse struct {
	AgentId string `json:"agentId"`
	Token   string `json:"token"`
}

func createOrGetToken(ctx base.Context, _ any, _ any, _ any) (any, error) {
	// todo @清矢
	// 这个接口要完成几个逻辑：
	// 1. 认证用户的阿里云的身份，包括子账号和主账号
	// 2. 查询主账号有没有创建 AiDeveloperAgent，如果没有，创建之
	// 3. 查询对应子账号有么有创建 Identity，如果没有，创建之，并获取 token
	// 4. 向前端返回 agentId 和 token
	//暂时先写死，模拟主账号访问
	ctx.GetLogger().Info("create or get token", "agentName", agent.AiDeveloperAgent)
	agentName := agent.AiDeveloperAgent
	opts := &agent.ListAgentOptions{
		AgentName: agentName,
		UserId:    ctx.GetUid(),
	}
	agentsResult, err := core.GetCoreService().GetAgentService().ListAgents(ctx, opts)
	if err != nil {
		return nil, err
	}
	agents := agentsResult.Items
	var agentInfo *agent.AgentInfo
	if len(agents) == 0 {
		//创建agent
		agentInfo, err = core.GetCoreService().GetAgentService().CreateAgent(ctx, &agent.CreateAgentRequest{
			AgentName: agentName,
			AgentProperty: &agent.AgentProperty{
				SkipSandbox: true,
			},
		})
		if err != nil {
			return nil, err
		}
	} else {
		agentInfo = agents[0]
	}
	var token string
	if identityInfo, err := core.GetCoreService().GetIdentityService().GetAiDeveloperIdentityByLoginUserId(ctx); err != nil {
		if !commonerrors.Is(err, codes.ErrRecordNotFound) {
			return nil, errors.WithStack(err)
		}
		//创建identity
		req := &identity.CreateIdentityRequest{
			AgentId: agentInfo.AgentId,
			Source:  identity.SourceAiDeveloper,
		}
		identityInfo, err = core.GetCoreService().GetIdentityService().CreateIdentity(ctx, req)
		if err != nil {
			ctx.GetLogger().Error("create identity failed", "err", err)
			return nil, errors.WithStack(err)
		}
		token = identityInfo.WebhookToken
	} else {
		token = identityInfo.WebhookToken
	}
	res := &createOrGetTokenResponse{
		AgentId: agentInfo.AgentId,
		Token:   token,
	}
	return res, nil
}
