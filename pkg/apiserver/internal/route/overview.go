package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/overview"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"net/http"
)

func SetOverviewRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/overview", engine)
	r.MustRegister(http.MethodGet, "", getOverviewMetrics)
}

func getOverviewMetrics(ctx base.Context, _ any, _ any, _ any) (any, error) {
	metrics, err := core.GetCoreService().GetOverviewService().ListUserMetrics(ctx)
	if err != nil {
		return nil, err
	}
	results := page.TransferSlice(metrics, func(m overview.MetricInfo) map[string]any {
		return map[string]any{
			"name":           m.Name,
			"period":         m.Period,
			"value":          m.Value,
			"compareRatio":   m.CompareRatio,
			"comparePercent": util.Keep2Digits(m.CompareRatio * 100),
		}
	})
	return map[string]any{
		"metrics": results,
	}, err
}
