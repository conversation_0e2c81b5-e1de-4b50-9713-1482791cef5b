package route

import (
	"context"
	"encoding/csv"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/authorization"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	comnhttp "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"net/http"
	"strings"
	"time"
)

func SetSessionRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/sessions", engine)
	adminRouter := router.NewApiRouter("admin", engine)
	if innerAccess {
		r.MustRegister(http.MethodGet, "/:sessionId", getSession)
		r.MustRegister(http.MethodPut, "/:sessionId", updateSession)
		r.MustRegister(http.MethodPost, "/:sessionId/chat", chat)
		adminRouter.MustRegister(http.MethodGet, "sessions/:sessionId/events", listSessionEventsByAdmin)
		adminRouter.MustRegister(http.MethodGet, "/agent-sessions", listAgentSessions)
	}
	r.MustRegister(http.MethodGet, "/:sessionId/token-usage", getSessionTokenUsage)

	r2 := router.NewApiRouter("v1", engine)
	r2.MustRegister(http.MethodGet, "/github/session-query", queryGithubSession)
	r2.MustRegister(http.MethodGet, "/codeup/session-query", queryCodeupSession)
	r2.MustRegister(http.MethodGet, "/gitlab/session-query", queryGitlabSession)

	// ai-developer 已经占用了 /v1/sessions下的接口
	// 这里我们设计新的/v1/agent-sessions 来满足灵码智能体控制台的产品需要
	// 产品需求和我们的会话模型有些许出入，需要一些额外的映射逻辑来补全数据
	r3 := router.NewApiRouter("v1/agent-sessions", engine)
	r3.MustRegister(http.MethodGet, "", listAgentSessions)
	r3.MustRegister(http.MethodGet, "/export-history", listExportAgentSessionHistory)
	// 导出AgentSession，需要响应一个文件
	engine.GET("v1/agent-sessions/export", exportAgentSession)

}

type sessionPathParams struct {
	SessionId string `uri:"sessionId"`
}

func (p *sessionPathParams) Validate() error {
	if p.SessionId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "sessionId")
	}
	return nil
}

func getSession(ctx base.Context, path *sessionPathParams, _ any, _ any) (any, error) {
	// todo @清矢
	// 在 AiDeveloper 的场景下 AR 需要通过此接口来查询会话信息
	return core.GetCoreService().GetSessionService().GetSession(ctx, path.SessionId)
}

func updateSession(ctx base.Context, path *sessionPathParams, _ any, body *session.UpdateSessionRequest) (any, error) {
	if body == nil {
		ctx.GetLogger().Error("body of session % is blank", path.SessionId)
		return nil, commonerrors.New(codes.ErrInvalidParameterWithEmpty, "body")
	}
	// todo @清矢
	// 在 AiDeveloper 的场景下 AR 需要通过此接口来写回会话的标题和缩略图，需要扩展 Property 字段
	return nil, core.GetCoreService().GetSessionService().UpdateSession(ctx, path.SessionId, body)
}

func chat(ctx base.Context, path *sessionPathParams, _ any, body *session.ChatRequest) (any, error) {
	// todo @清矢
	// 这个接口提供给 Agent Runtime 使用，AR会使用此接口来向 前端 投递消息
	// 请求 body 应该为 agent_connect.Message格式
	// 接到请求后调用 Agent Connect Service 的 Callback 接口来进行消息的回写
	// session control
	if body.Message == nil {
		ctx.GetLogger().Error("message of session % is blank", path.SessionId)
		return nil, commonerrors.New(codes.ErrInvalidParameterWithEmpty, "message")
	}
	if err := core.GetCoreService().GetAgentConnectService().Callback(ctx, nil, *body.Message); err != nil {
		ctx.GetLogger().Error("fail to callback", "err", err)
		return nil, err
	}
	return nil, nil
}

type queryGithubSessionParam struct {
	Repo              string `form:"repo"`
	Owner             string `form:"owner"`
	PullRequestNumber int    `form:"pullRequestNumber"`
}

func (p *queryGithubSessionParam) Validate() error {
	if p.Owner == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "owner")
	}
	if p.PullRequestNumber == 0 {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "pullRequestNumber")
	}
	if p.Repo == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "repo")
	}
	return nil
}

func queryGithubSession(ctx base.Context, _ any, query *queryGithubSessionParam, _ any) (any, error) {
	sessionsResp, err := core.GetCoreService().GetSessionService().ListSessions(ctx, &session.ListSessionsRequest{
		GithubOwner:             query.Owner,
		GithubPullRequestNumber: query.PullRequestNumber,
		GithubRepo:              query.Repo,
		PagesParams: page.PagesParams{
			PageNumber: 1,
			PageSize:   1,
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(sessionsResp.Items) == 0 {
		return nil, errors.WithStack(commonerrors.New(codes.ErrRecordNotFound))
	}
	return sessionsResp.Items[0], nil
}

type queryGitlabSessionParam struct {
	ProjectId      string `form:"projectId" uri:"projectId"`
	MergeRequestId string `form:"mergeRequestId" uri:"mergeRequestId"`
}

func (p *queryGitlabSessionParam) Validate() error {
	if p.ProjectId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "projectId")
	}
	if p.MergeRequestId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "mergeRequestId")
	}
	return nil
}

func queryGitlabSession(ctx base.Context, _ any, query *queryGitlabSessionParam, _ any) (any, error) {
	sessionsResp, err := core.GetCoreService().GetSessionService().ListSessions(ctx, &session.ListSessionsRequest{
		GitlabProjectId:      query.ProjectId,
		GitlabMergeRequestId: query.MergeRequestId,
		PagesParams: page.PagesParams{
			PageNumber: 1,
			PageSize:   1,
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(sessionsResp.Items) == 0 {
		return nil, errors.WithStack(commonerrors.New(codes.ErrRecordNotFound))
	}
	return sessionsResp.Items[0], nil
}

type queryCodeupSessionParam struct {
	OrganizationId string `form:"organizationId"`
	RepositoryId   string `form:"repositoryId"`
	LocalId        int    `form:"localId"`
}

func (p *queryCodeupSessionParam) Validate() error {
	if p.RepositoryId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "repository")
	}
	if p.LocalId == 0 {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "localId")
	}
	if p.OrganizationId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "OrganizationId")
	}
	return nil
}

func queryCodeupSession(ctx base.Context, _ any, query *queryCodeupSessionParam, _ any) (any, error) {
	ctx = base.NewContextForBareUser(query.OrganizationId, ctx.GetTraceId(), "codeup", base.SiteTypeUnknown)
	sessionsResp, err := core.GetCoreService().GetSessionService().ListSessions(ctx, &session.ListSessionsRequest{
		CodeupOrganizationId: query.OrganizationId,
		CodeupRepositoryId:   query.RepositoryId,
		CodeupLocalId:        query.LocalId,
		PagesParams: page.PagesParams{
			PageNumber: 1,
			PageSize:   1,
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(sessionsResp.Items) == 0 {
		return nil, errors.WithStack(commonerrors.New(codes.ErrRecordNotFound))
	}
	s := sessionsResp.Items[0]
	var headCommitIds []string
	lastTaskType := ""
	lastTaskStartAt := int64(0)
	lastTaskEndAt := int64(0)
	// 按照创建时间升序
	if tasks, err := core.GetCoreService().GetAgentService().ListAgentTasks(ctx, &agent.ListAgentTaskOptions{
		States:      []agent.TaskState{agent.TaskStateTriggered},
		SessionId:   s.SessionId,
		PagesParams: page.PagesParams{PageNumber: 1, PageSize: 100},
	}); err != nil {
		return nil, errors.WithStack(err)
	} else {
		for _, task := range tasks.Items {
			if commitId := task.TaskConfig.GetCodeReviewHeadCommitId(); commitId != "" {
				headCommitIds = append(headCommitIds, commitId)
			}
			if taskType := task.TaskConfig.GetCodeReviewTaskType(); taskType != "" {
				lastTaskType = taskType
			}
			if task.State == agent.TaskStateTriggered {
				// 任务开始时间
				lastTaskStartAt = task.UpdateTimestamp
			}
		}
	}
	state := s.State.SimplifyForCodeup()
	if s.State == session.Idle || s.State == session.Init {
		// 这里有一个时间延迟，即webhook收到了任务，但是session仍然是Idle状态。 需要等待agent_task下发到AR里 才会更新为running
		// 这里做一下补偿，对codeup不感知这个间隙
		// 即捞一下是否有pending的task，认为这个task任务是马上要提交到AR变成Running
		tasks, err := core.GetCoreService().GetAgentService().ListAgentTasks(ctx, &agent.ListAgentTaskOptions{
			States:      []agent.TaskState{agent.TaskStatePending},
			SessionId:   s.SessionId,
			PagesParams: page.PagesParams{PageNumber: 1, PageSize: 1},
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(tasks.Items) > 0 {
			state = session.Running
			task := tasks.Items[0]
			lastTaskType = task.TaskConfig.GetCodeReviewTaskType()
			lastTaskStartAt = task.UpdateTimestamp
		}
	}

	if state == session.Idle || state == session.Finished {
		// 如果会话已经运行完成，则将会话更新时间作为上一次任务完成时间
		lastTaskEndAt = s.UpdateTimestamp
	}
	data := map[string]any{
		"sessionId":            s.SessionId,
		"state":                state,
		"triggeredHeadCommits": headCommitIds,
		"lastTaskType":         lastTaskType,
		"lastTaskStartAt":      lastTaskStartAt,
		"lastTaskEndAt":        lastTaskEndAt,
	}
	return data, nil
}

type listAgentSessionsRequest struct {
	page.PagesParams
	SessionName   string `form:"sessionName"`
	RepositoryId  string `form:"repositoryId"`
	Author        string `form:"author"`
	States        string `form:"states"`
	FromTimestamp int64  `form:"fromTimestamp"`
	ToTimestamp   int64  `form:"toTimestamp"`
}

func listAgentSessions(ctx base.Context, _ any, query *listAgentSessionsRequest, _ any) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ViewAgentLogs); err != nil {
		return nil, err
	}
	var states []session.AgentSessionState
	if query.States != "" {
		split := strings.Split(query.States, ",")
		states = page.TransferSlice(split, func(s string) session.AgentSessionState { return session.AgentSessionState(s) })
	}
	return core.GetCoreService().GetSessionService().ListAgentSessions(ctx, &session.ListAgentSessionsRequest{
		PagesParams:   query.PagesParams,
		SessionName:   query.SessionName,
		RepositoryId:  query.RepositoryId,
		Author:        query.Author,
		States:        states,
		FromTimestamp: query.FromTimestamp,
		ToTimestamp:   query.ToTimestamp,
	})
}

func exportAgentSession(c *gin.Context) {
	baseCtx := base.NewContextFromRequest(c)
	ctx, cancel := context.WithTimeout(baseCtx, 5*time.Minute)
	defer cancel()

	query := &listAgentSessionsRequest{}
	if err := c.Bind(query); err != nil {
		baseCtx.GetLogger().Error("fail to bind parameter", "err", fmt.Sprintf("%+v", err))
		comnhttp.InvalidParameter(c, "InvalidParameter", "failed to bind query", nil)
		return
	}
	var states []session.AgentSessionState
	if query.States != "" {
		split := strings.Split(query.States, ",")
		states = page.TransferSlice(split, func(s string) session.AgentSessionState { return session.AgentSessionState(s) })
	}
	filename := fmt.Sprintf("agent-session-%s.csv", time.Now().Format("20060102150405"))
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Transfer-Encoding", "chunked")

	_, _ = c.Writer.Write([]byte{0xEF, 0xBB, 0xBF})
	writer := csv.NewWriter(c.Writer)
	headers := []string{"智能体", "触发对象", "代码库", "创建时间", "创建人", "状态", "操作"}
	if err := writer.Write(headers); err != nil {
		baseCtx.GetLogger().Error("fail to write csv header", "err", fmt.Sprintf("%+v", err))
		_ = c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	writer.Flush()

	pageSize := 50
	req := &session.ListAgentSessionsRequest{
		PagesParams:   page.PagesParams{PageNumber: 1, PageSize: pageSize},
		SessionName:   query.SessionName,
		RepositoryId:  query.RepositoryId,
		Author:        query.Author,
		States:        states,
		FromTimestamp: query.FromTimestamp,
		ToTimestamp:   query.ToTimestamp,
	}
	core.GetCoreService().GetSessionService().RecordExportAgentSession(baseCtx, &session.ExportAgentSessionsRequest{
		SessionName:   query.SessionName,
		RepositoryId:  query.RepositoryId,
		Author:        query.Author,
		States:        query.States,
		FromTimestamp: query.FromTimestamp,
		ToTimestamp:   query.ToTimestamp,
	})
	sendItems := func(items []*session.AgentSessionInfo) error {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
			for _, item := range items {
				if err := writer.Write(convertAgentSessionToCsvRow(item)); err != nil {
					baseCtx.GetLogger().Error("fail to write csv row", "err", fmt.Sprintf("%+v", err))
					return c.AbortWithError(http.StatusInternalServerError, err)
				}
			}
			writer.Flush()
			return writer.Error()
		}
	}

	resp, err := core.GetCoreService().GetSessionService().ListAgentSessions(baseCtx, req)
	if err != nil {
		baseCtx.GetLogger().Error("fail to list agent sessions", "err", fmt.Sprintf("%+v", err))
		_ = c.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if err := sendItems(resp.Items); err != nil {
		return
	}
	total := resp.TotalSize
	totalPages := (int(total) + pageSize - 1) / pageSize
	for i := 2; i <= totalPages; i++ {
		req.PagesParams.PageNumber = i
		resp, err := core.GetCoreService().GetSessionService().ListAgentSessions(baseCtx, req)
		if err != nil {
			baseCtx.GetLogger().Error("fail to list agent sessions", "err", fmt.Sprintf("%+v", err))
			_ = c.AbortWithError(http.StatusInternalServerError, err)
			return
		}
		if err := sendItems(resp.Items); err != nil {
			return
		}
	}
}

func convertAgentSessionToCsvRow(info *session.AgentSessionInfo) []string {
	return []string{
		info.AgentName.ToChineseName(),
		info.SessionName,
		info.RepositoryName,
		time.UnixMilli(info.CreateTimestamp).Format("2006-01-02 14:30:15"),
		info.Author,
		string(info.State),
		info.ExternalLink,
	}
}

type listExportAgentSessionHistoryRequest struct {
	page.PagesParams
}

func (p *listExportAgentSessionHistoryRequest) Validate() error {
	if err := p.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}

func listExportAgentSessionHistory(ctx base.Context, _ any, query *listExportAgentSessionHistoryRequest, _ any) (any, error) {
	return core.GetCoreService().GetSessionService().ListAgentSessionExportHistory(ctx, &session.ListAgentSessionExportHistoryRequest{
		PagesParams: query.PagesParams,
	})
}

func getSessionTokenUsage(ctx base.Context, path *sessionPathParams, _ any, _ any) (any, error) {
	return core.GetCoreService().GetSessionService().GetSessionTokenUsage(ctx, path.SessionId)
}

type listSessionEventsByAdminRequest struct {
	page.PagesParams
	WithIdAsc bool `form:"withIdAsc"`
}

func (p *listSessionEventsByAdminRequest) Validate() error {
	if err := p.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}

func listSessionEventsByAdmin(ctx base.Context, path *sessionPathParams, query *listSessionEventsByAdminRequest, _ any) (any, error) {
	return core.GetCoreService().GetEventService().ListEvents(ctx, &event.ListEventsOptions{
		SessionId: path.SessionId,
		WithIdAsc: query.WithIdAsc,
		Page:      query.PagesParams})
}
