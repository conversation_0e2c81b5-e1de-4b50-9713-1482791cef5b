package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/authorization"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	event2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"net/http"
	"strings"
)

func SetIdentityRoutes(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/identities", engine)
	r.MustRegister(http.MethodPost, "", createIdentity)
	r.MustRegister(http.MethodGet, "/:identityId", getIdentity)
	r.MustRegister(http.MethodPatch, "/:identityId", updateIdentity)
	r.MustRegister(http.MethodGet, "", listIdentities)
	r.MustRegister(http.MethodDelete, "/:identityId", deleteIdentity)
}

func createIdentity(ctx base.Context, _ any, _ any, body *identity.CreateIdentityRequest) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
		return nil, err
	}
	info, err := core.GetCoreService().GetIdentityService().CreateIdentity(ctx, body)
	if err != nil {
		return nil, err
	}
	event := event2.NewIdentityCreatedEvent(ctx, info.IdentityId, &event2.IdentityProperty{
		IdentityId:       info.IdentityId,
		AgentId:          info.AgentId,
		Source:           string(info.Source),
		PlatformEndpoint: info.PlatformEndpoint,
		PlatformToken:    util.MaskSecret(info.PlatformToken),
		WebhookToken:     info.WebhookToken,
		Description:      info.Description,
	})
	core.GetCoreService().GetEventService().Audit(ctx, event)
	return info.ToApiResponse(), nil
}

type identityPathParams struct {
	IdentityId string `uri:"identityId"`
}

func (p *identityPathParams) Validate() error {
	if p.IdentityId == "" {
		return commonerrors.New(codes.ErrInvalidParameter, "identityId", p.IdentityId)
	}
	return nil
}

func getIdentity(ctx base.Context, path *identityPathParams, _ any, _ any) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ViewAgentConfiguration); err != nil {
		return nil, err
	}
	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentity(ctx, path.IdentityId)
	if err != nil {
		return nil, err
	}
	return idInfo.ToApiResponse(), nil
}

type listIdentitiesParam struct {
	AgentId     string `form:"agentId"`
	SourceTypes string `form:"sourceTypes"`
	page.PagesParams
}

func (p *listIdentitiesParam) Validate() error {
	if err := p.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}

func (p *listIdentitiesParam) GetSourceTypes() []string {
	if p.SourceTypes == "" {
		return nil
	}
	return strings.Split(p.SourceTypes, ",")
}

func listIdentities(ctx base.Context, _ any, query *listIdentitiesParam, _ any) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ViewAgentConfiguration); err != nil {
		return nil, err
	}
	options := &identity.ListIdentityOptions{
		AgentId:     query.AgentId,
		SourceTypes: query.GetSourceTypes(),
		PagesParams: query.PagesParams,
	}
	listResult, err := core.GetCoreService().GetIdentityService().ListIdentities(ctx, options)
	if err != nil {
		return nil, err
	}
	results := page.TransferSlice(listResult.Items, func(item identity.IdentityInfo) map[string]any {
		return item.ToApiResponse()
	})
	return &page.PagedItems[map[string]any]{
		Items:      results,
		PageNumber: listResult.PageNumber,
		PageSize:   listResult.PageSize,
		TotalSize:  listResult.TotalSize,
	}, nil
}

func deleteIdentity(ctx base.Context, path *identityPathParams, _ any, _ any) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
		return nil, err
	}
	idInfo, err := core.GetCoreService().GetIdentityService().GetIdentity(ctx, path.IdentityId)
	if err != nil {
		return nil, err
	}
	if err := core.GetCoreService().GetIdentityService().DeleteIdentity(ctx, path.IdentityId); err != nil {
		return nil, err
	}
	// 删除时需要完整记录信息，以便追溯
	event := event2.NewIdentityDeletedEvent(ctx, path.IdentityId, &event2.IdentityProperty{
		IdentityId:       path.IdentityId,
		AgentId:          idInfo.AgentId,
		Source:           string(idInfo.Source),
		PlatformEndpoint: idInfo.PlatformEndpoint,
		PlatformToken:    util.MaskSecret(idInfo.PlatformToken),
		WebhookToken:     idInfo.WebhookToken,
	})
	core.GetCoreService().GetEventService().Audit(ctx, event)
	return nil, nil
}

type updateIdentityRequest struct {
	PlatformToken *string `json:"platformToken"`
	// SyncAssociated 一并更新与当前Identity AccessToken相同的Identity记录
	SyncAssociated *bool   `json:"syncAssociated"`
	Description    *string `json:"description"`
}

func (r *updateIdentityRequest) Validate() error {
	if r.PlatformToken == nil && r.Description == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "platformToken or description")
	}
	if r.Description != nil {
		if len(*r.Description) > 1024 {
			return commonerrors.New(codes.ErrInvalidParameterWithDetail, "description", r.Description, "length should be less than 1024")
		}
	}
	if r.PlatformToken != nil {
		// 移除空格
		v := strings.TrimSpace(*r.PlatformToken)
		r.PlatformToken = &v
	}
	return nil
}

func updateIdentity(ctx base.Context, path *identityPathParams, _ any, body *updateIdentityRequest) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
		return nil, err
	}
	return nil, core.GetCoreService().GetIdentityService().UpdateIdentity(ctx, path.IdentityId, &identity.UpdateIdentityRequest{
		PlatformToken:  body.PlatformToken,
		SyncAssociated: body.SyncAssociated,
		Description:    body.Description,
	})
}
