package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/authorization"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	event2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"net/http"
)

func SetAgentRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/agents", engine)
	adminRouter := router.NewApiRouter("admin/", engine)

	r.MustRegister(http.MethodGet, "", listAgents)
	r.MustRegister(http.MethodGet, "/:agentId", getAgent)
	r.MustRegister(http.MethodPatch, "/:agentId", patchAgent)

	if innerAccess {
		r.MustRegister(http.MethodGet, "/:agentId/snapshots", listAgentSnapshots)
		r.MustRegister(http.MethodPost, "/:agentId/snapshots", createAgentSnapshot)
		// 暂未对外开放
		r.MustRegister(http.MethodPost, "", createAgent)
		r.MustRegister(http.MethodDelete, "/:agentId", deleteAgent)
		r.MustRegister(http.MethodPost, "/:agentId", updateAgent)

		adminRouter.MustRegister(http.MethodGet, "/agent-snapshots", listAgentSnapshotsByAdmin)
	}

	r.MustRegister(http.MethodGet, "/:agentId/tool-description", listToolDescription)
}

type agentPathParams struct {
	AgentId string `uri:"agentId"`
}

func (p *agentPathParams) Validate() error {
	if p.AgentId == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "agentId")
	}
	return nil
}

func createAgent(ctx base.Context, _ any, _ any, body *agent.CreateAgentRequest) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
		return nil, err
	}
	return core.GetCoreService().GetAgentService().CreateAgent(ctx, body)
}

type listAgentParams struct {
	AgentName agent.AgentName `form:"agentName"`
	page.PagesParams
}

func (r *listAgentParams) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}

func listAgents(ctx base.Context, _ any, query *listAgentParams, _ any) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ViewAgentConfiguration); err != nil {
		return nil, err
	}

	// 产品形态要求 每个组织下 默认有一个 CR Agent
	opt := &agent.ListAgentOptions{AgentName: agent.CodeReviewAgent, PagesParams: page.PagesParams{PageNumber: 1, PageSize: 1}}
	if resp, err := core.GetCoreService().GetAgentService().ListAgents(ctx, opt); err != nil {
		return nil, err
	} else if resp.TotalSize == 0 {
		if _, err := core.GetCoreService().GetAgentService().CreateAgent(ctx, &agent.CreateAgentRequest{
			AgentName: agent.CodeReviewAgent,
		}); err != nil {
			return nil, err
		}
	}

	return core.GetCoreService().GetAgentService().ListAgents(ctx, &agent.ListAgentOptions{
		AgentName:   query.AgentName,
		PagesParams: query.PagesParams,
	})
}

func getAgent(ctx base.Context, pathParams *agentPathParams, _ any, _ any) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ViewAgentConfiguration); err != nil {
		return nil, err
	}
	return core.GetCoreService().GetAgentService().GetAgent(ctx, pathParams.AgentId)
}

func updateAgent(ctx base.Context, pathParams *agentPathParams, _ any, body *updateAgentParams) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
		return nil, err
	}
	return nil, core.GetCoreService().GetAgentService().UpdateAgent(ctx, pathParams.AgentId, &agent.UpdateAgentRequest{
		AgentProperty: body.AgentProperty,
	})
}

type updateAgentParams struct {
	AgentProperty *agent.AgentProperty `json:"agentProperty,omitempty"`
}

func (p *updateAgentParams) Validate() error {
	if p.AgentProperty == nil {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "agentProperty")
	}
	if p.AgentProperty != nil {
		if err := p.AgentProperty.Validate(); err != nil {
			return err
		}
	}
	return nil
}

func deleteAgent(ctx base.Context, pathParams *agentPathParams, _ any, _ any) (any, error) {
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
		return nil, err
	}
	return nil, core.GetCoreService().GetAgentService().DeleteAgent(ctx, pathParams.AgentId)
}

type listAgentSnapshotsRequest struct {
	page.PagesParams
	SessionId string `form:"sessionId"`
}

func (r *listAgentSnapshotsRequest) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}

func listAgentSnapshots(ctx base.Context, pathParams *agentPathParams, query *listAgentSnapshotsRequest, _ any) (any, error) {
	return core.GetCoreService().GetAgentService().ListSnapshots(ctx, &agent.ListSnapshotOptions{
		AgentId:   pathParams.AgentId,
		SessionId: query.SessionId,
		Page:      query.PagesParams,
	})
}

func listAgentSnapshotsByAdmin(ctx base.Context, _ any, query *listAgentSnapshotsRequest, _ any) (any, error) {
	return core.GetCoreService().GetAgentService().ListSnapshots(ctx, &agent.ListSnapshotOptions{
		SessionId: query.SessionId,
		Page:      query.PagesParams,
	})
}

type createAgentSnapshotRequest struct {
	SessionId string         `json:"sessionId"`
	Data      map[string]any `json:"data"`
}

func (r *createAgentSnapshotRequest) Validate() error {
	if r.SessionId == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "sessionId")
	}
	return nil
}

func createAgentSnapshot(ctx base.Context, pathParams *agentPathParams, _ any, body *createAgentSnapshotRequest) (any, error) {
	return nil, core.GetCoreService().GetAgentService().CreateOrUpdateSnapshot(ctx, pathParams.AgentId, body.SessionId, body.Data)
}

type listToolDescriptionRequest struct {
	page.PagesParams
	Language string `form:"language"`
}

func (r *listToolDescriptionRequest) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	switch r.Language {
	case "zh-CN", "en-US":
	case "":
		r.Language = "zh-CN"
	default:
		return errors.New(codes.ErrInvalidParameter, "language", r.Language)
	}
	return nil
}

func listToolDescription(ctx base.Context, pathParams *agentPathParams, query *listToolDescriptionRequest, _ any) (any, error) {
	return core.GetCoreService().GetAgentService().ListCodeReviewTools(ctx, pathParams.AgentId, &agent.ListToolDescriptionsRequest{
		Language:    query.Language,
		PagesParams: query.PagesParams,
	})
}

type patchAgentParams struct {
	Enable           *bool                     `json:"enable,omitempty"`
	CodeReview       *agent.CodeReviewProperty `json:"codeReview,omitempty"`
	GithubCodeReview *agent.CodeReviewProperty `json:"githubCodeReview,omitempty"`
}

func (p *patchAgentParams) Validate() error {
	if p.Enable == nil && p.CodeReview == nil && p.GithubCodeReview == nil {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "enable, codeReview, githubCodeReview")
	}
	if p.CodeReview != nil {
		if err := p.CodeReview.Validate(); err != nil {
			return err
		}
	}
	if p.GithubCodeReview != nil {
		if err := p.GithubCodeReview.Validate(); err != nil {
			return err
		}
	}
	return nil
}

func patchAgent(ctx base.Context, pathParams *agentPathParams, _ any, body *patchAgentParams) (any, error) {
	if body.Enable != nil {
		if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ToggleAgent); err != nil {
			return nil, err
		}
	}
	if body.CodeReview != nil || body.GithubCodeReview != nil {
		if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
			return nil, err
		}
	}

	agentId := pathParams.AgentId

	oldAgentInfo, err := core.GetCoreService().GetAgentService().GetAgent(ctx, agentId)
	if err != nil {
		return nil, err
	}

	if err := core.GetCoreService().GetAgentService().PatchAgent(ctx, agentId, &agent.PatchAgentRequest{
		Enable:           body.Enable,
		CodeReview:       body.CodeReview,
		GithubCodeReview: body.GithubCodeReview,
	}); err != nil {
		return nil, err
	}

	latestAgentInfo, err := core.GetCoreService().GetAgentService().GetAgent(ctx, agentId)
	if err != nil {
		return nil, err
	}

	changes := &event2.AgentChanges{}
	if body.Enable != nil {
		changes.Enable = &event2.BoolDiff{
			Before: oldAgentInfo.Enabled(),
			After:  latestAgentInfo.Enabled(),
		}
	}
	if body.CodeReview != nil {
		changes.CodeReview = &event2.StringDiff{
			Before: util.SafeJsonMarshal(oldAgentInfo.GetCodeReviewProperty()),
			After:  util.SafeJsonMarshal(latestAgentInfo.GetCodeReviewProperty()),
		}
	}
	if body.GithubCodeReview != nil {
		changes.GithubCodeReview = &event2.StringDiff{
			Before: util.SafeJsonMarshal(oldAgentInfo.GetGithubCodeReviewProperty()),
			After:  util.SafeJsonMarshal(latestAgentInfo.GetGithubCodeReviewProperty()),
		}
	}
	property := &event2.AgentProperty{
		AgentId:          agentId,
		AgentName:        string(latestAgentInfo.AgentName),
		Enable:           latestAgentInfo.Enabled(),
		CodeReview:       util.SafeJsonMarshal(latestAgentInfo.GetCodeReviewProperty()),
		GithubCodeReview: util.SafeJsonMarshal(latestAgentInfo.GetGithubCodeReviewProperty()),
	}
	event := event2.NewAgentPatchedEvent(ctx, agentId, property, changes)
	core.GetCoreService().GetEventService().Audit(ctx, event)
	return nil, nil
}
