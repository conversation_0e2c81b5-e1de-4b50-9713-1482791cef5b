package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"net/http"
)

func SetFeatureRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/features", engine)
	r.<PERSON>(http.MethodGet, "", listFeatures)
}

func listFeatures(ctx base.Context, _ any, _ any, _ any) (any, error) {
	features, err := core.GetCoreService().GetFeatureService().ListUserFeatures(ctx)
	if err != nil {
		return nil, err
	}
	return map[string]any{
		"items": features,
	}, nil
}
