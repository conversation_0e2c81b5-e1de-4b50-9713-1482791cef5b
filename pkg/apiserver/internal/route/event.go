package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"net/http"
)

func SetEventRoutes(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/events", engine)
	if innerAccess {
		r.MustRegister(http.MethodPost, "", collectEvent)
		r.MustRegister(http.MethodGet, "", listEvents)
	}
}

type collectEventRequest struct {
	EventId   string          `json:"event_id"`
	SessionId string          `json:"session_id"`
	Property  *event.Property `json:"property,omitempty"`
}

func (r *collectEventRequest) Validate() error {
	if r.SessionId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "session_id")
	}
	// EventId允许为空，将自动生成
	return nil
}

func collectEvent(ctx base.Context, _ any, _ any, body *collectEventRequest) (any, error) {
	return nil, core.GetCoreService().GetEventService().CollectEvent(ctx, &event.Event{
		EventId:   body.EventId,
		SessionId: body.SessionId,
		Property:  body.Property,
	})
}

func listEvents(ctx base.Context, _ any, req *event.ListEventsOptions, _ any) (any, error) {
	return core.GetCoreService().GetEventService().ListEvents(ctx, req)
}
