package route

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/router"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/authorization"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	event2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"net/http"
	"strings"
)

func SetRepositoryRoute(engine *gin.Engine, innerAccess bool) {
	r := router.NewApiRouter("v1/repositories", engine)
	r.MustRegister(http.MethodGet, "", listRepositories)
	r.MustRegister(http.MethodGet, "/:repositoryId", getRepository)
	r.MustRegister(http.MethodPut, "/:repositoryId", putRepository)
}

type listRepositoriesRequest struct {
	page.PagesParams
	Sources string `form:"sources"`
}

func (r *listRepositoriesRequest) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}

func listRepositories(ctx base.Context, _ any, query *listRepositoriesRequest, _ any) (any, error) {
	var sources []identity.Source
	if query.Sources != "" {
		sources = page.TransferSlice(strings.Split(query.Sources, ","), func(s string) identity.Source { return identity.Source(s) })
	}
	options := &repository.ListRepositoryOptions{
		PagesParams: query.PagesParams,
		Sources:     sources,
	}
	return core.GetCoreService().GetRepositoryService().ListRepositories(ctx, options)
}

type repoPathParams struct {
	RepositoryId string `uri:"repositoryId"`
}

func (p *repoPathParams) Validate() error {
	if p.RepositoryId == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "repositoryId")
	}
	return nil
}

func putRepository(ctx base.Context, path *repoPathParams, _ any, body *repository.UpdateRepositoryRequest) (any, error) {
	// 管理仓库配置 使用管理智能体权限配置约束
	if err := core.GetCoreService().GetAuthorizationService().VerifyOperationPermission(ctx, authorization.ManageAgentConfiguration); err != nil {
		return nil, err
	}
	repoId := path.RepositoryId
	oldRepoInfo, err := core.GetCoreService().GetRepositoryService().GetRepository(ctx, repoId)
	if err != nil {
		return nil, err
	}
	if err := core.GetCoreService().GetRepositoryService().UpdateRepository(ctx, repoId, body); err != nil {
		return nil, err
	}
	latestRepoInfo, err := core.GetCoreService().GetRepositoryService().GetRepository(ctx, repoId)
	if err != nil {
		return nil, err
	}
	event := event2.NewRepositoryUpdatedEvent(ctx, repoId, &event2.RepositoryProperty{
		RepositoryId:   repoId,
		RepositoryName: latestRepoInfo.Name,
		Setting:        util.SafeJsonMarshal(latestRepoInfo.Setting),
	}, &event2.RepositoryChanges{
		Setting: &event2.StringDiff{
			Before: util.SafeJsonMarshal(oldRepoInfo.Setting),
			After:  util.SafeJsonMarshal(latestRepoInfo.Setting),
		},
	})
	core.GetCoreService().GetEventService().Audit(ctx, event)
	return nil, nil
}

func getRepository(ctx base.Context, path *repoPathParams, _ any, _ any) (any, error) {
	return core.GetCoreService().GetRepositoryService().GetRepository(ctx, path.RepositoryId)
}
