package middleware

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"net/http"
	"regexp"
	"time"
)

var (
	labels = []string{"status", "endpoint", "method"}

	Uptime = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "api",
			Name:      "uptime",
			Help:      "HTTP service uptime.",
		}, nil,
	)

	ReqCount = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "api",
			Name:      "http_request_count_total",
			Help:      "Total number of HTTP requests made.",
		}, labels,
	)

	ReqDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "api",
			Name:      "http_request_duration_seconds",
			Help:      "HTTP request latencies in seconds.",
		}, labels,
	)

	ReqSizeBytes = prometheus.NewSummaryVec(
		prometheus.SummaryOpts{
			Namespace: "api",
			Name:      "http_request_size_bytes",
			Help:      "HTTP request sizes in bytes.",
		}, labels,
	)

	RespSizeBytes = prometheus.NewSummaryVec(
		prometheus.SummaryOpts{
			Namespace: "api",
			Name:      "http_response_size_bytes",
			Help:      "HTTP response sizes in bytes.",
		}, labels,
	)
)

// init registers the prometheus metrics
func init() {
	go recordUptime()
}

// recordUptime increases *service uptime per second.
func recordUptime() {
	for range time.Tick(time.Second) {
		Uptime.WithLabelValues().Inc()
	}
}

// calcRequestSize returns the size of request object.
func calcRequestSize(r *http.Request) float64 {
	size := 0
	if r.URL != nil {
		size = len(r.URL.String())
	}

	size += len(r.Method)
	size += len(r.Proto)

	for name, values := range r.Header {
		size += len(name)
		for _, value := range values {
			size += len(value)
		}
	}
	size += len(r.Host)

	// r.Form and r.MultipartForm are assumed to be included in r.URL.
	if r.ContentLength != -1 {
		size += int(r.ContentLength)
	}
	return float64(size)
}

type RequestLabelMappingFn func(c *gin.Context) string

// PromOpts represents the Prometheus middleware Options.
// It is used for filtering labels by regex.
type PromOpts struct {
	ExcludeRegexStatus     string
	ExcludeRegexEndpoint   string
	ExcludeRegexMethod     string
	EndpointLabelMappingFn RequestLabelMappingFn
}

// NewDefaultOpts return the default ProOpts
func NewDefaultOpts() *PromOpts {
	return &PromOpts{
		EndpointLabelMappingFn: func(c *gin.Context) string {
			//by default do nothing, return URL as is
			return c.Request.URL.Path
		},
	}
}

// EndpointFullPathOpts return ProOpts with a matched route full path endpoint
func EndpointFullPathOpts() *PromOpts {
	return &PromOpts{
		EndpointLabelMappingFn: func(c *gin.Context) string {
			return c.FullPath()
		},
	}
}

// checkLabel returns the match result of labels.
// Return true if regex-pattern compiles failed.
func (po *PromOpts) checkLabel(label, pattern string) bool {
	if pattern == "" {
		return true
	}

	matched, err := regexp.MatchString(pattern, label)
	if err != nil {
		return true
	}
	return !matched
}

// WithMetrics returns a gin.HandlerFunc for exporting some Web metrics
func WithMetrics(promOpts *PromOpts) gin.HandlerFunc {
	// make sure promOpts is not nil
	if promOpts == nil {
		promOpts = EndpointFullPathOpts()
	}

	// make sure EndpointLabelMappingFn is callable
	if promOpts.EndpointLabelMappingFn == nil {
		// 匹配Gin的默认路由，避免Value爆炸
		promOpts.EndpointLabelMappingFn = func(c *gin.Context) string {
			return c.FullPath()
		}
	}

	return func(c *gin.Context) {
		start := time.Now()
		c.Next()

		status := fmt.Sprintf("%d", c.Writer.Status())
		endpoint := promOpts.EndpointLabelMappingFn(c)
		method := c.Request.Method

		lvs := []string{status, endpoint, method}

		isOk := promOpts.checkLabel(status, promOpts.ExcludeRegexStatus) &&
			promOpts.checkLabel(endpoint, promOpts.ExcludeRegexEndpoint) &&
			promOpts.checkLabel(method, promOpts.ExcludeRegexMethod)

		if !isOk {
			return
		}
		// no response content will return -1
		respSize := c.Writer.Size()
		if respSize < 0 {
			respSize = 0
		}
		ReqCount.WithLabelValues(lvs...).Inc()
		ReqDuration.WithLabelValues(lvs...).Observe(time.Since(start).Seconds())
		ReqSizeBytes.WithLabelValues(lvs...).Observe(calcRequestSize(c.Request))
		RespSizeBytes.WithLabelValues(lvs...).Observe(float64(respSize))
	}
}
