package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/smartystreets/goconvey/convey"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context"
	"k8s.io/utils/ptr"
	"net/http"
	"strings"
	"testing"
	"time"
)

func TestWithRequestId(t *testing.T) {
	convey.Convey("WithRequestId", t, func() {
		time.Sleep(time.Millisecond)
		convey.Convey("请求中携带X-Acs-Request-Id头", func() {
			ctx := &gin.Context{
				Request: &http.Request{
					Header: http.Header{
						"X-Acs-Request-Id": []string{"reqId-test"},
					},
				},
			}
			h := WithRequestId()
			h(ctx)
			reqId := context.GetRequestId(ctx)
			convey.So(*reqId, convey.ShouldEqual, "reqId-test")
		})
		time.Sleep(time.Millisecond)
		convey.<PERSON><PERSON>("无请求头", func() {
			ctx := &gin.Context{
				Request: &http.Request{
					Header: http.Header{},
				},
			}
			h := WithRequestId()
			h(ctx)
			reqId := context.GetRequestId(ctx)
			convey.So(strings.HasPrefix(*reqId, "req-"), convey.ShouldBeTrue)
		})
	})
}

func TestWithAction(t *testing.T) {
	convey.Convey("WithAction", t, func() {
		time.Sleep(time.Millisecond)
		convey.Convey("请求中携带X-Acs-Api-Name头", func() {
			ctx := &gin.Context{
				Request: &http.Request{
					Header: http.Header{
						"X-Acs-Api-Name": []string{"ut"},
					},
				},
			}
			h := WithAction()
			h(ctx)
			action, ok := ctx.Get(context.KeyAction)
			convey.So(ok, convey.ShouldBeTrue)
			convey.So(action, convey.ShouldResemble, ptr.To("ut"))
		})
		time.Sleep(time.Millisecond)
		convey.Convey("无请求头", func() {
			ctx := &gin.Context{
				Request: &http.Request{
					Header: http.Header{},
				},
			}
			h := WithAction()
			h(ctx)
			_, ok := ctx.Get(context.KeyAction)
			convey.So(ok, convey.ShouldBeTrue)
		})
	})
}
