package middleware

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context"
	"time"
)

func AccessLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		start := time.Now()
		path := ctx.Request.URL.Path
		raw := ctx.Request.URL.RawQuery
		if raw != "" {
			path = path + "?" + raw
		}

		l := context.GetLogger(ctx)
		latency := time.Now().Sub(start)
		l.Info(path, "logger", "access",
			"statusCode", ctx.Writer.Status(),
			"latencyNanos", latency,
			"latency", latency.String(),
			"clientIp", ctx.ClientIP(),
			"method", ctx.Request.Method,
			"responseSize", ctx.Writer.Size(),
		)

		ctx.Next()
	}
}
