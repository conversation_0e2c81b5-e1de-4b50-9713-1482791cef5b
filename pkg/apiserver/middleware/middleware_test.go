package middleware

import (
	"bytes"
	"github.com/stretchr/testify/assert"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
)

// testResponseWriter 是用于测试的, 实现了 gin.ResponseWriter 接口的
type testResponseWriter struct {
	httptest.ResponseRecorder
	status int
	body   *bytes.Buffer
}

func newTestResponseWriter() *testResponseWriter {
	return &testResponseWriter{ResponseRecorder: *httptest.NewRecorder(), body: &bytes.Buffer{}}
}

// Status 返回记录的 HTTP 状态码.
func (w *testResponseWriter) Status() int {
	return w.status
}

// WriteHeader 记录状态码 （而不只是委托给 ResponseRecorder）.
func (w *testResponseWriter) WriteHeader(code int) {
	w.status = code
	w.ResponseRecorder.WriteHeader(code)
}

func (w *testResponseWriter) Write(data []byte) (int, error) {
	w.body.Write(data)
	return w.ResponseRecorder.Write(data)
}

func TestUserMock(t *testing.T) {
	// 创建gin的上下文
	gin.SetMode(gin.TestMode)
	w := newTestResponseWriter()
	c, _ := gin.CreateTestContext(w)

	requestBody := "test request body"
	c.Request, _ = http.NewRequest("POST", "/test?k=v", strings.NewReader(requestBody))
	c.Request.Header.Add("Content-Type", "application/json")
	c.Request.Header.Add("x-agent-ctl-mock-uid", "123")
	Authentication(true)(c)
	userCtx := base.NewContextFromRequest(c)
	assert.Equal(t, userCtx.GetUid(), "123")
}

func TestUserPop(t *testing.T) {
	// 创建gin的上下文
	gin.SetMode(gin.TestMode)
	w := newTestResponseWriter()
	c, _ := gin.CreateTestContext(w)
	uid := "uid"
	loginUid := uid
	bid := "bid"

	requestBody := "test request body"
	c.Request, _ = http.NewRequest("POST", "/test?k=v", strings.NewReader(requestBody))
	c.Request.Header.Add("Content-Type", "application/json")
	c.Request.Header.Add("x-acs-product", "agent-ctl")
	c.Request.Header.Add("x-acs-caller-uid", uid)
	c.Request.Header.Add("x-acs-caller-bid", bid)
	c.Request.Header.Add("x-acs-caller-type", string(base.UserTypeCustomer))

	Authentication(false)(c)
	userCtx := base.NewContextFromRequest(c)
	assert.Equal(t, userCtx.GetUid(), uid)
	assert.Equal(t, userCtx.GetLoginUid(), loginUid)
	assert.Equal(t, userCtx.GetBid(), bid)
	assert.Equal(t, userCtx.GetUserType(), base.UserTypeCustomer)
}

func TestUserPopSubUser(t *testing.T) {
	// 创建gin的上下文
	gin.SetMode(gin.TestMode)
	w := newTestResponseWriter()
	c, _ := gin.CreateTestContext(w)

	uid := "uid"
	loginUid := "loginUid"
	bid := "bid"

	requestBody := "test request body"
	c.Request, _ = http.NewRequest("POST", "/test?k=v", strings.NewReader(requestBody))
	c.Request.Header.Add("Content-Type", "application/json")
	c.Request.Header.Add("x-acs-product", "agent-ctl")
	c.Request.Header.Add("x-acs-caller-uid", loginUid)
	c.Request.Header.Add("x-acs-caller-bid", bid)
	c.Request.Header.Add("x-acs-parent-id", uid)
	c.Request.Header.Add("x-acs-caller-type", string(base.UserTypeSub))

	Authentication(false)(c)
	userCtx := base.NewContextFromRequest(c)
	assert.Equal(t, userCtx.GetUid(), uid)
	assert.Equal(t, userCtx.GetLoginUid(), loginUid)
	assert.Equal(t, userCtx.GetBid(), bid)
	assert.Equal(t, userCtx.GetUserType(), base.UserTypeSub)
}

func TestUserPopRole(t *testing.T) {
	// 创建gin的上下文
	gin.SetMode(gin.TestMode)
	w := newTestResponseWriter()
	c, _ := gin.CreateTestContext(w)

	uid := "uid"
	loginUid := "loginUid"
	bid := "bid"

	requestBody := "test request body"
	c.Request, _ = http.NewRequest("POST", "/test?k=v", strings.NewReader(requestBody))
	c.Request.Header.Add("Content-Type", "application/json")
	c.Request.Header.Add("x-acs-product", "agent-ctl")
	c.Request.Header.Add("x-acs-sts-token-caller-uid", uid)
	c.Request.Header.Add("x-acs-sts-token-caller-bid", bid)
	c.Request.Header.Add("x-acs-sts-token-role-id", loginUid)
	c.Request.Header.Add("x-acs-caller-type", string(base.UserTypeRole))

	Authentication(false)(c)
	userCtx := base.NewContextFromRequest(c)
	assert.Equal(t, userCtx.GetUid(), uid)
	assert.Equal(t, userCtx.GetLoginUid(), loginUid)
	assert.Equal(t, userCtx.GetBid(), bid)
	assert.Equal(t, userCtx.GetUserType(), base.UserTypeRole)
}
