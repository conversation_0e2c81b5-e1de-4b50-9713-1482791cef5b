package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/smartystreets/goconvey/convey"
	"net/http"
	"net/url"
	"testing"
)

func TestCORS(t *testing.T) {
	convey.Convey("Cors", t, func() {
		ctx := &gin.Context{
			Request: &http.Request{
				Header: http.Header{
					"X-Acs-Request-Id": []string{"reqId-test"},
				},
				URL: &url.URL{
					Path:     "/test",
					RawQuery: "a=bc",
				},
				Method: "OPTIONS",
			},
			Writer: &fakeResponseWriter{headers: make(http.Header)},
		}
		h := Cors()
		h(ctx)
	})
}
