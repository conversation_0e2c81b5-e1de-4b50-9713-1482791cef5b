package middleware

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/metrics"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	comnhttp "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	user2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/user"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"io"
	"io/ioutil"
	"log/slog"
	"net/http"
	"net/url"
	"strings"
)

// 用户认证中间件，支持以下几种
// 1. 主账号
// 2. 子账号
// 3. 客服账号
// 4. STS角色
const (
	// 模拟的主账号ID
	headerMockUid = "x-agent-ctl-mock-uid"
	// 模拟的子账号ID
	headerMockLoginUid = "x-agent-ctl-mock-login-uid"
	// 内部系统调用时，传递的用户主账号ID
	headerInnerMockUid = "x-user-id"
)

const (
	// 模拟的bid，使用公共云国内站bid
	mockBid = "26842"
)

type user struct {
	bid      string
	uid      string
	loginUid string
	userType base.UserType
	domain   string
	actBy    string
	siteType base.SiteType
}

func (u *user) GetSiteType() base.SiteType {
	return u.siteType
}

func (u *user) GetBid() string {
	return u.bid
}

func (u *user) GetUid() string {
	return u.uid
}

func (u *user) GetLoginUid() string {
	return u.loginUid
}

func (u *user) GetUserType() base.UserType {
	return u.userType
}

func (u *user) GetDomain() string {
	return u.domain
}
func (u *user) GetActBy() string {
	return u.actBy
}

func (u *user) LogValue() slog.Value {
	return slog.GroupValue(
		slog.String("uid", u.uid),
		slog.String("bid", u.bid),
		slog.String("loginUid", u.loginUid),
		slog.String("userType", string(u.userType)),
		slog.String("domain", u.domain),
		slog.String("actBy", u.actBy),
		slog.String("siteType", string(u.siteType)),
	)
}

func validateGithubWebhook(ctx *gin.Context) bool {
	l := context.GetLogger(ctx)
	signature := ctx.GetHeader("X-Hub-Signature-256")
	githubAppId := ctx.GetHeader("X-GitHub-Hook-Installation-Target-ID")
	// 读取请求体
	body, err := ctx.GetRawData()
	if err != nil {
		return false
	}

	// 将请求体重新设置回 gin.Context
	ctx.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

	githubAppIdInt, err := cast.ToInt64E(githubAppId)
	if err != nil {
		return false
	}
	secret := config.GetGithubAppWebhookSecret(githubAppIdInt)
	l.Info("validateGithubWebhook", "signature", signature, "githubAppId", githubAppId)
	// 计算 HMAC SHA256 哈希值
	mac := hmac.New(sha256.New, []byte(secret))
	mac.Write(body)
	expectedMAC := mac.Sum(nil)

	// 将哈希值转换为十六进制字符串
	expectedSignature := "sha256=" + hex.EncodeToString(expectedMAC)
	l.Info("validateGithubWebhook", "signature", signature, "expectedSignature", expectedSignature, "body", string(body))
	// 比较签名
	return hmac.Equal([]byte(signature), []byte(expectedSignature))
}

func validateCodeupWebhook(ctx *gin.Context) bool {
	l := context.GetLogger(ctx)
	sign := ctx.GetHeader("X-Tb-Signature")
	// todo 目前云效使用全局AppID，取全局的AppSecret来校验，后续支持
	appId := config.FormatCodeupAppId("")
	appSecret := config.GetCodeupAppSecret(appId)
	mac := hmac.New(sha256.New, []byte(appSecret))

	// 读取请求体
	body, err := ctx.GetRawData()
	if err != nil {
		return false
	}

	// 将请求体重新设置回 gin.Context
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	mac.Write(body)
	expectedSign := mac.Sum(nil)
	expectedSignStr := hex.EncodeToString(expectedSign)
	l.Info("validateCodeupWebhook", "requestSignature", sign, "calcSignature", expectedSignStr, "appId", appId)
	return expectedSignStr == sign
}

func resolveUser(ctx *gin.Context, isTesting bool, isInnerAccess bool) base.UserCtx {
	// fixme 测试可以用请求头模拟任何账号
	if isTesting && ctx.GetHeader(headerMockUid) != "" {
		uid := ctx.GetHeader(headerMockUid)
		ut := base.UserTypeSub
		luid := ctx.GetHeader(headerMockLoginUid)
		if luid == "" {
			luid = uid
			ut = base.UserTypeCustomer
		}
		return &user{
			bid:      mockBid,
			uid:      uid,
			loginUid: luid,
			userType: ut,
			domain:   ctx.GetHeader("host"),
			actBy:    "",
		}
	}
	if isInnerAccess {
		// 内部访问可以通过请求头传递任意用户身份
		// todo 需要校验内部调用身份可信
		if uid := ctx.GetHeader(headerInnerMockUid); uid != "" {
			return &user{
				bid:      mockBid,
				uid:      uid,
				loginUid: uid,
				userType: base.UserTypeInternal,
				domain:   ctx.GetHeader("host"),
				actBy:    "inner-system",
			}
		}
		innerAnonymousPathPrefix := []string{
			"/admin/ops",
			"/admin/ops-detail",
		}
		for _, pathPrefix := range innerAnonymousPathPrefix {
			if strings.HasPrefix(ctx.FullPath(), pathPrefix) {
				return base.NewAnonymousUser()
			}
		}
	}
	// todo 除了 对外的匿名路径，例如webhook callback等，其他需要移除掉。 使用上面的X-User-ID头解析内部调用
	anonymousPathPrefixes := []string{
		"/v1/agent-connect/:agentId",
		"/v1/agent-connect",
		"/v1/ai-developer/token",
		"/favicon.ico",
		"/users/login",
	}
	for _, pathPrefix := range anonymousPathPrefixes {
		if strings.HasPrefix(ctx.FullPath(), pathPrefix) {
			return base.NewAnonymousUser()
		}
	}
	if strings.HasPrefix(ctx.FullPath(), "/v1/github/webhook") {
		if pass := validateGithubWebhook(ctx); pass {
			return base.NewAnonymousUserWithActBy(base.ActByGithubWebhook)
		}
		return nil
	} else if strings.HasPrefix(ctx.FullPath(), "/v1/github/callback") {
		return base.NewAnonymousUserWithActBy(base.ActByGithubWebhook)
	} else if strings.HasPrefix(ctx.FullPath(), "/v1/codeup") {
		if pass := validateCodeupWebhook(ctx); pass {
			return base.NewAnonymousUserWithActBy(base.ActByCodeupWebhook)
		}
		return nil
	} else if strings.HasPrefix(ctx.FullPath(), "/v1/codeaone/webhook") {
		return base.NewAnonymousUserWithActBy(base.ActByCodeAoneWebhook)
	} else if strings.HasPrefix(ctx.FullPath(), "/v1/gitlab/webhook") {
		return base.NewAnonymousUserWithActBy(base.ActByGitlabWebhook)
	}

	// POP 认证
	p := base.NewPopCtx(ctx)
	if p != nil {
		u := &user{
			userType: base.UserType(p.GetCallerType()),
			domain:   ctx.GetHeader("x-acs-web-code"),
		}

		switch u.userType {
		case base.UserTypeCustomer:
			u.bid = p.GetCallerBid()
			u.uid = p.GetCallerUid()
			u.loginUid = p.GetCallerUid()
		case base.UserTypeSub:
			u.bid = p.GetCallerBid()
			u.uid = p.GetCallerParentId()
			u.loginUid = p.GetCallerUid()
		case base.UserTypeRole:
			u.bid = p.GetStsTokenCallerBid()
			u.uid = p.GetStsTokenCallerUid()
			u.loginUid = p.GetStsTokenRoleId()
		default:
			return nil
		}

		if u.bid != "" {
			if u.bid == base.BidIntl {
				u.siteType = base.SiteTypeIntl
			} else {
				u.siteType = base.SiteTypeDomestic
			}
		} else {
			u.siteType = base.SiteTypeUnknown
		}

		return u
	}

	return nil
}

var noAuthNeeded = []string{
	"/v1/code-platform/sessions/:sessionId/feedback",
}

// ShouldSkipAuth 判断当前请求路径是否需要跳过鉴权
func ShouldSkipAuth(requestPath string, noAuthPaths []string) bool {
	// 分割请求路径为段（去除前后斜杠，过滤空段）
	requestSegments := splitPath(requestPath)

	// 遍历无需鉴权的路径列表
	for _, path := range noAuthPaths {
		// 分割预定义路径为段
		pathSegments := splitPath(path)

		// 段数不一致，跳过
		if len(pathSegments) != len(requestSegments) {
			continue
		}

		// 逐段比较
		match := true
		for i := range pathSegments {
			// 如果预定义段是动态参数（以 : 开头），跳过比较
			if strings.HasPrefix(pathSegments[i], ":") {
				continue
			}

			// 非动态段必须完全一致
			if pathSegments[i] != requestSegments[i] {
				match = false
				break
			}
		}

		// 如果所有非动态段匹配，返回 true
		if match {
			return true
		}
	}

	return false
}

// splitPath 将路径按斜杠分割，并过滤空段
func splitPath(path string) []string {
	// 去除前后斜杠
	path = strings.Trim(path, "/")
	if path == "" {
		return []string{}
	}

	// 分割并过滤空段
	segments := strings.Split(path, "/")
	var result []string
	for _, seg := range segments {
		if seg != "" {
			result = append(result, seg)
		}
	}
	return result
}

func resolveYunxiaoUser(ctx *gin.Context) base.YunxiaoUser {
	tempCtx := base.NewContextFromRequest(ctx)
	yunxiaoLoginCookie, err := ctx.Cookie("yunxiao_login_cookie")
	if err != nil {
		tempCtx.GetLogger().Warn("failed to get yunxiao login cookie", "err", err)
		return nil
	}
	secretKey := config.GetYunxiaoSecretKey()
	endpoint := config.Get(config.KeySystemYunxiaoEndpoint)
	fullUrl, err := url.JoinPath(endpoint, "/users:byCookies")
	if err != nil {
		tempCtx.GetLogger().Error("failed to assemble yunxiao webhook endpoint", "err", err)
		return nil
	}
	data := map[string]string{
		"name":  "yunxiao_login_cookie",
		"value": yunxiaoLoginCookie,
	}
	bodyBytes, err := json.Marshal(data)
	if err != nil {
		tempCtx.GetLogger().Error("failed to marshal yunxiao login cookie", "err", err)
		return nil
	}
	resp, err := comnhttp.InvokeHttp[base.YunxiaoUserInfo](tempCtx, &comnhttp.InvokeHttpRequest{
		Method: "POST",
		Url:    fullUrl,
		Headers: map[string]string{
			"Authorization": fmt.Sprintf("Bearer %s", secretKey),
			"Content-Type":  "application/json",
		},
		Body: bodyBytes,
	})
	if err != nil {
		tempCtx.GetLogger().Error("failed to get user info by yunxiao login cookie", "err", err)
		return nil
	}
	u := base.NewCodeupUser(resp)

	userData := &user2.User{
		UserId:   u.GetUid(),
		UserType: user2.UserTypeYunxiao,
	}
	if err := core.GetCoreService().GetUserService().SaveUserIfNotExist(tempCtx, userData); err != nil {
		tempCtx.GetLogger().Error("failed to save user", "user", userData, "err", err)
		return nil
	}
	return u
}

// Authentication 中间件
func Authentication(isTesting bool, isInnerAccess bool) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		requestPath := ctx.Request.URL.Path
		var userCtx base.UserCtx
		// 检查是否需要跳过鉴权
		if ShouldSkipAuth(requestPath, noAuthNeeded) {
			userCtx = base.NewAnonymousUser()
		} else if yunxiaoCtx := resolveYunxiaoUser(ctx); yunxiaoCtx != nil {
			if yunxiaoCtx.NeedLogin() {
				ctx.Redirect(http.StatusFound, base.GetYunxiaoLoginUri(ctx))
				ctx.Abort()
				return
			}
			if yunxiaoCtx.NeedUpdatePassword() {
				ctx.Redirect(http.StatusFound, base.GetYunxiaoResetPasswordUri(ctx))
				ctx.Abort()
				return
			}
			userCtx = yunxiaoCtx
		} else if u := resolveUser(ctx, isTesting, isInnerAccess); u != nil {
			userCtx = u
		}

		if userCtx == nil {
			metrics.ErrorCounter.WithLabelValues("-", "Unauthorized", "Unauthorized").Inc()
			ctx.Redirect(http.StatusFound, base.GetYunxiaoLoginUri(ctx))
			ctx.Abort()
			return
		}
		// 尝试构建客服账号
		//if newCtx, err := isActUserAndBuildContext(ctx, userCtx); err != nil {
		//	ctx.AbortWithStatusJSON(http.StatusBadRequest, commonhttp.BadRequestResponse(ctx, err.Error()))
		//	return
		//} else if newCtx != nil {
		//	userCtx = newCtx
		//}

		ctx.Set(base.KeyUser, userCtx)
		ctx.Next()
	}
}
