package middleware

//
//import (
//	"fmt"
//	"github.com/gin-gonic/gin"
//	"github.com/smartystreets/goconvey/convey"
//	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
//	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
//	"net/http"
//	"net/http/httptest"
//	"testing"
//	"time"
//)
//
//func TestIsActUserAndBuildContext(t *testing.T) {
//	convey.Convey("Test isActUserAndBuildContext", t, func() {
//		patches := gomonkey.ApplyFunc(config.GetOrDefault, func(key string, defaultValue string) string {
//			if key == config.ActCallerId {
//				return "1419633767709936"
//			}
//			if key == config.ActRoleId {
//				return "300105115517647805"
//			}
//			if key == config.ActSessionDuration {
//				return "3600"
//			}
//			return defaultValue
//		})
//
//		defer patches.Reset()
//
//		convey.Convey("wrong caller uid", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			mockUser := &user{
//				userType: base.UserTypeRole,
//				uid:      "123123",
//				loginUid: "123123",
//			}
//			actUser, err := isActUserAndBuildContext(ctx, mockUser)
//
//			convey.So(err, convey.ShouldBeNil)
//			convey.So(actUser, convey.ShouldBeNil)
//		})
//
//		convey.Convey("wrong login uid", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			mockUser := &user{
//				userType: base.UserTypeRole,
//				uid:      "1419633767709936",
//				loginUid: "123123",
//			}
//			actUser, err := isActUserAndBuildContext(ctx, mockUser)
//
//			convey.So(err, convey.ShouldBeNil)
//			convey.So(actUser, convey.ShouldBeNil)
//		})
//
//		user := &user{
//			userType: base.UserTypeRole,
//			uid:      "1419633767709936",
//			loginUid: "300105115517647805",
//			domain:   "test_domain",
//			bid:      "test_bid",
//		}
//
//		convey.Convey("empty session id", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//
//			actUser, err := isActUserAndBuildContext(ctx, user)
//
//			convey.So(err, convey.ShouldBeNil)
//			convey.So(actUser, convey.ShouldBeNil)
//		})
//
//		convey.Convey("Valid session ID", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			now := time.Now().UnixMilli()
//			ctx.Request.Header.Set("x-act-session-id", fmt.Sprintf("123_1234_%d", now))
//
//			actUser, err := isActUserAndBuildContext(ctx, user)
//
//			convey.So(err, convey.ShouldBeNil)
//			convey.So(actUser, convey.ShouldNotBeNil)
//			convey.So(actUser.GetLoginUid(), convey.ShouldEqual, "123")
//			convey.So(actUser.GetActBy(), convey.ShouldEqual, "300105115517647805")
//		})
//
//		convey.Convey("Invalid session ID format", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			ctx.Request.Header.Set("x-act-session-id", "invalid_format")
//
//			actUser, err := isActUserAndBuildContext(ctx, user)
//
//			convey.So(err, convey.ShouldNotBeNil)
//			convey.So(actUser, convey.ShouldBeNil)
//		})
//
//		convey.Convey("Session ID expired", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			timestamp := time.Now().Add(-2 * time.Hour).UnixMilli() // 2小时以前的时间戳
//			ctx.Request.Header.Set("x-act-session-id", fmt.Sprintf("123_1234_%d", timestamp))
//
//			actUser, err := isActUserAndBuildContext(ctx, user)
//
//			convey.So(err, convey.ShouldNotBeNil)
//			convey.So(actUser, convey.ShouldBeNil)
//		})
//
//		convey.Convey("invalid session timestamp", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			timestamp := time.Now().Add(2 * time.Hour).UnixMilli()
//			ctx.Request.Header.Set("x-act-session-id", fmt.Sprintf("123_1234_%d", timestamp))
//
//			actUser, err := isActUserAndBuildContext(ctx, user)
//
//			convey.So(err, convey.ShouldNotBeNil)
//			convey.So(actUser, convey.ShouldBeNil)
//		})
//
//		convey.Convey("User type is not correct", func() {
//			user.userType = base.UserTypeCustomer
//
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			actUser, err := isActUserAndBuildContext(ctx, user)
//
//			convey.So(err, convey.ShouldBeNil)     // 应该不返回错误
//			convey.So(actUser, convey.ShouldBeNil) // 返回 nil
//		})
//	})
//}
//
//func TestResolveUser(t *testing.T) {
//	convey.Convey("Test resolveUser", t, func() {
//		patches := gomonkey.ApplyFunc(base.NewPopCtx, func(ctx *gin.Context) base.PopCtx {
//			// return nil for default case to test user mock
//			return nil
//		})
//		defer patches.Reset()
//
//		convey.Convey("Testing mode with mock user", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			ctx.Request.Header.Set(headerMockUid, "mock_uid")
//			ctx.Request.Header.Set(headerMockLoginUid, "mock_login_uid")
//			ctx.Request.Header.Set("host", "mock_domain")
//
//			user := resolveUser(ctx, true)
//
//			convey.So(user, convey.ShouldNotBeNil)
//			convey.So(user.GetUid(), convey.ShouldEqual, "mock_uid")
//			convey.So(user.GetLoginUid(), convey.ShouldEqual, "mock_login_uid")
//			convey.So(user.GetDomain(), convey.ShouldEqual, "mock_domain")
//			convey.So(user.GetUserType(), convey.ShouldEqual, base.UserTypeSub)
//		})
//
//		convey.Convey("Testing mode with customer type", func() {
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			ctx.Request.Header.Set(headerMockUid, "mock_uid")
//			ctx.Request.Header.Set("host", "mock_domain")
//
//			user := resolveUser(ctx, true)
//
//			convey.So(user, convey.ShouldNotBeNil)
//			convey.So(user.GetUid(), convey.ShouldEqual, "mock_uid")
//			convey.So(user.GetLoginUid(), convey.ShouldEqual, "mock_uid")
//			convey.So(user.GetDomain(), convey.ShouldEqual, "mock_domain")
//			convey.So(user.GetUserType(), convey.ShouldEqual, base.UserTypeCustomer)
//		})
//
//		convey.Convey("POP context returns customer user", func() {
//			patches.ApplyFunc(base.NewPopCtx, func(ctx *gin.Context) base.PopCtx {
//				return &basemock.MockPopInfo{
//					CallerType:        "customer",
//					CallerBid:         "26888",
//					CallerUid:         "mock_caller_uid",
//					StsTokenCallerBid: "mock_sts_token_bid",
//					StsTokenCallerUid: "mock_sts_token_uid",
//					StsTokenRoleId:    "mock_role_id",
//				}
//			})
//
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			user := resolveUser(ctx, false)
//
//			convey.So(user, convey.ShouldNotBeNil)
//			convey.So(user.GetBid(), convey.ShouldEqual, "26888")
//			convey.So(user.GetUid(), convey.ShouldEqual, "mock_caller_uid")
//			convey.So(user.GetLoginUid(), convey.ShouldEqual, "mock_caller_uid")
//			convey.So(user.GetUserType(), convey.ShouldEqual, base.UserTypeCustomer)
//			convey.So(user.GetSiteType(), convey.ShouldEqual, base.SiteTypeIntl)
//		})
//
//		convey.Convey("POP context returns sub user", func() {
//			patches.ApplyFunc(base.NewPopCtx, func(ctx *gin.Context) base.PopCtx {
//				return &basemock.MockPopInfo{
//					CallerType:        "sub",
//					CallerBid:         "mock_bid",
//					CallerUid:         "mock_caller_uid",
//					StsTokenCallerBid: "mock_sts_token_bid",
//					StsTokenCallerUid: "mock_sts_token_uid",
//					StsTokenRoleId:    "mock_role_id",
//					CallerParentId:    "mock_parent_id",
//				}
//			})
//
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			user := resolveUser(ctx, false)
//
//			convey.So(user, convey.ShouldNotBeNil)
//			convey.So(user.GetBid(), convey.ShouldEqual, "mock_bid")
//			convey.So(user.GetUid(), convey.ShouldEqual, "mock_parent_id")
//			convey.So(user.GetLoginUid(), convey.ShouldEqual, "mock_caller_uid")
//			convey.So(user.GetUserType(), convey.ShouldEqual, base.UserTypeSub)
//		})
//
//		convey.Convey("POP context returns role user", func() {
//			patches.ApplyFunc(base.NewPopCtx, func(ctx *gin.Context) base.PopCtx {
//				return &basemock.MockPopInfo{
//					CallerType:        "AssumedRoleUser",
//					CallerBid:         "mock_bid",
//					CallerUid:         "mock_caller_uid",
//					StsTokenCallerBid: "mock_sts_token_bid",
//					StsTokenCallerUid: "mock_sts_token_uid",
//					StsTokenRoleId:    "mock_role_id",
//				}
//			})
//
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			user := resolveUser(ctx, false)
//
//			convey.So(user, convey.ShouldNotBeNil)
//			convey.So(user.GetBid(), convey.ShouldEqual, "mock_sts_token_bid")
//			convey.So(user.GetUid(), convey.ShouldEqual, "mock_sts_token_uid")
//			convey.So(user.GetLoginUid(), convey.ShouldEqual, "mock_role_id")
//			convey.So(user.GetUserType(), convey.ShouldEqual, base.UserTypeRole)
//		})
//
//		convey.Convey("POP context returns none user", func() {
//			patches.ApplyFunc(base.NewPopCtx, func(ctx *gin.Context) base.PopCtx {
//				return &basemock.MockPopInfo{
//					CallerType:        "none",
//					CallerBid:         "mock_bid",
//					CallerUid:         "mock_caller_uid",
//					StsTokenCallerBid: "mock_sts_token_bid",
//					StsTokenCallerUid: "mock_sts_token_uid",
//					StsTokenRoleId:    "mock_role_id",
//				}
//			})
//
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			user := resolveUser(ctx, false)
//
//			convey.So(user, convey.ShouldBeNil)
//		})
//
//		convey.Convey("POP context returns nil", func() {
//			patches.ApplyFunc(base.NewPopCtx, func(ctx *gin.Context) base.PopCtx {
//				return nil
//			})
//
//			ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
//			ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
//			user := resolveUser(ctx, false) // Should still return nil
//
//			convey.So(user, convey.ShouldBeNil)
//		})
//	})
//}
//
//func TestAuthentication(t *testing.T) {
//	convey.Convey("Test Authentication Middleware", t, func() {
//		gin.SetMode(gin.TestMode) // 设置为测试模式
//
//		// Monkey patch dependencies
//		patches := gomonkey.ApplyFunc(resolveUser, func(ctx *gin.Context, isTesting bool) base.UserCtx {
//			// 默认返回有效用户
//			return &user{
//				uid:      "test_uid",
//				loginUid: "test_login_uid",
//				userType: base.UserTypeRole,
//				bid:      "test_bid",
//			}
//		})
//
//		patches.ApplyFunc(isActUserAndBuildContext, func(ctx *gin.Context, u base.UserCtx) (base.UserCtx, error) {
//			// 默认返回 nil，表示建立客服账号失败
//			return nil, nil
//		})
//
//		// Create a test router
//		router := gin.New()
//
//		// Use the Authentication middleware
//		router.Use(Authentication(false))
//
//		router.GET("/test", func(ctx *gin.Context) {
//			ctx.JSON(http.StatusOK, gin.H{"status": "ok"})
//		})
//
//		convey.Convey("When user is authenticated", func() {
//			req, _ := http.NewRequest(http.MethodGet, "/test", nil)
//			w := httptest.NewRecorder()
//			router.ServeHTTP(w, req)
//
//			convey.So(w.Code, convey.ShouldEqual, http.StatusOK)
//			convey.So(w.Body.String(), convey.ShouldContainSubstring, `"status":"ok"`)
//		})
//
//		time.Sleep(time.Millisecond)
//		convey.Convey("When user is unauthorized", func() {
//			// Mock resolveUser return nil
//			patches.ApplyFunc(resolveUser, func(ctx *gin.Context, isTesting bool) base.UserCtx {
//				return nil
//			})
//
//			req, _ := http.NewRequest(http.MethodGet, "/test", nil)
//			w := httptest.NewRecorder()
//			router.ServeHTTP(w, req)
//
//			convey.So(w.Code, convey.ShouldEqual, http.StatusUnauthorized)
//			convey.So(w.Body.String(), convey.ShouldContainSubstring, `"message":"Unauthorized"`)
//		})
//
//		convey.Convey("When isActUserAndBuildContext returns an error", func() {
//			// Mock resolveUser to return a user
//			patches.ApplyFunc(resolveUser, func(ctx *gin.Context, isTesting bool) base.UserCtx {
//				return &user{
//					uid:      "test_uid",
//					loginUid: "test_login_uid",
//					userType: base.UserTypeRole,
//					bid:      "test_bid",
//				}
//			})
//
//			// Mock isActUserAndBuildContext to return an error
//			patches.ApplyFunc(isActUserAndBuildContext, func(ctx *gin.Context, u base.UserCtx) (base.UserCtx, error) {
//				return nil, fmt.Errorf("failed to build context")
//			})
//
//			req, _ := http.NewRequest(http.MethodGet, "/test", nil)
//			w := httptest.NewRecorder()
//			router.ServeHTTP(w, req)
//
//			convey.So(w.Code, convey.ShouldEqual, http.StatusBadRequest)
//			convey.So(w.Body.String(), convey.ShouldContainSubstring, `"message":"failed to build context"`)
//		})
//
//		convey.Convey("When isActUserAndBuildContext returns an UserCtx", func() {
//			// Mock resolveUser to return a user
//			patches.ApplyFunc(resolveUser, func(ctx *gin.Context, isTesting bool) base.UserCtx {
//				return &user{
//					uid:      "test_uid",
//					loginUid: "test_login_uid",
//					userType: base.UserTypeRole,
//					bid:      "test_bid",
//				}
//			})
//
//			// Mock isActUserAndBuildContext to return an error
//			patches.ApplyFunc(isActUserAndBuildContext, func(ctx *gin.Context, u base.UserCtx) (base.UserCtx, error) {
//				return &user{
//					userType: base.UserTypeAct,
//					uid:      "1419633767709936",
//					loginUid: "123123",
//					actBy:    "1419633767709936",
//				}, nil
//			})
//
//			req, _ := http.NewRequest(http.MethodGet, "/test", nil)
//			w := httptest.NewRecorder()
//			router.ServeHTTP(w, req)
//
//			convey.So(w.Code, convey.ShouldEqual, http.StatusOK)
//			convey.So(w.Body.String(), convey.ShouldContainSubstring, `"status":"ok"`)
//		})
//
//		defer patches.Reset()
//	})
//}
