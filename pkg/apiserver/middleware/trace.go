package middleware

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
)

var (
	reqIdHeaders   = []string{"x-acs-request-id", "x-acs-req-uuid", "x-request-id"}
	traceHeaderKey = "x-trace-id"
)

const ActionHeader = "X-Acs-Api-Name"

func WithRequestId() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// todo rpc请求从PostForm参数中获取requestId
		reqId := getRequestIdFromHeaders(ctx)
		if reqId == "" {
			reqId = helper.NewRequestId()
		}
		ctx.Set(context.KeyRequestId, &reqId)
		ctx.Next()
	}
}

func WithTraceId() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		traceId := getTraceIdFromHeaders(ctx)
		if traceId == "" {
			traceId = helper.NewTraceId()
		}
		ctx.Set(context.KeyTraceId, &traceId)
		ctx.Next()
	}
}

func WithAction() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// todo rpc请求从PostForm参数中获取action
		action := getActionFromHeaders(ctx)
		if action == "" {
			action = ctx.FullPath()
		}
		ctx.Set(context.KeyAction, &action)
		ctx.Next()
	}
}

func getActionFromHeaders(ctx *gin.Context) string {
	if v := ctx.Request.Header.Get(ActionHeader); v != "" {
		return v
	}
	return ""
}

func getRequestIdFromHeaders(ctx *gin.Context) string {
	for _, key := range reqIdHeaders {
		if v := ctx.Request.Header.Get(key); v != "" {
			return v
		}
	}
	return ""
}

func getTraceIdFromHeaders(ctx *gin.Context) string {
	if v := ctx.Request.Header.Get(traceHeaderKey); v != "" {
		return v
	}
	return ""
}
