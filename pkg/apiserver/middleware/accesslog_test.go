package middleware

import (
	"bufio"
	. "github.com/bytedance/mockey"
	"github.com/gin-gonic/gin"
	"net"
	"net/http"
	"net/url"
	"testing"
	"time"
)

type fakeResponseWriter struct {
	headers http.Header
	status  int
}

func (w *fakeResponseWriter) Header() http.Header {
	return w.headers
}

func (w *fakeResponseWriter) Write(i []byte) (int, error) {
	return len(i), nil
}

func (w *fakeResponseWriter) WriteHeader(statusCode int) {
	w.status = statusCode
}

func (w *fakeResponseWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) Flush() {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) CloseNotify() <-chan bool {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) Status() int {
	return w.status
}

func (w *fakeResponseWriter) Size() int {
	return 0
}

func (w *fakeResponseWriter) WriteString(s string) (int, error) {
	return len(s), nil
}

func (w *fakeResponseWriter) Written() bool {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) WriteHeaderNow() {
	return
}

func (w *fakeResponseWriter) Pusher() http.Pusher {
	//TODO implement me
	panic("implement me")
}

func TestAccessLog(t *testing.T) {
	PatchConvey("AccessLog", t, func() {
		time.Sleep(time.Millisecond)
		PatchConvey("记录AccessLog", func() {
			ctx := &gin.Context{
				Request: &http.Request{
					URL: &url.URL{
						Path:     "/v1",
						RawQuery: "a=b",
					},
				},
				Writer: &fakeResponseWriter{},
			}
			Mock(ctx.ClientIP).Return("127.0.0.1").Build()
			h := AccessLog()
			h(ctx)
		})
	})
}
