package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/smartystreets/goconvey/convey"
	"net/http"
	"net/http/httptest"
	"testing"
)

func handler(ctx *gin.Context) {

}
func TestWithMetrics(t *testing.T) {
	convey.Convey("Testing metrics", t, func() {
		r := gin.New()
		r.Use(WithMetrics(nil))
		om := r.Group("v1/om")
		om.POST("loglevel/:level", handler)

		req, _ := http.NewRequest("POST", "/v1/om/loglevel/debug", nil)
		resp := httptest.NewRecorder()
		r.ServeHTTP(resp, req)
	})
}
