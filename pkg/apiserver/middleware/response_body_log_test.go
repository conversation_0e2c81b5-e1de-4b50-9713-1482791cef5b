package middleware

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"github.com/smartystreets/goconvey/convey"
	"net/http"
	"net/url"
	"testing"
)

func TestResponseBodyLogger(t *testing.T) {
	convey.Convey("ResponseBodyLogger", t, func() {
		writer := &fakeResponseWriter{status: 403}
		ctx := &gin.Context{
			Request: &http.Request{
				Header: http.Header{
					"X-Acs-Request-Id": []string{"reqId-test"},
				},
				URL: &url.URL{
					Path:     "/test",
					RawQuery: "a=bc",
				},
				Body: NewRequestBody("test"),
			},
			Writer: writer,
		}
		h := ResponseBodyLogger()
		h(ctx)
	})
}

func TestResponseBodyLoggerWriter(t *testing.T) {
	convey.Convey("ResponseBodyLogger", t, func() {
		convey.<PERSON>vey("Write", func() {
			w := &fakeResponseWriter{}
			buf := &bytes.Buffer{}
			writer := responseBodyLoggerWriter{
				ResponseWriter: w,
				body:           buf,
			}
			n, err := writer.Write([]byte("ut"))
			convey.So(err, convey.ShouldBeNil)
			convey.So(n, convey.ShouldEqual, 2)
			convey.So(buf.String(), convey.ShouldEqual, "ut")
		})

		convey.Convey("WriteString", func() {
			w := &fakeResponseWriter{}
			buf := &bytes.Buffer{}
			writer := responseBodyLoggerWriter{
				ResponseWriter: w,
				body:           buf,
			}
			n, err := writer.WriteString("ut")
			convey.So(err, convey.ShouldBeNil)
			convey.So(n, convey.ShouldEqual, 2)
			convey.So(buf.String(), convey.ShouldEqual, "ut")
		})
	})
}
