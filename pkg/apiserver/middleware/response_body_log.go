package middleware

import (
	"bytes"
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context"
	"net/http"
)

type responseBodyLoggerWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w responseBodyLoggerWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w responseBodyLoggerWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}

// ResponseBodyLogger 中间件，记录非200状态码的响应体
func ResponseBodyLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		blw := &responseBodyLoggerWriter{body: &bytes.Buffer{}, ResponseWriter: c.Writer}
		c.Writer = blw

		// 继续处理请求
		c.Next()
		l := context.GetLogger(c)

		logFn := l.Debug
		// 如果状态码不是200，提升至Error
		if c.Writer.Status() != http.StatusOK {
			logFn = l.Error
		}

		// 将响应体的内容输出
		responseBody := blw.body.String()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		if raw != "" {
			path = path + "?" + raw
		}
		logFn(path, "logger", "err-resp", "resp-content", responseBody)
	}
}
