package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/smartystreets/goconvey/convey"
	"io"
	"net/http"
	"net/url"
	"strings"
	"testing"
)

type requestBody struct {
	reader io.Reader
}

func NewRequestBody(s string) io.ReadCloser {
	return &requestBody{
		reader: strings.NewReader(s),
	}
}

func (r requestBody) Read(p []byte) (n int, err error) {
	return r.reader.Read(p)
}

func (r requestBody) Close() error {
	return nil
}

func TestRequestLogger(t *testing.T) {
	convey.Convey("RequestLogger", t, func() {
		writer := &fakeResponseWriter{status: 403}
		ctx := &gin.Context{
			Request: &http.Request{
				Header: http.Header{
					"X-Acs-Request-Id": []string{"reqId-test"},
				},
				URL: &url.URL{
					Path:     "/test",
					RawQuery: "a=bc",
				},
				Body: NewRequestBody("test"),
			},
			Writer: writer,
		}
		h := RequestLogger()
		h(ctx)
	})
}
