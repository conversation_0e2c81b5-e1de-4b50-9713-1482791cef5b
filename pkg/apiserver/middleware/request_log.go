package middleware

import (
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context"
	"io"
	"net/http"
)

// RequestLogger 中间件记录非200响应时的请求体和头部
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 保存请求体
		var requestBodyBytes []byte
		if c.Request.Body != nil {
			requestBodyBytes, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBodyBytes)) // 重置请求体
		}
		// 保存请求头
		requestHeaders := c.Request.Header
		l := context.GetLogger(c)
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery
		if raw != "" {
			path = path + "?" + raw
		}
		reqBody := ""
		if len(requestBodyBytes) > 0 {
			reqBody = string(requestBodyBytes)
		}
		headerBytes, _ := json.Marshal(requestHeaders)

		// 继续处理请求
		c.Next()

		logFn := l.Debug
		// 非200响应时记录请求体和头部
		if c.Writer.Status() != http.StatusOK {
			logFn = l.Error
		}
		logFn(path, "logger", "err-req", "req-body", reqBody, "req-headers", string(headerBytes))
	}
}
