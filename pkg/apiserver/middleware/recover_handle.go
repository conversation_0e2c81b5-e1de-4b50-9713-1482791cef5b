package middleware

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	comnhttp "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"log/slog"
)

type RecoveryWriter struct {
	Logger *slog.Logger
}

func (rw *RecoveryWriter) Write(p []byte) (n int, err error) {
	rw.Logger.Error("Recovery panic", "info", p)
	return len(p), nil
}

func HandleRecovery(c *gin.Context, err any) {
	if err != nil {
		ctx := base.NewContextFromRequest(c)
		if e, ok := err.(error); ok {
			comnerrors.AlertError(ctx, comnerrors.AlertScopeGinRoutes, []string{c.<PERSON>(), ctx.GetSource()}, "panic for gin", e)
		} else {
			comnerrors.AlertError(ctx, comnerrors.AlertScopeGinRoutes, []string{c.Full<PERSON>(), ctx.GetSource()}, "panic for gin", nil, "errObj", err)
		}
	}
	comnhttp.ServerFail(c, comnhttp.CodeServiceInternalError, "ServiceInternalError", nil)
	c.Abort()
}
