package router

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver/metrics"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	comnhttp "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	i18n "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/in18"
	"log/slog"
	"reflect"
	"runtime"
	"slices"
	"sync/atomic"
	"time"
)

const (
	// 慢调用的告警阈值（ms）
	slowActionThreshold = 3000
)

var (
	// 已知的慢接口，暂不做优化
	tolerableSlowActions = []string{}
)

type ApiRoute struct {
	baseUrl     string
	routerGroup gin.IRouter
}

func (a *ApiRoute) validate(method string, uri string, handler any) error {
	ht := reflect.TypeOf(handler)
	if ht.Kind() != reflect.Func {
		return errors.New("handler must be a func with prototype of \"func(base.Context, any, any, any) (any, error)\"")
	}

	if ht.NumIn() < 4 {
		return errors.New("handler must accept at least 4 params, which are base.Context, pathParams, queryParams, bodyParams")
	}

	bp := (*base.Context)(nil)
	bt := reflect.TypeOf(bp).Elem()
	if !ht.In(0).Implements(bt) {
		return errors.New("handler's first param is not base.Context")
	}

	if ht.In(1).Kind() == reflect.Struct {
		return errors.New("pathParams should not be a struct, use pointer instead, or use any to ignore")
	}

	if ht.In(2).Kind() == reflect.Struct {
		return errors.New("queryParams should not be a struct, use pointer instead, or use any to ignore")
	}

	if ht.In(3).Kind() == reflect.Struct {
		return errors.New("bodyParams should not be a struct, use pointer instead, or use any to ignore")
	}

	if ht.NumOut() != 2 {
		return errors.New("handler must accept at least 2 return objects, which are response, error")
	}

	ep := (*error)(nil)
	et := reflect.TypeOf(ep).Elem()
	if !ht.Out(1).Implements(et) {
		return errors.New("handler's second return object is not error")
	}
	if ht.Out(0).Kind() == reflect.Chan {
		if ht.Out(0).ChanDir() == reflect.RecvDir {
			return errors.New("handler's first return object is chan, but chan direction is recv")
		}
	}

	return nil
}

// RegisterApi 使用此方法注册一个api。
//
// handler 必须为一个函数，接受四个参数，依次为：
//   - base.Context
//   - pathParams，路径参数，必须为结构体指针，如果参数为空，写any
//   - queryParams，查询参数，必须为结构体指针，如果参数为空，写any
//   - bodyParams，body参数，必须为结构体指针，如果参数为空，写any
//
// handler 必须返回两个值，依次为：
//   - response，返回的的内容，可以为nil.
//   - 若response返回的是 chan xxx类型，其中xxx可以是任何类型，无类型要求。则会使用SSE方式返回数据，当chan关闭时，响应data: [DONE]
//     请勿返回只读chan，在异常情况下需要由服务端主动关闭chan，防止goroutine泄漏
//   - error，返回的错误
func (a *ApiRoute) RegisterApi(method string, uri string, handler any) error {
	if err := a.validate(method, uri, handler); err != nil {
		return err
	}

	ht := reflect.TypeOf(handler)
	hv := reflect.ValueOf(handler)
	slog.Info(fmt.Sprintf("registering %s %s -> %s", method, a.baseUrl+uri, runtime.FuncForPC(reflect.ValueOf(handler).Pointer()).Name()))

	var hasPathParams, hasQueryParams, hasBodyParams bool

	pt := ht.In(1)
	var pte reflect.Type
	if pt.Kind() == reflect.Pointer && pt.Elem().Kind() == reflect.Struct {
		hasPathParams = true
		pte = pt.Elem()
	}

	qt := ht.In(2)
	var qte reflect.Type
	if qt.Kind() == reflect.Pointer && qt.Elem().Kind() == reflect.Struct {
		hasQueryParams = true
		qte = qt.Elem()
	}

	bt := ht.In(3)
	var bte reflect.Type
	if bt.Kind() == reflect.Pointer {
		hasBodyParams = true
		bte = bt.Elem()
	}

	wrapper := func(ctx *gin.Context) {
		startTime := time.Now()
		baseCtx := base.NewContextFromRequest(ctx)
		var pathParams reflect.Value
		var queryParams reflect.Value
		var bodyParams reflect.Value

		// uri参数处理
		if hasPathParams {
			pathParams = reflect.New(pte)
			pi := pathParams.Interface()
			if err := ctx.BindUri(pi); err != nil {
				baseCtx.GetLogger().Error("fail to bind uri parameter", "err", fmt.Sprintf("%+v", err))
				errorToResponse(baseCtx, comnerrors.New(codes.ErrInvalidParameterFailToBindUriParameter))
				return
			}

			if v, ok := pi.(Validator); ok {
				if err := v.Validate(); err != nil {
					baseCtx.GetLogger().Error("path parameter invalid", "err", fmt.Sprintf("%+v", err))
					errorToResponse(baseCtx, err)
					return
				}
			}

		} else {
			pathParams = reflect.New(pt).Elem()
		}

		// query参数处理
		if hasQueryParams {
			queryParams = reflect.New(qte)
			qi := queryParams.Interface()

			if err := ctx.ShouldBindQuery(qi); err != nil {
				baseCtx.GetLogger().Error("fail to bind query", "err", fmt.Sprintf("%+v", err))
				errorToResponse(baseCtx, comnerrors.New(codes.ErrInvalidParameterFailToBindQuery))
				return
			}
			if v, ok := qi.(Validator); ok {
				if err := v.Validate(); err != nil {
					baseCtx.GetLogger().Error("query parameter invalid", "err", fmt.Sprintf("%+v", err))
					errorToResponse(baseCtx, err)
					return
				}
			}

		} else {
			queryParams = reflect.New(qt).Elem()
		}

		// body 参数处理
		if hasBodyParams {
			bodyParams = reflect.New(bte)
			bi := bodyParams.Interface()
			if err := ctx.ShouldBind(bi); err != nil {
				baseCtx.GetLogger().Error("fail to bind body", "err", fmt.Sprintf("%+v", err))
				errorToResponse(baseCtx, comnerrors.New(codes.ErrInvalidParameterFailToBindBody))
				return
			}
			if v, ok := bi.(Validator); ok {
				if err := v.Validate(); err != nil {
					baseCtx.GetLogger().Error("body parameter invalid", "err", fmt.Sprintf("%+v", err))
					errorToResponse(baseCtx, err)
					return
				}
			}
		} else {
			bodyParams = reflect.New(bt).Elem()
		}

		baseCtx.GetLogger().Debug("request params bind result", "path", pathParams.Interface(), "query", queryParams.Interface(), "body", bodyParams.Interface())
		ret := hv.Call([]reflect.Value{reflect.ValueOf(baseCtx), pathParams, queryParams, bodyParams})

		if ret[1].Interface() != nil {
			errorToResponse(baseCtx, (ret[1].Interface()).(error))
			return
		}
		obj := ret[0].Interface()
		v := reflect.ValueOf(obj)
		if v.Kind() == reflect.Chan {
			// 流式响应
			handleStreamResponse(baseCtx, ctx, v, startTime)
		} else {
			// 非流式响应
			baseCtx.GetLogger().Debug("service response", "result", obj)
			comnhttp.Ok(ctx, obj)
			// 如果接口正常返回，记录调用耗时
			recordPopActionCost(baseCtx, startTime)
		}
	}

	a.routerGroup.Handle(method, uri, wrapper)
	return nil
}

func (a *ApiRoute) MustRegister(method string, uri string, handler any) {
	if err := a.RegisterApi(method, uri, handler); err != nil {
		panic(err)
	}
}

func NewApiRouter(baseUrl string, router gin.IRouter) *ApiRoute {
	return &ApiRoute{
		baseUrl:     baseUrl,
		routerGroup: router.Group(baseUrl),
	}
}

type Validator interface {
	Validate() error
}

type causer interface {
	Cause() error
}

func errorToResponse(baseCtx base.Context, err error) {
	l := baseCtx.GetLogger()
	l.Error("error occurred", "err", fmt.Sprintf("%+v", err))

	for {
		c, ok := err.(causer)
		if !ok {
			break
		}
		err = c.Cause()
	}

	bizErr := comnerrors.AsBizError(err)
	ctx := baseCtx.GetGinContext()
	action := "-"
	if baseCtx.IsPopRequest() {
		action = baseCtx.GetAction()
	}

	// 已包装的业务错误处理
	if bizErr != nil {
		// 国际化
		errorMsg := getLocalizedMessage(baseCtx, bizErr)
		switch bizErr.ErrorType {
		case comnerrors.TypeRedirect:
			//国际化时会将错误参数拼到错误信息中，重定向场景不需要，直接使用原始的错误信息
			comnhttp.Redirect(ctx, bizErr.FullCode(), bizErr.ErrorMessage, nil)
		case comnerrors.TypeDatabaseError:
			if bizErr.ErrorCode == codes.ErrRecordNotFound.Code {
				metrics.ErrorCounter.WithLabelValues(action, "NotFound", bizErr.FullCode()).Inc()
				comnhttp.NotFound(ctx, bizErr.FullCode(), errorMsg, nil)
				return
			}

			// 数据库的未知错误，出现此日志需要澄清并整改
			l.Error("unrecognized error, should be clarified later", "fromDatabase", "true")
			metrics.ErrorCounter.WithLabelValues(action, "ServiceInternalError", "ServiceInternalError.UnknownError").Inc()
			comnhttp.ServerFail(ctx, "ServiceInternalError.UnknownError", i18n.LocalizeOrDefault(baseCtx.GetLocale(), "Error.ServiceInternalError.UnknownError", "UnknownError"), nil)
			return
		case comnerrors.TypeInvalidParameter:
			comnhttp.InvalidParameter(ctx, string(comnerrors.TypeInvalidParameter), errorMsg, nil)
		case comnerrors.TypeNotFound:
			comnhttp.NotFound(ctx, string(comnerrors.TypeNotFound), errorMsg, nil)
		case comnerrors.TypeConflict:
			comnhttp.Conflict(ctx, string(comnerrors.TypeConflict), errorMsg, nil)
		case comnerrors.TypeUnauthorized:
			// 简化Code 固定为 Unauthorized
			comnhttp.Unauthorized(ctx, string(comnerrors.TypeUnauthorized), errorMsg, nil)
		case comnerrors.TypeForbidden:
			// 简化Code固定为Forbidden
			comnhttp.Forbidden(ctx, string(comnerrors.TypeForbidden), errorMsg, nil)

		case comnerrors.TypeCloudProductInactive:
			comnhttp.CloudProductInactiveResponse(ctx, bizErr.FullCode(), errorMsg, nil)
		case comnerrors.TypeTooManyRequests:
			comnhttp.TooManyRequests(ctx, string(comnerrors.TypeTooManyRequests), errorMsg, nil)
		default:
			if bizErr.ErrorCode == codes.ErrUnknownError.Code {
				// 未知错误，出现此日志需要澄清并整改
				l.Error("unrecognized error, should be clarified later", "unknown", "true")
			}
			// 未分类的BizError 统一以500状态码返回
			comnhttp.ServerFail(ctx, bizErr.FullCode(), errorMsg, nil)
		}

		metrics.ErrorCounter.WithLabelValues(action, string(bizErr.ErrorType), bizErr.FullCode()).Inc()
		return
	}

	// 未知错误，出现此日志需要澄清并整改
	l.Error("unrecognized error, should be clarified later", "fromDatabase", "false")
	metrics.ErrorCounter.WithLabelValues(action, "ServiceInternalError", "ServiceInternalError.UnknownError").Inc()
	// 普通错误不得直接吐出，统一对外是ServerInternalError
	comnhttp.ServerFail(ctx, "ServiceInternalError.UnknownError", i18n.LocalizeOrDefault(baseCtx.GetLocale(), "Error.ServiceInternalError.UnknownError", "UnknownError"), nil)
}

func getLocalizedMessage(baseCtx base.Context, bizError *comnerrors.BizError) string {
	key := "Error." + bizError.FullCode()
	msg := i18n.LocalizeOrDefault(baseCtx.GetLocale(), key, bizError.ErrorMessage, bizError.Params...)
	return msg
}

func recordPopActionCost(baseCtx base.Context, startTime time.Time) {
	// 只记录pop请求的耗时
	if !baseCtx.IsPopRequest() {
		return
	}

	action := baseCtx.GetAction()
	cost := time.Now().Sub(startTime).Milliseconds()

	// 对慢调用记录日志，配合告警
	if cost > slowActionThreshold && !slices.Contains(tolerableSlowActions, action) {
		baseCtx.GetLogger().Error("slow action occurred", "action", action)
	}

	metrics.ActionCostHistogram.WithLabelValues(action).Observe(float64(cost))
}

// 用记录进程sse某一时刻请求数
var sseCounter int32

func handleStreamResponse(baseCtx base.Context, ctx *gin.Context, v reflect.Value, startTime time.Time) {
	if v.Kind() != reflect.Chan {
		return
	}
	metrics.SSEventGauge.Inc()
	defer metrics.SSEventGauge.Dec()
	count := atomic.AddInt32(&sseCounter, 1)
	defer atomic.AddInt32(&sseCounter, -1)

	// recover必须放在栈顶，防止panic击穿，导致后续defer不执行
	defer func() {
		if r := recover(); r != nil {
			// 只读chan 或者 已关闭的chan，再次关闭时，会发生panic
			baseCtx.GetLogger().Error("stream response panic", "err", r)
		}
	}()

	threshold := config.GetOrDefaultInt(config.KeySystemSSEventConcurrentLimit, 100)
	if count > int32(threshold) {
		baseCtx.GetLogger().Error("too many sse requests", "count", count, "threshold", threshold)
		// 过多SSE的场景下，主动拒绝
		v.Close()
		errorToResponse(baseCtx, comnerrors.New(codes.TooManySSERequests))
		return
	}

	for {
		select {
		case <-ctx.Request.Context().Done():
			// 请求取消的场景下尝试主动关闭chan，可能会发生panic，外层有保护不要紧
			v.Close()
			return
		default:
			val, ok := v.Recv()
			if !ok {
				// 以 data: [DONE] 作为SSE 终结事件，非SSE规范，但是在LLM流式场景下，是行业常见规约。
				// 后续流式常见在前后端交互上，可以以 data: [DONE] 结束，以此依据作为数据全部传输完成。
				comnhttp.SSEvent(ctx, "[DONE]")
				baseCtx.GetLogger().Debug("service stream response", "result", "[DONE]")
				// 记录调用耗时
				recordPopActionCost(baseCtx, startTime)
				return
			}
			realVal := val.Interface()
			baseCtx.GetLogger().Debug("service stream response", "result", realVal)
			comnhttp.SSEvent(ctx, realVal)
		}
	}
}
