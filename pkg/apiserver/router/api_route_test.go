package router

import (
	"bufio"
	"bytes"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/test/basemock"
	"log/slog"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"strconv"
	"strings"
	"testing"
	"time"
)

type pathParams struct {
	Id int `uri:"id"`
}

func (p *pathParams) Validate() error {
	if p.Id == 222 {
		//return errors.New("123")
		return errors.WithStack(comnerrors.New(codes.ErrInvalidParameter, "id", p.Id))
	}

	return nil
}

type queryParams struct {
	Query  string `form:"query"`
	Number int    `form:"number"`
}

type bodyParams struct {
	Body   string `json:"body,omitempty"`
	Number int    `form:"number"`
}

func handlerError1()                                         {}
func handlerError2(a, b, c, d string)                        {}
func handlerError3(a base.Context, b, c, d string)           {}
func handlerError4(a base.Context, b, c, d any) error        { return nil }
func handlerError5(a base.Context, b, c, d any) (error, any) { return nil, nil }

func handler1(a base.Context, b, c, d any) (any, error) { return nil, nil }

func handler2(a base.Context, path *pathParams, query *queryParams, body any) (any, error) {
	return strconv.Itoa(path.Id) + query.Query, nil
}

func handler3(a base.Context, path, query, body any) (any, error) {
	return nil, errors.New("error")
}

func handler4(a base.Context, path *pathParams, query *queryParams, body *bodyParams) (any, error) {
	return strconv.Itoa(path.Id) + query.Query + body.Body, nil
}

func TestValidate(t *testing.T) {
	r := gin.New()
	ar := &ApiRoute{routerGroup: r}

	// 异常
	assert.NotNil(t, ar.RegisterApi(http.MethodGet, "/", handlerError1))
	assert.NotNil(t, ar.RegisterApi(http.MethodGet, "/", handlerError2))
	assert.NotNil(t, ar.RegisterApi(http.MethodGet, "/", handlerError3))
	assert.NotNil(t, ar.RegisterApi(http.MethodGet, "/", handlerError4))
	assert.NotNil(t, ar.RegisterApi(http.MethodGet, "/", handlerError5))

	// 正常
	assert.Nil(t, ar.RegisterApi(http.MethodGet, "/", handler1))
}

func TestRegister(t *testing.T) {
	slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})))

	r := gin.New()
	ar := &ApiRoute{routerGroup: r}
	assert.Nil(t, ar.RegisterApi(http.MethodGet, "/:id", handler2))

	req, _ := http.NewRequest("GET", "/111?query=foo", nil)
	resp := httptest.NewRecorder()
	r.ServeHTTP(resp, req)

	assert.Equal(t, "{\"code\":\"Ok\",\"data\":\"111foo\"}", resp.Body.String())
	assert.Equal(t, http.StatusOK, resp.Code)

	// 参数验证
	req, _ = http.NewRequest("GET", "/222", nil)
	resp = httptest.NewRecorder()
	r.ServeHTTP(resp, req)
	assert.Equal(t, http.StatusBadRequest, resp.Code)
}

func TestErrorHandle(t *testing.T) {
	r := gin.New()
	ar := &ApiRoute{routerGroup: r}
	assert.Nil(t, ar.RegisterApi(http.MethodGet, "/error", handler3))

	req, _ := http.NewRequest("GET", "/error", nil)
	resp := httptest.NewRecorder()
	r.ServeHTTP(resp, req)

	assert.Equal(t, "{\"code\":\"ServiceInternalError.UnknownError\",\"message\":\"UnknownError\"}", resp.Body.String())
	assert.Equal(t, http.StatusInternalServerError, resp.Code)
}

func TestBody(t *testing.T) {
	slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})))
	r := gin.New()
	ar := &ApiRoute{routerGroup: r}
	assert.Nil(t, ar.RegisterApi(http.MethodPost, "/:id", handler4))

	req, _ := http.NewRequest(http.MethodPost, "/111?query=foo", strings.NewReader("{\"body\":\"bar\"}"))
	req.Header.Add("Content-Type", "application/json")

	resp := httptest.NewRecorder()
	r.ServeHTTP(resp, req)

	assert.Equal(t, "{\"code\":\"Ok\",\"data\":\"111foobar\"}", resp.Body.String())
	assert.Equal(t, http.StatusOK, resp.Code)
}

func TestPopAction(t *testing.T) {
	slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})))
	r := gin.New()
	ar := &ApiRoute{routerGroup: r}
	assert.Nil(t, ar.RegisterApi(http.MethodPost, "/:id", handler4))

	req, _ := http.NewRequest(http.MethodPost, "/111?query=foo", strings.NewReader("{\"body\":\"bar\"}"))
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("x-acs-product", "APIG")
	req.Header.Add("x-acs-api-name", "GetDomain")

	resp := httptest.NewRecorder()
	r.ServeHTTP(resp, req)

	assert.Equal(t, "{\"code\":\"Ok\",\"data\":\"111foobar\"}", resp.Body.String())
	assert.Equal(t, http.StatusOK, resp.Code)
}

func TestValidateErrorHandle(t *testing.T) {
	slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})))
	r := gin.New()
	ar := &ApiRoute{routerGroup: r}
	assert.Nil(t, ar.RegisterApi(http.MethodPost, "/:id", handler4))

	req, _ := http.NewRequest(http.MethodPost, "/222?query=foo", strings.NewReader("{\"body\":\"bar\"}"))
	req.Header.Add("Content-Type", "application/json")

	resp := httptest.NewRecorder()
	r.ServeHTTP(resp, req)

	assert.Equal(t, "{\"code\":\"InvalidParameter.WithValue\",\"message\":\"parameter id is invalid, value: 222.\"}", resp.Body.String())
	assert.Equal(t, http.StatusBadRequest, resp.Code)
}

func TestBindError(t *testing.T) {
	slog.SetDefault(slog.New(slog.NewJSONHandler(os.Stdout, &slog.HandlerOptions{Level: slog.LevelDebug})))
	r := gin.New()
	ar := &ApiRoute{routerGroup: r}
	assert.Nil(t, ar.RegisterApi(http.MethodPost, "/:id", handler4))
	convey.Convey("Test bind uri", t, func() {
		req, _ := http.NewRequest(http.MethodPost, "/abc", strings.NewReader("{\"body\":\"bar\"}"))
		req.Header.Add("Content-Type", "application/json")

		resp := httptest.NewRecorder()
		r.ServeHTTP(resp, req)

		convey.So(resp.Code, convey.ShouldEqual, http.StatusBadRequest)
		convey.So(resp.Body.String(), convey.ShouldEqual, "{\"code\":\"InvalidParameter.FailToBindUriParameter\",\"message\":\"fail to bind uri parameter.\"}")
	})

	convey.Convey("Test bind query", t, func() {
		req, _ := http.NewRequest(http.MethodPost, "/111?number=abc", strings.NewReader("{\"body\":\"bar\"}"))
		req.Header.Add("Content-Type", "application/json")

		resp := httptest.NewRecorder()
		r.ServeHTTP(resp, req)

		convey.So(resp.Code, convey.ShouldEqual, http.StatusBadRequest)
		convey.So(resp.Body.String(), convey.ShouldEqual, "{\"code\":\"InvalidParameter.FailToBindQuery\",\"message\":\"fail to bind query.\"}")
	})

	convey.Convey("Test bind body", t, func() {
		req, _ := http.NewRequest(http.MethodPost, "/111?number=111", strings.NewReader("{\"body\":111}"))
		req.Header.Add("Content-Type", "application/json")

		resp := httptest.NewRecorder()
		r.ServeHTTP(resp, req)

		convey.So(resp.Code, convey.ShouldEqual, http.StatusBadRequest)
		convey.So(resp.Body.String(), convey.ShouldEqual, "{\"code\":\"InvalidParameter.FailToBindBody\",\"message\":\"fail to bind body.\"}")
	})

}

type fakeResponseWriter struct {
	headers http.Header
	status  int
	buffer  bytes.Buffer
}

func (w *fakeResponseWriter) Header() http.Header {
	return w.headers
}

func (w *fakeResponseWriter) Write(i []byte) (int, error) {
	return w.buffer.Write(i)
}

func (w *fakeResponseWriter) WriteHeader(statusCode int) {
	w.status = statusCode
}

func (w *fakeResponseWriter) Hijack() (net.Conn, *bufio.ReadWriter, error) {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) Flush() {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) CloseNotify() <-chan bool {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) Status() int {
	return w.status
}

func (w *fakeResponseWriter) Size() int {
	return 0
}

func (w *fakeResponseWriter) WriteString(s string) (int, error) {
	return w.buffer.WriteString(s)
}

func (w *fakeResponseWriter) Written() bool {
	//TODO implement me
	panic("implement me")
}

func (w *fakeResponseWriter) WriteHeaderNow() {
	return
}

func (w *fakeResponseWriter) Pusher() http.Pusher {
	//TODO implement me
	panic("implement me")
}

func Test_handleStreamResponse(t *testing.T) {
	convey.Convey("handleStreamResponse", t, func() {
		req, err := http.NewRequest("POST", "http://127.0.0.1:8080", nil)
		convey.So(err, convey.ShouldBeNil)
		writer := &fakeResponseWriter{
			status:  200,
			headers: make(http.Header),
		}
		ginCtx := &gin.Context{
			Request: req,
			Writer:  writer,
		}
		ctx := basemock.NewMockContext("test")

		ch := make(chan int)
		go func() {
			defer close(ch)
			for i := 0; i < 5; i++ {
				ch <- i
			}
		}()
		val := reflect.ValueOf(ch)
		handleStreamResponse(ctx, ginCtx, val, time.Now())
		result := writer.buffer.String()
		expected := `event:data
data:0

event:data
data:1

event:data
data:2

event:data
data:3

event:data
data:4

event:data
data:[DONE]

`
		convey.So(result, convey.ShouldEqual, expected)
	})
}
