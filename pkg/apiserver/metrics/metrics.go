package metrics

import "github.com/prometheus/client_golang/prometheus"

var (
	labels = []string{"action", "type", "code"}

	ErrorCounter = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: "api",
			Name:      "error_counter",
			Help:      "Counter for error.",
		}, labels,
	)

	ActionCostHistogram = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: "api",
			Name:      "action_cost_histogram",
			Buckets:   []float64{10, 50, 100, 200, 500, 1000, 2000, 5000, 10000},
		}, []string{"action"})
	SSEventGauge = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: "api",
			Name:      "sse_gauge",
			Help:      "Gauge for sse.",
		})
)
