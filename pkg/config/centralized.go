package config

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/aes"
	"gorm.io/gorm"
	"log/slog"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"
)

var keyCache map[string][]*centralizedConfig
var reloadLock sync.RWMutex
var stopChan chan struct{}

type centralizedConfig struct {
	ID          uint32    `gorm:"primaryKey"`
	Group       string    `gorm:"size:128;index:idx_group;not null;comment:分组"`
	ConfigKey   string    `gorm:"size:128;index:idx_key;not null;comment:配置键"`
	ConfigValue string    `gorm:"type:text;comment:配置值"`
	EncryptInt  int       `gorm:"column:encrypt"`
	EnvTag      string    `gorm:"size:64;index:idx_env_tag;not null;comment:生效环境"`
	GmtCreate   time.Time `gorm:"autoCreateTime;comment:创建时间"`
	GmtModified time.Time `gorm:"autoUpdateTime;comment:更新时间"`
}

func (c *centralizedConfig) IsEncrypted() bool {
	return c.EncryptInt > 0
}

func (c *centralizedConfig) TableName() string {
	return "t_config"
}

func (c *centralizedConfig) encryptValue() error {
	if !c.IsEncrypted() {
		return nil
	}
	encryptString, err := aes.EncryptString(c.ConfigValue)
	if err != nil {
		return err
	}
	c.ConfigValue = encryptString
	return nil
}

func (c *centralizedConfig) decryptValue() error {
	if !c.IsEncrypted() {
		return nil
	}
	decryptString, err := aes.DecryptString(c.ConfigValue)
	if err != nil {
		return err
	}
	c.ConfigValue = decryptString
	return nil
}

func (c *centralizedConfig) AfterFind(tx *gorm.DB) error {
	return c.decryptValue()
}

func (c *centralizedConfig) BeforeCreate(tx *gorm.DB) error {
	return c.encryptValue()
}

func (c *centralizedConfig) AfterCreate(tx *gorm.DB) error {
	return c.decryptValue()
}

func (c *centralizedConfig) BeforeUpdate(tx *gorm.DB) error {
	return c.encryptValue()
}

func (c *centralizedConfig) AfterUpdate(tx *gorm.DB) error {
	return c.decryptValue()
}

func Init(ctx context.Context, db *gorm.DB, reloadInterval time.Duration) error {
	stopChan = make(chan struct{})
	if err := reload(db); err != nil {
		return err
	}

	startListening(ctx, db, reloadInterval)
	return nil
}

func reload(db *gorm.DB) error {
	defer func() {
		if err := recover(); err != nil {
			slog.Error("panic in reloading config", "error", err)
		}
	}()

	var r []centralizedConfig
	result := db.Find(&r)
	if result.Error != nil {
		slog.Error("fail to load config from database", "error", result.Error)
		return errors.WithStack(result.Error)
	}

	slog.Debug(fmt.Sprintf("got %d configs from database", result.RowsAffected))
	if result.RowsAffected == 0 {
		return nil
	}

	tkc := map[string][]*centralizedConfig{}
	tgc := map[string]map[string][]*centralizedConfig{}

	for i := range r {
		l1 := tkc[r[i].ConfigKey]
		if l1 == nil {
			tkc[r[i].ConfigKey] = []*centralizedConfig{&r[i]}
		} else {
			tkc[r[i].ConfigKey] = append(tkc[r[i].ConfigKey], &r[i])
		}

		g := tgc[r[i].Group]
		if g == nil {
			tgc[r[i].Group] = map[string][]*centralizedConfig{
				r[i].ConfigKey: {&r[i]},
			}
		} else if g[r[i].ConfigKey] == nil {
			g[r[i].ConfigKey] = []*centralizedConfig{&r[i]}
		} else {
			g[r[i].ConfigKey] = append(g[r[i].ConfigKey], &r[i])
		}
	}

	reloadLock.Lock()
	defer reloadLock.Unlock()
	keyCache = tkc
	slog.Debug("config reloaded")
	return nil
}

func startListening(ctx context.Context, db *gorm.DB, reloadInterval time.Duration) {
	go func() {
		t := time.Tick(reloadInterval)
		for {
			select {
			case <-t:
				err := reload(db)
				if err != nil {
					slog.Error("fail to reload config, waiting for next round")
				}
			case <-stopChan:
				slog.Info("stop reloading config")
				return
			case <-ctx.Done():
				slog.Info("context is done, stop reloading config")
				return
			}
		}
	}()
}

func StopListening() {
	close(stopChan)
}

func set(db *gorm.DB, group, key, value, envTag string, encrypt bool) error {
	encryptVal := 0
	if encrypt {
		encryptVal = 1
	}
	c := centralizedConfig{ConfigKey: key, EnvTag: envTag, EncryptInt: encryptVal}
	r := db.First(&c, c)
	if r.Error != nil && !errors.Is(r.Error, gorm.ErrRecordNotFound) {
		slog.Error("fail to get config", "key", key, "envTag", envTag)
		return errors.WithStack(r.Error)
	}

	c.Group = group
	c.ConfigValue = value
	r = db.Save(&c)
	if r.Error != nil {
		slog.Error("fail to update config", "config", c)
		return errors.WithStack(r.Error)
	}
	slog.Info("config updated", "config", c)
	return nil
}

func Set(db *gorm.DB, group, key, value, envTag string) error {
	return set(db, group, key, value, envTag, false)
}

// SetAutoEncrypt 自动加密Value
func SetAutoEncrypt(db *gorm.DB, group, key, value, envTag string) error {
	return set(db, group, key, value, envTag, true)
}

func Get(key string) string {
	envTag := GetEnvTag()
	if key == "" {
		return ""
	}

	l := keyCache[key]
	if len(l) == 0 {
		return ""
	}
	return getConfigForEnv(envTag, l)
}

// GetWithSiteType 区分国际站和国内站的配置项
func GetWithSiteType(site base.SiteType, key string) string {
	envTag := EnvAny
	if site == base.SiteTypeIntl {
		envTag = "intl"
	}
	l := keyCache[key]
	if len(l) == 0 {
		return ""
	}
	return getConfigForEnv(envTag, l)
}

func GetOrDefault(key string, defaultValue string) string {
	v := Get(key)
	if v == "" {
		return defaultValue
	}
	return v
}

func GetOrDefaultInt(key string, defaultValue int) int {
	v := Get(key)
	if v == "" {
		return defaultValue
	}
	i, err := strconv.Atoi(v)
	if err != nil {
		return defaultValue
	}
	return i
}

func GetDurationOrDefault(key string, unit time.Duration, defaultValue time.Duration) time.Duration {
	val := Get(key)
	if val == "" {
		return defaultValue
	}
	if v, e := strconv.ParseInt(val, 10, 64); e == nil {
		return unit * time.Duration(v)
	}
	return defaultValue
}

func GetOrDefaultInt64(key string, defaultValue int64) int64 {
	if Get(key) == "" {
		return defaultValue
	}
	if v, e := strconv.ParseInt(Get(key), 10, 64); e == nil {
		return v
	}
	return defaultValue
}

func GetBool(key string) bool {
	v := Get(key)
	if v == "TRUE" || v == "True" || v == "true" || v == "1" {
		return true
	}

	return false
}

func GetOrDefaultBool(key string, defaultValue bool) bool {
	v := Get(key)
	if strings.ToLower(v) == "true" || v == "1" {
		return true
	}
	if strings.ToLower(v) == "false" || v == "0" {
		return false
	}
	return defaultValue
}

func getConfigForEnv(envTag string, configs []*centralizedConfig) string {
	d := ""
	for i := range configs {
		if configs[i].EnvTag == envTag {
			return configs[i].ConfigValue
		} else if configs[i].EnvTag == EnvAny {
			d = configs[i].ConfigValue
		}
	}
	return d
}

func GetPopConnectionTimeoutOrDefault(key string) int {
	timeout, err := strconv.Atoi(Get(key))
	if err != nil {
		return DefaultPopConnectionTimeout
	}
	return timeout
}

func GetPopReadTimeoutOrDefault(key string) int {
	timeout, err := strconv.Atoi(Get(key))
	if err != nil {
		return DefaultPopReadTimeout
	}
	return timeout
}

func GetFromConfigAndEnv(key string) string {
	configVal := Get(key)
	//for test
	if os.Getenv("kubeone.env.config.enable") == "true" {
		if envVal := os.Getenv(key); len(envVal) > 0 {
			configVal = envVal
		}
	}
	return configVal
}
