package config

import (
	"fmt"
	"os"
)

const (
	EnvAny = "*"
)

const (
	GroupSystem              = "system"
	KeySystemRegionId        = "system.regionId"
	KeySystemBid             = "system.bid"
	KeyConsoleNetworkSegment = "system.console.network-segment"
	// KeySystemSSEventConcurrentLimit 系统处理SSE请求的并发数量，配置格式为数字，如：100
	KeySystemSSEventConcurrentLimit = "system.request.sse.concurrent.limit"
	// KeyUnifiedAlertBlockConfigPrefix 统一告警黑名单配置，结构体见alertBlockConfig
	KeyUnifiedAlertBlockConfigPrefix = "key.unified.alert.block.config."
	// 云效底座服务端地址 http://xxxx.com:8080
	KeySystemYunxiaoEndpoint      = "system.yunxiao.endpoint"
	KeySystemYunxiaoCdnPath       = "system.yunxiao.cdn-path"
	KeySystemAppIcon              = "system.app.icon"
	KeySystemAppTitle             = "system.app.title"
	KeySystemAppDescription       = "system.app.description"
	KeySystemEnv                  = "system.env"
	KeySystemAppId                = "system.app.id"
	KeySystemHomePageVersion      = "system.app.home-page-version"
	KeySystemTeamixUIVersion      = "system.app.teamix-ui-version"
	KeySystemNaviagtionSDKVersion = "system.navigation.sdk.version"
	KeySystemCodeLibVersion       = "system.app.codelib-version"
	// KeySystemDomainTlsEnabled 默认为true，表示启用tls
	KeySystemDomainTlsEnabled = "system.domain.tls-enabled"
	// KeySystemIngressPrefix ingress前缀，默认为空，专属版环境 应该是 /lingma-agents
	KeySystemIngressPrefix = "system.ingress.prefix"
	// KeySystemYunxiaoAuthorizationDisabled 默认为false，若为true表示关闭云效权限验证
	KeySystemYunxiaoAuthorizationDisabled = "system.yunxiao.authorization.disabled"
	// KeySystemFrontPageCSPEnabled 前端页面CSP是否启用，true/false
	KeySystemFrontPageCSPEnabled            = "system.front-page.csp-enabled"
	KeySystemFrontPagePermissionMockEnabled = "system.front-page.permission-mock-enabled"
)

const (
	DefaultPopConnectionTimeout = int(5000)
)

const (
	DefaultPopReadTimeout = int(15000)
)
const (
	GroupDb             = "db"
	DbInsertBatchSize   = "db.insert.batch-size"
	DbSaveBatchSize     = "db.save.batch-size"
	DBListAutoPageSize  = "db.list.auto.page-size"
	MnsEndpointIntranet = "mns.config.endpoint.intranet"
)

const (
	GroupGitlab = "gitlab"
	GitlabUrl   = "gitlab.url"
	GitlabToken = "gitlab.token"
)

const (
	GroupComponent                    = "component"
	KeyAgentRuntimeEndpoint           = "component.agent-runtime.endpoint"
	KeySandboxServerEndpoint          = "component.sandbox-server.endpoint"
	KeySandboxRequestTimeoutInSeconds = "component.sandbox-server.request.timeout-in-seconds"
)

const (
	GroupAgent                       = "agent"
	KeyAgentTaskFailureThreshold     = "agent.task.failure.threshold"
	KeyAgentLLMBaseUrl               = "agent.llm.base-url"
	KeyAgentLLMApiKey                = "agent.llm.api-key"
	DefaultAgentLLMBaseUrl           = "https://dashscope.aliyuncs.com/compatible-mode/v1"
	DefaultAgentTaskFailureThreshold = 10
	// prefixAgentGitlabIntegrationMode gitlab集成模式，可选值：AccessToken / AgentConnect
	prefixAgentGitlabIntegrationMode = "agent.gitlab.integration-mode."

	KeyAgentRecordAutoCleanEnabled = "agent.record.auto-clean.enabled"
	KeyAgentRecordKeepDays         = "agent.record.keep-days"
	KeyEventRecordKeepDays         = "agent.event-record.keep-days"
)

func GetGitlabIntegrationMode(uid string) string {
	key := fmt.Sprintf("%s%s", prefixAgentGitlabIntegrationMode, uid)
	return GetOrDefault(key, "AccessToken")
}

const (
	GroupSession               = "session"
	KeySessionAutoCloseEnabled = "session.auto-close.enabled"
	KeySessionIdleKeepDays     = "session.idle.keep-days"
)

const (
	GroupGithub                     = "github"
	keyGithubAppConfigPrefix        = "github.app.config."
	keyGithubAppClientIdPrefix      = "github.app.client-id."
	keyGithubAppClientSecretPrefix  = "github.app.client-secret."
	KeyGithubAppWebhookSecretPrefix = "github.app.webhook-secret."
	KeyGithubAppInstallationLimit   = "github.app.installation-limit"
	KeyGithubBindAccountMessage     = "github.bind-account.message"
)

const (
	GroupCodeup = "codeup"
	// 云效当前使用全局APP配置，即所有用户使用相同的APP ID
	// 这里预留了不同的APP ID入口，支持未来根据请求中的APP ID切换Secret发送请求
	KeyCodeupAppSecretPrefix = "codeup.app-secret."
	KeyCodeupAppTokenPrefix  = "codeup.app-token."
	KeyCodeupEndpointPrefix  = "codeup.endpoint."
	keyCodeupAppNamePrefix   = "codeup.app-name."
	// keyCodeupGlobalAppId 云效的全局APP ID
	keyCodeupGlobalAppId = "codeup.app-id.global"
)

// FormatCodeupAppId 如果APP ID为空，则返回全局APP ID。 该设计为云效保留切换APP的能力，后续若云效侧支持在请求中埋入APP ID，则以请求中的为准
func FormatCodeupAppId(appId string) string {
	if appId == "" {
		return Get(keyCodeupGlobalAppId)
	}
	return appId
}

// GetCodeupEndpoint 根据APP ID获取云效访问端点
func GetCodeupEndpoint(appId string) string {
	return GetOrDefault(fmt.Sprintf("%s%s", KeyCodeupEndpointPrefix, appId), "https://openapi-rdc.aliyuncs.com")
}

// GetCodeupAppSecret 获取云效AppSecret
func GetCodeupAppSecret(appId string) string {
	return Get(fmt.Sprintf("%s%s", KeyCodeupAppSecretPrefix, appId))
}

// GetCodeupAppToken
// Depreacated 应该改造成从云效底座获取
func GetCodeupAppToken(appId string) string {
	return Get(fmt.Sprintf("%s%s", KeyCodeupAppTokenPrefix, appId))
}

func GetCodeupAppName(appId string) string {
	return Get(fmt.Sprintf("%s%s", keyCodeupAppNamePrefix, appId))
}

const (
	GroupCodeaone           = "codeaone"
	KeyCodeaonePrivateToken = "codeaone.private-token"
	KeyCodeaoneEndpoint     = "codeaone.endpoint"
)

const (
	GroupRam                      = "ram"
	KeyRamOAuthAppId              = "ram.oauth.app-id"
	KeyRamOAuthClientIdPrefix     = "ram.oauth.client-id."
	KeyRamOAuthClientSecretPrefix = "ram.oauth.client-secret."
)

func GetGithubAppConfig(appId int64) string {
	return Get(fmt.Sprintf("%s%d", keyGithubAppConfigPrefix, appId))
}

func GetGithubAppClientId(appId int64) string {
	return Get(fmt.Sprintf("%s%d", keyGithubAppClientIdPrefix, appId))
}

func GetGithubAppClientSecret(appId int64) string {
	return Get(fmt.Sprintf("%s%d", keyGithubAppClientSecretPrefix, appId))
}

func GetGithubAppWebhookSecret(appId int64) string {
	return Get(fmt.Sprintf("%s%d", KeyGithubAppWebhookSecretPrefix, appId))
}

func GetRamOAuthClientId(appId string) string {
	return Get(fmt.Sprintf("%s%s", KeyRamOAuthClientIdPrefix, appId))
}

func GetRamOAuthClientSecret(appId string) string {
	return Get(fmt.Sprintf("%s%s", KeyRamOAuthClientSecretPrefix, appId))
}

const (
	GroupWebhook = "webhook"
	// KeyWebhookExternalHandlers 外部webhook处理器，格式为：["http://handler1/path/to/post", "http://handler2/path/to/post"]
	KeyWebhookExternalHandlers = "webhook.external-handlers"
)

const (
	GroupCodeReview                  = "code-review"
	KeyCodeReviewCommentReplyContent = "code-review.comment.reply-content"
	KeyCodeReviewAbortMessage        = "code-review.abort-message"
	// KeyCodeReviewMetricScanStopAfterCreationInDays 这个CR创建之后每天会扫描指标数据，超过X天以后 不再扫描
	KeyCodeReviewMetricScanStopAfterCreationInDays = "code-review.metric.scan-stop-after-creation-in-days"
)

func GetSystemDomain() string {
	host := os.Getenv("SYSTEM_DOMAIN")
	if host == "" {
		host = "localhost"
	}
	return host
}

func GetYunxiaoSecretKey() string {
	return os.Getenv("YUNXIAO_SECRET_KEY")
}

func GetSystemEndpoint() string {
	host := GetSystemDomain()
	schema := "http"
	if GetOrDefaultBool(KeySystemDomainTlsEnabled, true) {
		schema = "https"
	}
	prefix := Get(KeySystemIngressPrefix)

	url := fmt.Sprintf("%s://%s%s", schema, host, prefix)
	return url
}
