package config

import "os"

type RedisConfig struct {
	Host     string
	Port     uint32
	Password string
	DB       int
}

type DatabaseConfig struct {
	Host                   string
	Port                   uint32
	User                   string
	Password               string
	Driver                 string
	Name                   string
	MaxIdleConns           *int
	MaxOpenConns           *int
	ConnMaxLifetimeSeconds *int64
}

type HttpServerConfig struct {
	Address        string
	InnerAddress   string
	MetricsAddress string
	Name           string
	AdminAddress   string
	PprofAddress   string
}

type Config struct {
	Redis    RedisConfig
	Database DatabaseConfig
	Server   HttpServerConfig
}

func GetEnvTag() string {
	if os.Getenv("ENV_TAG") == "" {
		return EnvAny
	}

	return os.Getenv("ENV_TAG")
}
