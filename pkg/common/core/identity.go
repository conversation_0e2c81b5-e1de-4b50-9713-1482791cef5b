package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type IdentityService interface {
	RegistrableService

	GetIdentity(ctx base.Context, identityId string) (*identity.IdentityInfo, error)
	GetIdentityByToken(ctx base.Context, token string) (*identity.IdentityInfo, error)
	CreateIdentity(ctx base.Context, req *identity.CreateIdentityRequest) (*identity.IdentityInfo, error)
	DeleteIdentity(ctx base.Context, identityId string) error
	UpdateIdentity(ctx base.Context, identityId string, req *identity.UpdateIdentityRequest) error
	ListIdentities(ctx base.Context, options *identity.ListIdentityOptions) (*page.PagedItems[identity.IdentityInfo], error)

	GetIdentityByGithubInstallationId(ctx base.Context, installationId int64) (*identity.IdentityInfo, error)
	GetAiDeveloperIdentityByLoginUserId(ctx base.Context) (*identity.IdentityInfo, error)
	UpdateIdentityByGithubInstallationId(ctx base.Context, installationId int64, req *identity.UpdateIdentityRequest) error

	GetIdentityByCodeupOrganizationIdAndAppId(ctx base.Context, orgId string, appId string) (*identity.IdentityInfo, error)

	GetGitToken(ctx base.Context, identityId *string, idInfo *identity.IdentityInfo) (string, error)
}
