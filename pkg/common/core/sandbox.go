package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/sandbox"
)

type SandboxService interface {
	RegistrableService

	CreateSandbox(ctx base.Context, request *sandbox.CreateSandboxRequest) (*sandbox.SandboxInfo, error)
	DeleteSandbox(ctx base.Context, sandboxId string) error
	DeleteSandboxForSession(ctx base.Context, sessionId string) error
	CreateOrLoadSandbox(ctx base.Context, sessionId string, sandboxConfig *sandbox.Config) (*sandbox.SandboxInfo, error)
}
