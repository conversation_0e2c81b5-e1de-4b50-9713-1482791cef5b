package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	sdk "gitlab.com/gitlab-org/api/client-go"
)

type AgentConnectService interface {
	RegistrableService
	GitlabAgentConnectInterface
	ChatAgentConnectInterface
	// Serve 连接一个 Agent Client，启动监听
	Serve(ctx base.Context, conn *http.SafeConn) error
	// Detach 断联 Agent Client，停止监听
	Detach(ctx base.Context, agentId string) error
}

type GitlabAgentConnectInterface interface {
	GetMergeRequest(ctx base.Context, sessionId, projectId, mrId string) (*sdk.MergeRequest, error)
	GetMergeRequestChanges(ctx base.Context, sessionId, projectId, mrId string) (*sdk.MergeRequest, error)
	AddMergeRequestDiscussionNote(ctx base.Context, sessionId, projectId, mrId, discussionId, content string) (*sdk.Note, error)
	CreateMergeRequestDiscussion(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.CreateMergeRequestCommentOpts) (*sdk.Discussion, error)
	ListMergeRequestComments(ctx base.Context, sessionId, projectId, mrId string, request *codeplatform.ListMergeRequestCommentsRequest) (*sdk.Discussion, error)
	ListMergeRequestAllComments(ctx base.Context, sessionId, projectId, mrId string) ([]*sdk.Discussion, error)
	GetCommitDiff(ctx base.Context, sessionId, projectId, sha string) ([]*sdk.Diff, error)
	ListMergeRequestCommits(ctx base.Context, sessionId, projectId, mrId string) ([]*sdk.Commit, error)
	ResolveMergeRequestDiscussion(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.ResolveMergeRequestCommentOpts) error
	GetRepositoryFile(ctx base.Context, sessionId, projectId string, opts *codeplatform.GetRepositoryFileOpts) (*sdk.File, error)
	CreateMergeRequestNote(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.CreateMergeRequestCommentOpts) (*sdk.Note, error)
	GetMergeRequestNote(ctx base.Context, sessionId, projectId, mrId, noteId string) (*sdk.Note, error)
	UpdateMergeRequestNote(ctx base.Context, sessionId, projectId, mrId string, opts *codeplatform.UpdateMergeRequestCommentOpts) (*sdk.Note, error)
	UpdateMergeRequestDiscussionNote(ctx base.Context, sessionId, projectId, mergeRequestId, discussionId string, opts *codeplatform.UpdateMergeRequestCommentOpts) (*sdk.Note, error)
	CreateDraftNotes(ctx base.Context, sessionId, projectId, mergeRequestId string, draftNotes []codeplatform.ReviewComment) error
	GetVersion(ctx base.Context, sessionId string) (string, error)
	PublishAllDraftNotes(ctx base.Context, sessionId, projectId, mergeRequestId string) error
	ListNotes(ctx base.Context, sessionId, projectId, mergeRequestId string) ([]*sdk.Note, error)
}

type SessionAgentConnectInterface interface {
	// CreateSession 创建新会话
	CreateSession(ctx base.Context, conn *http.SafeConn, message agent_connect.Message)
	// RestoreSession 恢复会话连接
	RestoreSession(ctx base.Context, conn *http.SafeConn, message agent_connect.Message)
	// ListSessions 查询会话列表
	ListSessions(ctx base.Context, conn *http.SafeConn, message agent_connect.Message)
}

type ChatAgentConnectInterface interface {
	// Chat 为提供给客户端的对话接口，持续向agent投递，允许taskinfo并行，控制逻辑由前端及agent约束
	Chat(ctx base.Context, conn *http.SafeConn, message agent_connect.Message)
	// Callback 为提供给 Agent Runtime 的回调接口
	Callback(ctx base.Context, conn *http.SafeConn, message agent_connect.Message) error
}
