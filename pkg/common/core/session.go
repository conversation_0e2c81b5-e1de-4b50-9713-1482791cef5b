package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/overview"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
)

type SessionService interface {
	RegistrableService
	// Deprecated
	CreateOrLoadForCodeReview(ctx base.Context, identityId string, mergeRequestId string, projectId string) (*session.Info, error)
	GetGitlabCodeReviewSession(ctx base.Context, identityId string, mergeRequestId string, projectId string) (*session.Info, error)
	UpdateSession(ctx base.Context, sessionId string, req *session.UpdateSessionRequest) error
	GetSession(ctx base.Context, sessionId string) (*session.Info, error)
	CreateOrLoadSession(ctx base.Context, req *session.CreateSessionRequest) (*session.Info, error)
	ListSessions(ctx base.Context, req *session.ListSessionsRequest) (*page.PagedItems[*session.Info], error)
	ListAgentSessions(ctx base.Context, req *session.ListAgentSessionsRequest) (*page.PagedItems[*session.AgentSessionInfo], error)

	ScanMergeRequestMetrics(ctx base.Context, sessionId string, ignoreClosed bool) error
	RecordMergeRequestInfo(ctx base.Context, sessionId string, repoId string, info *session.MergeRequestProperty) error

	// GetCodeReviewProperty 获取当前会话的代码评审配置，如果没有配置，则会返回nil
	GetCodeReviewProperty(ctx base.Context, sessionId string) (*agent.RepositoryAgentRule, error)
	// ProcessCodeReviewEligibility 检查当前会话是否满足代码评审准入条件，若不满足则进行评论并返回false，若满足则返回true
	ProcessCodeReviewEligibility(ctx base.Context, sessionId *string, sessionInfo *session.Info, rule *agent.CodeReviewProperty) (bool, error)

	GetMergeRequestMetrics(ctx base.Context) ([]overview.MetricInfo, error)
	RecordExportAgentSession(ctx base.Context, req *session.ExportAgentSessionsRequest)
	ListAgentSessionExportHistory(ctx base.Context, req *session.ListAgentSessionExportHistoryRequest) (*page.PagedItems[*session.ExportAgentSessionOperation], error)

	// GetSessionTokenUsage 查询当前会话的token使用数据
	GetSessionTokenUsage(ctx base.Context, sessionId string) (*session.GetSessionTokenUsageResponse, error)
}
