package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/repository"
)

type RepositoryService interface {
	RegistrableService
	// EnsureRepository 根据UID、Source和RepoUrl来唯一定位仓库记录，若存在则更新LastReviewTime，若不存在则创建
	EnsureRepository(ctx base.Context, source identity.Source, repoName string, repoUrl string) (*repository.RepositoryInfo, error)
	GetRepository(ctx base.Context, repoId string) (*repository.RepositoryInfo, error)
	GetRepositoryByUrl(ctx base.Context, repoUrl string) (*repository.RepositoryInfo, error)
	UpdateRepository(ctx base.Context, repositoryId string, req *repository.UpdateRepositoryRequest) error
	ListRepositories(ctx base.Context, options *repository.ListRepositoryOptions) (*page.PagedItems[*repository.RepositoryInfo], error)
}
