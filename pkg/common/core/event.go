package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type EventService interface {
	RegistrableService

	CollectEvent(ctx base.Context, event *event.Event) error
	ListEvents(ctx base.Context, options *event.ListEventsOptions) (*page.PagedItems[event.Event], error)
	Audit(ctx base.Context, event *event.AuditEvent)
}
