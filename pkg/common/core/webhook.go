package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
)

type WebhookService interface {
	RegistrableService

	GitlabWebhook(ctx base.Context, token string, data map[string]any) (string, error)
	GithubWebhook(ctx base.Context, event webhook.GithubEvent, data map[string]any) error
	CodeupWebhook(ctx base.Context, data map[string]any) error
	CodeaoneWebhook(ctx base.Context, token string, data map[string]any) error
}
