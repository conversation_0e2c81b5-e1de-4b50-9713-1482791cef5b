package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type AgentService interface {
	RegistrableService

	// AgentClassService agentClass service
	AgentClassService
	AgentLifeCycleService
	AgentSnapshotService
}

type ListAgentClassOptions struct {
	page.PagesParams
	Type *agent.Type
}
type AgentClassService interface {
	// CreateAgentClass 创建agentClass
	CreateAgentClass(ctx base.Context, agentClass *agent.Class) error

	// ListAgentClasses 获取agentClass列表
	ListAgentClasses(ctx base.Context, options *ListAgentClassOptions) (*page.PagedItems[agent.Class], error)
}

type AgentLifeCycleService interface {
	// CreateAgent 创建agent
	CreateAgent(ctx base.Context, agent *agent.CreateAgentRequest) (*agent.AgentInfo, error)

	// DeleteAgent 删除agent
	DeleteAgent(ctx base.Context, agentId string) error

	// UpdateAgent 更新agent
	UpdateAgent(ctx base.Context, agentId string, agent *agent.UpdateAgentRequest) error

	// GetAgent 获取agent
	GetAgent(ctx base.Context, agentId string) (*agent.AgentInfo, error)

	// ListAgents 获取agent列表
	ListAgents(ctx base.Context, options *agent.ListAgentOptions) (*page.PagedItems[*agent.AgentInfo], error)

	// ToggleAgent 开/关智能体
	ToggleAgent(ctx base.Context, agentId string, enable bool) error

	PatchAgent(ctx base.Context, agentId string, req *agent.PatchAgentRequest) error

	ListAgentTasks(ctx base.Context, options *agent.ListAgentTaskOptions) (*page.PagedItems[*agent.AgentTaskInfo], error)

	// Step 运行agent
	Step(ctx base.Context, agentId string, sessionId string, taskConfig *agentruntime.TaskConfig) error

	// ShouldSkip 是否跳过本次 Agent 运行
	ShouldSkip(ctx base.Context, agentId, sessionId string) (bool, *agent.SkipReason, error)

	ListCodeReviewTools(ctx base.Context, agentId string, req *agent.ListToolDescriptionsRequest) (*page.PagedItems[*agent.CodeReviewTool], error)
}

type AgentSnapshotService interface {
	ListSnapshots(ctx base.Context, options *agent.ListSnapshotOptions) (*page.PagedItems[agent.Snapshot], error)
	CreateOrUpdateSnapshot(ctx base.Context, agentId string, sessionId string, data map[string]any) error
}
