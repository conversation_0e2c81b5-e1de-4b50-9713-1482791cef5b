package core

import (
	agent_runtime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
)

type AgentRuntimeService interface {
	RegistrableService

	StepSession(ctx base.Context, sessionId string, req *agent_runtime.StepSessionRequest) error
	AbortSession(ctx base.Context, sessionId string, options *agent_runtime.AbortSessionOptions) error
	CleanTaskInfo(ctx base.Context, sessionId string) error
}
