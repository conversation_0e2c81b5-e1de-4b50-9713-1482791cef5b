package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
)

type CallbackService interface {
	RegistrableService

	GithubOAuthCallback(ctx base.Context, appId int64, code string, installationId int64, setupAction string, state string) (string, error)
	RamOAuthCallback(ctx base.Context, appId string, code string, state string, errorMessage string) (string, error)
	GetUserIdByGithubInstallationId(ctx base.Context, installationId int64) (*string, error)
	GetValidInstallationCount(ctx base.Context) (int64, error)
	DeleteInstallationBindingIfExists(ctx base.Context, installationId int64) error
}
