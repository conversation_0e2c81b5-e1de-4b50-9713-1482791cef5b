package core

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/user"
)

type UserService interface {
	RegistrableService
	// EnsureUser 如不存在用户，则将用户保存到数据库
	EnsureUser(ctx base.Context, userId string) error
	GetUserInfoById(ctx base.Context, userId string) (*user.UserInfo, error)

	SaveUserIfNotExist(ctx base.Context, user *user.User) error
}
