package core

import (
	"context"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"sync"

	"github.com/prometheus/client_golang/prometheus"
)

var coreOnce sync.Once
var core Core

func InitCore(coreService Core) {
	coreOnce.Do(func() {
		setCore(coreService)
	})
}

func setCore(service Core) {
	core = service
}

func GetCoreService() Core {
	return core
}

type ManagedService interface {
	// Start 启动服务，并行启动，由服务自身处理依赖关系，服务中启动的所有goroutine的生命周期需要遵循此上下文
	Start(ctx context.Context) error
	// Started 服务是否已经完成启动
	Started() <-chan struct{}
	// IsReady 服务是否已就绪
	IsReady() bool
	// Stop 停止服务，实现优雅下线，被顺序调用
	Stop()
	// Metrics 服务相关的数据汇报
	Metrics() []prometheus.Collector
}

type Core interface {
	ManagedService
	GetConfig() config.Config
	ErrorChan() chan error

	GetAgentService() AgentService
	GetCodePlatformService() CodePlatformService
	GetWebhookService() WebhookService
	GetSessionService() SessionService
	GetAgentRuntimeService() AgentRuntimeService
	GetSandboxService() SandboxService
	GetIdentityService() IdentityService
	GetEventService() EventService
	GetCronjobService() CronjobService
	GetCallbackService() CallbackService
	GetAgentConnectService() AgentConnectService
	GetStatsService() StatsService
	GetRepositoryService() RepositoryService
	GetFeatureService() FeatureService
	GetOverviewService() OverviewService
	GetAuthorizationService() AuthorizationService
	GetUserService() UserService
}

// RegistrableService 被注册到Core的服务需要实现这个接口
type RegistrableService interface {
	ManagedService
	// GetName 获取服务标识
	GetName() string
	// Register 注册服务
	Register(Core)
}
