package component

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	http2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/sandbox"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"net/url"
	"time"
)

func CreateSandbox(ctx base.Context, request *sandbox.CreateSandboxRequest) (*sandbox.SandboxInfo, error) {
	endpoint := config.Get(config.KeySandboxServerEndpoint)
	fullUrl, err := url.JoinPath(endpoint, "/v1/admin/sandboxes")
	if err != nil {
		return nil, errors.WithStack(err)
	}
	reqBodyBytes, err := json.Marshal(request)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	timeout := config.GetDurationOrDefault(config.KeySandboxRequestTimeoutInSeconds, time.Second, 15*time.Second)
	resp, err := http2.InvokeHttp[sandbox.CreateSandboxResponse](ctx, &http2.InvokeHttpRequest{
		Method: "POST",
		Url:    fullUrl,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body:    reqBodyBytes,
		Timeout: timeout,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp.Data.ServerEndpoint == "" {
		ctx.GetLogger().Error("create sandbox runtime failed, due to response empty endpoint")
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	return resp.Data, nil
}

func DeleteSandbox(ctx base.Context, sandboxId string) error {
	endpoint := config.Get(config.KeySandboxServerEndpoint)
	fullUrl, err := url.JoinPath(endpoint, fmt.Sprintf("/v1/admin/sandboxes/%s", sandboxId))
	if err != nil {
		return errors.WithStack(err)
	}
	_, err = http2.InvokeHttp[struct{}](ctx, &http2.InvokeHttpRequest{
		Method: "DELETE",
		Url:    fullUrl,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

// GetAvailableSandbox 获取可用的沙箱服务，若没有可用的沙箱服务，则返回nil，nil
func GetAvailableSandbox(ctx base.Context, sessionId string) (*sandbox.SandboxInfo, error) {
	endpoint := config.Get(config.KeySandboxServerEndpoint)
	fullUrl, err := url.JoinPath(endpoint, "/v1/admin/sandboxes")
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp, err := http2.InvokeHttp[sandbox.GetAvailableSandboxResponse](ctx, &http2.InvokeHttpRequest{
		Method: "GET",
		Url:    fullUrl,
		Query:  map[string]string{"sessionId": sessionId},
	})
	if err != nil {
		if commonerrors.Is(err, codes.ErrExternalResourceNotFound) {
			return nil, nil
		}
		return nil, errors.WithStack(err)
	}
	if resp.Data.ServerEndpoint == "" {
		ctx.GetLogger().Error("get available sandbox runtime failed, due to response empty endpoint")
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	return resp.Data, nil
}
