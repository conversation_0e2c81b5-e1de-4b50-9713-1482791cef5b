package component

import (
	"encoding/json"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	http2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/webhook"
)

func TriggerWebhook(ctx base.Context, uri string, identityId string, source string, event string, payload map[string]any) error {
	data := map[string]any{
		"source":     source,
		"identityId": identityId,
		"event":      event,
		"payload":    payload,
	}
	bodyBytes, err := json.Marshal(data)
	if err != nil {
		ctx.GetLogger().Error("marshal webhook data failed", "data", data, "err", err)
		return errors.WithStack(err)
	}

	if resp, err := http2.InvokeHttp[webhook.ExternalHandlerResponse](ctx, &http2.InvokeHttpRequest{
		Method:  "POST",
		Url:     uri,
		Headers: map[string]string{"Content-Type": "application/json"},
		Body:    bodyBytes,
	}); err != nil {
		ctx.GetLogger().Error("trigger external webhook failed", "uri", uri, "data", data, "resp", resp, "err", err)
		return errors.WithStack(err)
	}
	return nil
}
