package component

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/callback"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
)

func GetUserInfoByOauth(ctx base.Context, headers map[string]string, url string) (*callback.UserInfoResponse, error) {
	userInfo, err := http.InvokeHttp[callback.UserInfoResponse](ctx, &http.InvokeHttpRequest{
		Method:  "GET",
		Url:     url,
		Headers: headers,
	})
	if err != nil {
		ctx.GetLogger().Error("failed to get user info")
		return nil, errors.WithStack(err)
	}
	return userInfo, nil
}
