package component

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	agent_runtime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	http2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"net/url"
)

func AgentRuntimeStepSession(ctx base.Context, sessionId string, request *agent_runtime.StepSessionRequest) (*agent_runtime.StepSessionResponse, error) {
	endpoint := config.Get(config.KeyAgentRuntimeEndpoint)
	fullUrl, err := url.JoinPath(endpoint, fmt.Sprintf("/v1/agent-runtime/sessions/%s/step", sessionId))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	reqBodyBytes, err := json.Marshal(request)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp, err := http2.InvokeHttp[agent_runtime.StepSessionResponse](ctx, &http2.InvokeHttpRequest{
		Method:  "POST",
		Url:     fullUrl,
		Headers: map[string]string{"Content-Type": "application/json"},
		Body:    reqBodyBytes,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return resp, nil
}

func AgentRuntimeAbortSession(ctx base.Context, abortUrl string, req *agent_runtime.AbortSessionRequest) error {
	var reqBodyBytes []byte
	if req != nil {
		if marshal, err := json.Marshal(req); err != nil {
			return errors.WithStack(err)
		} else {
			reqBodyBytes = marshal
		}
	}
	_, err := http2.InvokeHttp[agent_runtime.StepSessionResponse](ctx, &http2.InvokeHttpRequest{
		Method:  "POST",
		Url:     abortUrl,
		Headers: map[string]string{"Content-Type": "application/json"},
		Body:    reqBodyBytes,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
