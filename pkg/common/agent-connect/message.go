package agent_connect

import (
	"encoding/json"
	"fmt"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"strconv"
	"strings"
)

type Message interface {
	ToString() string
	GetType() MessageType
	GetSessionId() string
	GetRequestId() string
	GetSerial() int
	GetPayload() []byte
	SetSessionId(sessionId string)
}

// BaseMessage 基础消息格式
// {MessageType}|{RequestId}|{SessionId}|{Serial}|{Payload}
//
// {MessageType}: 消息类型
// {RequestId}: 请求ID，对于请求无关的消息，该字段为空
// {SessionId}: 会话ID，对于会话无关类的消息，如 NewSession，该字段为空
// {Serial}: 该条消息为对应请求的第几条消息，用流式输入用来保序，如果是该请求的最后一条消息，或者仅有一条消息，该字段为“0”，否则从1开始
// {Payload}: 消息内容，通常为JSON格式
// todo 增加时间戳，超时时间等控制字段
type BaseMessage struct {
	Type      MessageType `json:"type,omitempty"`
	RequestId string      `json:"requestId,omitempty"`
	SessionId string      `json:"sessionId,omitempty"`
	Serial    int         `json:"serial,omitempty"`
	Payload   []byte      `json:"payload,omitempty"`
}

func (m *BaseMessage) ToString() string {
	return fmt.Sprintf("%v|%s|%s|%d|%s", m.Type, m.RequestId, m.SessionId, m.Serial, string(m.Payload))
}

func (m *BaseMessage) GetType() MessageType {
	return m.Type
}

func (m *BaseMessage) GetSessionId() string {
	return m.SessionId
}

func (m *BaseMessage) GetRequestId() string {
	return m.RequestId
}

func (m *BaseMessage) GetSerial() int {
	return m.Serial
}

func (m *BaseMessage) GetPayload() []byte {
	return m.Payload
}

func (m *BaseMessage) SetSessionId(sessionId string) {
	m.SessionId = sessionId
}

func ParseMessage(rawMsg []byte) (Message, error) {
	sp := strings.SplitN(string(rawMsg), "|", 5)
	if len(sp) < 5 {
		return nil, errors.New(codes.ErrMalformedClientMessage)
	}

	s, err := strconv.Atoi(sp[3])
	if err != nil {
		return nil, errors.New(codes.ErrMalformedClientMessage)
	}

	return &BaseMessage{
		Type:      MessageType(sp[0]),
		RequestId: sp[1],
		SessionId: sp[2],
		Serial:    s,
		Payload:   []byte(sp[4]),
	}, nil
}

func NewClientCommonError(errMsg string) Message {
	p, _ := json.Marshal(BaseResponse{
		Success:      false,
		ErrorMessage: errMsg,
	})
	return &BaseMessage{
		Type:      ClientError,
		RequestId: "",
		SessionId: "",
		Serial:    0,
		Payload:   p,
	}
}

func NewClientRequestError(requestId, errMsg string) Message {
	p, _ := json.Marshal(BaseResponse{
		Success:      false,
		ErrorMessage: errMsg,
	})
	return &BaseMessage{
		Type:      ClientError,
		RequestId: requestId,
		SessionId: "",
		Serial:    0,
		Payload:   p,
	}
}
