package agent_connect

type MessageType string

// 通用消息类型
var (
	ClientError MessageType = "ClientError"
	ServerError MessageType = "ServerError"
	Connection  MessageType = "Connection"
)

// 会话管理相关消息类型
var (
	RestoreSession MessageType = "RestoreSession"
	CreateSession  MessageType = "CreateSession"
	ListSessions   MessageType = "ListSessions"
)

// Agent 对话相关消息类型
var (
	Chat           MessageType = "Chat"
	SessionControl MessageType = "SessionControl"
)

// Gitlab 相关消息类型
var (
	WebhookInvocation                MessageType = "WebhookInvocation"
	SyncUsername                     MessageType = "SyncUsername"
	GetMergeRequest                  MessageType = "GetMergeRequest"
	GetMergeRequestChanges           MessageType = "GetMergeRequestChanges"
	AddMergeRequestDiscussionNote    MessageType = "AddMergeRequestDiscussionNote"
	CreateMergeRequestDiscussion     MessageType = "CreateMergeRequestDiscussion"
	GetMergeRequestDiscussion        MessageType = "GetMergeRequestDiscussion"
	ListMergeRequestDiscussions      MessageType = "ListMergeRequestDiscussions"
	GetCommitDiff                    MessageType = "GetCommitDiff"
	GetMergeRequestCommits           MessageType = "GetMergeRequestCommits"
	ResolveMergeRequestDiscussion    MessageType = "ResolveMergeRequestDiscussion"
	GetFile                          MessageType = "GetFile"
	CreateMergeRequestNote           MessageType = "CreateMergeRequestNote"
	GetMergeRequestNote              MessageType = "GetMergeRequestNote"
	UpdateMergeRequestNote           MessageType = "UpdateMergeRequestNote"
	UpdateMergeRequestDiscussionNote MessageType = "UpdateMergeRequestDiscussionNote"
	GetVersion                       MessageType = "GetVersion"
	ListMergeRequestNotes            MessageType = "ListMergeRequestNotes"
	PublishAllDraftNotes             MessageType = "PublishAllDraftNotes"
	CreateDraftNotes                 MessageType = "CreateDraftNotes"
)
