package user

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"

type UserInfo struct {
	UserId    string `json:"userId"`
	UserName  string `json:"userName"`
	AvatarUrl string `json:"avatarUrl"`
}

type User struct {
	UserId          string        `json:"userId"`
	UserName        string        `json:"userName"`
	AvatarUrl       string        `json:"avatarUrl"`
	UserType        Type          `json:"userType"`
	IsInnerCustomer bool          `json:"isInnerCustomer"`
	SiteType        base.SiteType `json:"siteType"`
}

type Type string

const (
	UserTypeYunxiao = "yunxiao"
	UserTypeCodeup  = "codeup"
)
