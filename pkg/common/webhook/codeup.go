package webhook

import (
	"encoding/base64"
	"strings"

	errors2 "github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"gopkg.in/yaml.v3"
)

type CodeupEvent string

const (
	CodeupMergeRequestEvent CodeupEvent = "merge_request"
	CodeupCommentEvent      CodeupEvent = "note"
)

type CodeupMergeRequestPayload struct {
	ResourceId string `json:"resourceId"`
	Data       struct {
		Features struct {
			Suggestion struct {
				Enable bool `json:"enable"`
			} `json:"suggestion"`
			TriggerFilter struct {
				CommitLimit int `json:"commit_limit"`
				FileLimit   int `json:"file_limit"`
				LineLimit   int `json:"line_limit"`
			} `json:"trigger_filter"`
		} `json:"features"`
		Instructions     []any `json:"instructions"`
		ObjectAttributes struct {
			Action         string `json:"action"`
			AuthorAliyunPk string `json:"author_aliyun_pk"`
			AuthorId       int    `json:"author_id"`
			BizId          string `json:"biz_id"`
			CreatedAt      int64  `json:"created_at"`
			Description    string `json:"description"`
			IsUpdateByPush bool   `json:"is_update_by_push"`
			LastCommit     struct {
				Author struct {
					Email string `json:"email"`
					Name  string `json:"name"`
				} `json:"author"`
				Id        string `json:"id"`
				Message   string `json:"message"`
				Timestamp int64  `json:"timestamp"`
				Url       string `json:"url"`
			} `json:"last_commit"`
			LocalId     int    `json:"local_id"`
			MergeStatus string `json:"merge_status"`
			ProjectId   int    `json:"project_id"`
			Source      struct {
				HttpUrl         string `json:"http_url"`
				Name            string `json:"name"`
				Namespace       string `json:"namespace"`
				SshUrl          string `json:"ssh_url"`
				VisibilityLevel int    `json:"visibility_level"`
				WebUrl          string `json:"web_url"`
			} `json:"source"`
			SourceBranch    string `json:"source_branch"`
			SourceProjectId int64  `json:"source_project_id"`
			SourceType      string `json:"source_type"`
			SourceCommitId  string `json:"source_commit_id"`
			State           string `json:"state"`
			Target          struct {
				HttpUrl         string `json:"http_url"`
				Name            string `json:"name"`
				Namespace       string `json:"namespace"`
				SshUrl          string `json:"ssh_url"`
				VisibilityLevel int    `json:"visibility_level"`
				WebUrl          string `json:"web_url"`
			} `json:"target"`
			TargetBranch    string `json:"target_branch"`
			TargetProjectId int64  `json:"target_project_id"`
			Title           string `json:"title"`
			UpdatedAt       int64  `json:"updated_at"`
			Url             string `json:"url"`
			WorkInProgress  bool   `json:"work_in_progress"`
		} `json:"object_attributes"`
		ObjectKind string `json:"object_kind"`
		Repository struct {
			GitHttpUrl   string `json:"git_http_url"`
			GitSshUrl    string `json:"git_ssh_url"`
			Homepage     string `json:"homepage"`
			Name         string `json:"name"`
			Url          string `json:"url"`
			SecondaryUrl string `json:"secondary_url"`
		} `json:"repository"`
		ReviewRule struct {
			Content  string `json:"content"`
			Encoding string `json:"encoding"`
		} `json:"reviewRule"`
		User struct {
			AliyunPk  string `json:"aliyun_pk"`
			AvatarUrl string `json:"avatar_url"`
			ExternUid string `json:"extern_uid"`
			// Name 显示的用户名，可读性良好如：张三
			Name string `json:"name"`
			// Username 唯一标识，如：aliyun:xxxxxxxxx"
			Username string `json:"username"`
		} `json:"user"`
		Version string `json:"version"`
	} `json:"data"`
	AppId        string `json:"appId"`
	Event        string `json:"event"`
	ResourceType string `json:"resourceType"`
}

func (p *CodeupMergeRequestPayload) MergeRequestClosed() bool {
	switch p.Data.ObjectAttributes.Action {
	case "close", "merge":
		return true
	}
	return false
}

func (p *CodeupMergeRequestPayload) Validate() error {
	if p.Data.Repository.GitSshUrl == "" && p.Data.Repository.SecondaryUrl == "" {
		return errors.New(codes.ErrInvalidParameterWithBothEmpty, "Repository.SecondaryUrl", "Repository.GitSshUrl")
	}
	return nil
}

func (p *CodeupMergeRequestPayload) GetOrganizationId() (string, error) {
	// 优先使用 secondary_url 获取组织ID
	if p.Data.Repository.SecondaryUrl != "" {
		return GetOrganizationIdFromSecondaryUrl(p.Data.Repository.SecondaryUrl)
	}

	// 如果没有 secondary_url，则使用 ssh_url
	if p.Data.Repository.GitSshUrl != "" {
		return GetOrganizationIdFromGitUrl(p.Data.Repository.GitSshUrl)
	}

	return "", errors.New(codes.ErrInvalidParameterWithBothEmpty, "Repository.SecondaryUrl", "Repository.GitSshUrl")
}

func (p *CodeupMergeRequestPayload) LoadCodeReviewProperty(ctx base.Context) (*agent.CodeReviewProperty, error) {
	crp := agent.NewDefaultCodeReviewProperty()
	// codeup侧要求的默认配置
	crp.OutputSetting.ProblemLevel = agent.Critical
	crp.OutputSetting.WalkThrough.Collapse = false
	crp.OutputSetting.WalkThrough.Statistics = false
	crp.OutputSetting.Report.Scan = false
	crp.OutputSetting.Report.UnitTest = false
	if p == nil {
		return crp, nil
	}
	filter := p.Data.Features.TriggerFilter
	if filter.CommitLimit > 0 {
		crp.TriggerFilter.CommitLimit = filter.CommitLimit
	}
	if filter.FileLimit > 0 {
		crp.TriggerFilter.FileLimit = filter.FileLimit
	}
	if filter.LineLimit > 0 {
		crp.TriggerFilter.LineLimit = filter.LineLimit
	}
	rule, err := p.GetCodeReviewRule()
	if err != nil {
		// ignore error 这里是用户输入部分
		ctx.GetLogger().Warn("get code review rule failed, ignore it", "err", err)
		return crp, nil
	}
	if rule == nil || rule.Reviews == nil {
		// 无用户自定义规则，直接返回
		return crp, nil
	}
	if len(rule.Reviews.Ignores) > 0 {
		crp.ContentFilter.FileGlobs = append(crp.ContentFilter.FileGlobs, rule.Reviews.Ignores...)
	}
	if rule.Reviews.Language != "" {
		crp.OutputSetting.Language = agent.CodeReviewLanguage(rule.Reviews.Language)
	}
	if rule.Reviews.ProblemLevel != "" {
		crp.OutputSetting.ProblemLevel = agent.CodeReviewProblemLevel(rule.Reviews.ProblemLevel)
	}
	if rule.Reviews.WalkThrough != nil {
		crp.OutputSetting.WalkThrough = &agent.CodeReviewWalkThrough{
			Changes:  util.GetBoolOrDefault(rule.Reviews.WalkThrough.Changes, crp.OutputSetting.WalkThrough.Changes),
			Collapse: util.GetBoolOrDefault(rule.Reviews.WalkThrough.Collapse, crp.OutputSetting.WalkThrough.Collapse),
			Diagram:  util.GetBoolOrDefault(rule.Reviews.WalkThrough.Diagram, crp.OutputSetting.WalkThrough.Diagram),
			Enable:   util.GetBoolOrDefault(rule.Reviews.WalkThrough.Enable, crp.OutputSetting.WalkThrough.Enable),
			Summary:  util.GetBoolOrDefault(rule.Reviews.WalkThrough.Summary, crp.OutputSetting.WalkThrough.Summary),
		}
	}
	if rule.Reviews.Report != nil {
		crp.OutputSetting.Report = &agent.CodeReviewReport{
			Enable:            util.GetBoolOrDefault(rule.Reviews.Report.Enable, crp.OutputSetting.Report.Enable),
			Collapse:          util.GetBoolOrDefault(rule.Reviews.Report.Collapse, crp.OutputSetting.Report.Collapse),
			Summary:           util.GetBoolOrDefault(rule.Reviews.Report.Summary, crp.OutputSetting.Report.Summary),
			CodeSuggestion:    util.GetBoolOrDefault(rule.Reviews.Report.CodeSuggestion, crp.OutputSetting.Report.CodeSuggestion),
			HighLevelFeedback: util.GetBoolOrDefault(rule.Reviews.Report.HighLevelFeedback, crp.OutputSetting.Report.HighLevelFeedback),
		}
	}

	for _, pi := range rule.Reviews.PathInstructions {
		crp.PathInstructions = append(crp.PathInstructions, agent.CodeReviewPathInstruction{
			PathSelectors: []string{pi.Path},
			Text:          pi.Instructions,
		})
	}
	return crp, nil
}

func (p *CodeupMergeRequestPayload) GetCodeReviewRule() (*CodeupReviewRule, error) {
	rule := p.Data.ReviewRule
	if len(rule.Content) == 0 {
		return nil, nil
	}
	switch rule.Encoding {
	case "base64":
		var crr CodeupReviewRule
		decoded, err := base64.StdEncoding.DecodeString(rule.Content)
		if err != nil {
			return nil, errors2.WithStack(err)
		}
		if err := yaml.Unmarshal(decoded, &crr); err != nil {
			return nil, errors2.WithStack(err)
		}
		return &crr, nil
	}

	return nil, errors.New(codes.ErrInvalidParameter, "reviewRule.encoding", rule.Encoding)
}

type CodeupCommentPayload struct {
	Data struct {
		User struct {
			Name      string `json:"name"`
			Username  string `json:"username"`
			AliyunPk  string `json:"aliyun_pk"`
			AvatarUrl string `json:"avatar_url"`
			ExternUid string `json:"extern_uid"`
		} `json:"user"`
		Version    string `json:"version"`
		Repository struct {
			Url          string `json:"url"`
			Name         string `json:"name"`
			Homepage     string `json:"homepage"`
			GitSshUrl    string `json:"git_ssh_url"`
			GitHttpUrl   string `json:"git_http_url"`
			SecondaryUrl string `json:"secondary_url"`
		} `json:"repository"`
		ObjectKind   string `json:"object_kind"`
		MergeRequest struct {
			Url    string `json:"url"`
			State  string `json:"state"`
			Title  string `json:"title"`
			BizId  string `json:"biz_id"`
			Source struct {
				Name            string `json:"name"`
				SshUrl          string `json:"ssh_url"`
				WebUrl          string `json:"web_url"`
				HttpUrl         string `json:"http_url"`
				Namespace       string `json:"namespace"`
				VisibilityLevel int    `json:"visibility_level"`
			} `json:"source"`
			Target struct {
				Name            string `json:"name"`
				SshUrl          string `json:"ssh_url"`
				WebUrl          string `json:"web_url"`
				HttpUrl         string `json:"http_url"`
				Namespace       string `json:"namespace"`
				VisibilityLevel int    `json:"visibility_level"`
			} `json:"target"`
			LocalId    int   `json:"local_id"`
			AuthorId   int   `json:"author_id"`
			CreatedAt  int64 `json:"created_at"`
			ProjectId  int64 `json:"project_id"`
			UpdatedAt  int64 `json:"updated_at"`
			LastCommit struct {
				Id     string `json:"id"`
				Url    string `json:"url"`
				Author struct {
					Name  string `json:"name"`
					Email string `json:"email"`
				} `json:"author"`
				Message   string `json:"message"`
				Timestamp int64  `json:"timestamp"`
			} `json:"last_commit"`
			SourceType      string `json:"source_type"`
			MergeStatus     string `json:"merge_status"`
			SourceBranch    string `json:"source_branch"`
			TargetBranch    string `json:"target_branch"`
			AuthorAliyunPk  string `json:"author_aliyun_pk"`
			WorkInProgress  bool   `json:"work_in_progress"`
			SourceProjectId int64  `json:"source_project_id"`
			TargetProjectId int64  `json:"target_project_id"`
		} `json:"merge_request"`
		ObjectAttributes struct {
			Url             string `json:"url"`
			Note            string `json:"note"`
			State           string `json:"state"`
			Closed          int    `json:"closed"`
			System          bool   `json:"system"`
			AuthorId        int    `json:"author_id"`
			CommitId        string `json:"commit_id"`
			LineCode        string `json:"line_code"`
			NoteType        string `json:"note_type"`
			CreatedAt       int64  `json:"created_at"`
			ProjectId       int64  `json:"project_id"`
			UpdatedAt       int64  `json:"updated_at"`
			NoteBizId       string `json:"note_biz_id"`
			NoteableId      string `json:"noteable_id"`
			RelatedType     string `json:"related_type"`
			NoteableType    string `json:"noteable_type"`
			RelatedBizId    string `json:"related_biz_id"`
			AuthorAliyunPk  string `json:"author_aliyun_pk"`
			RelatedPatchSet struct {
				Branch        string `json:"branch"`
				CommitId      string `json:"commit_id"`
				MrBizId       string `json:"mr_biz_id"`
				VersionNo     int    `json:"version_no"`
				CreateTime    int64  `json:"create_time"`
				PatchSetName  string `json:"patch_set_name"`
				PatchSetBizId string `json:"patch_set_biz_id"`
			} `json:"related_patch_set"`
		} `json:"object_attributes"`
	} `json:"data"`
	AppId        string `json:"appId"`
	Event        string `json:"event"`
	ResourceId   string `json:"resourceId"`
	ResourceType string `json:"resourceType"`
}

func (p *CodeupCommentPayload) Validate() error {
	if p.Data.Repository.GitSshUrl == "" && p.Data.Repository.SecondaryUrl == "" {
		return errors.New(codes.ErrInvalidParameterWithBothEmpty, "Repository.SecondaryUrl", "Repository.GitSshUrl")
	}
	return nil
}

func (p *CodeupCommentPayload) MergeRequestClosed() bool {
	switch p.Data.MergeRequest.State {
	case "closed", "merged":
		return true
	}
	return false
}

func (p *CodeupCommentPayload) GetOrganizationId() (string, error) {
	// 优先使用 secondary_url 获取组织ID
	if p.Data.Repository.SecondaryUrl != "" {
		return GetOrganizationIdFromSecondaryUrl(p.Data.Repository.SecondaryUrl)
	}

	// 如果没有 secondary_url，则使用 ssh_url
	if p.Data.Repository.GitSshUrl != "" {
		return GetOrganizationIdFromGitUrl(p.Data.Repository.GitSshUrl)
	}

	return "", errors.New(codes.ErrInvalidParameterWithBothEmpty, "Repository.SecondaryUrl", "Repository.GitSshUrl")
}

type CodeupWebhookBasicPayload struct {
	ResourceId string `json:"resourceId"`
	Data       struct {
		ObjectKind string `json:"object_kind"`
		Repository struct {
			GitHttpUrl   string `json:"git_http_url"`
			GitSshUrl    string `json:"git_ssh_url"`
			Homepage     string `json:"homepage"`
			Name         string `json:"name"`
			Url          string `json:"url"`
			SecondaryUrl string `json:"secondary_url"`
		} `json:"repository"`
		User struct {
			AliyunPk  string `json:"aliyun_pk"`
			AvatarUrl string `json:"avatar_url"`
			ExternUid string `json:"extern_uid"`
			Name      string `json:"name"`
			Username  string `json:"username"`
		} `json:"user"`
		Version string `json:"version"`
	} `json:"data"`
	AppId        string `json:"appId"`
	Event        string `json:"event"`
	ResourceType string `json:"resourceType"`
}

func (p *CodeupWebhookBasicPayload) Validate() error {
	if p.Data.Repository.GitSshUrl == "" && p.Data.Repository.SecondaryUrl == "" {
		return errors.New(codes.ErrInvalidParameterWithBothEmpty, "Repository.SecondaryUrl", "Repository.GitSshUrl")
	}
	return nil
}

func (p *CodeupWebhookBasicPayload) GetOrganizationId() (string, error) {
	// 优先使用 secondary_url 获取组织ID
	if p.Data.Repository.SecondaryUrl != "" {
		return GetOrganizationIdFromSecondaryUrl(p.Data.Repository.SecondaryUrl)
	}

	// 如果没有 secondary_url，则使用 ssh_url
	if p.Data.Repository.GitSshUrl != "" {
		return GetOrganizationIdFromGitUrl(p.Data.Repository.GitSshUrl)
	}

	return "", errors.New(codes.ErrInvalidParameterWithBothEmpty, "Repository.SecondaryUrl", "Repository.GitSshUrl")
}

// GetOrganizationIdFromGitUrl 从云效仓库Git SSH地址中解析出组织ID
// 云效Webhook数据中没有组织ID，Repository.Namespace字段可能是组织ID也可能是 组织ID+代码组。
// 致信 推荐从git ssh url里解析出组织ID

// 例如
// 有代码组
// *********************:{orgId}/{repo_name}.git
// 无代码组
// *********************:{orgId}/{group_name}/{repo_name}.git
func GetOrganizationIdFromGitUrl(gitSshUrl string) (string, error) {
	// 检查输入参数
	if gitSshUrl == "" {
		return "", errors.New(codes.ErrInvalidParameterWithEmpty, "gitSshUrl")
	}

	// 解析 git SSH URL
	// 移除 "git@" 前缀和 ".git" 后缀
	trimmedUrl := strings.TrimPrefix(gitSshUrl, "git@")
	trimmedUrl = strings.TrimSuffix(trimmedUrl, ".git")

	// 分割域名和路径
	parts := strings.SplitN(trimmedUrl, ":", 2)
	if len(parts) != 2 {
		return "", errors.New(codes.ErrInvalidParameter, "gitSshUrl", gitSshUrl)
	}

	// 获取路径部分
	path := parts[1]

	// 分割路径组件
	components := strings.Split(path, "/")

	// 检查组件数量
	if len(components) < 2 {
		return "", errors.New(codes.ErrInvalidParameter, "gitSshUrl", gitSshUrl)
	}

	// 组织ID总是第一个组件
	orgId := components[0]

	// 验证组织ID的合法性
	if orgId == "" {
		return "", errors.New(codes.ErrInvalidParameter, "gitSshUrl", gitSshUrl)
	}

	// 可以添加额外的验证，例如检查组织ID的格式
	// 这里假设组织ID应该是数字格式
	if !isValidOrgId(orgId) {
		return "", errors.New(codes.ErrInvalidParameter, "gitSshUrl", gitSshUrl)
	}

	return orgId, nil
}

// 辅助函数：验证组织ID是否合法
func isValidOrgId(orgId string) bool {
	// 这里可以根据实际需求添加验证规则
	// 例如：检查是否为数字、长度限制等

	return len(orgId) > 0
}

// GetOrganizationIdFromSecondaryUrl 从云效仓库Secondary URL中解析出组织ID
// 当git namespace不等于orgId时，webhook额外传递一个secondary_url包含orgId
// secondary_url 格式示例: https://codeup.aliyun.com/{orgId}/practice/v2/media.shop.busionline.com.git
func GetOrganizationIdFromSecondaryUrl(secondaryUrl string) (string, error) {
	// 检查输入参数
	if secondaryUrl == "" {
		return "", errors.New(codes.ErrInvalidParameterWithEmpty, "secondaryUrl")
	}

	// 检查是否为有效的HTTP URL
	if !strings.HasPrefix(secondaryUrl, "http://") && !strings.HasPrefix(secondaryUrl, "https://") {
		return "", errors.New(codes.ErrInvalidParameter, "secondaryUrl", secondaryUrl)
	}

	// 移除协议前缀和 .git 后缀
	trimmedUrl := strings.TrimPrefix(secondaryUrl, "https://")
	trimmedUrl = strings.TrimPrefix(trimmedUrl, "http://")
	trimmedUrl = strings.TrimSuffix(trimmedUrl, ".git")

	// 按 / 分隔，格式为: domain/{orgId}/...
	parts := strings.Split(trimmedUrl, "/")

	// 需要至少有域名和orgId两个部分
	if len(parts) < 2 {
		return "", errors.New(codes.ErrInvalidParameter, "secondaryUrl", secondaryUrl)
	}

	// 第二个组件就是orgId（索引为1）
	orgId := parts[1]

	// 验证组织ID的合法性
	if orgId == "" {
		return "", errors.New(codes.ErrInvalidParameter, "secondaryUrl", secondaryUrl)
	}

	if !isValidOrgId(orgId) {
		return "", errors.New(codes.ErrInvalidParameter, "secondaryUrl", secondaryUrl)
	}

	return orgId, nil
}

type CodeupReviewRule struct {
	Reviews *struct {
		Ignores      []string `json:"ignores" yaml:"ignores"`
		Language     string   `json:"language" yaml:"language"`
		ProblemLevel string   `json:"problem_level" yaml:"problem_level"`
		WalkThrough  *struct {
			Enable   *bool `json:"enable" yaml:"enable"`
			Collapse *bool `json:"collapse" yaml:"collapse"`
			Summary  *bool `json:"summary" yaml:"summary"`
			Changes  *bool `json:"changes" yaml:"changes"`
			Diagram  *bool `json:"diagram" yaml:"diagram"`
		} `json:"walk_through" yaml:"walk_through"`
		Report *struct {
			Enable            *bool `json:"enable" yaml:"enable"`
			Collapse          *bool `json:"collapse" yaml:"collapse"`
			Summary           *bool `json:"summary" yaml:"summary"`
			CodeSuggestion    *bool `json:"code_suggestion" yaml:"code_suggestion"`
			HighLevelFeedback *bool `json:"high_level_feedback" yaml:"high_level_feedback"`
		} `json:"report" yaml:"report"`
		PathInstructions []struct {
			Path         string `json:"path" yaml:"path"`
			Instructions string `json:"instructions" yaml:"instructions"`
		} `json:"path_instructions" yaml:"path_instructions"`
	} `json:"reviews" yaml:"reviews"`
}
