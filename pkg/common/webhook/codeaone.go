package webhook

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
)

type CodeaoneEvent string

const (
	CodeaonePushEvent         CodeaoneEvent = "push"
	CodeaoneTagPushEvent      CodeaoneEvent = "tag_push"
	CodeaoneCommentEvent      CodeaoneEvent = "note"
	CodeaoneMergeRequestEvent CodeaoneEvent = "merge_request"
	CodeaoneIssueEvent        CodeaoneEvent = "issue"
)

func (e CodeaoneEvent) Validate() error {
	switch e {
	case CodeaoneCommentEvent, CodeaoneMergeRequestEvent:
		return nil
	}
	return errors.New(codes.ErrInvalidParameter, "event_type", e)
}

type CodeaoneMergeRequestWebhookPayload struct {
	ObjectKind string `json:"object_kind"`
	User       struct {
		Name      string `json:"name"`
		Username  string `json:"username"`
		AvatarUrl string `json:"avatar_url"`
	} `json:"user"`
	ObjectAttributes struct {
		Id              int    `json:"id"`
		TargetBranch    string `json:"target_branch"`
		TargetProjectId int    `json:"target_project_id"`
		SourceBranch    string `json:"source_branch"`
		SourceProjectId int    `json:"source_project_id"`
		AuthorId        int    `json:"author_id"`
		AssigneeId      int    `json:"assignee_id"`
		Title           string `json:"title"`
		CreatedAt       string `json:"created_at"`
		UpdatedAt       string `json:"updated_at"`
		StCommits       struct {
		} `json:"st_commits"`
		StDiff struct {
		} `json:"st_diff"`
		MilestoneId struct {
		} `json:"milestone_id"`
		State       string `json:"state"`
		MergeStatus string `json:"merge_status"`
		IID         int    `json:"iid"`
		Description string `json:"description"`
		Oldrev      string `json:"oldrev"`
		Source      struct {
			Name            string `json:"name"`
			SSHUrl          string `json:"ssh_url"`
			HttpUrl         string `json:"http_url"`
			WebUrl          string `json:"web_url"`
			VisibilityLevel int    `json:"visibility_level"`
			Namespace       string `json:"namespace"`
		} `json:"source"`
		Target struct {
			Name            string `json:"name"`
			SSHUrl          string `json:"ssh_url"`
			HttpUrl         string `json:"http_url"`
			WebUrl          string `json:"web_url"`
			VisibilityLevel int    `json:"visibility_level"`
			Namespace       string `json:"namespace"`
		} `json:"target"`
		LastCommit struct {
			Id        string `json:"id"`
			Message   string `json:"message"`
			Timestamp string `json:"timestamp"`
			Url       string `json:"url"`
			Author    struct {
				Name  string `json:"name"`
				Email string `json:"email"`
			} `json:"author"`
		} `json:"last_commit"`
		WorkInProgress bool   `json:"work_in_progress"`
		URL            string `json:"url"`
		Action         string `json:"action"`
	} `json:"object_attributes"`
	Repository struct {
		Url         string `json:"url"`
		Name        string `json:"name"`
		Homepage    string `json:"homepage"`
		Description string `json:"description"`
	} `json:"repository"`
}

func (p *CodeaoneMergeRequestWebhookPayload) Validate() error {
	return nil
}

type CodeaoneCommentWebhookPayload struct {
	ObjectKind string `json:"object_kind"`
	User       struct {
		Name      string `json:"name"`
		Username  string `json:"username"`
		AvatarUrl string `json:"avatar_url"`
	} `json:"user"`
	ProjectId  int `json:"project_id"`
	Repository struct {
		Name        string `json:"name"`
		Url         string `json:"url"`
		Description string `json:"description"`
		Homepage    string `json:"homepage"`
	} `json:"repository"`
	ObjectAttributes struct {
		Id           int    `json:"id"`
		ProjectId    int    `json:"project_id"`
		Note         string `json:"note"`
		NoteableType string `json:"noteable_type"`
		NoteableId   int    `json:"noteable_id"`
		ParentNoteId int    `json:"parent_note_id"`
		AuthorId     int    `json:"author_id"`
		System       bool   `json:"system"`
		CreatedAt    string `json:"created_at"`
		UpdatedAt    string `json:"updated_at"`
		Attachment   struct {
		} `json:"attachment"`
		LineCode string `json:"line_code"`
		CommitId string `json:"commit_id"`
		StDiff   struct {
		} `json:"st_diff"`
		URL string `json:"url"`
	} `json:"object_attributes"`
	MergeRequest struct {
		Id              int    `json:"id"`
		TargetBranch    string `json:"target_branch"`
		TargetProjectId int    `json:"target_project_id"`
		SourceBranch    string `json:"source_branch"`
		SourceProjectId int    `json:"source_project_id"`
		AuthorId        int    `json:"author_id"`
		AssigneeId      int    `json:"assignee_id"`
		Title           string `json:"title"`
		CreatedAt       string `json:"created_at"`
		UpdatedAt       string `json:"updated_at"`
		MilestoneId     struct {
		} `json:"milestone_id"`
		State       string `json:"state"`
		MergeStatus string `json:"merge_status"`
		IID         int    `json:"iid"`
		Description string `json:"description"`
		Position    int    `json:"position"`
		LockedAt    struct {
		} `json:"locked_at"`
		Source struct {
			Name            string `json:"name"`
			SSHUrl          string `json:"ssh_url"`
			HttpUrl         string `json:"http_url"`
			WebUrl          string `json:"web_url"`
			VisibilityLevel int    `json:"visibility_level"`
			Namespace       string `json:"namespace"`
		} `json:"source"`
		Target struct {
			Name            string `json:"name"`
			SSHUrl          string `json:"ssh_url"`
			HttpUrl         string `json:"http_url"`
			WebUrl          string `json:"web_url"`
			VisibilityLevel int    `json:"visibility_level"`
			Namespace       string `json:"namespace"`
		} `json:"target"`
		LastCommit struct {
			Id        string `json:"id"`
			Message   string `json:"message"`
			Timestamp string `json:"timestamp"`
			Url       string `json:"url"`
			Author    struct {
				Name  string `json:"name"`
				Email string `json:"email"`
			} `json:"author"`
		} `json:"last_commit"`
		WorkInProgress bool `json:"work_in_progress"`
	} `json:"merge_request"`
}

func (p *CodeaoneCommentWebhookPayload) Validate() error {
	return nil
}

func (p *CodeaoneCommentWebhookPayload) MergeRequestClosed() bool {
	switch p.MergeRequest.State {
	case "closed", "merged":
		return true
	}
	return false
}
