package webhook

import (
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
)

type GitlabWebhookPayload struct {
	ObjectKind string      `json:"object_kind"`
	EventType  GitlabEvent `json:"event_type"`
	User       struct {
		Id        int    `json:"id"`
		Name      string `json:"name"`
		Username  string `json:"username"`
		AvatarUrl string `json:"avatar_url"`
		Email     string `json:"email"`
	} `json:"user"`
	Project struct {
		Id                int    `json:"id"`
		Name              string `json:"name"`
		Description       string `json:"description"`
		WebUrl            string `json:"web_url"`
		AvatarUrl         string `json:"avatar_url"`
		GitSshUrl         string `json:"git_ssh_url"`
		GitHttpUrl        string `json:"git_http_url"`
		Namespace         string `json:"namespace"`
		VisibilityLevel   int    `json:"visibility_level"`
		PathWithNamespace string `json:"path_with_namespace"`
		DefaultBranch     string `json:"default_branch"`
		CiConfigPath      string `json:"ci_config_path"`
		Homepage          string `json:"homepage"`
		Url               string `json:"url"`
		SshUrl            string `json:"ssh_url"`
		HttpUrl           string `json:"http_url"`
	} `json:"project"`
	Repository struct {
		Name        string `json:"name"`
		Url         string `json:"url"`
		Description string `json:"description"`
		Homepage    string `json:"homepage"`
	} `json:"repository"`
	ObjectAttributes map[string]any `json:"object_attributes"`
	MergeRequest     struct {
		Iid   int    `json:"iid"`
		State string `json:"state"`
	} `json:"merge_request"`
}

func (p *GitlabWebhookPayload) Validate() error {
	if err := p.EventType.Validate(); err != nil {
		return err
	}
	switch p.EventType {
	case GitlabCommentEvent:
	case GitlabMergeRequestEvent:
	}
	return nil
}

func (p *GitlabWebhookPayload) MergeRequestOpened() bool {
	state := ""
	switch p.EventType {
	case "merge_request":
		state = cast.ToString(p.ObjectAttributes["state"])
	case "note":
		state = p.MergeRequest.State
	}
	return state == "opened" || state == "reopened"
}

func (p *GitlabWebhookPayload) MergeRequestClosed() bool {
	state := ""
	switch p.EventType {
	case "merge_request":
		state = cast.ToString(p.ObjectAttributes["state"])
	case "note":
		state = p.MergeRequest.State
	}
	return state == "closed" || state == "merged"
}

func (p *GitlabWebhookPayload) GetDiscussionId() string {
	dId := ""
	if p.EventType == GitlabCommentEvent {
		dId = cast.ToString(p.ObjectAttributes["discussion_id"])
	}
	return dId
}

func (p *GitlabWebhookPayload) GetHeadCommitId() string {
	headCommitId := ""
	if lastCommit, ok := p.ObjectAttributes["last_commit"].(map[string]any); ok && lastCommit != nil {
		if id, exists := lastCommit["id"]; exists && id != nil {
			headCommitId = cast.ToString(id)
		}
	}
	return headCommitId
}

type GitlabEvent string

const (
	GitlabCommentEvent      GitlabEvent = "note"
	GitlabMergeRequestEvent GitlabEvent = "merge_request"
)

func (e GitlabEvent) Validate() error {
	switch e {
	case GitlabCommentEvent, GitlabMergeRequestEvent:
		return nil
	}
	return errors.New(codes.ErrInvalidParameter, "event_type", e)
}
