package webhook

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"gopkg.in/yaml.v3"
)

func TestGetOrganizationIdFromGitUrl(t *testing.T) {
	urls := []string{
		"*********************:12345/repo-name.git",            // 无代码组
		"*********************:12345/group-name/repo-name.git", // 有代码组

	}
	invalidurls := []string{
		"invalid-url", // 无效的URL格式
	}

	for _, url := range urls {
		orgId, err := GetOrganizationIdFromGitUrl(url)
		if err != nil {
			t.Errorf("Error processing %s: %v\n", url, err)
			continue
		}
		assert.True(t, orgId == "12345")
	}
	for _, url := range invalidurls {
		_, err := GetOrganizationIdFromGitUrl(url)
		if err == nil {
			t.Errorf("Expected error for invalid URL: %s", url)
		}
	}
}

func TestGetOrganizationIdFromSecondaryUrl(t *testing.T) {
	urls := []string{
		"https://codeup.ccc.com/61d3ef52f9a895d4b3a5e705/practice/v2/media.shop.busionline.com.git",
		"https://codeup.aliyun.com/12345/group/repo.git",
		"https://codeup.example.com/67890/project.git",
	}
	expectedOrgIds := []string{
		"61d3ef52f9a895d4b3a5e705",
		"12345",
		"67890",
	}
	invalidurls := []string{
		"invalid-url",                 // 无效的URL格式
		"https://codeup.ccc.com/.git", // 缺少组织ID
		"",                            // 空字符串
	}

	for i, url := range urls {
		orgId, err := GetOrganizationIdFromSecondaryUrl(url)
		if err != nil {
			t.Errorf("Error processing %s: %v\n", url, err)
			continue
		}
		assert.Equal(t, expectedOrgIds[i], orgId, "URL: %s", url)
	}

	for _, url := range invalidurls {
		_, err := GetOrganizationIdFromSecondaryUrl(url)
		if err == nil {
			t.Errorf("Expected error for invalid URL: %s", url)
		}
	}
}

func TestCodeupPayloadGetOrganizationId(t *testing.T) {
	// 测试优先使用 secondary_url 的情况
	payloadWithSecondary := &CodeupWebhookBasicPayload{
		Data: struct {
			ObjectKind string `json:"object_kind"`
			Repository struct {
				GitHttpUrl   string `json:"git_http_url"`
				GitSshUrl    string `json:"git_ssh_url"`
				Homepage     string `json:"homepage"`
				Name         string `json:"name"`
				Url          string `json:"url"`
				SecondaryUrl string `json:"secondary_url"`
			} `json:"repository"`
			User struct {
				AliyunPk  string `json:"aliyun_pk"`
				AvatarUrl string `json:"avatar_url"`
				ExternUid string `json:"extern_uid"`
				Name      string `json:"name"`
				Username  string `json:"username"`
			} `json:"user"`
			Version string `json:"version"`
		}{
			Repository: struct {
				GitHttpUrl   string `json:"git_http_url"`
				GitSshUrl    string `json:"git_ssh_url"`
				Homepage     string `json:"homepage"`
				Name         string `json:"name"`
				Url          string `json:"url"`
				SecondaryUrl string `json:"secondary_url"`
			}{
				GitSshUrl:    "*********************:12345/repo.git",
				SecondaryUrl: "https://codeup.ccc.com/61d3ef52f9a895d4b3a5e705/practice/v2/media.shop.busionline.com.git",
			},
		},
	}

	// 应该优先使用 secondary_url
	orgId, err := payloadWithSecondary.GetOrganizationId()
	assert.NoError(t, err)
	assert.Equal(t, "61d3ef52f9a895d4b3a5e705", orgId)

	// 测试没有 secondary_url 时使用 ssh_url 的情况
	payloadWithoutSecondary := &CodeupWebhookBasicPayload{
		Data: struct {
			ObjectKind string `json:"object_kind"`
			Repository struct {
				GitHttpUrl   string `json:"git_http_url"`
				GitSshUrl    string `json:"git_ssh_url"`
				Homepage     string `json:"homepage"`
				Name         string `json:"name"`
				Url          string `json:"url"`
				SecondaryUrl string `json:"secondary_url"`
			} `json:"repository"`
			User struct {
				AliyunPk  string `json:"aliyun_pk"`
				AvatarUrl string `json:"avatar_url"`
				ExternUid string `json:"extern_uid"`
				Name      string `json:"name"`
				Username  string `json:"username"`
			} `json:"user"`
			Version string `json:"version"`
		}{
			Repository: struct {
				GitHttpUrl   string `json:"git_http_url"`
				GitSshUrl    string `json:"git_ssh_url"`
				Homepage     string `json:"homepage"`
				Name         string `json:"name"`
				Url          string `json:"url"`
				SecondaryUrl string `json:"secondary_url"`
			}{
				GitSshUrl:    "*********************:67890/repo.git",
				SecondaryUrl: "", // 空的 secondary_url
			},
		},
	}

	// 应该使用 ssh_url
	orgId, err = payloadWithoutSecondary.GetOrganizationId()
	assert.NoError(t, err)
	assert.Equal(t, "67890", orgId)

	// 测试两个URL都为空的错误情况
	payloadWithBothEmpty := &CodeupWebhookBasicPayload{
		Data: struct {
			ObjectKind string `json:"object_kind"`
			Repository struct {
				GitHttpUrl   string `json:"git_http_url"`
				GitSshUrl    string `json:"git_ssh_url"`
				Homepage     string `json:"homepage"`
				Name         string `json:"name"`
				Url          string `json:"url"`
				SecondaryUrl string `json:"secondary_url"`
			} `json:"repository"`
			User struct {
				AliyunPk  string `json:"aliyun_pk"`
				AvatarUrl string `json:"avatar_url"`
				ExternUid string `json:"extern_uid"`
				Name      string `json:"name"`
				Username  string `json:"username"`
			} `json:"user"`
			Version string `json:"version"`
		}{
			Repository: struct {
				GitHttpUrl   string `json:"git_http_url"`
				GitSshUrl    string `json:"git_ssh_url"`
				Homepage     string `json:"homepage"`
				Name         string `json:"name"`
				Url          string `json:"url"`
				SecondaryUrl string `json:"secondary_url"`
			}{
				GitSshUrl:    "", // 空的 ssh_url
				SecondaryUrl: "", // 空的 secondary_url
			},
		},
	}

	// 应该返回 ErrInvalidParameterWithBothEmpty 错误
	_, err = payloadWithBothEmpty.GetOrganizationId()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "BothEmpty")
}

func TestYamlUnmarshal(t *testing.T) {
	y := `
reviews:
  # files that ignore to review, glob pattern
  ignores:
    - myfile.txt
    - myfolder/
    - "*.log"
    - "**/logs"
    - /debug.log

  # basic config
  language: "zh-CN"
  problem_level: "CRITICAL"
  path_instructions:
  - path: "**/*.java"
    instructions: '确保类名使用 PascalCase，方法名和变量名使用 camelCase。常量使用 SNAKE_CASE'
  - path: "*.js"
    instructions: |-
      对于TODO和FIXME: 要求清理所有未解决的TODO和FIXME标记，确保代码处于最终完成状态。`

	rule := CodeupReviewRule{}
	if err := yaml.Unmarshal([]byte(y), &rule); err != nil {
		t.Errorf("yaml.Unmarshal error: %v", err)
	}
	assert.Equal(t, rule.Reviews.Language, "zh-CN")
	assert.Equal(t, rule.Reviews.ProblemLevel, "CRITICAL")
}
