package webhook

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"time"
)

type GithubInstallationPayload struct {
	Action       string `json:"action"`
	Installation struct {
		Id       int64  `json:"id"`
		ClientId string `json:"client_id"`
		Account  struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"account"`
		RepositorySelection string `json:"repository_selection"`
		AccessTokensUrl     string `json:"access_tokens_url"`
		RepositoriesUrl     string `json:"repositories_url"`
		HtmlUrl             string `json:"html_url"`
		AppId               int64  `json:"app_id"`
		AppSlug             string `json:"app_slug"`
		TargetId            int    `json:"target_id"`
		TargetType          string `json:"target_type"`
		Permissions         struct {
			Discussions  string `json:"discussions"`
			Issues       string `json:"issues"`
			Metadata     string `json:"metadata"`
			PullRequests string `json:"pull_requests"`
		} `json:"permissions"`
		Events                 []string      `json:"events"`
		CreatedAt              time.Time     `json:"created_at"`
		UpdatedAt              time.Time     `json:"updated_at"`
		SingleFileName         interface{}   `json:"single_file_name"`
		HasMultipleSingleFiles bool          `json:"has_multiple_single_files"`
		SingleFilePaths        []interface{} `json:"single_file_paths"`
		SuspendedBy            interface{}   `json:"suspended_by"`
		SuspendedAt            interface{}   `json:"suspended_at"`
	} `json:"installation"`
	Repositories []struct {
		Id       int    `json:"id"`
		NodeId   string `json:"node_id"`
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		Private  bool   `json:"private"`
	} `json:"repositories"`
	Requester interface{} `json:"requester"`
	Sender    struct {
		Login             string `json:"login"`
		Id                int    `json:"id"`
		NodeId            string `json:"node_id"`
		AvatarUrl         string `json:"avatar_url"`
		GravatarId        string `json:"gravatar_id"`
		Url               string `json:"url"`
		HtmlUrl           string `json:"html_url"`
		FollowersUrl      string `json:"followers_url"`
		FollowingUrl      string `json:"following_url"`
		GistsUrl          string `json:"gists_url"`
		StarredUrl        string `json:"starred_url"`
		SubscriptionsUrl  string `json:"subscriptions_url"`
		OrganizationsUrl  string `json:"organizations_url"`
		ReposUrl          string `json:"repos_url"`
		EventsUrl         string `json:"events_url"`
		ReceivedEventsUrl string `json:"received_events_url"`
		Type              string `json:"type"`
		UserViewType      string `json:"user_view_type"`
		SiteAdmin         bool   `json:"site_admin"`
	} `json:"sender"`
}

func (p *GithubInstallationPayload) Validate() error {
	if p.Installation.Id <= 0 {
		return errors.New(codes.ErrInvalidParameter, "installation.id", p.Installation.Id)
	}
	if p.Installation.Account.Id <= 0 {
		return errors.New(codes.ErrInvalidParameter, "installation.account.id", p.Installation.Account.Id)
	}
	if p.Installation.AppId <= 0 {
		return errors.New(codes.ErrInvalidParameter, "installation.app_id", p.Installation.AppId)
	}
	return nil
}

type GithubPingPayload struct {
	Zen    string `json:"zen"`
	HookId int    `json:"hook_id"`
	Hook   struct {
		Type   string   `json:"type"`
		Id     int      `json:"id"`
		Name   string   `json:"name"`
		Active bool     `json:"active"`
		Events []string `json:"events"`
		Config struct {
			ContentType string `json:"content_type"`
			InsecureSsl string `json:"insecure_ssl"`
			Url         string `json:"url"`
		} `json:"config"`
		UpdatedAt     time.Time `json:"updated_at"`
		CreatedAt     time.Time `json:"created_at"`
		AppId         int64     `json:"app_id"`
		DeliveriesUrl string    `json:"deliveries_url"`
	} `json:"hook"`
}

type GithubPullRequestPayload struct {
	Action      string `json:"action"`
	Number      int    `json:"number"`
	PullRequest struct {
		Url      string `json:"url"`
		Id       int64  `json:"id"`
		NodeId   string `json:"node_id"`
		HtmlUrl  string `json:"html_url"`
		DiffUrl  string `json:"diff_url"`
		PatchUrl string `json:"patch_url"`
		IssueUrl string `json:"issue_url"`
		Number   int    `json:"number"`
		State    string `json:"state"`
		Locked   bool   `json:"locked"`
		Title    string `json:"title"`
		User     struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"user"`
		Body               string        `json:"body"`
		CreatedAt          time.Time     `json:"created_at"`
		UpdatedAt          time.Time     `json:"updated_at"`
		ClosedAt           interface{}   `json:"closed_at"`
		MergedAt           interface{}   `json:"merged_at"`
		MergeCommitSha     interface{}   `json:"merge_commit_sha"`
		Assignee           interface{}   `json:"assignee"`
		Assignees          []interface{} `json:"assignees"`
		RequestedReviewers []interface{} `json:"requested_reviewers"`
		RequestedTeams     []interface{} `json:"requested_teams"`
		Labels             []interface{} `json:"labels"`
		Milestone          interface{}   `json:"milestone"`
		Draft              bool          `json:"draft"`
		CommitsUrl         string        `json:"commits_url"`
		ReviewCommentsUrl  string        `json:"review_comments_url"`
		ReviewCommentUrl   string        `json:"review_comment_url"`
		CommentsUrl        string        `json:"comments_url"`
		StatusesUrl        string        `json:"statuses_url"`
		Head               struct {
			Label string `json:"label"`
			Ref   string `json:"ref"`
			Sha   string `json:"sha"`
			User  struct {
				Login             string `json:"login"`
				Id                int    `json:"id"`
				NodeId            string `json:"node_id"`
				AvatarUrl         string `json:"avatar_url"`
				GravatarId        string `json:"gravatar_id"`
				Url               string `json:"url"`
				HtmlUrl           string `json:"html_url"`
				FollowersUrl      string `json:"followers_url"`
				FollowingUrl      string `json:"following_url"`
				GistsUrl          string `json:"gists_url"`
				StarredUrl        string `json:"starred_url"`
				SubscriptionsUrl  string `json:"subscriptions_url"`
				OrganizationsUrl  string `json:"organizations_url"`
				ReposUrl          string `json:"repos_url"`
				EventsUrl         string `json:"events_url"`
				ReceivedEventsUrl string `json:"received_events_url"`
				Type              string `json:"type"`
				UserViewType      string `json:"user_view_type"`
				SiteAdmin         bool   `json:"site_admin"`
			} `json:"user"`
			Repo struct {
				Id       int    `json:"id"`
				NodeId   string `json:"node_id"`
				Name     string `json:"name"`
				FullName string `json:"full_name"`
				Private  bool   `json:"private"`
				Owner    struct {
					Login             string `json:"login"`
					Id                int    `json:"id"`
					NodeId            string `json:"node_id"`
					AvatarUrl         string `json:"avatar_url"`
					GravatarId        string `json:"gravatar_id"`
					Url               string `json:"url"`
					HtmlUrl           string `json:"html_url"`
					FollowersUrl      string `json:"followers_url"`
					FollowingUrl      string `json:"following_url"`
					GistsUrl          string `json:"gists_url"`
					StarredUrl        string `json:"starred_url"`
					SubscriptionsUrl  string `json:"subscriptions_url"`
					OrganizationsUrl  string `json:"organizations_url"`
					ReposUrl          string `json:"repos_url"`
					EventsUrl         string `json:"events_url"`
					ReceivedEventsUrl string `json:"received_events_url"`
					Type              string `json:"type"`
					UserViewType      string `json:"user_view_type"`
					SiteAdmin         bool   `json:"site_admin"`
				} `json:"owner"`
				HtmlUrl          string      `json:"html_url"`
				Description      string      `json:"description"`
				Fork             bool        `json:"fork"`
				Url              string      `json:"url"`
				ForksUrl         string      `json:"forks_url"`
				KeysUrl          string      `json:"keys_url"`
				CollaboratorsUrl string      `json:"collaborators_url"`
				TeamsUrl         string      `json:"teams_url"`
				HooksUrl         string      `json:"hooks_url"`
				IssueEventsUrl   string      `json:"issue_events_url"`
				EventsUrl        string      `json:"events_url"`
				AssigneesUrl     string      `json:"assignees_url"`
				BranchesUrl      string      `json:"branches_url"`
				TagsUrl          string      `json:"tags_url"`
				BlobsUrl         string      `json:"blobs_url"`
				GitTagsUrl       string      `json:"git_tags_url"`
				GitRefsUrl       string      `json:"git_refs_url"`
				TreesUrl         string      `json:"trees_url"`
				StatusesUrl      string      `json:"statuses_url"`
				LanguagesUrl     string      `json:"languages_url"`
				StargazersUrl    string      `json:"stargazers_url"`
				ContributorsUrl  string      `json:"contributors_url"`
				SubscribersUrl   string      `json:"subscribers_url"`
				SubscriptionUrl  string      `json:"subscription_url"`
				CommitsUrl       string      `json:"commits_url"`
				GitCommitsUrl    string      `json:"git_commits_url"`
				CommentsUrl      string      `json:"comments_url"`
				IssueCommentUrl  string      `json:"issue_comment_url"`
				ContentsUrl      string      `json:"contents_url"`
				CompareUrl       string      `json:"compare_url"`
				MergesUrl        string      `json:"merges_url"`
				ArchiveUrl       string      `json:"archive_url"`
				DownloadsUrl     string      `json:"downloads_url"`
				IssuesUrl        string      `json:"issues_url"`
				PullsUrl         string      `json:"pulls_url"`
				MilestonesUrl    string      `json:"milestones_url"`
				NotificationsUrl string      `json:"notifications_url"`
				LabelsUrl        string      `json:"labels_url"`
				ReleasesUrl      string      `json:"releases_url"`
				DeploymentsUrl   string      `json:"deployments_url"`
				CreatedAt        time.Time   `json:"created_at"`
				UpdatedAt        time.Time   `json:"updated_at"`
				PushedAt         time.Time   `json:"pushed_at"`
				GitUrl           string      `json:"git_url"`
				SshUrl           string      `json:"ssh_url"`
				CloneUrl         string      `json:"clone_url"`
				SvnUrl           string      `json:"svn_url"`
				Homepage         interface{} `json:"homepage"`
				Size             int         `json:"size"`
				StargazersCount  int         `json:"stargazers_count"`
				WatchersCount    int         `json:"watchers_count"`
				Language         string      `json:"language"`
				HasIssues        bool        `json:"has_issues"`
				HasProjects      bool        `json:"has_projects"`
				HasDownloads     bool        `json:"has_downloads"`
				HasWiki          bool        `json:"has_wiki"`
				HasPages         bool        `json:"has_pages"`
				HasDiscussions   bool        `json:"has_discussions"`
				ForksCount       int         `json:"forks_count"`
				MirrorUrl        interface{} `json:"mirror_url"`
				Archived         bool        `json:"archived"`
				Disabled         bool        `json:"disabled"`
				OpenIssuesCount  int         `json:"open_issues_count"`
				License          struct {
					Key    string `json:"key"`
					Name   string `json:"name"`
					SpdxId string `json:"spdx_id"`
					Url    string `json:"url"`
					NodeId string `json:"node_id"`
				} `json:"license"`
				AllowForking              bool          `json:"allow_forking"`
				IsTemplate                bool          `json:"is_template"`
				WebCommitSignoffRequired  bool          `json:"web_commit_signoff_required"`
				Topics                    []interface{} `json:"topics"`
				Visibility                string        `json:"visibility"`
				Forks                     int           `json:"forks"`
				OpenIssues                int           `json:"open_issues"`
				Watchers                  int           `json:"watchers"`
				DefaultBranch             string        `json:"default_branch"`
				AllowSquashMerge          bool          `json:"allow_squash_merge"`
				AllowMergeCommit          bool          `json:"allow_merge_commit"`
				AllowRebaseMerge          bool          `json:"allow_rebase_merge"`
				AllowAutoMerge            bool          `json:"allow_auto_merge"`
				DeleteBranchOnMerge       bool          `json:"delete_branch_on_merge"`
				AllowUpdateBranch         bool          `json:"allow_update_branch"`
				UseSquashPrTitleAsDefault bool          `json:"use_squash_pr_title_as_default"`
				SquashMergeCommitMessage  string        `json:"squash_merge_commit_message"`
				SquashMergeCommitTitle    string        `json:"squash_merge_commit_title"`
				MergeCommitMessage        string        `json:"merge_commit_message"`
				MergeCommitTitle          string        `json:"merge_commit_title"`
			} `json:"repo"`
		} `json:"head"`
		Base struct {
			Label string `json:"label"`
			Ref   string `json:"ref"`
			Sha   string `json:"sha"`
			User  struct {
				Login             string `json:"login"`
				Id                int    `json:"id"`
				NodeId            string `json:"node_id"`
				AvatarUrl         string `json:"avatar_url"`
				GravatarId        string `json:"gravatar_id"`
				Url               string `json:"url"`
				HtmlUrl           string `json:"html_url"`
				FollowersUrl      string `json:"followers_url"`
				FollowingUrl      string `json:"following_url"`
				GistsUrl          string `json:"gists_url"`
				StarredUrl        string `json:"starred_url"`
				SubscriptionsUrl  string `json:"subscriptions_url"`
				OrganizationsUrl  string `json:"organizations_url"`
				ReposUrl          string `json:"repos_url"`
				EventsUrl         string `json:"events_url"`
				ReceivedEventsUrl string `json:"received_events_url"`
				Type              string `json:"type"`
				UserViewType      string `json:"user_view_type"`
				SiteAdmin         bool   `json:"site_admin"`
			} `json:"user"`
			Repo struct {
				Id       int    `json:"id"`
				NodeId   string `json:"node_id"`
				Name     string `json:"name"`
				FullName string `json:"full_name"`
				Private  bool   `json:"private"`
				Owner    struct {
					Login             string `json:"login"`
					Id                int    `json:"id"`
					NodeId            string `json:"node_id"`
					AvatarUrl         string `json:"avatar_url"`
					GravatarId        string `json:"gravatar_id"`
					Url               string `json:"url"`
					HtmlUrl           string `json:"html_url"`
					FollowersUrl      string `json:"followers_url"`
					FollowingUrl      string `json:"following_url"`
					GistsUrl          string `json:"gists_url"`
					StarredUrl        string `json:"starred_url"`
					SubscriptionsUrl  string `json:"subscriptions_url"`
					OrganizationsUrl  string `json:"organizations_url"`
					ReposUrl          string `json:"repos_url"`
					EventsUrl         string `json:"events_url"`
					ReceivedEventsUrl string `json:"received_events_url"`
					Type              string `json:"type"`
					UserViewType      string `json:"user_view_type"`
					SiteAdmin         bool   `json:"site_admin"`
				} `json:"owner"`
				HtmlUrl          string      `json:"html_url"`
				Description      string      `json:"description"`
				Fork             bool        `json:"fork"`
				Url              string      `json:"url"`
				ForksUrl         string      `json:"forks_url"`
				KeysUrl          string      `json:"keys_url"`
				CollaboratorsUrl string      `json:"collaborators_url"`
				TeamsUrl         string      `json:"teams_url"`
				HooksUrl         string      `json:"hooks_url"`
				IssueEventsUrl   string      `json:"issue_events_url"`
				EventsUrl        string      `json:"events_url"`
				AssigneesUrl     string      `json:"assignees_url"`
				BranchesUrl      string      `json:"branches_url"`
				TagsUrl          string      `json:"tags_url"`
				BlobsUrl         string      `json:"blobs_url"`
				GitTagsUrl       string      `json:"git_tags_url"`
				GitRefsUrl       string      `json:"git_refs_url"`
				TreesUrl         string      `json:"trees_url"`
				StatusesUrl      string      `json:"statuses_url"`
				LanguagesUrl     string      `json:"languages_url"`
				StargazersUrl    string      `json:"stargazers_url"`
				ContributorsUrl  string      `json:"contributors_url"`
				SubscribersUrl   string      `json:"subscribers_url"`
				SubscriptionUrl  string      `json:"subscription_url"`
				CommitsUrl       string      `json:"commits_url"`
				GitCommitsUrl    string      `json:"git_commits_url"`
				CommentsUrl      string      `json:"comments_url"`
				IssueCommentUrl  string      `json:"issue_comment_url"`
				ContentsUrl      string      `json:"contents_url"`
				CompareUrl       string      `json:"compare_url"`
				MergesUrl        string      `json:"merges_url"`
				ArchiveUrl       string      `json:"archive_url"`
				DownloadsUrl     string      `json:"downloads_url"`
				IssuesUrl        string      `json:"issues_url"`
				PullsUrl         string      `json:"pulls_url"`
				MilestonesUrl    string      `json:"milestones_url"`
				NotificationsUrl string      `json:"notifications_url"`
				LabelsUrl        string      `json:"labels_url"`
				ReleasesUrl      string      `json:"releases_url"`
				DeploymentsUrl   string      `json:"deployments_url"`
				CreatedAt        time.Time   `json:"created_at"`
				UpdatedAt        time.Time   `json:"updated_at"`
				PushedAt         time.Time   `json:"pushed_at"`
				GitUrl           string      `json:"git_url"`
				SshUrl           string      `json:"ssh_url"`
				CloneUrl         string      `json:"clone_url"`
				SvnUrl           string      `json:"svn_url"`
				Homepage         interface{} `json:"homepage"`
				Size             int         `json:"size"`
				StargazersCount  int         `json:"stargazers_count"`
				WatchersCount    int         `json:"watchers_count"`
				Language         string      `json:"language"`
				HasIssues        bool        `json:"has_issues"`
				HasProjects      bool        `json:"has_projects"`
				HasDownloads     bool        `json:"has_downloads"`
				HasWiki          bool        `json:"has_wiki"`
				HasPages         bool        `json:"has_pages"`
				HasDiscussions   bool        `json:"has_discussions"`
				ForksCount       int         `json:"forks_count"`
				MirrorUrl        interface{} `json:"mirror_url"`
				Archived         bool        `json:"archived"`
				Disabled         bool        `json:"disabled"`
				OpenIssuesCount  int         `json:"open_issues_count"`
				License          struct {
					Key    string `json:"key"`
					Name   string `json:"name"`
					SpdxId string `json:"spdx_id"`
					Url    string `json:"url"`
					NodeId string `json:"node_id"`
				} `json:"license"`
				AllowForking              bool          `json:"allow_forking"`
				IsTemplate                bool          `json:"is_template"`
				WebCommitSignoffRequired  bool          `json:"web_commit_signoff_required"`
				Topics                    []interface{} `json:"topics"`
				Visibility                string        `json:"visibility"`
				Forks                     int           `json:"forks"`
				OpenIssues                int           `json:"open_issues"`
				Watchers                  int           `json:"watchers"`
				DefaultBranch             string        `json:"default_branch"`
				AllowSquashMerge          bool          `json:"allow_squash_merge"`
				AllowMergeCommit          bool          `json:"allow_merge_commit"`
				AllowRebaseMerge          bool          `json:"allow_rebase_merge"`
				AllowAutoMerge            bool          `json:"allow_auto_merge"`
				DeleteBranchOnMerge       bool          `json:"delete_branch_on_merge"`
				AllowUpdateBranch         bool          `json:"allow_update_branch"`
				UseSquashPrTitleAsDefault bool          `json:"use_squash_pr_title_as_default"`
				SquashMergeCommitMessage  string        `json:"squash_merge_commit_message"`
				SquashMergeCommitTitle    string        `json:"squash_merge_commit_title"`
				MergeCommitMessage        string        `json:"merge_commit_message"`
				MergeCommitTitle          string        `json:"merge_commit_title"`
			} `json:"repo"`
		} `json:"base"`
		Links struct {
			Self struct {
				Href string `json:"href"`
			} `json:"self"`
			Html struct {
				Href string `json:"href"`
			} `json:"html"`
			Issue struct {
				Href string `json:"href"`
			} `json:"issue"`
			Comments struct {
				Href string `json:"href"`
			} `json:"comments"`
			ReviewComments struct {
				Href string `json:"href"`
			} `json:"review_comments"`
			ReviewComment struct {
				Href string `json:"href"`
			} `json:"review_comment"`
			Commits struct {
				Href string `json:"href"`
			} `json:"commits"`
			Statuses struct {
				Href string `json:"href"`
			} `json:"statuses"`
		} `json:"_links"`
		AuthorAssociation   string      `json:"author_association"`
		AutoMerge           interface{} `json:"auto_merge"`
		ActiveLockReason    interface{} `json:"active_lock_reason"`
		Merged              bool        `json:"merged"`
		Mergeable           interface{} `json:"mergeable"`
		Rebaseable          interface{} `json:"rebaseable"`
		MergeableState      string      `json:"mergeable_state"`
		MergedBy            interface{} `json:"merged_by"`
		Comments            int         `json:"comments"`
		ReviewComments      int         `json:"review_comments"`
		MaintainerCanModify bool        `json:"maintainer_can_modify"`
		Commits             int         `json:"commits"`
		Additions           int         `json:"additions"`
		Deletions           int         `json:"deletions"`
		ChangedFiles        int         `json:"changed_files"`
	} `json:"pull_request"`
	Repository struct {
		Id       int    `json:"id"`
		NodeId   string `json:"node_id"`
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		Private  bool   `json:"private"`
		Owner    struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"owner"`
		HtmlUrl          string      `json:"html_url"`
		Description      string      `json:"description"`
		Fork             bool        `json:"fork"`
		Url              string      `json:"url"`
		ForksUrl         string      `json:"forks_url"`
		KeysUrl          string      `json:"keys_url"`
		CollaboratorsUrl string      `json:"collaborators_url"`
		TeamsUrl         string      `json:"teams_url"`
		HooksUrl         string      `json:"hooks_url"`
		IssueEventsUrl   string      `json:"issue_events_url"`
		EventsUrl        string      `json:"events_url"`
		AssigneesUrl     string      `json:"assignees_url"`
		BranchesUrl      string      `json:"branches_url"`
		TagsUrl          string      `json:"tags_url"`
		BlobsUrl         string      `json:"blobs_url"`
		GitTagsUrl       string      `json:"git_tags_url"`
		GitRefsUrl       string      `json:"git_refs_url"`
		TreesUrl         string      `json:"trees_url"`
		StatusesUrl      string      `json:"statuses_url"`
		LanguagesUrl     string      `json:"languages_url"`
		StargazersUrl    string      `json:"stargazers_url"`
		ContributorsUrl  string      `json:"contributors_url"`
		SubscribersUrl   string      `json:"subscribers_url"`
		SubscriptionUrl  string      `json:"subscription_url"`
		CommitsUrl       string      `json:"commits_url"`
		GitCommitsUrl    string      `json:"git_commits_url"`
		CommentsUrl      string      `json:"comments_url"`
		IssueCommentUrl  string      `json:"issue_comment_url"`
		ContentsUrl      string      `json:"contents_url"`
		CompareUrl       string      `json:"compare_url"`
		MergesUrl        string      `json:"merges_url"`
		ArchiveUrl       string      `json:"archive_url"`
		DownloadsUrl     string      `json:"downloads_url"`
		IssuesUrl        string      `json:"issues_url"`
		PullsUrl         string      `json:"pulls_url"`
		MilestonesUrl    string      `json:"milestones_url"`
		NotificationsUrl string      `json:"notifications_url"`
		LabelsUrl        string      `json:"labels_url"`
		ReleasesUrl      string      `json:"releases_url"`
		DeploymentsUrl   string      `json:"deployments_url"`
		CreatedAt        time.Time   `json:"created_at"`
		UpdatedAt        time.Time   `json:"updated_at"`
		PushedAt         time.Time   `json:"pushed_at"`
		GitUrl           string      `json:"git_url"`
		SshUrl           string      `json:"ssh_url"`
		CloneUrl         string      `json:"clone_url"`
		SvnUrl           string      `json:"svn_url"`
		Homepage         interface{} `json:"homepage"`
		Size             int         `json:"size"`
		StargazersCount  int         `json:"stargazers_count"`
		WatchersCount    int         `json:"watchers_count"`
		Language         string      `json:"language"`
		HasIssues        bool        `json:"has_issues"`
		HasProjects      bool        `json:"has_projects"`
		HasDownloads     bool        `json:"has_downloads"`
		HasWiki          bool        `json:"has_wiki"`
		HasPages         bool        `json:"has_pages"`
		HasDiscussions   bool        `json:"has_discussions"`
		ForksCount       int         `json:"forks_count"`
		MirrorUrl        interface{} `json:"mirror_url"`
		Archived         bool        `json:"archived"`
		Disabled         bool        `json:"disabled"`
		OpenIssuesCount  int         `json:"open_issues_count"`
		License          struct {
			Key    string `json:"key"`
			Name   string `json:"name"`
			SpdxId string `json:"spdx_id"`
			Url    string `json:"url"`
			NodeId string `json:"node_id"`
		} `json:"license"`
		AllowForking             bool          `json:"allow_forking"`
		IsTemplate               bool          `json:"is_template"`
		WebCommitSignoffRequired bool          `json:"web_commit_signoff_required"`
		Topics                   []interface{} `json:"topics"`
		Visibility               string        `json:"visibility"`
		Forks                    int           `json:"forks"`
		OpenIssues               int           `json:"open_issues"`
		Watchers                 int           `json:"watchers"`
		DefaultBranch            string        `json:"default_branch"`
	} `json:"repository"`
	Sender struct {
		Login             string `json:"login"`
		Id                int    `json:"id"`
		NodeId            string `json:"node_id"`
		AvatarUrl         string `json:"avatar_url"`
		GravatarId        string `json:"gravatar_id"`
		Url               string `json:"url"`
		HtmlUrl           string `json:"html_url"`
		FollowersUrl      string `json:"followers_url"`
		FollowingUrl      string `json:"following_url"`
		GistsUrl          string `json:"gists_url"`
		StarredUrl        string `json:"starred_url"`
		SubscriptionsUrl  string `json:"subscriptions_url"`
		OrganizationsUrl  string `json:"organizations_url"`
		ReposUrl          string `json:"repos_url"`
		EventsUrl         string `json:"events_url"`
		ReceivedEventsUrl string `json:"received_events_url"`
		Type              string `json:"type"`
		UserViewType      string `json:"user_view_type"`
		SiteAdmin         bool   `json:"site_admin"`
	} `json:"sender"`
	Installation struct {
		Id     int64  `json:"id"`
		NodeId string `json:"node_id"`
	} `json:"installation"`
}

func (p *GithubPullRequestPayload) Validate() error {
	if p.Action == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "action")
	}
	if p.Number <= 0 {
		return errors.New(codes.ErrInvalidParameter, "number", p.Number)
	}
	if p.Installation.Id <= 0 {
		return errors.New(codes.ErrInvalidParameter, "installation.id", p.Installation.Id)
	}
	if p.PullRequest.User.Login == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "pull_request.user.login")
	}
	if p.Repository.Name == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "repository.name")
	}
	if p.Repository.CloneUrl == "" {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "repository.clone_url")
	}
	return nil
}

type GithubEvent string

const (
	GithubPingEvent                     GithubEvent = "ping"
	GithubInstallationEvent             GithubEvent = "installation"
	GithubPullRequestEvent              GithubEvent = "pull_request"
	GithubPullRequestReviewCommentEvent GithubEvent = "pull_request_review_comment"
	GithubIssueCommentEvent             GithubEvent = "issue_comment"
)

type GithubIssueCommentPayload struct {
	Action string `json:"action"`
	Issue  struct {
		Url           string `json:"url"`
		RepositoryUrl string `json:"repository_url"`
		LabelsUrl     string `json:"labels_url"`
		CommentsUrl   string `json:"comments_url"`
		EventsUrl     string `json:"events_url"`
		HtmlUrl       string `json:"html_url"`
		Id            int64  `json:"id"`
		NodeId        string `json:"node_id"`
		Number        int    `json:"number"`
		Title         string `json:"title"`
		User          struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"user"`
		Labels            []interface{} `json:"labels"`
		State             string        `json:"state"`
		Locked            bool          `json:"locked"`
		Assignee          interface{}   `json:"assignee"`
		Assignees         []interface{} `json:"assignees"`
		Milestone         interface{}   `json:"milestone"`
		Comments          int           `json:"comments"`
		CreatedAt         time.Time     `json:"created_at"`
		UpdatedAt         time.Time     `json:"updated_at"`
		ClosedAt          time.Time     `json:"closed_at"`
		AuthorAssociation string        `json:"author_association"`
		SubIssuesSummary  struct {
			Total            int `json:"total"`
			Completed        int `json:"completed"`
			PercentCompleted int `json:"percent_completed"`
		} `json:"sub_issues_summary"`
		ActiveLockReason interface{} `json:"active_lock_reason"`
		Draft            bool        `json:"draft"`
		PullRequest      struct {
			Url      string      `json:"url"`
			HtmlUrl  string      `json:"html_url"`
			DiffUrl  string      `json:"diff_url"`
			PatchUrl string      `json:"patch_url"`
			MergedAt interface{} `json:"merged_at"`
		} `json:"pull_request"`
		Body      string `json:"body"`
		Reactions struct {
			Url        string `json:"url"`
			TotalCount int    `json:"total_count"`
			Field3     int    `json:"+1"`
			Field4     int    `json:"-1"`
			Laugh      int    `json:"laugh"`
			Hooray     int    `json:"hooray"`
			Confused   int    `json:"confused"`
			Heart      int    `json:"heart"`
			Rocket     int    `json:"rocket"`
			Eyes       int    `json:"eyes"`
		} `json:"reactions"`
		TimelineUrl           string      `json:"timeline_url"`
		PerformedViaGithubApp interface{} `json:"performed_via_github_app"`
		StateReason           interface{} `json:"state_reason"`
	} `json:"issue"`
	Comment struct {
		Url      string `json:"url"`
		HtmlUrl  string `json:"html_url"`
		IssueUrl string `json:"issue_url"`
		Id       int64  `json:"id"`
		NodeId   string `json:"node_id"`
		User     struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"user"`
		CreatedAt         time.Time `json:"created_at"`
		UpdatedAt         time.Time `json:"updated_at"`
		AuthorAssociation string    `json:"author_association"`
		Body              string    `json:"body"`
		Reactions         struct {
			Url        string `json:"url"`
			TotalCount int    `json:"total_count"`
			Field3     int    `json:"+1"`
			Field4     int    `json:"-1"`
			Laugh      int    `json:"laugh"`
			Hooray     int    `json:"hooray"`
			Confused   int    `json:"confused"`
			Heart      int    `json:"heart"`
			Rocket     int    `json:"rocket"`
			Eyes       int    `json:"eyes"`
		} `json:"reactions"`
		PerformedViaGithubApp interface{} `json:"performed_via_github_app"`
	} `json:"comment"`
	Repository struct {
		Id       int    `json:"id"`
		NodeId   string `json:"node_id"`
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		Private  bool   `json:"private"`
		Owner    struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"owner"`
		HtmlUrl          string      `json:"html_url"`
		Description      string      `json:"description"`
		Fork             bool        `json:"fork"`
		Url              string      `json:"url"`
		ForksUrl         string      `json:"forks_url"`
		KeysUrl          string      `json:"keys_url"`
		CollaboratorsUrl string      `json:"collaborators_url"`
		TeamsUrl         string      `json:"teams_url"`
		HooksUrl         string      `json:"hooks_url"`
		IssueEventsUrl   string      `json:"issue_events_url"`
		EventsUrl        string      `json:"events_url"`
		AssigneesUrl     string      `json:"assignees_url"`
		BranchesUrl      string      `json:"branches_url"`
		TagsUrl          string      `json:"tags_url"`
		BlobsUrl         string      `json:"blobs_url"`
		GitTagsUrl       string      `json:"git_tags_url"`
		GitRefsUrl       string      `json:"git_refs_url"`
		TreesUrl         string      `json:"trees_url"`
		StatusesUrl      string      `json:"statuses_url"`
		LanguagesUrl     string      `json:"languages_url"`
		StargazersUrl    string      `json:"stargazers_url"`
		ContributorsUrl  string      `json:"contributors_url"`
		SubscribersUrl   string      `json:"subscribers_url"`
		SubscriptionUrl  string      `json:"subscription_url"`
		CommitsUrl       string      `json:"commits_url"`
		GitCommitsUrl    string      `json:"git_commits_url"`
		CommentsUrl      string      `json:"comments_url"`
		IssueCommentUrl  string      `json:"issue_comment_url"`
		ContentsUrl      string      `json:"contents_url"`
		CompareUrl       string      `json:"compare_url"`
		MergesUrl        string      `json:"merges_url"`
		ArchiveUrl       string      `json:"archive_url"`
		DownloadsUrl     string      `json:"downloads_url"`
		IssuesUrl        string      `json:"issues_url"`
		PullsUrl         string      `json:"pulls_url"`
		MilestonesUrl    string      `json:"milestones_url"`
		NotificationsUrl string      `json:"notifications_url"`
		LabelsUrl        string      `json:"labels_url"`
		ReleasesUrl      string      `json:"releases_url"`
		DeploymentsUrl   string      `json:"deployments_url"`
		CreatedAt        time.Time   `json:"created_at"`
		UpdatedAt        time.Time   `json:"updated_at"`
		PushedAt         time.Time   `json:"pushed_at"`
		GitUrl           string      `json:"git_url"`
		SshUrl           string      `json:"ssh_url"`
		CloneUrl         string      `json:"clone_url"`
		SvnUrl           string      `json:"svn_url"`
		Homepage         interface{} `json:"homepage"`
		Size             int         `json:"size"`
		StargazersCount  int         `json:"stargazers_count"`
		WatchersCount    int         `json:"watchers_count"`
		Language         string      `json:"language"`
		HasIssues        bool        `json:"has_issues"`
		HasProjects      bool        `json:"has_projects"`
		HasDownloads     bool        `json:"has_downloads"`
		HasWiki          bool        `json:"has_wiki"`
		HasPages         bool        `json:"has_pages"`
		HasDiscussions   bool        `json:"has_discussions"`
		ForksCount       int         `json:"forks_count"`
		MirrorUrl        interface{} `json:"mirror_url"`
		Archived         bool        `json:"archived"`
		Disabled         bool        `json:"disabled"`
		OpenIssuesCount  int         `json:"open_issues_count"`
		License          struct {
			Key    string `json:"key"`
			Name   string `json:"name"`
			SpdxId string `json:"spdx_id"`
			Url    string `json:"url"`
			NodeId string `json:"node_id"`
		} `json:"license"`
		AllowForking             bool          `json:"allow_forking"`
		IsTemplate               bool          `json:"is_template"`
		WebCommitSignoffRequired bool          `json:"web_commit_signoff_required"`
		Topics                   []interface{} `json:"topics"`
		Visibility               string        `json:"visibility"`
		Forks                    int           `json:"forks"`
		OpenIssues               int           `json:"open_issues"`
		Watchers                 int           `json:"watchers"`
		DefaultBranch            string        `json:"default_branch"`
	} `json:"repository"`
	Sender struct {
		Login             string `json:"login"`
		Id                int    `json:"id"`
		NodeId            string `json:"node_id"`
		AvatarUrl         string `json:"avatar_url"`
		GravatarId        string `json:"gravatar_id"`
		Url               string `json:"url"`
		HtmlUrl           string `json:"html_url"`
		FollowersUrl      string `json:"followers_url"`
		FollowingUrl      string `json:"following_url"`
		GistsUrl          string `json:"gists_url"`
		StarredUrl        string `json:"starred_url"`
		SubscriptionsUrl  string `json:"subscriptions_url"`
		OrganizationsUrl  string `json:"organizations_url"`
		ReposUrl          string `json:"repos_url"`
		EventsUrl         string `json:"events_url"`
		ReceivedEventsUrl string `json:"received_events_url"`
		Type              string `json:"type"`
		UserViewType      string `json:"user_view_type"`
		SiteAdmin         bool   `json:"site_admin"`
	} `json:"sender"`
	Installation struct {
		Id     int64  `json:"id"`
		NodeId string `json:"node_id"`
	} `json:"installation"`
}

func (p *GithubIssueCommentPayload) Validate() error {
	if p.Installation.Id <= 0 {
		return errors.New(codes.ErrInvalidParameter, "installation.id", p.Installation.Id)
	}
	return nil
}

type GithubPullRequestReviewCommentPayload struct {
	Action  string `json:"action"`
	Comment struct {
		Url                 string `json:"url"`
		PullRequestReviewId int64  `json:"pull_request_review_id"`
		Id                  int64  `json:"id"`
		NodeId              string `json:"node_id"`
		DiffHunk            string `json:"diff_hunk"`
		Path                string `json:"path"`
		CommitId            string `json:"commit_id"`
		OriginalCommitId    string `json:"original_commit_id"`
		User                struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"user"`
		Body              string    `json:"body"`
		CreatedAt         time.Time `json:"created_at"`
		UpdatedAt         time.Time `json:"updated_at"`
		HtmlUrl           string    `json:"html_url"`
		PullRequestUrl    string    `json:"pull_request_url"`
		AuthorAssociation string    `json:"author_association"`
		Links             struct {
			Self struct {
				Href string `json:"href"`
			} `json:"self"`
			Html struct {
				Href string `json:"href"`
			} `json:"html"`
			PullRequest struct {
				Href string `json:"href"`
			} `json:"pull_request"`
		} `json:"_links"`
		Reactions struct {
			Url        string `json:"url"`
			TotalCount int    `json:"total_count"`
			Field3     int    `json:"+1"`
			Field4     int    `json:"-1"`
			Laugh      int    `json:"laugh"`
			Hooray     int    `json:"hooray"`
			Confused   int    `json:"confused"`
			Heart      int    `json:"heart"`
			Rocket     int    `json:"rocket"`
			Eyes       int    `json:"eyes"`
		} `json:"reactions"`
		StartLine         interface{} `json:"start_line"`
		OriginalStartLine interface{} `json:"original_start_line"`
		StartSide         interface{} `json:"start_side"`
		Line              int         `json:"line"`
		OriginalLine      int         `json:"original_line"`
		Side              string      `json:"side"`
		OriginalPosition  int         `json:"original_position"`
		Position          int         `json:"position"`
		SubjectType       string      `json:"subject_type"`
	} `json:"comment"`
	PullRequest struct {
		Url      string `json:"url"`
		Id       int64  `json:"id"`
		NodeId   string `json:"node_id"`
		HtmlUrl  string `json:"html_url"`
		DiffUrl  string `json:"diff_url"`
		PatchUrl string `json:"patch_url"`
		IssueUrl string `json:"issue_url"`
		Number   int    `json:"number"`
		State    string `json:"state"`
		Locked   bool   `json:"locked"`
		Title    string `json:"title"`
		User     struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"user"`
		Body               interface{}   `json:"body"`
		CreatedAt          time.Time     `json:"created_at"`
		UpdatedAt          time.Time     `json:"updated_at"`
		ClosedAt           interface{}   `json:"closed_at"`
		MergedAt           interface{}   `json:"merged_at"`
		MergeCommitSha     string        `json:"merge_commit_sha"`
		Assignee           interface{}   `json:"assignee"`
		Assignees          []interface{} `json:"assignees"`
		RequestedReviewers []interface{} `json:"requested_reviewers"`
		RequestedTeams     []interface{} `json:"requested_teams"`
		Labels             []interface{} `json:"labels"`
		Milestone          interface{}   `json:"milestone"`
		Draft              bool          `json:"draft"`
		CommitsUrl         string        `json:"commits_url"`
		ReviewCommentsUrl  string        `json:"review_comments_url"`
		ReviewCommentUrl   string        `json:"review_comment_url"`
		CommentsUrl        string        `json:"comments_url"`
		StatusesUrl        string        `json:"statuses_url"`
		Head               struct {
			Label string `json:"label"`
			Ref   string `json:"ref"`
			Sha   string `json:"sha"`
			User  struct {
				Login             string `json:"login"`
				Id                int    `json:"id"`
				NodeId            string `json:"node_id"`
				AvatarUrl         string `json:"avatar_url"`
				GravatarId        string `json:"gravatar_id"`
				Url               string `json:"url"`
				HtmlUrl           string `json:"html_url"`
				FollowersUrl      string `json:"followers_url"`
				FollowingUrl      string `json:"following_url"`
				GistsUrl          string `json:"gists_url"`
				StarredUrl        string `json:"starred_url"`
				SubscriptionsUrl  string `json:"subscriptions_url"`
				OrganizationsUrl  string `json:"organizations_url"`
				ReposUrl          string `json:"repos_url"`
				EventsUrl         string `json:"events_url"`
				ReceivedEventsUrl string `json:"received_events_url"`
				Type              string `json:"type"`
				UserViewType      string `json:"user_view_type"`
				SiteAdmin         bool   `json:"site_admin"`
			} `json:"user"`
			Repo struct {
				Id       int    `json:"id"`
				NodeId   string `json:"node_id"`
				Name     string `json:"name"`
				FullName string `json:"full_name"`
				Private  bool   `json:"private"`
				Owner    struct {
					Login             string `json:"login"`
					Id                int    `json:"id"`
					NodeId            string `json:"node_id"`
					AvatarUrl         string `json:"avatar_url"`
					GravatarId        string `json:"gravatar_id"`
					Url               string `json:"url"`
					HtmlUrl           string `json:"html_url"`
					FollowersUrl      string `json:"followers_url"`
					FollowingUrl      string `json:"following_url"`
					GistsUrl          string `json:"gists_url"`
					StarredUrl        string `json:"starred_url"`
					SubscriptionsUrl  string `json:"subscriptions_url"`
					OrganizationsUrl  string `json:"organizations_url"`
					ReposUrl          string `json:"repos_url"`
					EventsUrl         string `json:"events_url"`
					ReceivedEventsUrl string `json:"received_events_url"`
					Type              string `json:"type"`
					UserViewType      string `json:"user_view_type"`
					SiteAdmin         bool   `json:"site_admin"`
				} `json:"owner"`
				HtmlUrl                   string        `json:"html_url"`
				Description               interface{}   `json:"description"`
				Fork                      bool          `json:"fork"`
				Url                       string        `json:"url"`
				ForksUrl                  string        `json:"forks_url"`
				KeysUrl                   string        `json:"keys_url"`
				CollaboratorsUrl          string        `json:"collaborators_url"`
				TeamsUrl                  string        `json:"teams_url"`
				HooksUrl                  string        `json:"hooks_url"`
				IssueEventsUrl            string        `json:"issue_events_url"`
				EventsUrl                 string        `json:"events_url"`
				AssigneesUrl              string        `json:"assignees_url"`
				BranchesUrl               string        `json:"branches_url"`
				TagsUrl                   string        `json:"tags_url"`
				BlobsUrl                  string        `json:"blobs_url"`
				GitTagsUrl                string        `json:"git_tags_url"`
				GitRefsUrl                string        `json:"git_refs_url"`
				TreesUrl                  string        `json:"trees_url"`
				StatusesUrl               string        `json:"statuses_url"`
				LanguagesUrl              string        `json:"languages_url"`
				StargazersUrl             string        `json:"stargazers_url"`
				ContributorsUrl           string        `json:"contributors_url"`
				SubscribersUrl            string        `json:"subscribers_url"`
				SubscriptionUrl           string        `json:"subscription_url"`
				CommitsUrl                string        `json:"commits_url"`
				GitCommitsUrl             string        `json:"git_commits_url"`
				CommentsUrl               string        `json:"comments_url"`
				IssueCommentUrl           string        `json:"issue_comment_url"`
				ContentsUrl               string        `json:"contents_url"`
				CompareUrl                string        `json:"compare_url"`
				MergesUrl                 string        `json:"merges_url"`
				ArchiveUrl                string        `json:"archive_url"`
				DownloadsUrl              string        `json:"downloads_url"`
				IssuesUrl                 string        `json:"issues_url"`
				PullsUrl                  string        `json:"pulls_url"`
				MilestonesUrl             string        `json:"milestones_url"`
				NotificationsUrl          string        `json:"notifications_url"`
				LabelsUrl                 string        `json:"labels_url"`
				ReleasesUrl               string        `json:"releases_url"`
				DeploymentsUrl            string        `json:"deployments_url"`
				CreatedAt                 time.Time     `json:"created_at"`
				UpdatedAt                 time.Time     `json:"updated_at"`
				PushedAt                  time.Time     `json:"pushed_at"`
				GitUrl                    string        `json:"git_url"`
				SshUrl                    string        `json:"ssh_url"`
				CloneUrl                  string        `json:"clone_url"`
				SvnUrl                    string        `json:"svn_url"`
				Homepage                  interface{}   `json:"homepage"`
				Size                      int           `json:"size"`
				StargazersCount           int           `json:"stargazers_count"`
				WatchersCount             int           `json:"watchers_count"`
				Language                  string        `json:"language"`
				HasIssues                 bool          `json:"has_issues"`
				HasProjects               bool          `json:"has_projects"`
				HasDownloads              bool          `json:"has_downloads"`
				HasWiki                   bool          `json:"has_wiki"`
				HasPages                  bool          `json:"has_pages"`
				HasDiscussions            bool          `json:"has_discussions"`
				ForksCount                int           `json:"forks_count"`
				MirrorUrl                 interface{}   `json:"mirror_url"`
				Archived                  bool          `json:"archived"`
				Disabled                  bool          `json:"disabled"`
				OpenIssuesCount           int           `json:"open_issues_count"`
				License                   interface{}   `json:"license"`
				AllowForking              bool          `json:"allow_forking"`
				IsTemplate                bool          `json:"is_template"`
				WebCommitSignoffRequired  bool          `json:"web_commit_signoff_required"`
				Topics                    []interface{} `json:"topics"`
				Visibility                string        `json:"visibility"`
				Forks                     int           `json:"forks"`
				OpenIssues                int           `json:"open_issues"`
				Watchers                  int           `json:"watchers"`
				DefaultBranch             string        `json:"default_branch"`
				AllowSquashMerge          bool          `json:"allow_squash_merge"`
				AllowMergeCommit          bool          `json:"allow_merge_commit"`
				AllowRebaseMerge          bool          `json:"allow_rebase_merge"`
				AllowAutoMerge            bool          `json:"allow_auto_merge"`
				DeleteBranchOnMerge       bool          `json:"delete_branch_on_merge"`
				AllowUpdateBranch         bool          `json:"allow_update_branch"`
				UseSquashPrTitleAsDefault bool          `json:"use_squash_pr_title_as_default"`
				SquashMergeCommitMessage  string        `json:"squash_merge_commit_message"`
				SquashMergeCommitTitle    string        `json:"squash_merge_commit_title"`
				MergeCommitMessage        string        `json:"merge_commit_message"`
				MergeCommitTitle          string        `json:"merge_commit_title"`
			} `json:"repo"`
		} `json:"head"`
		Base struct {
			Label string `json:"label"`
			Ref   string `json:"ref"`
			Sha   string `json:"sha"`
			User  struct {
				Login             string `json:"login"`
				Id                int    `json:"id"`
				NodeId            string `json:"node_id"`
				AvatarUrl         string `json:"avatar_url"`
				GravatarId        string `json:"gravatar_id"`
				Url               string `json:"url"`
				HtmlUrl           string `json:"html_url"`
				FollowersUrl      string `json:"followers_url"`
				FollowingUrl      string `json:"following_url"`
				GistsUrl          string `json:"gists_url"`
				StarredUrl        string `json:"starred_url"`
				SubscriptionsUrl  string `json:"subscriptions_url"`
				OrganizationsUrl  string `json:"organizations_url"`
				ReposUrl          string `json:"repos_url"`
				EventsUrl         string `json:"events_url"`
				ReceivedEventsUrl string `json:"received_events_url"`
				Type              string `json:"type"`
				UserViewType      string `json:"user_view_type"`
				SiteAdmin         bool   `json:"site_admin"`
			} `json:"user"`
			Repo struct {
				Id       int    `json:"id"`
				NodeId   string `json:"node_id"`
				Name     string `json:"name"`
				FullName string `json:"full_name"`
				Private  bool   `json:"private"`
				Owner    struct {
					Login             string `json:"login"`
					Id                int    `json:"id"`
					NodeId            string `json:"node_id"`
					AvatarUrl         string `json:"avatar_url"`
					GravatarId        string `json:"gravatar_id"`
					Url               string `json:"url"`
					HtmlUrl           string `json:"html_url"`
					FollowersUrl      string `json:"followers_url"`
					FollowingUrl      string `json:"following_url"`
					GistsUrl          string `json:"gists_url"`
					StarredUrl        string `json:"starred_url"`
					SubscriptionsUrl  string `json:"subscriptions_url"`
					OrganizationsUrl  string `json:"organizations_url"`
					ReposUrl          string `json:"repos_url"`
					EventsUrl         string `json:"events_url"`
					ReceivedEventsUrl string `json:"received_events_url"`
					Type              string `json:"type"`
					UserViewType      string `json:"user_view_type"`
					SiteAdmin         bool   `json:"site_admin"`
				} `json:"owner"`
				HtmlUrl                   string        `json:"html_url"`
				Description               interface{}   `json:"description"`
				Fork                      bool          `json:"fork"`
				Url                       string        `json:"url"`
				ForksUrl                  string        `json:"forks_url"`
				KeysUrl                   string        `json:"keys_url"`
				CollaboratorsUrl          string        `json:"collaborators_url"`
				TeamsUrl                  string        `json:"teams_url"`
				HooksUrl                  string        `json:"hooks_url"`
				IssueEventsUrl            string        `json:"issue_events_url"`
				EventsUrl                 string        `json:"events_url"`
				AssigneesUrl              string        `json:"assignees_url"`
				BranchesUrl               string        `json:"branches_url"`
				TagsUrl                   string        `json:"tags_url"`
				BlobsUrl                  string        `json:"blobs_url"`
				GitTagsUrl                string        `json:"git_tags_url"`
				GitRefsUrl                string        `json:"git_refs_url"`
				TreesUrl                  string        `json:"trees_url"`
				StatusesUrl               string        `json:"statuses_url"`
				LanguagesUrl              string        `json:"languages_url"`
				StargazersUrl             string        `json:"stargazers_url"`
				ContributorsUrl           string        `json:"contributors_url"`
				SubscribersUrl            string        `json:"subscribers_url"`
				SubscriptionUrl           string        `json:"subscription_url"`
				CommitsUrl                string        `json:"commits_url"`
				GitCommitsUrl             string        `json:"git_commits_url"`
				CommentsUrl               string        `json:"comments_url"`
				IssueCommentUrl           string        `json:"issue_comment_url"`
				ContentsUrl               string        `json:"contents_url"`
				CompareUrl                string        `json:"compare_url"`
				MergesUrl                 string        `json:"merges_url"`
				ArchiveUrl                string        `json:"archive_url"`
				DownloadsUrl              string        `json:"downloads_url"`
				IssuesUrl                 string        `json:"issues_url"`
				PullsUrl                  string        `json:"pulls_url"`
				MilestonesUrl             string        `json:"milestones_url"`
				NotificationsUrl          string        `json:"notifications_url"`
				LabelsUrl                 string        `json:"labels_url"`
				ReleasesUrl               string        `json:"releases_url"`
				DeploymentsUrl            string        `json:"deployments_url"`
				CreatedAt                 time.Time     `json:"created_at"`
				UpdatedAt                 time.Time     `json:"updated_at"`
				PushedAt                  time.Time     `json:"pushed_at"`
				GitUrl                    string        `json:"git_url"`
				SshUrl                    string        `json:"ssh_url"`
				CloneUrl                  string        `json:"clone_url"`
				SvnUrl                    string        `json:"svn_url"`
				Homepage                  interface{}   `json:"homepage"`
				Size                      int           `json:"size"`
				StargazersCount           int           `json:"stargazers_count"`
				WatchersCount             int           `json:"watchers_count"`
				Language                  string        `json:"language"`
				HasIssues                 bool          `json:"has_issues"`
				HasProjects               bool          `json:"has_projects"`
				HasDownloads              bool          `json:"has_downloads"`
				HasWiki                   bool          `json:"has_wiki"`
				HasPages                  bool          `json:"has_pages"`
				HasDiscussions            bool          `json:"has_discussions"`
				ForksCount                int           `json:"forks_count"`
				MirrorUrl                 interface{}   `json:"mirror_url"`
				Archived                  bool          `json:"archived"`
				Disabled                  bool          `json:"disabled"`
				OpenIssuesCount           int           `json:"open_issues_count"`
				License                   interface{}   `json:"license"`
				AllowForking              bool          `json:"allow_forking"`
				IsTemplate                bool          `json:"is_template"`
				WebCommitSignoffRequired  bool          `json:"web_commit_signoff_required"`
				Topics                    []interface{} `json:"topics"`
				Visibility                string        `json:"visibility"`
				Forks                     int           `json:"forks"`
				OpenIssues                int           `json:"open_issues"`
				Watchers                  int           `json:"watchers"`
				DefaultBranch             string        `json:"default_branch"`
				AllowSquashMerge          bool          `json:"allow_squash_merge"`
				AllowMergeCommit          bool          `json:"allow_merge_commit"`
				AllowRebaseMerge          bool          `json:"allow_rebase_merge"`
				AllowAutoMerge            bool          `json:"allow_auto_merge"`
				DeleteBranchOnMerge       bool          `json:"delete_branch_on_merge"`
				AllowUpdateBranch         bool          `json:"allow_update_branch"`
				UseSquashPrTitleAsDefault bool          `json:"use_squash_pr_title_as_default"`
				SquashMergeCommitMessage  string        `json:"squash_merge_commit_message"`
				SquashMergeCommitTitle    string        `json:"squash_merge_commit_title"`
				MergeCommitMessage        string        `json:"merge_commit_message"`
				MergeCommitTitle          string        `json:"merge_commit_title"`
			} `json:"repo"`
		} `json:"base"`
		Links struct {
			Self struct {
				Href string `json:"href"`
			} `json:"self"`
			Html struct {
				Href string `json:"href"`
			} `json:"html"`
			Issue struct {
				Href string `json:"href"`
			} `json:"issue"`
			Comments struct {
				Href string `json:"href"`
			} `json:"comments"`
			ReviewComments struct {
				Href string `json:"href"`
			} `json:"review_comments"`
			ReviewComment struct {
				Href string `json:"href"`
			} `json:"review_comment"`
			Commits struct {
				Href string `json:"href"`
			} `json:"commits"`
			Statuses struct {
				Href string `json:"href"`
			} `json:"statuses"`
		} `json:"_links"`
		AuthorAssociation string      `json:"author_association"`
		AutoMerge         interface{} `json:"auto_merge"`
		ActiveLockReason  interface{} `json:"active_lock_reason"`
	} `json:"pull_request"`
	Repository struct {
		Id       int    `json:"id"`
		NodeId   string `json:"node_id"`
		Name     string `json:"name"`
		FullName string `json:"full_name"`
		Private  bool   `json:"private"`
		Owner    struct {
			Login             string `json:"login"`
			Id                int    `json:"id"`
			NodeId            string `json:"node_id"`
			AvatarUrl         string `json:"avatar_url"`
			GravatarId        string `json:"gravatar_id"`
			Url               string `json:"url"`
			HtmlUrl           string `json:"html_url"`
			FollowersUrl      string `json:"followers_url"`
			FollowingUrl      string `json:"following_url"`
			GistsUrl          string `json:"gists_url"`
			StarredUrl        string `json:"starred_url"`
			SubscriptionsUrl  string `json:"subscriptions_url"`
			OrganizationsUrl  string `json:"organizations_url"`
			ReposUrl          string `json:"repos_url"`
			EventsUrl         string `json:"events_url"`
			ReceivedEventsUrl string `json:"received_events_url"`
			Type              string `json:"type"`
			UserViewType      string `json:"user_view_type"`
			SiteAdmin         bool   `json:"site_admin"`
		} `json:"owner"`
		HtmlUrl                  string        `json:"html_url"`
		Description              interface{}   `json:"description"`
		Fork                     bool          `json:"fork"`
		Url                      string        `json:"url"`
		ForksUrl                 string        `json:"forks_url"`
		KeysUrl                  string        `json:"keys_url"`
		CollaboratorsUrl         string        `json:"collaborators_url"`
		TeamsUrl                 string        `json:"teams_url"`
		HooksUrl                 string        `json:"hooks_url"`
		IssueEventsUrl           string        `json:"issue_events_url"`
		EventsUrl                string        `json:"events_url"`
		AssigneesUrl             string        `json:"assignees_url"`
		BranchesUrl              string        `json:"branches_url"`
		TagsUrl                  string        `json:"tags_url"`
		BlobsUrl                 string        `json:"blobs_url"`
		GitTagsUrl               string        `json:"git_tags_url"`
		GitRefsUrl               string        `json:"git_refs_url"`
		TreesUrl                 string        `json:"trees_url"`
		StatusesUrl              string        `json:"statuses_url"`
		LanguagesUrl             string        `json:"languages_url"`
		StargazersUrl            string        `json:"stargazers_url"`
		ContributorsUrl          string        `json:"contributors_url"`
		SubscribersUrl           string        `json:"subscribers_url"`
		SubscriptionUrl          string        `json:"subscription_url"`
		CommitsUrl               string        `json:"commits_url"`
		GitCommitsUrl            string        `json:"git_commits_url"`
		CommentsUrl              string        `json:"comments_url"`
		IssueCommentUrl          string        `json:"issue_comment_url"`
		ContentsUrl              string        `json:"contents_url"`
		CompareUrl               string        `json:"compare_url"`
		MergesUrl                string        `json:"merges_url"`
		ArchiveUrl               string        `json:"archive_url"`
		DownloadsUrl             string        `json:"downloads_url"`
		IssuesUrl                string        `json:"issues_url"`
		PullsUrl                 string        `json:"pulls_url"`
		MilestonesUrl            string        `json:"milestones_url"`
		NotificationsUrl         string        `json:"notifications_url"`
		LabelsUrl                string        `json:"labels_url"`
		ReleasesUrl              string        `json:"releases_url"`
		DeploymentsUrl           string        `json:"deployments_url"`
		CreatedAt                time.Time     `json:"created_at"`
		UpdatedAt                time.Time     `json:"updated_at"`
		PushedAt                 time.Time     `json:"pushed_at"`
		GitUrl                   string        `json:"git_url"`
		SshUrl                   string        `json:"ssh_url"`
		CloneUrl                 string        `json:"clone_url"`
		SvnUrl                   string        `json:"svn_url"`
		Homepage                 interface{}   `json:"homepage"`
		Size                     int           `json:"size"`
		StargazersCount          int           `json:"stargazers_count"`
		WatchersCount            int           `json:"watchers_count"`
		Language                 string        `json:"language"`
		HasIssues                bool          `json:"has_issues"`
		HasProjects              bool          `json:"has_projects"`
		HasDownloads             bool          `json:"has_downloads"`
		HasWiki                  bool          `json:"has_wiki"`
		HasPages                 bool          `json:"has_pages"`
		HasDiscussions           bool          `json:"has_discussions"`
		ForksCount               int           `json:"forks_count"`
		MirrorUrl                interface{}   `json:"mirror_url"`
		Archived                 bool          `json:"archived"`
		Disabled                 bool          `json:"disabled"`
		OpenIssuesCount          int           `json:"open_issues_count"`
		License                  interface{}   `json:"license"`
		AllowForking             bool          `json:"allow_forking"`
		IsTemplate               bool          `json:"is_template"`
		WebCommitSignoffRequired bool          `json:"web_commit_signoff_required"`
		Topics                   []interface{} `json:"topics"`
		Visibility               string        `json:"visibility"`
		Forks                    int           `json:"forks"`
		OpenIssues               int           `json:"open_issues"`
		Watchers                 int           `json:"watchers"`
		DefaultBranch            string        `json:"default_branch"`
	} `json:"repository"`
	Sender struct {
		Login             string `json:"login"`
		Id                int    `json:"id"`
		NodeId            string `json:"node_id"`
		AvatarUrl         string `json:"avatar_url"`
		GravatarId        string `json:"gravatar_id"`
		Url               string `json:"url"`
		HtmlUrl           string `json:"html_url"`
		FollowersUrl      string `json:"followers_url"`
		FollowingUrl      string `json:"following_url"`
		GistsUrl          string `json:"gists_url"`
		StarredUrl        string `json:"starred_url"`
		SubscriptionsUrl  string `json:"subscriptions_url"`
		OrganizationsUrl  string `json:"organizations_url"`
		ReposUrl          string `json:"repos_url"`
		EventsUrl         string `json:"events_url"`
		ReceivedEventsUrl string `json:"received_events_url"`
		Type              string `json:"type"`
		UserViewType      string `json:"user_view_type"`
		SiteAdmin         bool   `json:"site_admin"`
	} `json:"sender"`
	Installation struct {
		Id     int64  `json:"id"`
		NodeId string `json:"node_id"`
	} `json:"installation"`
}

func (p *GithubPullRequestReviewCommentPayload) Validate() error {
	if p.Installation.Id <= 0 {
		return errors.New(codes.ErrInvalidParameter, "installation.id", p.Installation.Id)
	}
	return nil
}
