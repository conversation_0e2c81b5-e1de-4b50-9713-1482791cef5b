package session

import (
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-connect"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type Session struct {
	SessionId string
}

type Info struct {
	SessionId       string    `json:"sessionId"`
	State           State     `json:"state"`
	IdentityId      string    `json:"identityId"`
	Property        *Property `json:"property"`
	Metadata        *Metadata `json:"metadata"`
	UserId          string    `json:"userId"`
	CreateTimestamp int64     `json:"createTimestamp"`
	UpdateTimestamp int64     `json:"updateTimestamp"`
}

type Property struct {
	Type               Type                        `json:"type"`
	CodeReview         *GitlabCodeReviewProperty   `json:"codeReview,omitempty"`
	GithubCodeReview   *GithubCodeReviewProperty   `json:"githubCodeReview,omitempty"`
	CodeupCodeReview   *CodeupCodeReviewProperty   `json:"codeupCodeReview,omitempty"`
	CodeAoneCodeReview *CodeAoneCodeReviewProperty `json:"codeaoneCodeReview,omitempty"`
	AiDeveloper        *AiDeveloperProperty        `json:"aiDeveloper,omitempty"`
}

type Type string

const (
	// Deprecated
	// CodeReview 特指Gitlab CodeReview
	CodeReview         Type = "CodeReview"
	GitlabCodeReview   Type = "GitlabCodeReview"
	GithubCodeReview   Type = "GithubCodeReview"
	CodeupCodeReview   Type = "CodeupCodeReview"
	CodeaoneCodeReview Type = "CodeaoneCodeReview"
	AiDeveloper        Type = "AiDeveloper"
)

type State string

const (
	Init            State = "Init"
	Running         State = "Running"
	WaitingForInput State = "WaitingForInput"
	Idle            State = "Idle"
	Abort           State = "Abort"
	Aborting        State = "Aborting"
	Finished        State = "Finished"
	Skipped         State = "Skipped"
)

func (s State) Busy() bool {
	return s == Running || s == WaitingForInput
}

func (s State) Idle() bool {
	return s == Idle || s == Finished || s == Abort || s == Skipped
}

func (s State) Validate() error {
	switch s {
	case Init, Running, WaitingForInput, Idle, Abort, Finished, Skipped:
		return nil
	default:
		return commonerrors.New(codes.ErrInvalidParameterWithDetail, "state", s)
	}
}

// SimplifyForCodeup 面向云效透出状态，简化为Idle/Running/Finished
func (s State) SimplifyForCodeup() State {
	switch s {
	case Idle, Abort, Init, Skipped:
		return Idle
	case Finished:
		return Finished
	default:
		return Running
	}
}

func (s State) ToAgentSessionState() AgentSessionState {
	switch s {
	case Init, Running, WaitingForInput:
		return AgentSessionStateRunning
	case "":
		return AgentSessionStateFailed
	case Finished, Abort, Aborting, Idle:
		return AgentSessionStateSuccess
	default:
		return AgentSessionStateSuccess
	}
}

type GitlabCodeReviewProperty struct {
	MergeRequestId string `json:"mergeRequestId"`
	ProjectId      string `json:"projectId"`
}
type CodeAoneCodeReviewProperty struct {
	MergeRequestId int64 `json:"mergeRequestId"`
	ProjectId      int64 `json:"projectId"`
}
type GithubCodeReviewProperty struct {
	Owner  string `json:"owner"`
	Repo   string `json:"repo"`
	Number int    `json:"number"`
}

type CodeupCodeReviewProperty struct {
	OrganizationId string `json:"organizationId"`
	RepositoryId   string `json:"repositoryId"`
	LocalId        int    `json:"localId"`
}

type AiDeveloperProperty struct {
	SessionName string `json:"sessionName"`
	Thumbnail   string `json:"thumbnail"`
}

type CreateSessionRequest struct {
	IdentityId string    `json:"identityId"`
	Property   *Property `json:"property"`
}

func (r *CreateSessionRequest) Validate() error {
	if r.IdentityId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "identityId")
	}
	if r.Property == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "property")
	}
	switch r.Property.Type {
	case GithubCodeReview:
		if r.Property.GithubCodeReview == nil {
			return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "githubCodeReview")
		}
	case GitlabCodeReview:
		if r.Property.CodeReview == nil {
			return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "codeReview")
		}
	}
	return nil
}

type UpdateSessionRequest struct {
	State    *State    `json:"state,omitempty"`
	Property *Property `json:"property,omitempty"`
	Metadata *Metadata `json:"metadata,omitempty"`
}

type ChatRequest struct {
	Message *agent_connect.Message `json:"message,omitempty"`
}

func (r *UpdateSessionRequest) Validate() error {
	if r.State == nil && r.Property == nil {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "state or property")
	}
	if r.State != nil {
		if err := r.State.Validate(); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

type ListSessionsRequest struct {
	// WithoutUserId 云效很难匹配到资源Owner，不允许通过外部参数传递反序列化开启该功能，防止横向越权
	WithoutUserId           bool   `json:"-"`
	GithubRepo              string `json:"githubRepo,omitempty"`
	GithubOwner             string `json:"githubOwner,omitempty"`
	GithubPullRequestNumber int    `json:"githubPullRequestNumber,omitempty"`
	IdentityId              string `json:"identityId,omitempty"`
	CodeupOrganizationId    string `json:"codeupOrganizationId,omitempty"`
	CodeupRepositoryId      string `json:"codeupRepositoryId,omitempty"`
	CodeupLocalId           int    `json:"codeupLocalId,omitempty"`
	GitlabProjectId         string `json:"gitlabProjectId,omitempty"`
	GitlabMergeRequestId    string `json:"gitlabMergeRequestId,omitempty"`

	SessionName   string
	RepositoryId  string
	Author        string
	States        []State
	FromTimestamp int64
	ToTimestamp   int64

	WithIdDesc     bool
	ExcludedStates []State
	page.PagesParams
}

func (r *ListSessionsRequest) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}

type MergeRequestProperty struct {
	Source          identity.Source `json:"source"`
	ProjectId       string          `json:"projectId"`
	MergeRequestId  string          `json:"mergeRequestId"`
	Title           string          `json:"title"`
	State           string          `json:"state"`
	Description     string          `json:"description,omitempty"`
	SourceProjectId string          `json:"sourceProjectId"`
	TargetProjectId string          `json:"targetProjectId"`
	SourceBranch    string          `json:"sourceBranch"`
	SourceRef       string          `json:"sourceRef"`
	TargetBranch    string          `json:"targetBranch"`
	Author          string          `json:"author"`
	Url             string          `json:"url"`
	CreateTimestamp int64           `json:"createTimestamp"`
	UpdateTimestamp int64           `json:"updateTimestamp"`
}

type MergeRequestMetric struct {
	AgentCommentCount               int `json:"agentCommentCount"`
	AgentMentionedCount             int `json:"agentMentionedCount"`
	AgentCommentResolvedCount       int `json:"agentCommentResolvedCount"`
	AgentCommentRepliedCount        int `json:"agentCommentRepliedCount"`
	AgentCodeSuggestionCount        int `json:"agentCodeSuggestionCount"`
	AgentCodeSuggestionAppliedCount int `json:"agentCodeSuggestionAppliedCount"`

	MergeRequestDetail *MergeRequestDetail `json:"mergeRequestDetail,omitempty"`
}

type MergeRequestDetail struct {
	FileCount            int `json:"fileCount"`
	NewLineCount         int `json:"newLineCount"`
	NewCharacterCount    int `json:"newCharacterCount"`
	DeleteCharacterCount int `json:"deleteCharacterCount"`
	DeleteLineCount      int `json:"deleteLineCount"`
}

type Metadata struct {
	AgentId      string          `json:"agentId"`
	AgentName    agent.AgentName `json:"agentName"`
	SessionName  string          `json:"sessionName"`
	RepositoryId string          `json:"repositoryId"`
	Author       string          `json:"author"`
}
