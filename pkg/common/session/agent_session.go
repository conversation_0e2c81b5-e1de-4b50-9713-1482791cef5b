package session

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type AgentSessionInfo struct {
	AgentId         string            `json:"agentId"`
	AgentName       agent.AgentName   `json:"agentName"`
	SessionId       string            `json:"sessionId"`
	SessionName     string            `json:"sessionName"`
	RepositoryId    string            `json:"repositoryId"`
	RepositoryName  string            `json:"repositoryName"`
	Author          string            `json:"author"`
	State           AgentSessionState `json:"state"`
	ExternalLink    string            `json:"externalLink"`
	CreateTimestamp int64             `json:"createTimestamp"`
}

type AgentSessionState string

const (
	AgentSessionStateRunning AgentSessionState = "Running"
	AgentSessionStateFailed  AgentSessionState = "Failed"
	AgentSessionStateSuccess AgentSessionState = "Success"
)

func (ass AgentSessionState) MapToStates() []State {
	switch ass {
	case AgentSessionStateFailed:
		return []State{""}
	case AgentSessionStateRunning:
		return []State{Init, Running, WaitingForInput}
	case AgentSessionStateSuccess:
		return []State{Finished, Abort, Aborting, Idle}
	}
	return nil
}

type ListAgentSessionsRequest struct {
	page.PagesParams
	SessionName   string
	RepositoryId  string
	Author        string
	States        []AgentSessionState
	FromTimestamp int64
	ToTimestamp   int64
}

func (r *ListAgentSessionsRequest) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	if r.FromTimestamp < 0 {
		return errors.New(codes.ErrInvalidParameter, "fromTimestamp", r.FromTimestamp)
	}
	if r.ToTimestamp < 0 {
		return errors.New(codes.ErrInvalidParameter, "toTimestamp", r.ToTimestamp)
	}
	if r.FromTimestamp > 0 && r.ToTimestamp > 0 && r.FromTimestamp > r.ToTimestamp {
		return errors.New(codes.ErrInvalidParameter, "fromTimestamp", r.FromTimestamp)
	}
	for _, s := range r.States {
		switch s {
		case AgentSessionStateFailed, AgentSessionStateRunning, AgentSessionStateSuccess:
		default:
			return errors.New(codes.ErrInvalidParameter, "state", s)
		}
	}
	return nil
}

type ExportAgentSessionsRequest struct {
	SessionName   string `json:"sessionName,omitempty"`
	RepositoryId  string `json:"repositoryId,omitempty"`
	Author        string `json:"author,omitempty"`
	States        string `json:"states,omitempty"`
	FromTimestamp int64  `json:"fromTimestamp,omitempty"`
	ToTimestamp   int64  `json:"toTimestamp,omitempty"`
}

type ListAgentSessionExportHistoryRequest struct {
	page.PagesParams
}

type ExportAgentSessionOperation struct {
	OperatorId      string                      `json:"operatorId"`
	OperatorName    string                      `json:"operatorName"`
	Ip              string                      `json:"ip"`
	Query           *ExportAgentSessionsRequest `json:"query"`
	CreateTimestamp int64                       `json:"createTimestamp"`
}

type GetSessionTokenUsageResponse struct {
	Usages []*TokenUsage `json:"usages"`
}

type TokenUsage struct {
	ModelName        string
	TotalTokens      int64
	PromptTokens     int64
	CompletionTokens int64
}
