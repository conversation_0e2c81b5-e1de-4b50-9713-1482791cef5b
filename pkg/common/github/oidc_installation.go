package github

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// OIDCInstallationStatus OIDC安装状态
type OIDCInstallationStatus string

const (
	OIDCInstallationStatusActive  OIDCInstallationStatus = "active"
	OIDCInstallationStatusDeleted OIDCInstallationStatus = "deleted"
)

// OIDCInstallation OIDC专用GitHub App安装记录
type OIDCInstallation struct {
	ID             int64                  `gorm:"primaryKey;column:id" json:"id"`
	AppId          int64                  `gorm:"column:app_id;not null" json:"app_id"`
	InstallationId int64                  `gorm:"column:installation_id;not null;uniqueIndex" json:"installation_id"`
	AccountId      int64                  `gorm:"column:account_id;not null" json:"account_id"`
	AccountLogin   string                 `gorm:"column:account_login;size:255;not null" json:"account_login"`
	AccountType    string                 `gorm:"column:account_type;size:32;not null" json:"account_type"`
	AppSlug        string                 `gorm:"column:app_slug;size:255" json:"app_slug,omitempty"`
	Repositories   *RepositoryList        `gorm:"column:repositories;type:jsonb" json:"repositories,omitempty"`
	Permissions    *PermissionSet         `gorm:"column:permissions;type:jsonb" json:"permissions,omitempty"`
	Status         OIDCInstallationStatus `gorm:"column:status;size:32;not null;default:active" json:"status"`
	GmtCreate      time.Time              `gorm:"column:gmt_create;autoCreateTime" json:"gmt_create"`
	GmtModified    time.Time              `gorm:"column:gmt_modified;autoUpdateTime" json:"gmt_modified"`
}

// TableName 指定表名
func (o *OIDCInstallation) TableName() string {
	return "t_github_oidc_installation"
}

// RepositoryInfo 仓库信息
type RepositoryInfo struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	FullName string `json:"full_name"`
	Private  bool   `json:"private"`
	Owner    struct {
		Login string `json:"login"`
		ID    int64  `json:"id"`
		Type  string `json:"type"`
	} `json:"owner"`
}

// RepositoryList 仓库列表
type RepositoryList []RepositoryInfo

// Scan 实现 sql.Scanner 接口
func (r *RepositoryList) Scan(value interface{}) error {
	if value == nil {
		*r = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, r)
}

// Value 实现 driver.Valuer 接口
func (r RepositoryList) Value() (interface{}, error) {
	if r == nil {
		return nil, nil
	}
	return json.Marshal(r)
}

// PermissionSet 权限集合
type PermissionSet map[string]string

// Scan 实现 sql.Scanner 接口
func (p *PermissionSet) Scan(value interface{}) error {
	if value == nil {
		*p = nil
		return nil
	}
	
	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}
	
	return json.Unmarshal(bytes, p)
}

// Value 实现 driver.Valuer 接口
func (p PermissionSet) Value() (interface{}, error) {
	if p == nil {
		return nil, nil
	}
	return json.Marshal(p)
}

// CreateOIDCInstallationRequest 创建OIDC安装记录请求
type CreateOIDCInstallationRequest struct {
	AppId          int64           `json:"app_id"`
	InstallationId int64           `json:"installation_id"`
	AccountId      int64           `json:"account_id"`
	AccountLogin   string          `json:"account_login"`
	AccountType    string          `json:"account_type"`
	AppSlug        string          `json:"app_slug,omitempty"`
	Repositories   *RepositoryList `json:"repositories,omitempty"`
	Permissions    *PermissionSet  `json:"permissions,omitempty"`
}

// UpdateOIDCInstallationRequest 更新OIDC安装记录请求
type UpdateOIDCInstallationRequest struct {
	Repositories *RepositoryList        `json:"repositories,omitempty"`
	Permissions  *PermissionSet         `json:"permissions,omitempty"`
	Status       OIDCInstallationStatus `json:"status,omitempty"`
}

// OIDCInstallationQuery 查询条件
type OIDCInstallationQuery struct {
	AppId          *int64                  `json:"app_id,omitempty"`
	InstallationId *int64                  `json:"installation_id,omitempty"`
	AccountId      *int64                  `json:"account_id,omitempty"`
	AccountLogin   *string                 `json:"account_login,omitempty"`
	Status         *OIDCInstallationStatus `json:"status,omitempty"`
	Repository     *string                 `json:"repository,omitempty"` // 格式: owner/repo
}

// IsRepositoryAuthorized 检查仓库是否已授权
func (o *OIDCInstallation) IsRepositoryAuthorized(owner, repo string) bool {
	if o.Repositories == nil {
		return false
	}
	
	fullName := owner + "/" + repo
	for _, r := range *o.Repositories {
		if r.FullName == fullName {
			return true
		}
	}
	return false
}

// HasPermission 检查是否有指定权限
func (o *OIDCInstallation) HasPermission(permission string, level string) bool {
	if o.Permissions == nil {
		return false
	}
	
	actualLevel, exists := (*o.Permissions)[permission]
	if !exists {
		return false
	}
	
	// 简单的权限级别检查: write > read
	switch level {
	case "read":
		return actualLevel == "read" || actualLevel == "write"
	case "write":
		return actualLevel == "write"
	default:
		return actualLevel == level
	}
}

// BeforeCreate GORM钩子
func (o *OIDCInstallation) BeforeCreate(tx *gorm.DB) error {
	if o.Status == "" {
		o.Status = OIDCInstallationStatusActive
	}
	return nil
}

// BeforeUpdate GORM钩子
func (o *OIDCInstallation) BeforeUpdate(tx *gorm.DB) error {
	o.GmtModified = time.Now()
	return nil
}
