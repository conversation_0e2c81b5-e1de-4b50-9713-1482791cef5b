package sandbox

type CreateSandboxRequest struct {
	SessionId        string            `json:"sessionId"`
	K8sRuntimeConfig *K8sRuntimeConfig `json:"k8sRuntimeConfig,omitempty"`
	SandboxConfig    *SandboxConfig    `json:"sandboxConfig,omitempty"`
}

type K8sRuntimeConfig struct {
	Image       string      `json:"image"`
	Namespace   string      `json:"namespace"`
	HostAliases []HostAlias `json:"hostAliases,omitempty"`
}

type CreateSandboxResponse struct {
	Data *SandboxInfo `json:"data"`
}

type Config struct {
	K8sRuntimeConfig *K8sRuntimeConfig `json:"k8sRuntimeConfig,omitempty"`
	SandboxConfig    *SandboxConfig    `json:"sandboxConfig,omitempty"`
}

type SandboxConfig struct {
	GitConfig     *GitConfig     `json:"gitConfig,omitempty"`
	PersistConfig *PersistConfig `json:"persistConfig"`
	SidecarConfig *SidecarConfig `json:"sidecarConfig"`
}

type GitConfig struct {
	Username string `json:"username"`
	Token    string `json:"token"`
}

type PersistConfig struct {
	Enable bool `json:"enable"`
}

type SidecarConfig struct {
	Enable bool   `json:"enable"`
	Image  string `json:"image"`
}

type GetAvailableSandboxResponse struct {
	Data *SandboxInfo `json:"data"`
}
type SandboxInfo struct {
	SandboxId      string `json:"sandboxId"`
	ServerEndpoint string `json:"serverEndpoint"`
}

type HostAlias struct {
	IP        string   `json:"ip"`
	Hostnames []string `json:"hostnames"`
}
