package event

import (
	"fmt"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/yunxiao"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"time"
)

type AuditAction string

const (
	AuditActionCreated AuditAction = "created"
	AuditActionUpdated AuditAction = "updated"
	AuditActionDeleted AuditAction = "deleted"
	AuditActionPatched AuditAction = "patched"
)

type AuditResource string

const (
	AuditResourceAgent      AuditResource = "agent"
	AuditResourceRepository AuditResource = "repository"
	AuditResourceIdentity   AuditResource = "identity"
)

type AuditOperator struct {
	Id string `json:"id"`
	Ip string `json:"ip"`
}

func NewOperatorFromContext(ctx base.Context) *AuditOperator {
	uid := ctx.GetUid()
	ip := ""
	if yunxiaoUser := base.GetYunxiaoUserFromContext(ctx); yunxiaoUser != nil {
		uid = yunxiaoUser.GetLoginUid()
	}
	if ginCtx := ctx.GetGinContext(); ginCtx != nil {
		ip = ginCtx.ClientIP()
	}
	return &AuditOperator{
		Id: uid,
		Ip: ip,
	}
}

func NewOrganizationFromContext(ctx base.Context) *AuditOrganization {
	name := ""
	id := ""
	if yunxiaoUser := base.GetYunxiaoUserFromContext(ctx); yunxiaoUser != nil {
		id = yunxiaoUser.GetUid()
		if orgInfo, err := yunxiao.GetOrganizationInfo(ctx); err == nil {
			// 错误就不处理了
			name = orgInfo.Name
		}
	}
	return &AuditOrganization{
		Id:   id,
		Name: name,
	}
}

type AuditOrganization struct {
	Id   string `json:"id"`
	Name string `json:"name"`
}

type AuditEvent struct {
	Action       AuditAction        `json:"action"`
	Resource     AuditResource      `json:"resource"`
	ResourceId   string             `json:"resourceId"`
	Content      map[string]any     `json:"content"`
	User         *AuditOperator     `json:"user"`
	Organization *AuditOrganization `json:"organization"`
}

func (e *AuditEvent) ToPayload() map[string]any {
	return map[string]any{
		"action":     e.Action,
		"resource":   e.Resource,
		"resourceId": e.ResourceId,
		"content":    e.Content,
		"operator": map[string]any{
			"user": e.User,
		},
		"organization": e.Organization,
	}
}

func (e *AuditEvent) ToCloudEvent() map[string]any {
	version := "v2"
	source := "agents"
	typ := fmt.Sprintf("yunxiao.%s.%s.%s.%s", source, e.Resource, version, e.Action)
	specVersion := "1.0"

	return map[string]any{
		"id":              helper.NewUUID(),
		"type":            typ,
		"specversion":     specVersion,
		"source":          source,
		"datacontenttype": "application/json",
		"organizationid":  e.Organization.Id,
		"data": map[string]any{
			"payload": e.ToPayload(),
		},
		"time": time.Now(),
	}
}

func NewAuditEvent(ctx base.Context, action AuditAction, resource AuditResource, resourceId string, content map[string]any) *AuditEvent {
	return &AuditEvent{
		Action:       action,
		Resource:     resource,
		ResourceId:   resourceId,
		Content:      content,
		User:         NewOperatorFromContext(ctx),
		Organization: NewOrganizationFromContext(ctx),
	}
}

type AgentProperty struct {
	AgentId          string `json:"agentId"`
	AgentName        string `json:"agentName"`
	Enable           bool   `json:"enable"`
	CodeReview       string `json:"codeReview"`
	GithubCodeReview string `json:"githubCodeReview"`
}

type BoolDiff struct {
	Before bool `json:"before"`
	After  bool `json:"after"`
}

type StringDiff struct {
	Before string `json:"before"`
	After  string `json:"after"`
}

type AgentChanges struct {
	Enable           *BoolDiff   `json:"enable,omitempty"`
	CodeReview       *StringDiff `json:"codeReview,omitempty"`
	GithubCodeReview *StringDiff `json:"githubCodeReview,omitempty"`
}

func NewAgentPatchedEvent(ctx base.Context, agentId string, property *AgentProperty, changes *AgentChanges) *AuditEvent {
	return NewAuditEvent(ctx, AuditActionPatched, AuditResourceAgent, agentId, map[string]any{
		string(AuditResourceAgent): property,
		"changes":                  changes,
	})
}

type RepositoryProperty struct {
	RepositoryId   string `json:"repositoryId"`
	RepositoryName string `json:"repositoryName"`
	Setting        string `json:"setting"`
}

type RepositoryChanges struct {
	Setting *StringDiff `json:"setting,omitempty"`
}

func NewRepositoryUpdatedEvent(ctx base.Context, repoId string, property *RepositoryProperty, changes *RepositoryChanges) *AuditEvent {
	return NewAuditEvent(ctx, AuditActionUpdated, AuditResourceRepository, repoId, map[string]any{
		string(AuditResourceRepository): property,
		"changes":                       changes,
	})
}

type IdentityProperty struct {
	IdentityId       string `json:"identityId"`
	AgentId          string `json:"agentId"`
	Source           string `json:"source,omitempty"`
	PlatformEndpoint string `json:"platformEndpoint,omitempty"`
	PlatformToken    string `json:"platformToken,omitempty"`
	WebhookToken     string `json:"webhookToken,omitempty"`
	Description      string `json:"description,omitempty"`
}

type IdentityChanges struct {
	PlatformToken *StringDiff `json:"platformToken,omitempty"`
}

func NewIdentityCreatedEvent(ctx base.Context, identityId string, property *IdentityProperty) *AuditEvent {
	return NewAuditEvent(ctx, AuditActionCreated, AuditResourceIdentity, identityId, map[string]any{
		string(AuditResourceIdentity): property,
	})
}

func NewIdentityUpdatedEvent(ctx base.Context, identityId string, property *IdentityProperty, changes *IdentityChanges) *AuditEvent {
	return NewAuditEvent(ctx, AuditActionUpdated, AuditResourceIdentity, identityId, map[string]any{
		string(AuditResourceIdentity): property,
		"changes":                     changes,
	})
}

func NewIdentityDeletedEvent(ctx base.Context, identityId string, property *IdentityProperty) *AuditEvent {
	return NewAuditEvent(ctx, AuditActionDeleted, AuditResourceIdentity, identityId, map[string]any{
		string(AuditResourceIdentity): property,
	})
}
