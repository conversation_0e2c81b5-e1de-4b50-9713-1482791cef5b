package event

import (
	"github.com/pkg/errors"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type Property struct {
	Type Type           `json:"type"`
	Data map[string]any `json:"data,omitempty"`
}

type Event struct {
	EventId         string    `json:"eventId"`
	SessionId       string    `json:"sessionId"`
	Property        *Property `json:"property,omitempty"`
	CreateTimestamp int64     `json:"createTimestamp"`
	UpdateTimestamp int64     `json:"updateTimestamp"`
}

type Type string

const (
	TypeAgentTask Type = "agent_task"
)

type ListEventsOptions struct {
	Page      page.PagesParams
	SessionId string `form:"sessionId"`
	WithIdAsc bool   `form:"withIdAsc"`
}

func (r *ListEventsOptions) Validate() error {
	if err := r.Page.Validate(); err != nil {
		return errors.WithStack(err)
	}
	if r.SessionId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "sessionId")
	}
	return nil
}
