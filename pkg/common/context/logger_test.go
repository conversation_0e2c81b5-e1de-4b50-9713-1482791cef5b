package context

import (
	"bytes"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"k8s.io/utils/ptr"
	"log/slog"
	"net/http/httptest"

	"testing"
)

// 用于捕获日志输出的 Writer
type LogBuffer struct {
	*bytes.Buffer
}

// 实现 slog Logger 方法
func (lb *LogBuffer) Write(p []byte) (n int, err error) {
	return lb.Buffer.Write(p)
}

func TestGetLogger(t *testing.T) {
	Convey("Given a rest pop request", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		dummyRequestID := "test-request-id"

		ctx.Set(KeyRequestId, &dummyRequestID)
		u := NewDummyUser()
		ctx.Set(base.KeyUser, u)
		ctx.Set(KeyAction, ptr.To("Action"))
		l := GetLogger(ctx)
		So(l, ShouldNotBeNil)
	})

	Convey("Given request id", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		dummyRequestID := "test-request-id"

		ctx.Set(KeyRequestId, &dummyRequestID)

		l := GetLogger(ctx)
		So(l, ShouldNotBeNil)
	})

	Convey("Given user", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		u := NewDummyUser()
		ctx.Set(base.KeyUser, u)

		l := GetLogger(ctx)
		So(l, ShouldNotBeNil)
	})

	Convey("Given action", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		ctx.Set(KeyAction, ptr.To("Action"))

		l := GetLogger(ctx)
		So(l, ShouldNotBeNil)
	})

	Convey("Given a nil gin.Context", t, func() {
		ctx := (*gin.Context)(nil)
		l := GetLogger(ctx)
		So(l, ShouldNotBeNil)
		So(l, ShouldEqual, slog.Default())

	})
}

func NewDummyUser() base.UserCtx {
	return base.NewNoneTypeUser("uid-123", "", "")
}
