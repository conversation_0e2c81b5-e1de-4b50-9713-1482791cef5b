package base

import (
	"encoding/base64"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"log/slog"
)

type PopCtx interface {
	GetCallerUid() string
	GetCallerBid() string
	GetCallerParentId() string
	GetCallerType() string
	GetRequestId() string
	GetAccessKeyId() string
	GetAction() string
	GetSecurityToken() string
	GetStsTokenPrincipalId() string
	GetStsTokenRoleId() string
	GetStsTokenCallerUid() string
	GetStsTokenCallerBid() string
	GetAkMfaPresent() string
	GetSecurityTransport() string
	GetSourceIp() string
	GetClientIp() string
	GetAkProven() string
	GetRAMAuthContext() map[string][]string
}

type popInfo struct {
	callerUid           string
	callerBid           string
	callerParentId      string
	callerType          string
	requestId           string
	accessKeyId         string
	action              string
	securityToken       string
	stsTokenPrincipalId string
	stsTokenRoleId      string
	stsTokenCallerUid   string
	stsTokenCallerBid   string
	akMfaPresent        string
	securityTransport   string
	sourceIp            string
	clientIp            string
	akProven            string
	ramAuthContext      map[string][]string
}

func (p *popInfo) GetCallerUid() string {
	return p.callerUid
}

func (p *popInfo) GetCallerBid() string {
	return p.callerBid
}

func (p *popInfo) GetCallerParentId() string {
	return p.callerParentId
}

func (p *popInfo) GetCallerType() string {
	return p.callerType
}

func (p *popInfo) GetRequestId() string {
	return p.requestId
}

func (p *popInfo) GetAccessKeyId() string {
	return p.accessKeyId
}

func (p *popInfo) GetAction() string {
	return p.action
}

func (p *popInfo) GetSecurityToken() string {
	return p.securityToken
}

func (p *popInfo) GetStsTokenPrincipalId() string {
	return p.stsTokenPrincipalId
}

func (p *popInfo) GetStsTokenRoleId() string {
	return p.stsTokenRoleId
}

func (p *popInfo) GetStsTokenCallerUid() string {
	return p.stsTokenCallerUid
}

func (p *popInfo) GetStsTokenCallerBid() string {
	return p.stsTokenCallerBid
}

func (p *popInfo) GetAkMfaPresent() string {
	return p.akMfaPresent
}

func (p *popInfo) GetSecurityTransport() string {
	return p.securityTransport
}

func (p *popInfo) GetSourceIp() string {
	return p.sourceIp
}

func (p *popInfo) GetClientIp() string {
	return p.clientIp
}

func (p *popInfo) GetAkProven() string {
	return p.akProven
}

func (p *popInfo) GetRAMAuthContext() map[string][]string { return p.ramAuthContext }

func NewPopCtx(ctx *gin.Context) PopCtx {
	if ctx == nil {
		return nil
	}

	// For rest call
	if ctx.GetHeader("x-acs-product") != "" {
		return newPopCtxFromHeader(ctx)
	}

	// For rpc call
	if ctx.PostForm("acsProduct") != "" {
		return newPopCtxFromForm(ctx)
	}

	return nil
}

func newPopCtxFromForm(ctx *gin.Context) PopCtx {
	p := &popInfo{
		callerUid:           ctx.PostForm("callerUid"),
		callerBid:           ctx.PostForm("callerBid"),
		callerParentId:      ctx.PostForm("callerParentId"),
		callerType:          ctx.PostForm("callerType"),
		requestId:           ctx.PostForm("requestId"),
		action:              ctx.PostForm("action"),
		akMfaPresent:        ctx.PostForm("akMfaPresent"),
		securityTransport:   ctx.PostForm("securityTransport"),
		sourceIp:            ctx.PostForm("proxyOriginalSourceIp"),
		accessKeyId:         ctx.PostForm("accessKeyId"),
		securityToken:       ctx.PostForm("securityToken"),
		stsTokenPrincipalId: ctx.PostForm("stsTokenPrincipalId"),
		stsTokenRoleId:      ctx.PostForm("stsTokenRoleId"),
		stsTokenCallerUid:   ctx.PostForm("stsTokenCallerUid"),
		stsTokenCallerBid:   ctx.PostForm("stsTokenCallerBid"),
		clientIp:            ctx.PostForm("appIp"),
	}

	return p
}

func newPopCtxFromHeader(ctx *gin.Context) PopCtx {
	p := &popInfo{
		callerUid:           ctx.GetHeader("x-acs-caller-uid"),
		callerBid:           ctx.GetHeader("x-acs-caller-bid"),
		callerParentId:      ctx.GetHeader("x-acs-parent-id"),
		callerType:          ctx.GetHeader("x-acs-caller-type"),
		requestId:           ctx.GetHeader("x-acs-request-id"),
		action:              ctx.GetHeader("x-acs-api-name"),
		akMfaPresent:        ctx.GetHeader("x-acs-ak-mfa-present"),
		securityTransport:   ctx.GetHeader("x-acs-security-transport"),
		sourceIp:            ctx.GetHeader("x-acs-source-ip"),
		accessKeyId:         ctx.GetHeader("x-acs-accesskey-id"),
		securityToken:       ctx.GetHeader("x-acs-security-token"),
		stsTokenPrincipalId: ctx.GetHeader("x-acs-sts-token-principal-id"),
		stsTokenRoleId:      ctx.GetHeader("x-acs-sts-token-role-id"),
		stsTokenCallerUid:   ctx.GetHeader("x-acs-sts-token-caller-uid"),
		stsTokenCallerBid:   ctx.GetHeader("x-acs-sts-token-caller-bid"),
		clientIp:            ctx.GetHeader("x-acs-client-ip"),
		akProven:            ctx.GetHeader("x-acs-ak-proven"),
	}

	if len(ctx.GetHeader("x-acs-ram-auth-context")) > 0 {
		ramAuthStr, err := base64.StdEncoding.DecodeString(ctx.GetHeader("x-acs-ram-auth-context"))
		if err != nil {
			slog.Default().Error("pop context failed to decode ramAuthContext", "ramAuthContextEncodingStr", ctx.GetHeader("x-acs-ram-auth-context"))
			return nil
		}
		p.ramAuthContext = map[string][]string{}
		err = json.Unmarshal(ramAuthStr, &p.ramAuthContext)
		if err != nil {
			slog.Default().Error("pop context failed to parse ramAuthContext", "ramAuthContext", ctx.GetHeader("x-acs-ram-auth-context"))
			return nil
		}
	}

	return p
}
