package base

import (
	"context"
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	i18n "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/in18"
	"k8s.io/utils/ptr"
	"log/slog"
)

const (
	KeyRequestId = "requestId"
	KeyTraceId   = "traceId"
)

type Context interface {
	context.Context
	UserCtx
	PopCtx
	IsPopRequest() bool
	GetLogger() *slog.Logger
	GetGinContext() *gin.Context
	GetSource() string
	GetTraceId() string
	GetRequestId() string
	GetLocale() i18n.Locale
}

type baseContext struct {
	context.Context
	UserCtx
	PopCtx
	traceId string
	reqId   string
	ctx     *gin.Context
	logger  *slog.Logger
}

func getRequestId(ctx *gin.Context) *string {
	u, exists := ctx.Get(KeyRequestId)
	if !exists {
		return ptr.To(helper.NewRequestId())
	}

	return u.(*string)
}

func getTraceId(ctx *gin.Context) *string {
	u, exists := ctx.Get(KeyTraceId)
	if !exists {
		return ptr.To(helper.NewTraceId())
	}

	return u.(*string)
}

func (b *baseContext) GetLogger() *slog.Logger {
	return b.logger
}

func (b *baseContext) GetGinContext() *gin.Context {
	return b.ctx
}

func (b *baseContext) IsPopRequest() bool {
	return b.PopCtx != nil
}

func (b *baseContext) GetTraceId() string {
	if b.IsPopRequest() {
		return b.PopCtx.GetRequestId()
	}
	return b.traceId
}

func (b *baseContext) GetAkProven() string {
	if b.IsPopRequest() {
		return b.PopCtx.GetAkProven()
	}
	return ""
}

func (b *baseContext) GetSource() string {
	if b.PopCtx == nil {
		return "gin"
	}

	return "pop|" + b.PopCtx.GetAction()
}

func (b *baseContext) GetLocale() i18n.Locale {
	// fixme 先写死中文
	return i18n.SimplifiedChinese
}

func (b *baseContext) GetRequestId() string {
	if b.IsPopRequest() {
		return b.PopCtx.GetRequestId()
	}
	return b.reqId
}

type workerContext struct {
	context.Context
	UserCtx
	PopCtx
	logger     *slog.Logger
	workerName string
	traceId    string
}

func (b *workerContext) GetLogger() *slog.Logger {
	return b.logger
}

func (b *workerContext) GetGinContext() *gin.Context {
	return nil
}

func (b *workerContext) IsPopRequest() bool {
	return false
}

func (b *workerContext) GetSource() string {
	return "worker|" + b.workerName
}

func (b *workerContext) GetLocale() i18n.Locale {
	return i18n.SimplifiedChinese
}

func (b *workerContext) GetAkProven() string {
	if b.IsPopRequest() {
		return b.PopCtx.GetAkProven()
	}
	return ""
}

type bareUserContext struct {
	context.Context
	UserCtx
	PopCtx
	logger  *slog.Logger
	caller  string
	traceId string
	reqId   string
}

func (b *workerContext) GetTraceId() string {
	return b.traceId
}

func (b *bareUserContext) GetLogger() *slog.Logger {
	return b.logger
}

func (b *bareUserContext) GetGinContext() *gin.Context {
	return nil
}

func (b *bareUserContext) IsPopRequest() bool {
	return false
}

func (b *bareUserContext) GetSource() string {
	return b.caller
}

func (b *bareUserContext) GetTraceId() string {
	return b.traceId
}

func (b *bareUserContext) GetLocale() i18n.Locale {
	return i18n.SimplifiedChinese
}

func (b *bareUserContext) GetAkProven() string {
	return ""
}

func (b *bareUserContext) GetRequestId() string {
	return b.reqId
}

func NewContextFromRequest(ctx *gin.Context) Context {
	l := slog.Default()
	reqId := helper.NewRequestId()
	traceId := helper.NewTraceId()
	var u UserCtx
	var p PopCtx
	if ctx != nil {
		reqId = *getRequestId(ctx)
		traceId = *getTraceId(ctx)
		l = l.With("reqId", reqId)
		l = l.With("traceId", traceId)
		p = NewPopCtx(ctx)
		u = getUserFromGinContext(ctx)
		if p != nil {
			l = l.With("action", p.GetAction())
		}

		if u == nil {
			u = NewAnonymousUser()
		}
		l = l.With("user", u)
	}

	return &baseContext{
		ctx:     ctx,
		logger:  l,
		PopCtx:  p,
		UserCtx: u,
		Context: ctx,
		traceId: traceId,
		reqId:   reqId,
	}
}

// NewBackgroundContext 根据当前ctx构造一个新ctx用于后台任务，注意ginContext会被至为nil
func NewBackgroundContext(originalCtx Context) Context {
	return &baseContext{
		ctx:     nil,
		logger:  originalCtx.GetLogger(),
		PopCtx:  NewPopCtx(originalCtx.GetGinContext()),
		UserCtx: originalCtx,
		Context: context.Background(),
		traceId: originalCtx.GetTraceId(),
		reqId:   originalCtx.GetRequestId(),
	}
}

func NewContextWithUserOverride(originalCtx Context, userId string, siteType SiteType) Context {
	u := NewNoneTypeUser(userId, "", siteType)
	traceId := originalCtx.GetTraceId()
	reqId := originalCtx.GetRequestId()
	l := slog.Default()
	if originalCtx.IsPopRequest() {
		l = l.With("action", originalCtx.GetAction())
	} else {
	}

	l = l.With("reqId", reqId)
	l = l.With("traceId", traceId)
	l = l.With("user", u)

	return &baseContext{
		ctx:     originalCtx.GetGinContext(),
		logger:  l,
		PopCtx:  NewPopCtx(originalCtx.GetGinContext()),
		UserCtx: u,
		Context: context.TODO(),
		traceId: traceId,
	}
}

func NewContextForWorkerFrom(ctx context.Context, userId, worker, traceId string, siteType SiteType) Context {
	l := slog.Default()
	var u UserCtx

	l = l.With("worker", worker)
	if traceId == "" {
		traceId = helper.NewIDWithPrefix("trace-")
	}

	l = l.With("traceId", traceId)
	u = NewWorkerTypeUser(userId, worker, siteType)
	l = l.With("user", u)

	return &workerContext{
		logger:     l,
		PopCtx:     nil,
		UserCtx:    u,
		Context:    ctx,
		workerName: worker,
		traceId:    traceId,
	}
}

func NewContextForWorker(userId, worker, traceId string, siteType SiteType) Context {
	return NewContextForWorkerFrom(context.TODO(), userId, worker, traceId, siteType)
}

// NewContextForAdmin 创建admin ctx，不分站点。
// 通常底层资源操作不分站点，如果涉及外调操作，且外调配置区分站点，请使用 NewContextForAdminWithSiteType。
func NewContextForAdmin(traceId, caller string) Context {
	return NewContextForBareUser(AdminUser, traceId, caller, SiteTypeUnknown)
}

// NewContextForAdminWithSiteType 指定siteType创建admin ctx，区分国内站和国际站。
// 一般商业化逻辑或者外调配置（例如waf）需要区分站点。
func NewContextForAdminWithSiteType(traceId, caller string, siteType SiteType) Context {
	return NewContextForBareUser(AdminUser, traceId, caller, siteType)
}

// NewContextForBareUser 构造一个用户ctx。如果任务下游不分站点，siteType可传unknown。
func NewContextForBareUser(userId, traceId, caller string, siteType SiteType) Context {
	l := slog.Default()
	var u UserCtx

	if traceId == "" {
		traceId = helper.NewIDWithPrefix("trace-")
	}

	l = l.With("traceId", traceId)
	u = NewNoneTypeUser(userId, caller, siteType)
	l = l.With("user", u)

	return &bareUserContext{
		logger:  l,
		PopCtx:  nil,
		UserCtx: u,
		Context: context.TODO(),
		caller:  caller,
		traceId: traceId,
	}
}
