package base

import (
	"github.com/gin-gonic/gin"
	"log/slog"
)

const (
	KeyUser        = "user"
	KeyYunxiaoUser = "YunxiaoUser"
	AdminUser      = "AdminUser"
)

const (
	BidIntl = "26888"
)

type UserType string

const (
	// UserTypeCustomer 主账号
	UserTypeCustomer = UserType("customer")
	// UserTypeSub 子账号
	UserTypeSub = UserType("sub")
	// UserTypeRole STS登陆
	UserTypeRole = UserType("AssumedRoleUser")
	// UserTypeAct 客服账号
	UserTypeAct = UserType("act")
	// UserTypeWorker Worker扮演用户
	UserTypeWorker = UserType("worker")
	// UserTypeNone 程序中根据上下文自动生成的一个UserCtx，不表示任何用户身份
	UserTypeNone = UserType("None")
	// UserTypeInternal 内部系统扮演用户
	UserTypeInternal = UserType("internal")
	// UserTypeCodeup Codeup用户，UID表示组织，LoginUid表示用户
	UserTypeCodeup = UserType("codeup")
	// UserTypeAnonymous 匿名用户
	UserTypeAnonymous = UserType("anonymous")
)

type SiteType string

const (
	SiteTypeIntl     = SiteType("intl")
	SiteTypeDomestic = SiteType("domestic")
	SiteTypeUnknown  = SiteType("unknown")
)

const (
	ActByCodeupWebhook   string = "codeup_webhook"
	ActByGitlabWebhook   string = "gitlab_webhook"
	ActByGithubWebhook   string = "github_webhook"
	ActByCodeAoneWebhook string = "codeaone_webhook"
)

type UserCtx interface {
	// GetBid 获取用户的bid
	GetBid() string
	// GetUid 获取用户的主账号ID
	GetUid() string
	// GetLoginUid 获取用户的登陆账号ID，对于子账号，会返回子账号ID
	GetLoginUid() string
	// GetUserType 获取用户类型
	GetUserType() UserType
	// GetDomain 获取登陆的域名
	GetDomain() string
	// GetActBy 客服账号的使用者
	GetActBy() string
	// GetSiteType 获取用户站点
	GetSiteType() SiteType
}

func getUserFromGinContext(ctx *gin.Context) UserCtx {
	u, exists := ctx.Get(KeyUser)
	if !exists {
		return nil
	}

	return u.(UserCtx)
}

type workerTypeUser struct {
	uid        string
	workerName string
	siteType   SiteType
}

func (w *workerTypeUser) GetSiteType() SiteType {
	return w.siteType
}

func NewWorkerTypeUser(uid, workerName string, siteType SiteType) UserCtx {
	u := &workerTypeUser{
		uid:        uid,
		workerName: workerName,
		siteType:   siteType,
	}
	return u
}

func (w *workerTypeUser) GetBid() string {
	return ""
}

func (w *workerTypeUser) GetUid() string {
	return w.uid
}

func (w *workerTypeUser) GetLoginUid() string {
	return w.uid
}

func (w *workerTypeUser) GetUserType() UserType {
	return UserTypeWorker
}

func (w *workerTypeUser) GetDomain() string {
	return ""
}

func (w *workerTypeUser) GetActBy() string {
	return w.workerName
}

func (w *workerTypeUser) LogValue() slog.Value {
	return slog.GroupValue(
		slog.String("uid", w.uid),
		slog.String("worker", w.workerName),
		slog.String("siteType", string(w.siteType)),
	)
}

type noneTypeUser struct {
	uid      string
	actBy    string
	siteType SiteType
}

func (n *noneTypeUser) GetSiteType() SiteType {
	return n.siteType
}

func (n *noneTypeUser) GetBid() string {
	return ""
}

func (n *noneTypeUser) GetUid() string {
	return n.uid
}

func (n *noneTypeUser) GetLoginUid() string {
	return n.uid
}

func (n *noneTypeUser) GetUserType() UserType {
	return UserTypeNone
}

func (n *noneTypeUser) GetDomain() string {
	return ""
}

func (n *noneTypeUser) GetActBy() string {
	return n.actBy
}

func (n *noneTypeUser) LogValue() slog.Value {
	return slog.GroupValue(
		slog.String("uid", n.uid),
		slog.String("siteType", string(n.siteType)),
	)
}

func NewNoneTypeUser(uid string, actBy string, siteType SiteType) UserCtx {
	return &noneTypeUser{uid: uid, actBy: actBy, siteType: siteType}
}

func NewAdminUser() UserCtx {
	return &noneTypeUser{uid: AdminUser, siteType: SiteTypeUnknown}
}

func GetSiteTypeByBid(bid string) SiteType {
	if bid == BidIntl {
		return SiteTypeIntl
	}
	return SiteTypeDomestic
}

type anonymousUser struct {
	siteType SiteType
	actBy    string
}

func (au *anonymousUser) GetBid() string {
	return ""
}

func (au *anonymousUser) GetUid() string {
	if au.actBy != "" {
		return au.actBy
	}
	return "anonymous"
}

func (au *anonymousUser) GetLoginUid() string {
	if au.actBy != "" {
		return au.actBy
	}
	return "anonymous"
}

func (au *anonymousUser) GetUserType() UserType {
	return UserTypeAnonymous
}

func (au *anonymousUser) GetDomain() string {
	return ""
}

func (au *anonymousUser) GetActBy() string {
	return au.actBy
}

func (au *anonymousUser) GetSiteType() SiteType {
	return au.siteType
}

func NewAnonymousUser() UserCtx {
	return &anonymousUser{siteType: SiteTypeUnknown}
}

func NewAnonymousUserWithActBy(actBy string) UserCtx {
	return &anonymousUser{siteType: SiteTypeUnknown, actBy: actBy}
}

func GetYunxiaoUserFromGinContext(ctx *gin.Context) YunxiaoUser {
	user := getUserFromGinContext(ctx)
	if yunxiao, ok := user.(YunxiaoUser); ok {
		return yunxiao
	}
	return nil
}
