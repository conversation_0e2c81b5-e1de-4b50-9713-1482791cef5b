package base

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"net/url"
	"time"
)

const (
	YunxiaoLoginUri         = "/users/login"
	YunxiaoResetPasswordUri = "/users/resetpassword"
)

func GetYunxiaoLoginUri(ctx *gin.Context) string {
	u := getGinFullURL(ctx)
	encodedUrl := url.QueryEscape(u)
	return fmt.Sprintf("%s?oauth_callback=%s", YunxiaoLoginUri, encodedUrl)
}

func GetYunxiaoResetPasswordUri(ctx *gin.Context) string {
	u := getGinFullURL(ctx)
	encodedUrl := url.QueryEscape(u)
	return fmt.Sprintf("%s?oauth_callback=%s", YunxiaoResetPasswordUri, encodedUrl)
}

func getGinFullURL(c *gin.Context) string {
	// 获取协议
	scheme := "http"
	if c.Request.TLS != nil {
		scheme = "https"
	}
	if forwardedProto := c.GetHeader("X-Forwarded-Proto"); forwardedProto != "" {
		scheme = forwardedProto
	}

	// 获取主机名
	host := c.Request.Host
	if forwardedHost := c.GetHeader("X-Forwarded-Host"); forwardedHost != "" {
		host = forwardedHost
	}
	// fixme 不能引入config包，循环依赖
	prefix := "/agents"
	// 获取完整路径（包含查询参数）
	uri := c.Request.RequestURI

	return fmt.Sprintf("%s://%s%s%s", scheme, host, prefix, uri)
}

// YunxiaoUser 以组织ID为UID，用户ID为LoginUid，所有资源挂靠到组织上
type YunxiaoUser interface {
	GetUsername() string
	GetName() string
	NeedLogin() bool
	NeedUpdatePassword() bool
	UserCtx
}

var _ YunxiaoUser = (*yunxiaoUser)(nil)

type YunxiaoUserInfo struct {
	Id                 string    `json:"id"`
	Name               string    `json:"name"`
	Username           string    `json:"username"`
	NickName           string    `json:"nick_name"`
	Avatar             string    `json:"avatar"`
	LastOrganization   string    `json:"last_organization"`
	Email              string    `json:"email"`
	NeedLogin          bool      `json:"need_login"`
	NeedUpdatePassword bool      `json:"need_update_password"`
	IsAdmin            bool      `json:"is_admin"`
	StaffId            string    `json:"staff_id"`
	PhoneNumber        string    `json:"phone_number"`
	CreatedAt          time.Time `json:"created_at"`
}

type yunxiaoUser struct {
	id                 string
	name               string
	username           string
	lastOrganizationId string
	needLogin          bool
	needUpdatePassword bool
}

func (c *yunxiaoUser) GetBid() string {
	return ""
}

func (c *yunxiaoUser) GetUid() string {
	return c.lastOrganizationId
}

func (c *yunxiaoUser) GetLoginUid() string {
	return c.id
}

func (c *yunxiaoUser) GetUserType() UserType {
	return UserTypeCodeup
}

func (c *yunxiaoUser) GetDomain() string {
	return ""
}

func (c *yunxiaoUser) GetActBy() string {
	return ""
}

func (c *yunxiaoUser) GetSiteType() SiteType {
	return SiteTypeUnknown
}

func (c *yunxiaoUser) GetUsername() string {
	return c.username
}

func (c *yunxiaoUser) GetName() string {
	return c.name
}

func (c *yunxiaoUser) NeedLogin() bool {
	return c.needLogin
}

func (c *yunxiaoUser) NeedUpdatePassword() bool {
	return c.needUpdatePassword
}
func NewCodeupUser(resp *YunxiaoUserInfo) YunxiaoUser {
	return &yunxiaoUser{
		id:                 resp.Id,
		name:               resp.Name,
		username:           resp.Username,
		lastOrganizationId: resp.LastOrganization,
		needLogin:          resp.NeedLogin,
		needUpdatePassword: resp.NeedUpdatePassword,
	}
}

func GetYunxiaoUserFromContext(ctx Context) YunxiaoUser {
	if ctx == nil {
		return nil
	}
	if v, ok := ctx.(YunxiaoUser); ok {
		return v
	}
	if baseCtx, ok := ctx.(*baseContext); ok {
		if v, ok := baseCtx.UserCtx.(YunxiaoUser); ok {
			return v
		}
	}
	if baseCtx, ok := ctx.(*bareUserContext); ok {
		if v, ok := baseCtx.UserCtx.(YunxiaoUser); ok {
			return v
		}
	}
	if baseCtx, ok := ctx.(*workerContext); ok {
		if v, ok := baseCtx.UserCtx.(YunxiaoUser); ok {
			return v
		}
	}
	return nil
}

func IsYunxiaoUser(ctx Context) bool {
	return GetYunxiaoUserFromContext(ctx) != nil
}
