package base

import (
	. "github.com/bytedance/mockey"
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewPopCtxFromHeader(t *testing.T) {
	PatchConvey("", t, func() {
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request, _ = http.NewRequest(http.MethodGet, "/", nil)

		headers := map[string]string{
			"x-acs-caller-uid":             "test-caller-uid",
			"x-acs-caller-bid":             "test-caller-bid",
			"x-acs-parent-id":              "test-parent-id",
			"x-acs-caller-type":            "test-caller-type",
			"x-acs-request-id":             "test-request-id",
			"x-acs-api-name":               "test-api-name",
			"x-acs-ak-mfa-present":         "true",
			"x-acs-security-transport":     "HTTPS",
			"x-acs-source-ip":              "***********",
			"x-acs-accesskey-id":           "test-accesskey-id",
			"x-acs-security-token":         "test-security-token",
			"x-acs-sts-token-principal-id": "test-principal-id",
			"x-acs-sts-token-role-id":      "test-role-id",
			"x-acs-sts-token-caller-uid":   "test-sts-caller-uid",
			"x-acs-sts-token-caller-bid":   "test-sts-caller-bid",
			"x-acs-client-ip":              "***********",
			"x-acs-ak-proven":              "yes",
		}

		for key, value := range headers {
			c.Request.Header.Add(key, value)
		}
		PatchConvey("with ram auth context", func() {
			c.Request.Header.Add("x-acs-ram-auth-context", "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
			popCtx := newPopCtxFromHeader(c)
			So(popCtx.GetCallerUid(), ShouldEqual, "test-caller-uid")
			So(popCtx.GetRAMAuthContext()["__sys:ProxyTrustTransportInfo"], ShouldResemble, []string{"false"})
		})

		PatchConvey("without ram auth context", func() {
			popCtx := newPopCtxFromHeader(c)
			So(popCtx.GetCallerUid(), ShouldEqual, "test-caller-uid")
			So(popCtx.GetRAMAuthContext(), ShouldBeNil)
		})
	})
}
