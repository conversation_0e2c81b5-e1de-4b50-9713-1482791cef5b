package base

import (
	"github.com/gin-gonic/gin"
	. "github.com/smartystreets/goconvey/convey"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewContextFromRequest(t *testing.T) {
	Convey("Given a rest pop request", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		dummyRequestID := "test-request-id"
		ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
		ctx.Request.Header.Set("x-acs-product", "apig")
		ctx.Request.Header.Set("x-acs-api-name", "Action")
		ctx.Request.Header.Set("x-acs-caller-uid", "uid-123")
		ctx.Request.Header.Set("x-acs-caller-bid", "bid")
		ctx.Request.Header.Set("x-acs-parent-id", "bid")
		ctx.Request.Header.Set("x-acs-caller-type", "customer")
		ctx.Request.Header.Set("x-acs-request-id", dummyRequestID)
		ctx.Request.Header.Set("x-acs-ak-mfa-present", "false")
		ctx.Request.Header.Set("x-acs-security-transport", "false")
		ctx.Request.Header.Set("x-acs-source-ip", "*******")
		ctx.Request.Header.Set("x-acs-accesskey-id", "ak")
		ctx.Request.Header.Set("x-acs-security-token", "sk")
		ctx.Request.Header.Set("x-acs-client-ip", "*******")
		ctx.Request.Header.Set("x-acs-sts-token-principal-id", "pid")
		ctx.Request.Header.Set("x-acs-sts-token-role-id", "roleId")
		ctx.Request.Header.Set("x-acs-sts-token-caller-uid", "uid-sts")
		ctx.Request.Header.Set("x-acs-sts-token-caller-bid", "bid-sts")
		ctx.Set(KeyRequestId, &dummyRequestID)
		u := NewDummyUser()
		ctx.Set(KeyUser, u)

		newCtx := NewContextFromRequest(ctx)

		So(newCtx, ShouldNotBeNil)
		So(newCtx.GetLogger(), ShouldNotBeNil)
		So(newCtx.GetGinContext(), ShouldNotBeNil)
		So(newCtx.IsPopRequest(), ShouldBeTrue)
		So(newCtx.GetSource(), ShouldEqual, "pop|Action")
		So(newCtx.GetUid(), ShouldEqual, "uid-123")
		So(newCtx.GetCallerUid(), ShouldEqual, "uid-123")
		So(newCtx.GetCallerBid(), ShouldEqual, "bid")
		So(newCtx.GetCallerParentId(), ShouldEqual, "bid")
		So(newCtx.GetCallerType(), ShouldEqual, "customer")
		So(newCtx.GetAccessKeyId(), ShouldEqual, "ak")
		So(newCtx.GetSecurityToken(), ShouldEqual, "sk")
		So(newCtx.GetSecurityTransport(), ShouldEqual, "false")
		So(newCtx.GetAkMfaPresent(), ShouldEqual, "false")
		So(newCtx.GetSourceIp(), ShouldEqual, "*******")
		So(newCtx.GetClientIp(), ShouldEqual, "*******")
		So(newCtx.GetStsTokenPrincipalId(), ShouldEqual, "pid")
		So(newCtx.GetStsTokenRoleId(), ShouldEqual, "roleId")
		So(newCtx.GetStsTokenCallerUid(), ShouldEqual, "uid-sts")
		So(newCtx.GetStsTokenCallerBid(), ShouldEqual, "bid-sts")
		So(newCtx.GetRequestId(), ShouldEqual, dummyRequestID)
	})

	Convey("Given a rpc pop request", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		dummyRequestID := "test-request-id"
		ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
		ctx.Request.ParseForm()
		ctx.Request.PostForm.Add("acsProduct", "apig")
		ctx.Request.PostForm.Add("callerUid", "uid-123")
		ctx.Request.PostForm.Add("callerType", "customer")
		ctx.Request.PostForm.Add("action", "Action")
		ctx.Request.PostForm.Add("requestId", dummyRequestID)

		ctx.Set(KeyRequestId, &dummyRequestID)
		u := NewDummyUser()
		ctx.Set(KeyUser, u)

		newCtx := NewContextFromRequest(ctx)

		So(newCtx, ShouldNotBeNil)
		So(newCtx.GetLogger(), ShouldNotBeNil)
		So(newCtx.GetGinContext(), ShouldNotBeNil)
		So(newCtx.IsPopRequest(), ShouldBeTrue)
		So(newCtx.GetSource(), ShouldEqual, "pop|Action")
		So(newCtx.GetUid(), ShouldEqual, "uid-123")
		So(newCtx.GetRequestId(), ShouldEqual, dummyRequestID)
	})

	Convey("Given a non-pop request", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		dummyRequestID := "test-request-id"
		ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)

		ctx.Set(KeyRequestId, &dummyRequestID)
		u := NewDummyUser()
		ctx.Set(KeyUser, u)

		newCtx := NewContextFromRequest(ctx)

		So(newCtx, ShouldNotBeNil)
		So(newCtx.GetLogger(), ShouldNotBeNil)
		So(newCtx.GetGinContext(), ShouldNotBeNil)
		So(newCtx.IsPopRequest(), ShouldBeFalse)
		So(newCtx.GetSource(), ShouldEqual, "gin")
	})

	Convey("Given a no user request", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		dummyRequestID := "test-request-id"
		ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)

		ctx.Set(KeyRequestId, &dummyRequestID)

		newCtx := NewContextFromRequest(ctx)

		So(newCtx, ShouldNotBeNil)
		So(newCtx.GetLogger(), ShouldNotBeNil)
		So(newCtx.GetGinContext(), ShouldNotBeNil)
	})

	Convey("Given a nil gin.Context", t, func() {
		newCtx := NewContextFromRequest(nil)

		So(newCtx, ShouldNotBeNil)
		So(newCtx.GetLogger(), ShouldNotBeNil)
		So(newCtx.GetGinContext(), ShouldBeNil)
		So(newCtx.IsPopRequest(), ShouldBeFalse)
		So(newCtx.GetSource(), ShouldEqual, "gin")
	})
}

func TestNewContextWithUserOverride(t *testing.T) {
	Convey("Given an pop Context", t, func() {
		ctx, _ := gin.CreateTestContext(httptest.NewRecorder())
		ctx.Request, _ = http.NewRequest(http.MethodGet, "/", nil)
		ctx.Request.Header.Set("x-acs-product", "apig")
		ctx.Request.Header.Set("x-acs-api-name", "Action")
		ctx.Request.Header.Set("x-acs-request-id", "requestId-123")

		existingBaseCtx := &baseContext{
			ctx: ctx,
		}

		newUser := "overriddenUserID"
		newCtx := NewContextWithUserOverride(existingBaseCtx, newUser, "test-site")

		So(newCtx, ShouldNotBeNil)
		So(newCtx.GetUid(), ShouldEqual, newUser)
		So(newCtx.GetRequestId(), ShouldEqual, "requestId-123")
		So(newCtx.GetTraceId(), ShouldEqual, "requestId-123")
		So(newCtx.IsPopRequest(), ShouldBeTrue)

	})

	Convey("Given an bare user Context", t, func() {
		existingBaseCtx := NewContextForBareUser("123", "traceId", "", SiteTypeUnknown)

		newUser := "overriddenUserID"
		newCtx := NewContextWithUserOverride(existingBaseCtx, newUser, "test-site")

		So(newCtx, ShouldNotBeNil)
		So(newCtx.GetUid(), ShouldEqual, newUser)
		So(newCtx.GetTraceId(), ShouldEqual, "traceId")
		So(newCtx.IsPopRequest(), ShouldBeFalse)
	})
}

func TestNewContextForWorker(t *testing.T) {
	Convey("New Context For Worker", t, func() {
		ctx := NewContextForWorker("uid-123", "worker", "", "")

		So(ctx.GetBid(), ShouldEqual, "")
		So(ctx.GetLoginUid(), ShouldEqual, "uid-123")
		So(ctx.GetUserType(), ShouldEqual, UserTypeWorker)
		So(ctx.GetDomain(), ShouldEqual, "")
		So(ctx.GetActBy(), ShouldEqual, "worker")
		So(ctx.GetLogger(), ShouldNotBeNil)
		So(ctx.GetGinContext(), ShouldBeNil)
		So(ctx.IsPopRequest(), ShouldBeFalse)
		So(ctx.GetUid(), ShouldEqual, "uid-123")
		So(ctx.GetSource(), ShouldEqual, "worker|worker")
		So(ctx.GetSiteType(), ShouldEqual, SiteType(""))

	})
}

func TestNewContextForBareUser(t *testing.T) {
	Convey("New Context For Bare User", t, func() {
		ctx := NewContextForBareUser("uid-123", "", "caller", SiteTypeUnknown)

		So(ctx.GetLogger(), ShouldNotBeNil)
		So(ctx.GetGinContext(), ShouldBeNil)
		So(ctx.IsPopRequest(), ShouldBeFalse)
		So(ctx.GetUid(), ShouldEqual, "uid-123")
		So(ctx.GetSource(), ShouldEqual, "caller")
		So(ctx.GetActBy(), ShouldEqual, "")
		So(ctx.GetDomain(), ShouldEqual, "")
		So(ctx.GetUserType(), ShouldEqual, UserTypeNone)
		So(ctx.GetLoginUid(), ShouldEqual, "uid-123")
		So(ctx.GetBid(), ShouldEqual, "")

	})
}

// 这里定义一些辅助函数以便为测试生成 Dummy entities
func NewDummyUser() UserCtx {
	return NewNoneTypeUser("uid-123", "", "")
}

func NewDummyPopCtx() PopCtx {
	return &popInfo{
		requestId: "requestId-123",
	}
}
