package context

import (
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"log/slog"
)

const KeyRequestId = "requestId"
const KeyTraceId = "traceId"
const KeyAction = "action"

func getUser(ctx *gin.Context) base.UserCtx {
	u, exists := ctx.Get(base.KeyUser)
	if !exists {
		return nil
	}

	return u.(base.UserCtx)
}

func GetRequestId(ctx *gin.Context) *string {
	u, exists := ctx.Get(KeyRequestId)
	if !exists {
		return nil
	}

	return u.(*string)
}

func GetTraceId(ctx *gin.Context) *string {
	u, exists := ctx.Get(KeyTraceId)
	if !exists {
		return nil
	}

	return u.(*string)
}

func GetAction(ctx *gin.Context) *string {
	u, exist := ctx.Get(KeyAction)
	if !exist {
		return nil
	}
	return u.(*string)
}

func GetLogger(ctx *gin.Context) *slog.Logger {
	l := slog.Default()
	if ctx == nil {
		return l
	}

	t := GetRequestId(ctx)
	if t != nil {
		l = l.With("reqId", *t)
	}

	if traceId := GetTraceId(ctx); traceId != nil {
		l = l.With("traceId", *traceId)
	}

	a := GetAction(ctx)
	if a != nil {
		l = l.With("reqAction", *a)
	}

	u := getUser(ctx)
	if u != nil {
		l = l.With("user", u)
	}

	return l
}
