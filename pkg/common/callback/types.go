package callback

// UserInfo 定义返回的用户信息结构体
type UserInfoResponse struct {
	Sub       string `json:"sub"`        //唯一代表登录用户的字符串，但并不包含阿里云UID、用户名等信息。
	Uid       string `json:"uid"`        // 登录用户的ID。
	LoginName string `json:"login_name"` //阿里云账号（主账号）的登录名称。(主账号登陆才会返回)
	RequestID string `json:"requestid"`
	Name      string `json:"name"` //登录用户的显示名称。（RAM用户和RAM角色请求时才会返回该参数。）
	Bid       string `json:"bid"`
	Type      string `json:"type"` //account、user、role
	Aid       string `json:"aid"`  //登录用户所属的阿里云账号（主账号）ID。
}
