package page

import (
	"encoding/json"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
)

type ApiObject interface {
	ToMap() map[string]any
}

// PagesParams 所有的分页请求需要用此参数，不要自己定义分页参数
type PagesParams struct {
	PageNumber int `json:"pageNumber,omitempty" form:"pageNumber"`
	PageSize   int `json:"pageSize,omitempty" form:"pageSize"`
}

func (p *PagesParams) Validate() error {
	if p.PageNumber == 0 {
		p.PageNumber = 1
	}
	if p.PageNumber < 1 {
		return comnerrors.New(codes.ErrInvalidParameter, "pageNumber", p.PageNumber)
	}
	if p.PageSize == 0 {
		p.PageSize = 10
	}
	if p.PageSize < 1 || p.PageSize > 500 {
		return comnerrors.New(codes.ErrInvalidParameter, "pageSize", p.PageSize)
	}
	return nil
}

// PagesResult 如果数组元素是ApiObject类型，使用此分页结果
type PagesResult[T ApiObject] struct {
	PageNumber uint32 `json:"pageNumber"`
	PageSize   uint32 `json:"pageSize"`
	TotalSize  uint32 `json:"totalSize"`
	Items      []T    `json:"items"`
}

func TransferSlice[S any, D any](source []S, t func(S) D) []D {
	dest := make([]D, len(source))
	for i := range source {
		dest[i] = t(source[i])
	}
	return dest
}

func (r PagesResult[T]) ToMap() map[string]any {
	return map[string]any{
		"pageNumber": r.PageNumber,
		"pageSize":   r.PageSize,
		"totalSize":  r.TotalSize,
		"items": TransferSlice(r.Items, func(s T) map[string]any {
			return s.ToMap()
		}),
	}
}

// PagedItems 如果数组元素是其他类型，使用此分页结果
type PagedItems[T any] struct {
	PageNumber int   `json:"pageNumber"`
	PageSize   int   `json:"pageSize"`
	TotalSize  int64 `json:"totalSize"`
	Items      []T   `json:"items"`
}

func (p PagedItems[T]) MarshalJSON() ([]byte, error) {
	items := p.Items
	if items == nil {
		items = []T{} // 确保如果Items为nil，将其视为一个空切片
	}

	// 使用map来构建JSON结构，以便自定义Items的序列化行为
	return json.Marshal(map[string]any{
		"pageNumber": p.PageNumber,
		"pageSize":   p.PageSize,
		"totalSize":  p.TotalSize,
		"items":      items,
	})
}
