package page

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"testing"
)

type User struct {
	UserId   string
	Password string
}

type UserRequest struct {
	PagesParams
	UserId string
}

func (u *User) ToMap() map[string]any {
	return map[string]any{
		"userId": u.UserId,
	}
}

func TestPagesParams(t *testing.T) {
	j := []byte("{}")
	req := &UserRequest{}
	err := json.Unmarshal(j, req)
	assert.Nil(t, err)
	assert.Nil(t, req.Validate())

	assert.Equal(t, req.PageNumber, 1)
	assert.Equal(t, req.PageSize, 10)

	req.PageNumber = -1
	assert.True(t, comnerrors.Is(req.Validate(), codes.ErrInvalidParameter))

	req.PageNumber = 1
	req.PageSize = 500000
	assert.True(t, comnerrors.Is(req.Validate(), codes.ErrInvalidParameter))
}

func TestPagesResult(t *testing.T) {
	pr := PagesResult[*User]{
		PageNumber: 1,
		PageSize:   1,
		TotalSize:  uint32(2),
		Items: []*User{
			{
				UserId:   "1",
				Password: "2",
			},
		},
	}

	m := pr.ToMap()
	assert.NotNil(t, m)
	items, ok := m["items"].([]map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, len(items), 1)
	assert.Equal(t, items[0]["password"], nil)
}
