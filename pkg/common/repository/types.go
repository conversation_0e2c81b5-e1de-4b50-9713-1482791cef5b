package repository

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type RepositoryInfo struct {
	RepositoryId        string             `json:"repositoryId"`
	Name                string             `json:"name"`
	Url                 string             `json:"url"`
	Source              identity.Source    `json:"source"`
	Setting             *RepositorySetting `json:"setting"`
	CreateTimestamp     int64              `json:"createTimestamp"`
	LastReviewTimestamp int64              `json:"lastReviewTimestamp"`
}

type UpdateRepositoryRequest struct {
	Setting *RepositorySetting `json:"setting"`
}

func (r *UpdateRepositoryRequest) Validate() error {
	if r.Setting == nil {
		return errors.New(codes.ErrInvalidParameterWithEmpty, "setting")
	}
	if err := r.Setting.Validate(); err != nil {
		return err
	}
	return nil
}

type ListRepositoryOptions struct {
	Sources        []identity.Source
	RepositoryIds  []string
	NameAndUrlOnly bool
	page.PagesParams
}

func (r *ListRepositoryOptions) Validate() error {
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	for _, s := range r.Sources {
		switch s {
		case identity.SourceGitLab, identity.SourceGithub, identity.SourceCodeup:
		default:
			return errors.New(codes.ErrInvalidParameter, "source")
		}
	}
	return nil
}

type RepositorySetting struct {
	CustomCodeReviewRule bool                      `json:"customCodeReviewRule"`
	CodeReview           *agent.CodeReviewProperty `json:"codeReview"`
}

func (rs *RepositorySetting) Validate() error {
	if rs.CustomCodeReviewRule {
		if rs.CodeReview == nil {
			return errors.New(codes.ErrInvalidParameterWithEmpty, "codeReivew")
		}
		if err := rs.CodeReview.Validate(); err != nil {
			return err
		}
	}
	return nil
}
