package yunxiao

import "time"

type CheckPermissionResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type ListUserPermissionsResponse struct {
	OrganizationId string   `json:"organization_id"`
	UserId         string   `json:"user_id"`
	Permissions    []string `json:"permissions"`
}

type GetOrganizationInfoResponse struct {
	Id          string `json:"id"`
	Name        string `json:"name"`
	Logo        string `json:"logo"`
	Description string `json:"description"`
	CreatorId   string `json:"creator_id"`
	Owners      []struct {
		UserId   string        `json:"user_id"`
		Name     string        `json:"name"`
		Avatar   string        `json:"avatar"`
		IsAdmin  bool          `json:"is_admin"`
		Username string        `json:"username"`
		Email    string        `json:"email"`
		NickName string        `json:"nick_name"`
		StaffId  string        `json:"staff_id"`
		Licenses []interface{} `json:"licenses"`
	} `json:"owners"`
	Source            string        `json:"source"`
	Type              string        `json:"type"`
	DefaultRole       string        `json:"default_role"`
	BindSysDept       interface{}   `json:"bind_sys_dept"`
	SyncScope         int           `json:"sync_scope"`
	BindSysDepts      []interface{} `json:"bind_sys_depts"`
	CreatedAt         time.Time     `json:"created_at"`
	UpdatedAt         time.Time     `json:"updated_at"`
	IpWhiteListEnable bool          `json:"ip_white_list_enable"`
	AppSuites         []interface{} `json:"app_suites"`
}
