package yunxiao

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	http2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"net/url"
)

func getFullUrl(uri string) (string, error) {
	endpoint := config.Get(config.KeySystemYunxiaoEndpoint)
	return url.JoinPath(endpoint, uri)
}

func loadAuthHeaderFun(ctx base.Context) (map[string]string, error) {
	return map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", config.GetYunxiaoSecretKey()),
	}, nil
}

func CheckPermission(ctx base.Context, permissions []string) (bool, error) {
	if len(permissions) == 0 {
		return true, nil
	}
	uid := ctx.GetLoginUid()
	orgId := ctx.GetUid()

	uri := fmt.Sprintf("/organizations/%s/permissions:check", orgId)
	fullUrl, err := getFullUrl(uri)
	if err != nil {
		return false, errors.WithStack(err)
	}
	body := map[string]any{
		"organization_id": orgId,
		"permissions":     permissions,
		"resource":        "base/organization",
		"resource_id":     orgId,
		"user_id":         uid,
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return false, errors.WithStack(err)
	}
	resp, err := http2.InvokeHttp[CheckPermissionResponse](ctx, &http2.InvokeHttpRequest{
		Method: "POST",
		Url:    fullUrl,
		Headers: map[string]string{
			"Content-Type":     "application/json",
			"x-yunxiao-userid": uid,
		},
		Body:       bodyBytes,
		AuthLoader: loadAuthHeaderFun,
	})
	if err != nil {
		return false, errors.WithStack(err)
	}
	return resp.Success, nil
}

func ListUserPermissions(ctx base.Context) (*ListUserPermissionsResponse, error) {
	if !base.IsYunxiaoUser(ctx) {
		return nil, commonerrors.New(codes.ErrInvalidUserType)
	}
	uri := fmt.Sprintf("/organizations/%s/members:permissions", ctx.GetUid())
	fullUrl, err := getFullUrl(uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	body := map[string]any{
		"organization_id": ctx.GetUid(),
		"resources":       []string{"base/organization"},
		"user_id":         ctx.GetLoginUid(),
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp, err := http2.InvokeHttp[ListUserPermissionsResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "POST",
		Url:        fullUrl,
		Headers:    map[string]string{"Content-Type": "application/json"},
		Body:       bodyBytes,
		AuthLoader: loadAuthHeaderFun,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return resp, nil
}

func GetOrganizationInfo(ctx base.Context) (*GetOrganizationInfoResponse, error) {
	yunxiaoUser := base.GetYunxiaoUserFromContext(ctx)
	if yunxiaoUser == nil {
		return nil, commonerrors.New(codes.ErrInvalidUserType)
	}
	uri := fmt.Sprintf("/organizations/%s", ctx.GetUid())
	fullUrl, err := getFullUrl(uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	resp, err := http2.InvokeHttp[GetOrganizationInfoResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "GET",
		Url:        fullUrl,
		AuthLoader: loadAuthHeaderFun,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return resp, nil
}

func PublishAuditEvent(ctx base.Context, event map[string]any) error {
	yunxiaoUser := base.GetYunxiaoUserFromContext(ctx)
	if yunxiaoUser == nil {
		return commonerrors.New(codes.ErrInvalidUserType)
	}
	uri := "/api/v1/cloudevents"
	fullUrl, err := getFullUrl(uri)
	if err != nil {
		return errors.WithStack(err)
	}
	bodyBytes, err := json.Marshal(event)
	if err != nil {
		return errors.WithStack(err)
	}
	if _, err := http2.InvokeHttp[struct{}](ctx, &http2.InvokeHttpRequest{
		Method:     "POST",
		Url:        fullUrl,
		AuthLoader: loadAuthHeaderFun,
		Headers: map[string]string{
			"Content-Type": "application/cloudevents+json",
		},
		Body: bodyBytes,
	}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}
