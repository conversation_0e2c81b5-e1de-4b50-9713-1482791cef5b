package errors

import (
	"fmt"
	"github.com/pkg/errors"
	"sync"
)

var _ error = (*BizError)(nil)

type ErrorType string

type ErrorTemplate struct {
	Type            ErrorType
	Code            string
	MessageTemplate string
}

var lock *sync.Mutex = &sync.Mutex{}
var KnownErrors []ErrorTemplate

const (
	TypeInvalidParameter     = ErrorType("InvalidParameter")
	TypeNotFound             = ErrorType("NotFound")
	TypeConflict             = ErrorType("Conflict")
	TypeInternalError        = ErrorType("ServiceInternalError")
	TypeDatabaseError        = ErrorType("DatabaseError")
	TypeUnauthorized         = ErrorType("Unauthorized")
	TypeForbidden            = ErrorType("Forbidden")
	TypeCloudProductInactive = ErrorType("CloudProductInactive")
	TypeAccessDenied         = ErrorType("AccessDenied")
	TypeTooManyRequests      = ErrorType("TooManyRequests")
	TypeQuotaExceeded        = ErrorType("QuotaExceeded")
	TypeRedirect             = ErrorType("Redirect")
)

// BizError 带有业务信息的Error, 在接口返回的时候会根据ErrorCode获取国际化的错误信息, 如果没有则采用Message
type BizError struct {
	ErrorType
	ErrorCode    string
	ErrorMessage string
	Params       []any
	// retryable 表达这个错误是否可以重试
	retryable bool
}

// Error 只是用于实现error接口, 对于带有业务信息的, 不要通过这个返回string方法
func (b *BizError) Error() string {
	return fmt.Sprintf("ErrorType: %s, ErrorCode: %s, Message: %s, Params: %v", b.ErrorType, b.ErrorCode, b.ErrorMessage, b.Params)
}

func (b *BizError) FullCode() string {
	return fmt.Sprintf("%s.%s", b.ErrorType, b.ErrorCode)
}

// IsBizError 判断该错误类型是否为BizError
func IsBizError(err error) bool {
	if err == nil {
		return false
	}
	var bizError *BizError
	if errors.As(err, &bizError) {
		return true
	}
	return false
}

func IsDatabaseError(err error) bool {
	return errorWithType(err, TypeDatabaseError)
}
func errorWithType(err error, errorType ErrorType) bool {
	if !IsBizError(err) {
		return false
	}
	var v *BizError
	errors.As(err, &v)
	return v.ErrorType == errorType
}

func New(errorTemplate ErrorTemplate, params ...any) error {
	return errors.WithStack(&BizError{
		ErrorType:    errorTemplate.Type,
		ErrorCode:    errorTemplate.Code,
		Params:       params,
		ErrorMessage: errorTemplate.MessageTemplate,
	})
}

// NewRetryable 构造一个可重试的错误
func NewRetryable(errorTemplate ErrorTemplate, params ...any) error {
	return errors.WithStack(&BizError{
		ErrorType:    errorTemplate.Type,
		ErrorCode:    errorTemplate.Code,
		Params:       params,
		ErrorMessage: fmt.Sprintf(errorTemplate.MessageTemplate, params...),
		retryable:    true,
	})
}

func NewTemplate(t ErrorType, code string, msgTemplate string) ErrorTemplate {
	template := ErrorTemplate{Type: t, Code: code, MessageTemplate: msgTemplate}
	lock.Lock()
	defer lock.Unlock()
	KnownErrors = append(KnownErrors, template)
	return template
}

// Is 判断错误是否为指定错误类型，这里仅对比Type和Code两个字段，所以定义错误码时需要尽量独立
func Is(err error, template ErrorTemplate) bool {
	if !IsBizError(err) {
		return false
	}
	var v *BizError
	errors.As(err, &v)
	return v.ErrorType == template.Type && v.ErrorCode == template.Code
}

func AsBizError(err error) *BizError {
	if err == nil {
		return nil
	}
	var bizError *BizError
	if errors.As(err, &bizError) {
		return bizError
	}
	return nil
}

func IsRetryable(err error) bool {
	bizErr := AsBizError(err)
	if bizErr == nil {
		return false
	}
	return bizErr.retryable
}

func FormatFullStack(err error) string {
	return fmt.Sprintf("%+v", err)
}
