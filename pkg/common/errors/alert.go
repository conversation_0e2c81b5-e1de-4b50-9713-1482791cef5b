package errors

import (
	"encoding/json"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"regexp"
)

type AlertScope string

const (
	AlertScopeGinRoutes       AlertScope = "gin.routes"
	AlertScopeSandbox         AlertScope = "sandbox"
	AlertScopeAgentRuntime    AlertScope = "agent-runtime"
	AlertScopeAgent           AlertScope = "agent"
	AlertScopeCodePlatform    AlertScope = "codeplatform"
	AlertScopeExternalWebhook AlertScope = "external.webhook"
	AlertScopeCodeReview      AlertScope = "codereview"
	AlertScopeGoroutine       AlertScope = "goroutine"
	AlertScopeMetrics         AlertScope = "metrics"
	AlertScopeRedis           AlertScope = "redis"
)

// alertBlockConfig 支持精确ID匹配与正则表达式匹配。
type alertBlockConfig struct {
	// 用户纬度
	UserIds []string `json:"userIds,omitempty"`
	// 精确ID匹配
	ExactIds []string `json:"exactIds,omitempty"`
	// 正则表达式匹配
	RegexPatterns []string `json:"regexPatterns,omitempty"`
}

func isAlertBlocked(ctx base.Context, scope AlertScope, resourceIds []string) bool {
	configKey := string(config.KeyUnifiedAlertBlockConfigPrefix + scope)
	configJSON := config.GetOrDefault(configKey, "{}")

	abc := &alertBlockConfig{}
	if err := json.Unmarshal([]byte(configJSON), abc); err != nil {
		ctx.GetLogger().Error("parse alert block config error", "key", configKey, "value", configJSON, "err", err)
		return false
	}
	for _, uid := range abc.UserIds {
		if uid == ctx.GetUid() {
			return true
		}
	}
	for _, id := range abc.ExactIds {
		for _, resourceId := range resourceIds {
			if resourceId == id {
				return true
			}
		}
	}
	for _, regexPattern := range abc.RegexPatterns {
		regex, err := regexp.Compile(regexPattern)
		if err != nil {
			ctx.GetLogger().Error("parse alert block regexp error", "key", configKey, "regex", regexPattern, "err", err)
			continue
		}
		for _, resourceId := range resourceIds {
			if regex.MatchString(resourceId) {
				return true
			}
		}
	}
	return false
}

const (
	alertPrefixWarning = "[AGENT_ALERT_WARNING] "
	alertPrefixError   = "[AGENT_ALERT_ERROR] "
	alertPrefixFatal   = "[AGENT_ALERT_FATAL] "
)

// AlertWarning 构建并记录一条警告级别的告警信息。
// 该信息会以 "[WARNING] " 为前缀，以表明其严重程度。
// 这种告警表示问题需要通过告警手段显露，但可能不需要立即处理。
//
// 参数说明：
// - ctx: 上下文对象，提供日志记录功能。告警黑名单支持从ctx上自动获取UID，并根据配置决定是否跳过告警行为。
// - scope: 字符串，表示告警的作用域。
// - resourceIds: 字符串切片，与该告警关联的资源ID。例如: [gwId, apiId]，任意ID匹配中规则，则忽略告警。可以为空，为空时无法通过配置针对性屏蔽告警。
// - msg: 字符串，要记录的告警消息。
// - err: error，告警关联的错误信息。
// - params: 可选参数，用于添加额外的日志内容。
//
// 注意事项：
// - 如果告警被屏蔽（基于作用域和资源ID），则依然会输出日志，但不会触发告警。
func AlertWarning(ctx base.Context, scope AlertScope, resourceIds []string, msg string, err error, params ...any) {
	if !isAlertBlocked(ctx, scope, resourceIds) {
		msg = alertPrefixWarning + msg
	}
	logParams := []any{"scope", scope, "resourceIds", resourceIds, "err", FormatFullStack(errors.WithStack(err))}
	logParams = append(logParams, params...)
	ctx.GetLogger().Warn(msg, logParams...)
}

// AlertError 构建并记录一条错误级别的告警信息。
// 该信息会以 "[ERROR] " 为前缀，以表明其严重程度。
// 这种告警表示问题需要通过告警手段显露，尽可能尽快处理。
//
// 参数说明：
// - ctx: 上下文对象，提供日志记录功能。告警黑名单支持从ctx上自动获取UID，并根据配置决定是否跳过告警行为。
// - scope: 字符串，表示告警的作用域。
// - resourceIds: 字符串切片，与该告警关联的资源ID。例如: [gwId, apiId]，任意ID匹配中规则，则忽略告警。可以为空，为空时无法通过配置针对性屏蔽告警。
// - msg: 字符串，要记录的告警消息。
// - err: error，告警关联的错误信息。
// - params: 可选参数，用于添加额外的日志内容。
//
// 注意事项：
// - 如果告警被屏蔽（基于作用域和资源ID），则依然会输出日志，但不会触发告警。
func AlertError(ctx base.Context, scope AlertScope, resourceIds []string, msg string, err error, params ...any) {
	if !isAlertBlocked(ctx, scope, resourceIds) {
		msg = alertPrefixError + msg
	}
	logParams := []any{"scope", scope, "resourceIds", resourceIds, "err", FormatFullStack(errors.WithStack(err))}
	logParams = append(logParams, params...)
	ctx.GetLogger().Error(msg, logParams...)
}

// AlertFatal 构建并记录一条致命级别的告警信息。
// 该信息会以 "[FATAL] " 为前缀，以表明其严重程度。
// 这种告警表示问题需要通过告警手段显露，需要立即处理。
//
// 参数说明：
// - ctx: 上下文对象，提供日志记录功能。告警黑名单支持从ctx上自动获取UID，并根据配置决定是否跳过告警行为。
// - scope: 字符串，表示告警的作用域。
// - resourceIds: 字符串切片，与该告警关联的资源ID。例如: [gwId, apiId]，任意ID匹配中规则，则忽略告警。可以为空，为空时无法通过配置针对性屏蔽告警。
// - msg: 字符串，要记录的告警消息。
// - err: error，告警关联的错误信息。
// - params: 可选参数，用于添加额外的日志内容。
//
// 注意事项：
// - 如果告警被屏蔽（基于作用域和资源ID），则依然会输出日志，但不会触发告警。
func AlertFatal(ctx base.Context, scope AlertScope, resourceIds []string, msg string, err error, params ...any) {
	if !isAlertBlocked(ctx, scope, resourceIds) {
		msg = alertPrefixFatal + msg
	}
	logParams := []any{"scope", scope, "resourceIds", resourceIds, "err", FormatFullStack(errors.WithStack(err))}
	logParams = append(logParams, params...)
	ctx.GetLogger().Error(msg, logParams...)
}
