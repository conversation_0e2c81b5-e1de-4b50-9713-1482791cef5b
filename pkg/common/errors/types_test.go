package errors

import (
	"errors"
	"fmt"
	errorswrapper "github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"testing"
)

var (
	ErrVpcNotFound = ErrorTemplate{Type: TypeNotFound, Code: "VpcNotFound", MessageTemplate: "vpc %v is not found."}
	ErrOther       = ErrorTemplate{Type: TypeNotFound, Code: "Other", MessageTemplate: "other error."}
)

func TestIsBizError(t *testing.T) {
	type args struct {
		err error
	}
	tests := []struct {
		args args
		want bool
	}{
		{
			args: args{err: errors.New("test")},
			want: false,
		},
		{
			args: args{err: errorswrapper.New("test-2")},
			want: false,
		},
		{
			args: args{err: New(ErrOther, "postpaid")},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.args.err.Error(), func(t *testing.T) {
			if got := IsBizError(tt.args.err); got != tt.want {
				t.Errorf("IsBizError() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewError(t *testing.T) {
	err := New(ErrVpcNotFound, "vpc-123")
	var v *BizError
	errors.As(err, &v)
	assert.Equal(t, v.ErrorMessage, "vpc vpc-123 is not found.")
	fmt.Println(v.ErrorMessage)
}

func TestIs(t *testing.T) {
	err := New(ErrVpcNotFound, "vpc-123")
	assert.True(t, Is(err, ErrVpcNotFound))
	assert.True(t, Is(errorswrapper.WithStack(err), ErrVpcNotFound))
	assert.False(t, Is(errors.New("abc"), ErrVpcNotFound))
	assert.False(t, Is(New(ErrOther), ErrVpcNotFound))
	assert.False(t, IsBizError(errors.New("abc")))
	assert.False(t, IsBizError(nil))

}

func TestAs(t *testing.T) {
	err := New(ErrVpcNotFound, "vpc-123")
	bizError := AsBizError(err)
	assert.NotNil(t, bizError)
	assert.Equal(t, bizError.ErrorCode, ErrVpcNotFound.Code)
	assert.Nil(t, AsBizError(nil))
	assert.Nil(t, AsBizError(errors.New("abc")))
}

func TestOther(t *testing.T) {
	err := New(ErrVpcNotFound, "vpc-123")
	assert.Equal(t, AsBizError(err).FullCode(), "NotFound.VpcNotFound")
	assert.False(t, IsDatabaseError(err))
	assert.False(t, IsDatabaseError(errors.New("abc")))
}

func TestIsRetryable(t *testing.T) {

	tests := []struct {
		err  error
		want bool
	}{
		{
			err:  New(ErrVpcNotFound),
			want: false,
		},
		{
			err:  NewRetryable(ErrVpcNotFound),
			want: true,
		},
		{
			err:  errors.New("err"),
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.err.Error(), func(t *testing.T) {
			assert.Equalf(t, tt.want, IsRetryable(tt.err), "IsRetryable(%v)", tt.err)
		})
	}
}
