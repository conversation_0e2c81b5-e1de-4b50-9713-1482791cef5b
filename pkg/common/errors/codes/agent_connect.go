package codes

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"

var (
	ErrAgentConnectUnauthorized = errors.NewTemplate(errors.TypeUnauthorized, "Unauthorized", "agent connect unauthorized.")
	ErrFailToUpgrade            = errors.NewTemplate(errors.TypeInvalidParameter, "FailToUpgrade", "fail to upgrade to websocket connection.")
	ErrMalformedClientMessage   = errors.NewTemplate(errors.TypeInvalidParameter, "MalformedMessage", "malformed message from client.")
	ErrSessionIdMissingMessage  = errors.NewTemplate(errors.TypeInvalidParameter, "SessionIdMissing", "missing sessionId.")
	ErrRequestIdMissingMessage  = errors.NewTemplate(errors.TypeInvalidParameter, "RequestIdMissing", "missing requestId.")

	ErrFailToSendMessage         = errors.NewTemplate(errors.TypeInternalError, "FailToSend", "fail to send message.")
	ErrFailToRequestMessage      = errors.NewTemplate(errors.TypeInternalError, "FailToRequest", "fail to get response.")
	ErrResponseWithDetailMessage = errors.NewTemplate(errors.TypeInternalError, "ResponseError", "agent connect response with error: %v.")

	ErrRequestTimeoutMessage = errors.NewTemplate(errors.TypeInternalError, "RequestTimeout", "request timeout.")
)
