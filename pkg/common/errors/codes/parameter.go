package codes

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"

var (
	ErrInvalidParameter                       = errors.NewTemplate(errors.TypeInvalidParameter, "WithValue", "parameter %s is invalid, value: %v.")
	ErrInvalidParameterWithDetail             = errors.NewTemplate(errors.TypeInvalidParameter, "WithDetail", "parameter %s is invalid, detail: %v.")
	ErrInvalidParameterWithEmpty              = errors.NewTemplate(errors.TypeInvalidParameter, "IsEmpty", "parameter %s should not be empty.")
	ErrInvalidParameterWithBothEmpty          = errors.NewTemplate(errors.TypeInvalidParameter, "BothEmpty", "parameter %s and %s should not both be empty.")
	ErrInvalidParameterFailToBindUriParameter = errors.NewTemplate(errors.TypeInvalidParameter, "FailToBindUriParameter", "fail to bind uri parameter.")
	ErrInvalidParameterFailToBindQuery        = errors.NewTemplate(errors.TypeInvalidParameter, "FailToBindQuery", "fail to bind query.")
	ErrInvalidParameterFailToBindBody         = errors.NewTemplate(errors.TypeInvalidParameter, "FailToBindBody", "fail to bind body.")
	ErrInvalidGitlabToken                     = errors.NewTemplate(errors.TypeInvalidParameter, "InvalidGitlabToken", "invalid gitlab token.")
	ErrInvalidGitlabHost                      = errors.NewTemplate(errors.TypeInvalidParameter, "InvalidGitlabHost", "invalid gitlab host.")
	ErrInvalidParameterStringTooLong          = errors.NewTemplate(errors.TypeInvalidParameter, "StringTooLong", "parameter %s is too long, max length: %d, actual length: %d.")
)
