package codes

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"

var (
	// ErrUnknownError
	// Deprecated: 不要使用这个异常，所有异常要做分类
	ErrUnknownError = errors.NewTemplate(errors.TypeInternalError, "UnknownError", "unknown error occurred.")
)

var (
	ServerInternalError               = errors.NewTemplate(errors.TypeInternalError, "ServerInternalError", "server internal error.")
	TooManySSERequests                = errors.NewTemplate(errors.TypeTooManyRequests, "TooManySSERequests", "too many sse requests.")
	ErrRequestExternalFailed          = errors.NewTemplate(errors.TypeInternalError, "ExternalRequestError", "request to other API failed, requestId: %s")
	ErrExternalResourceNotFound       = errors.NewTemplate(errors.TypeNotFound, "ExternalResourceNotFound", "external resource not found, requestId: %s")
	ErrInstallationNumberExceedsLimit = errors.NewTemplate(errors.TypeQuotaExceeded, "InstallationNumberExceedsLimit", "已达到应用安装上限")
)
