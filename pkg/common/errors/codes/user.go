package codes

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"

var (
	ErrFailToQueryUser            = errors.NewTemplate(errors.TypeInternalError, "FailToQueryUser", "fail to query user of id %v.")
	ErrFailToSaveUser             = errors.NewTemplate(errors.TypeInternalError, "FailToSaveUser", "fail to save user of id %v.")
	ErrGithubWebhooktUnauthorized = errors.NewTemplate(errors.TypeUnauthorized, "Unauthorized", "fail to validate github webhook signature.")
	ErrForbidden                  = errors.NewTemplate(errors.TypeForbidden, "Forbidden", "no permission of %v")
	ErrInvalidUserType            = errors.NewTemplate(errors.TypeInternalError, "InvalidUserType", "invalid user type %v")
)
