package errors

import (
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/test/basemock"
	"testing"
)

func TestIsiAlertBlocked(t *testing.T) {
	mockey.PatchConvey("isAlertBlocked", t, func() {
		mockey.PatchConvey("UID命中", func() {
			mockey.Mock(config.GetOrDefault).Return(`{"userIds":["123","456"]}`).Build()

			ctx := basemock.NewMockContext("123")
			isBlocked := isAlertBlocked(ctx, "test", []string{"gw-abc"})
			convey.So(isBlocked, convey.ShouldBeTrue)
		})
		mockey.PatchConvey("精确资源ID", func() {
			mockey.Mock(config.GetOrDefault).Return(`{"userIds":["123","456"],"exactIds": ["gw-abc", "gw-def"]}`).Build()

			ctx := basemock.NewMockContext("test")
			isBlocked := isAlertBlocked(ctx, "test", []string{"gw-abc"})
			convey.So(isBlocked, convey.ShouldBeTrue)
		})
		mockey.PatchConvey("正则匹配", func() {
			mockey.Mock(config.GetOrDefault).Return(`{"userIds":["123","456"],"regexPatterns": ["gw-.*"]}`).Build()

			ctx := basemock.NewMockContext("test")
			isBlocked := isAlertBlocked(ctx, "test", []string{"gw-abc"})
			convey.So(isBlocked, convey.ShouldBeTrue)
		})
		mockey.PatchConvey("未命中", func() {
			mockey.Mock(config.GetOrDefault).Return(`{"userIds":["123","456"],"regexPatterns": ["gw-.*"]}`).Build()
			ctx := basemock.NewMockContext("test")
			isBlocked := isAlertBlocked(ctx, "test", []string{"api-abc"})
			convey.So(isBlocked, convey.ShouldBeFalse)
		})
	})
}
