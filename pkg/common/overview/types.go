package overview

type MetricInfo struct {
	Name         MetricName   `json:"name"`
	Value        float64      `json:"value"`
	CompareRatio float64      `json:"compareRatio"`
	Period       MetricPeriod `json:"period"`
}

type MetricName string

const (
	MergeRequestCount               MetricName = "MergeRequestCount"
	CodeReviewAgentCommentCount     MetricName = "CodeReviewAgentCommentCount"
	AverageMergeRequestTime         MetricName = "AverageMergeRequestTime"
	AverageCodeSuggestionApplyRatio MetricName = "AverageCodeSuggestionApplyRatio"
)

type MetricPeriod string

const (
	Period7day MetricPeriod = "7d"
)
