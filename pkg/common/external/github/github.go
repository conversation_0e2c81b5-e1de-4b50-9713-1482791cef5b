package github

import (
	"github.com/bradleyfalzon/ghinstallation/v2"
	"github.com/google/go-github/v69/github"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"golang.org/x/oauth2"
	"k8s.io/utils/ptr"
	"net/http"
	"os"
	"strconv"
)

var (
	oauthConfig = &oauth2.Config{
		//ClientID:     "",
		//ClientSecret: "",
		Endpoint: oauth2.Endpoint{
			AuthURL:  "https://github.com/login/oauth/authorize",
			TokenURL: "https://github.com/login/oauth/access_token",
		},
	}
)

func newClientByAuthCode(ctx base.Context, appId int64, code string) (*github.Client, error) {
	// 使用授权码换取访问令牌
	appClientId := config.GetGithubAppClientId(appId)
	if appClientId == "" {
		ctx.GetLogger().Error("failed to get github app client id", "appId", appId)
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	appClientSecret := config.GetGithubAppClientSecret(appId)
	if appClientSecret == "" {
		ctx.GetLogger().Error("failed to get github app client secret", "appId", appId)
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	oauthConfig.ClientID = appClientId
	oauthConfig.ClientSecret = appClientSecret
	ctx.GetLogger().Info("exchange token", "ClientID", oauthConfig.ClientID, "ClientSecret", oauthConfig.ClientSecret)
	token, err := oauthConfig.Exchange(ctx, code)
	if err != nil {
		ctx.GetLogger().Error("failed to exchange token", "authorization code", code, "err", err)
		return nil, errors.WithStack(err)
	}
	ts := oauth2.StaticTokenSource(
		&oauth2.Token{AccessToken: token.AccessToken},
	)
	tc := oauth2.NewClient(ctx, ts)
	client := github.NewClient(tc)
	return client, nil
}

func newAppClient(ctx base.Context, appId int64) (*github.Client, error) {
	appConfig := config.GetGithubAppConfig(appId)
	if appConfig == "" {
		ctx.GetLogger().Error("failed to get github app config", "appId", appId)
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	itr, err := ghinstallation.NewAppsTransport(http.DefaultTransport, appId, []byte(appConfig))
	if err != nil {
		ctx.GetLogger().Error("failed to create github app client", "appId", appId, "err", err)
		return nil, errors.WithStack(err)
	}
	cli := github.NewClient(&http.Client{Transport: itr})
	return cli, nil
}

func GetGithubUserInfoByAuthCode(ctx base.Context, appId int64, code string) (*github.User, error) {
	client, err := newClientByAuthCode(ctx, appId, code)
	if err != nil {
		ctx.GetLogger().Error("failed to create github client", "authorization code", code, "err", err)
		return nil, errors.WithStack(err)
	}
	user, _, err := client.Users.Get(ctx, "")
	if err != nil {
		return nil, errors.WithStack(err)
	}
	ctx.GetLogger().Info("successfully get github user info", "user", user)
	return user, nil
}

func GetGithubUserInfoByInstallationId(ctx base.Context, appId int64, installationId int64) (*github.User, error) {
	appClient, err := newAppClient(ctx, appId)
	if err != nil {
		ctx.GetLogger().Error("failed to create github app client", "appId", appId, "err", err)
		return nil, errors.WithStack(err)
	}
	installation, _, err := appClient.Apps.GetInstallation(ctx, installationId)
	if err != nil {
		ctx.GetLogger().Error("failed to get installation", "installationId", installationId, "err", err)
		return nil, errors.WithStack(err)
	}
	return installation.Account, nil
}

func UserIsOrganizationMember(ctx base.Context, appId int64, installationId int64, userName string, orgName string) (bool, error) {
	appClient, err := newClient(ctx, appId, installationId)
	if err != nil {
		ctx.GetLogger().Error("failed to create github app client", "appId", appId, "err", err)
		return false, errors.WithStack(err)
	}
	res, _, err := appClient.Organizations.IsMember(ctx, orgName, userName)
	if err != nil {
		ctx.GetLogger().Error("failed to check if user is member of organization", "userName", userName, "orgName", orgName, "err", err)
		return false, errors.WithStack(err)
	}
	return res, nil
}

func newClient(ctx base.Context, appId int64, installationId int64) (*github.Client, error) {
	appConfig := config.GetGithubAppConfig(appId)
	if appConfig == "" {
		ctx.GetLogger().Error("failed to get github app config", "appId", appId)
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	itr, err := ghinstallation.New(http.DefaultTransport, appId, installationId, []byte(appConfig))
	if err != nil {
		ctx.GetLogger().Error("failed to create ghinstallation", "appId", appId, "installationId", installationId, "err", err)
		return nil, errors.WithStack(err)
	}

	cli := github.NewClient(&http.Client{Transport: itr})
	return cli, nil
}

func newClientByIdentity(ctx base.Context, idInfo *identity.IdentityInfo) (*github.Client, error) {
	if idInfo.Source != identity.SourceGithub {
		ctx.GetLogger().Error("failed to create github client, invalid source", "source", idInfo.Source)
		return nil, commonerrors.New(codes.ErrInvalidParameter, "source", idInfo.Source)
	}
	if os.Getenv("GITHUB_WITH_BASE_AUTH") == "true" {
		githubClient := github.NewClient(nil).WithAuthToken(idInfo.PlatformToken)
		return githubClient, nil
	} else {
		if idInfo.PlatformProperty == nil {
			ctx.GetLogger().Error("failed to create github client, invalid platform property")
			return nil, commonerrors.New(codes.ErrInvalidParameter, "platformProperty", idInfo.PlatformProperty)
		}
		return newClient(ctx, int64(idInfo.PlatformProperty.GithubAppId), int64(idInfo.PlatformProperty.GithubInstallationId))
	}

}

func GetMergeRequest(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int) (*github.PullRequest, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	pr, resp, err := client.PullRequests.Get(ctx, owner, repo, prId)
	if err != nil {
		ctx.GetLogger().Error("failed to get merge request",
			"owner", owner,
			"repo", repo,
			"prId", prId,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return pr, nil
}

func ListCommits(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int) ([]*github.RepositoryCommit, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commits, resp, err := client.PullRequests.ListCommits(ctx, owner, repo, prId, &github.ListOptions{Page: 1, PerPage: 100})
	if err != nil {
		ctx.GetLogger().Error("failed to list commits", "owner", owner,
			"repo", repo,
			"prId", prId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return commits, nil
}

func ListPullRequestComments(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, number int) ([]*github.PullRequestComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	opts := &github.PullRequestListCommentsOptions{
		Sort:      "created",
		Direction: "asc",
		ListOptions: github.ListOptions{
			Page:    1,
			PerPage: 100,
		},
	}
	comments, resp, err := client.PullRequests.ListComments(ctx, owner, repo, number, opts)
	if err != nil {
		ctx.GetLogger().Error("failed to list pull request comment", "owner", owner,
			"repo", repo,
			"pull request number", number,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return comments, nil
}

func GetIssueComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, commentId int64) (*github.IssueComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	comment, resp, err := client.Issues.GetComment(ctx, owner, repo, commentId)
	if err != nil {
		if resp != nil && resp.StatusCode == http.StatusNotFound {
			return nil, commonerrors.New(codes.ErrMergeRequestCommentNotFound, commentId)
		}
		ctx.GetLogger().Error("failed to get issue comment", "owner", owner,
			"repo", repo,
			"commentId", commentId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return comment, nil
}

func GetPRComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, commentId int64) (*github.PullRequestComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	comment, resp, err := client.PullRequests.GetComment(ctx, owner, repo, commentId)
	if err != nil {
		ctx.GetLogger().Error("failed to get pull request comment", "owner", owner,
			"repo", repo,
			"commentId", commentId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return comment, nil
}

func ListPullRequestAllFiles(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int) ([]*github.CommitFile, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	files, resp, err := client.PullRequests.ListFiles(ctx, owner, repo, prId, &github.ListOptions{Page: 1, PerPage: 100})
	if err != nil {
		ctx.GetLogger().Error("failed to list pull request all files", "owner", owner,
			"repo", repo,
			"prId", prId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return files, nil
}

func CreatePullRequestComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int, comment *github.PullRequestComment) (*github.PullRequestComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commentResp, resp, err := client.PullRequests.CreateComment(ctx, owner, repo, prId, comment)
	if err != nil {
		ctx.GetLogger().Error("failed to create pull request comment", "owner", owner,
			"repo", repo,
			"prId", prId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	ctx.GetLogger().Info("create pull request comment success", "comment", comment)
	return commentResp, nil
}

func ReplyPullRequestComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int, commentId int64, comment *github.PullRequestComment) (*github.PullRequestComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commentResp, resp, err := client.PullRequests.CreateCommentInReplyTo(ctx, owner, repo, prId, comment.GetBody(), commentId)
	if err != nil {
		ctx.GetLogger().Error("failed to reply to pull request comment", "owner", owner,
			"repo", repo,
			"prId", prId,
			"commentId", commentId,
			"resp", resp,
			"err", err.Error())
	}
	return commentResp, nil
}

func CreateIssueComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, issueId int, comment *github.IssueComment) (*github.IssueComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commentResp, resp, err := client.Issues.CreateComment(ctx, owner, repo, issueId, comment)
	if err != nil {
		ctx.GetLogger().Error("failed to create issue comment", "owner", owner,
			"repo", repo,
			"issueId", issueId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return commentResp, nil
}

func UpdateIssueComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, commentId int64, comment *github.IssueComment) (*github.IssueComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commentResp, resp, err := client.Issues.EditComment(ctx, owner, repo, commentId, comment)
	if err != nil {
		ctx.GetLogger().Error("failed to update issue comment", "owner", owner,
			"repo", repo,
			"commentId", commentId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return commentResp, nil
}

func DeleteIssueComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, commentId int64) error {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return errors.WithStack(err)
	}
	resp, err := client.Issues.DeleteComment(ctx, owner, repo, commentId)
	if err != nil {
		ctx.GetLogger().Error("failed to delete issue comment", "owner", owner,
			"repo", repo,
			"commentId", commentId,
			"resp", resp,
			"err", err.Error())
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("delete issue comment success", "owner", owner, "repo", repo, "commentId", commentId)
	return nil
}

func DeletePullRequestComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, commentId int64) error {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return errors.WithStack(err)
	}
	resp, err := client.PullRequests.DeleteComment(ctx, owner, repo, commentId)
	if err != nil {
		ctx.GetLogger().Error("failed to delete pull request comment", "owner", owner,
			"repo", repo,
			"commentId", commentId,
			"resp", resp,
			"err", err.Error())
		return errors.WithStack(err)
	}
	return nil
}

func UpdatePullRequestComment(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, commentId int64, comment *github.PullRequestComment) (*github.PullRequestComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	commentResp, resp, err := client.PullRequests.EditComment(ctx, owner, repo, commentId, comment)
	if err != nil {
		ctx.GetLogger().Error("failed to update pull request comment", "owner", owner,
			"repo", repo,
			"commentId", commentId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return commentResp, nil
}

func GetRepositoryFile(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, opts *codeplatform.GetRepositoryFileOpts) (*github.RepositoryContent, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var options *github.RepositoryContentGetOptions
	if opts.Ref != nil {
		options = &github.RepositoryContentGetOptions{Ref: *opts.Ref}
	}
	fileContent, _, resp, err := client.Repositories.GetContents(ctx, owner, repo, opts.Path, options)
	if err != nil {
		ctx.GetLogger().Error("failed to get repository file", "owner", owner,
			"repo", repo,
			"path", opts.Path,
			"ref", opts.Ref,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return fileContent, nil
}

// CreateReview 创建并提交 pr review
func CreateReview(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int, review *github.PullRequestReviewRequest) (*github.PullRequestReview, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	prReview, resp, err := client.PullRequests.CreateReview(ctx, owner, repo, prId, review)
	if err != nil {
		ctx.GetLogger().Error("failed to create PR review",
			"owner", owner,
			"repo", repo,
			"prId", prId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	ctx.GetLogger().Info("create github review", "review", prReview)
	review.Event = ptr.To("COMMENT")
	review.Body = ptr.To("> Lingma Agent Reviewing...")

	subReview, resp, err := client.PullRequests.SubmitReview(ctx, owner, repo, prId, prReview.GetID(), review)
	if err != nil {
		ctx.GetLogger().Error("failed to submit PR review, delete pending review",
			"owner", owner,
			"repo", repo,
			"prId", prId,
			"resp", resp,
			"subReview", subReview,
			"err", err.Error())

		// 删掉提交的review
		_, resp, delErr := client.PullRequests.DeletePendingReview(ctx, owner, repo, prId, prReview.GetID())
		if delErr != nil {
			ctx.GetLogger().Error("failed to delete pending review",
				"owner", owner,
				"repo", repo,
				"prId", prId,
				"resp", resp,
				"err", delErr.Error())
		}
		return nil, errors.WithStack(err)
	}

	return prReview, nil
}

func ListReviewComments(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int, reviewId int64) ([]*github.PullRequestComment, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// todo 这里没传page参数
	comments, resp, err := client.PullRequests.ListReviewComments(ctx, owner, repo, prId, reviewId, nil)
	if err != nil {
		ctx.GetLogger().Error("failed to list review comments",
			"owner", owner,
			"repo", repo,
			"prId", prId,
			"reviewId", reviewId,
			"resp", resp,
			"err", err.Error())
		return nil, errors.WithStack(err)
	}
	return comments, nil
}

func UpdateReview(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int, reviewId int64, content string) error {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return errors.WithStack(err)
	}
	_, resp, err := client.PullRequests.UpdateReview(ctx, owner, repo, prId, reviewId, content)
	if err != nil {
		ctx.GetLogger().Error("failed to update review",
			"owner", owner,
			"repo", repo,
			"prId", prId,
			"reviewId", reviewId,
			"resp", resp,
			"err", err.Error())
		return errors.WithStack(err)
	}
	return nil
}

func CreateCheckRun(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo, headSha string, opts *codeplatform.CreateCheckRunOpts) (*github.CheckRun, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	checkRun, resp, err := client.Checks.CreateCheckRun(ctx, owner, repo, github.CreateCheckRunOptions{
		Name:    opts.Name,
		Status:  opts.Status,
		Output:  &github.CheckRunOutput{Title: opts.Output, Summary: opts.Output, Text: opts.Output},
		HeadSHA: headSha,
	})
	if err != nil {
		ctx.GetLogger().Error("failed to create check run", "resp", resp, "err", err.Error(), "owner", owner, "repo", repo, "headSha", headSha)
		return nil, errors.WithStack(err)

	}
	ctx.GetLogger().Info("create check run", "checkRun", checkRun)
	return checkRun, nil
}

func UpdateCheckRun(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, opts *codeplatform.UpdateCheckRunOpts) error {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return errors.WithStack(err)
	}
	checkRunId, err := strconv.ParseInt(opts.Id, 10, 64)
	if err != nil {
		return commonerrors.New(codes.ErrInvalidParameter, "id", opts.Id)
	}
	checkRun, resp, err := client.Checks.UpdateCheckRun(ctx, owner, repo, checkRunId, github.UpdateCheckRunOptions{
		Output:     &github.CheckRunOutput{Title: opts.Output, Summary: opts.Output, Text: opts.Output},
		Name:       opts.Name,
		DetailsURL: opts.DetailUrl,
		Status:     opts.Status,
		Conclusion: opts.Conclusion,
	})
	if err != nil {
		ctx.GetLogger().Error("failed to update check run", "resp", resp, "err", err.Error(), "owner", owner, "repo", repo, "checkRunId", checkRunId)
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("update check run", "checkRun", checkRun)
	return nil
}

func ListPullRequestCommits(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo string, prId int) ([]*github.RepositoryCommit, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var allCommits []*github.RepositoryCommit
	opts := &github.ListOptions{
		Page:    1,
		PerPage: 100,
	}
	for {
		commits, resp, err := client.PullRequests.ListCommits(ctx, owner, repo, prId, nil)
		if err != nil {
			ctx.GetLogger().Error("failed to list pull request commits", "err", err.Error(), "resp", resp)
			return nil, errors.WithStack(err)
		}
		allCommits = append(allCommits, commits...)

		if len(commits) < opts.PerPage {
			break
		}
		opts.Page++
	}
	return allCommits, nil
}

func Compare(ctx base.Context, identityInfo *identity.IdentityInfo, owner, repo, base, head string) (*github.CommitsComparison, error) {
	client, err := newClientByIdentity(ctx, identityInfo)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	comparedCommits, resp, err := client.Repositories.CompareCommits(ctx, owner, repo, base, head, &github.ListOptions{})
	if err != nil {
		ctx.GetLogger().Error("failed to compare commits", "repo", repo, "resp", resp, "err", err)
		return nil, errors.WithStack(err)
	}

	return comparedCommits, nil
}
