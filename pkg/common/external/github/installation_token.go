package github

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/go-github/v69/github"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
)

// InstallationTokenRequest 请求Installation Token的参数
type InstallationTokenRequest struct {
	Repository   string   `json:"repository"`   // owner/repo格式
	Permissions  map[string]string `json:"permissions,omitempty"` // 权限设置
	Repositories []string `json:"repositories,omitempty"` // 限制访问的仓库列表
}

// InstallationTokenResponse GitHub API返回的Installation Token响应
type InstallationTokenResponse struct {
	Token        string                 `json:"token"`
	ExpiresAt    time.Time             `json:"expires_at"`
	Permissions  map[string]string     `json:"permissions"`
	Repositories []github.Repository   `json:"repositories,omitempty"`
}

// TokenExchangeService Installation Token交换服务
type TokenExchangeService struct {
	oidcValidator *OIDCValidator
}

// NewTokenExchangeService 创建新的Token交换服务
func NewTokenExchangeService() *TokenExchangeService {
	return &TokenExchangeService{
		oidcValidator: NewOIDCValidator(),
	}
}

// ExchangeOIDCForInstallationToken 使用OIDC令牌换取Installation Token
func (s *TokenExchangeService) ExchangeOIDCForInstallationToken(ctx base.Context, oidcToken string, req *InstallationTokenRequest) (*InstallationTokenResponse, error) {
	// 1. 验证OIDC令牌
	claims, err := s.oidcValidator.ValidateOIDCToken(ctx, oidcToken, req.Repository)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 2. 解析repository信息
	repoParts := strings.Split(claims.Repository, "/")
	if len(repoParts) != 2 {
		return nil, commonerrors.New(codes.ErrInvalidParameter, "repository", "invalid repository format")
	}
	owner := repoParts[0]
	repo := repoParts[1]

	// 3. 查找对应的GitHub App安装
	installationId, appId, err := s.findInstallationForRepository(ctx, owner, repo)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 4. 生成Installation Token
	token, err := s.createInstallationToken(ctx, appId, installationId, req)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	ctx.GetLogger().Info("Installation token created successfully",
		"repository", claims.Repository,
		"actor", claims.Actor,
		"installation_id", installationId,
		"expires_at", token.ExpiresAt)

	return token, nil
}

// findInstallationForRepository 查找仓库对应的GitHub App安装
func (s *TokenExchangeService) findInstallationForRepository(ctx base.Context, owner, repo string) (int64, int64, error) {
	// 方案1：从集中配置获取默认的App ID
	appId := config.GetOrDefaultInt64(config.KeyGithubDefaultAppId, 0)
	if appId == 0 {
		return 0, 0, commonerrors.New(codes.ErrInvalidParameter, "app_id", "no default GitHub App configured")
	}

	// 验证该App ID是否有对应的私钥配置
	appConfig := config.GetGithubAppConfig(appId)
	if appConfig == "" {
		ctx.GetLogger().Error("GitHub App private key not configured", "appId", appId)
		return 0, 0, commonerrors.New(codes.ErrInvalidParameter, "app_config", "GitHub App private key not found")
	}

	// 使用GitHub App客户端查找installation
	client, err := newAppClient(ctx, appId)
	if err != nil {
		return 0, 0, errors.WithStack(err)
	}

	// 查找仓库的installation
	installation, _, err := client.Apps.FindRepositoryInstallation(ctx, owner, repo)
	if err != nil {
		ctx.GetLogger().Error("failed to find installation for repository",
			"owner", owner, "repo", repo, "appId", appId, "err", err)
		return 0, 0, commonerrors.New(codes.ErrRecordNotFound, "installation", "no installation found for repository")
	}

	ctx.GetLogger().Info("found GitHub App installation",
		"owner", owner, "repo", repo, "appId", appId, "installationId", installation.GetID())

	return installation.GetID(), appId, nil
}

// createInstallationToken 创建Installation Token
func (s *TokenExchangeService) createInstallationToken(ctx base.Context, appId, installationId int64, req *InstallationTokenRequest) (*InstallationTokenResponse, error) {
	// 创建GitHub App客户端
	client, err := newAppClient(ctx, appId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 准备Installation Token请求
	tokenReq := &github.InstallationTokenOptions{}
	
	// 设置权限（如果指定）
	if len(req.Permissions) > 0 {
		tokenReq.Permissions = &github.InstallationPermissions{}
		for perm, level := range req.Permissions {
			switch perm {
			case "contents":
				tokenReq.Permissions.Contents = &level
			case "metadata":
				tokenReq.Permissions.Metadata = &level
			case "pull_requests":
				tokenReq.Permissions.PullRequests = &level
			case "issues":
				tokenReq.Permissions.Issues = &level
			case "actions":
				tokenReq.Permissions.Actions = &level
			// 可以根据需要添加更多权限
			}
		}
	}

	// 设置仓库限制（如果指定）
	if len(req.Repositories) > 0 {
		tokenReq.Repositories = req.Repositories
	}

	// 调用GitHub API创建Installation Token
	token, _, err := client.Apps.CreateInstallationToken(ctx, installationId, tokenReq)
	if err != nil {
		ctx.GetLogger().Error("failed to create installation token", 
			"installation_id", installationId, "err", err)
		return nil, errors.WithStack(err)
	}

	// 构造响应
	response := &InstallationTokenResponse{
		Token:       token.GetToken(),
		ExpiresAt:   token.GetExpiresAt().Time,
		Permissions: make(map[string]string),
	}

	// 填充权限信息
	if perms := token.GetPermissions(); perms != nil {
		if perms.Contents != nil {
			response.Permissions["contents"] = *perms.Contents
		}
		if perms.Metadata != nil {
			response.Permissions["metadata"] = *perms.Metadata
		}
		if perms.PullRequests != nil {
			response.Permissions["pull_requests"] = *perms.PullRequests
		}
		if perms.Issues != nil {
			response.Permissions["issues"] = *perms.Issues
		}
		if perms.Actions != nil {
			response.Permissions["actions"] = *perms.Actions
		}
	}

	// 填充仓库信息
	if repos := token.Repositories; repos != nil {
		response.Repositories = make([]github.Repository, len(repos))
		for i, repo := range repos {
			response.Repositories[i] = *repo
		}
	}

	return response, nil
}

// ValidateInstallationTokenRequest 验证Installation Token请求参数
func ValidateInstallationTokenRequest(req *InstallationTokenRequest) error {
	if req.Repository == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "repository")
	}

	// 验证repository格式
	parts := strings.Split(req.Repository, "/")
	if len(parts) != 2 || parts[0] == "" || parts[1] == "" {
		return commonerrors.New(codes.ErrInvalidParameter, "repository", "must be in format 'owner/repo'")
	}

	// 验证权限设置
	validPermissions := map[string][]string{
		"contents":      {"read", "write"},
		"metadata":      {"read", "write"},
		"pull_requests": {"read", "write"},
		"issues":        {"read", "write"},
		"actions":       {"read", "write"},
	}

	for perm, level := range req.Permissions {
		validLevels, exists := validPermissions[perm]
		if !exists {
			return commonerrors.New(codes.ErrInvalidParameter, "permissions", fmt.Sprintf("unknown permission: %s", perm))
		}

		valid := false
		for _, validLevel := range validLevels {
			if level == validLevel {
				valid = true
				break
			}
		}
		if !valid {
			return commonerrors.New(codes.ErrInvalidParameter, "permissions", 
				fmt.Sprintf("invalid level '%s' for permission '%s'", level, perm))
		}
	}

	return nil
}
