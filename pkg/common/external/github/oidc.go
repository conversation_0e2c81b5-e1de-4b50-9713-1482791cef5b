package github

import (
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"github.com/lestrrat-go/jwx/v2/jwk"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
)

// GitHubOIDCClaims GitHub Actions OIDC Token的Claims结构
type GitHubOIDCClaims struct {
	jwt.RegisteredClaims
	Repository       string `json:"repository"`        // owner/repo
	RepositoryOwner  string `json:"repository_owner"`  // owner
	RepositoryId     string `json:"repository_id"`     // repo id
	RunId            string `json:"run_id"`            // workflow run id
	RunNumber        string `json:"run_number"`        // workflow run number
	RunAttempt       string `json:"run_attempt"`       // workflow run attempt
	Actor            string `json:"actor"`             // 触发workflow的用户
	ActorId          string `json:"actor_id"`          // 触发workflow的用户ID
	Workflow         string `json:"workflow"`          // workflow名称
	WorkflowRef      string `json:"workflow_ref"`      // workflow引用
	WorkflowSha      string `json:"workflow_sha"`      // workflow的commit sha
	JobWorkflowRef   string `json:"job_workflow_ref"`  // job的workflow引用
	JobWorkflowSha   string `json:"job_workflow_sha"`  // job的workflow sha
	Ref              string `json:"ref"`               // git ref
	RefType          string `json:"ref_type"`          // ref类型 (branch/tag)
	Environment      string `json:"environment"`       // 环境名称
	EnvironmentNodeId string `json:"environment_node_id"` // 环境节点ID
}

// GitHubJWKS GitHub的JWKS响应结构
type GitHubJWKS struct {
	Keys []GitHubJWK `json:"keys"`
}

type GitHubJWK struct {
	Kty string   `json:"kty"`
	Use string   `json:"use"`
	Kid string   `json:"kid"`
	X5t string   `json:"x5t"`
	N   string   `json:"n"`
	E   string   `json:"e"`
	X5c []string `json:"x5c"`
}

const (
	GitHubOIDCIssuer   = "https://token.actions.githubusercontent.com"
	GitHubJWKSEndpoint = "https://token.actions.githubusercontent.com/.well-known/jwks"
)

// OIDCValidator OIDC令牌验证器
type OIDCValidator struct {
	jwksCache map[string]*rsa.PublicKey
	cacheTime time.Time
	cacheTTL  time.Duration
}

// NewOIDCValidator 创建新的OIDC验证器
func NewOIDCValidator() *OIDCValidator {
	return &OIDCValidator{
		jwksCache: make(map[string]*rsa.PublicKey),
		cacheTTL:  time.Hour, // JWKS缓存1小时
	}
}

// ValidateOIDCToken 验证GitHub Actions OIDC令牌
func (v *OIDCValidator) ValidateOIDCToken(ctx base.Context, tokenString string, expectedRepo string) (*GitHubOIDCClaims, error) {
	// 解析JWT token但不验证签名
	token, err := jwt.ParseWithClaims(tokenString, &GitHubOIDCClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 检查签名算法
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// 获取kid
		kid, ok := token.Header["kid"].(string)
		if !ok {
			return nil, fmt.Errorf("kid not found in token header")
		}

		// 获取公钥
		publicKey, err := v.getPublicKey(ctx, kid)
		if err != nil {
			return nil, err
		}

		return publicKey, nil
	})

	if err != nil {
		ctx.GetLogger().Error("failed to parse OIDC token", "err", err)
		return nil, commonerrors.New(codes.ErrInvalidParameter, "oidc_token", "invalid token")
	}

	if !token.Valid {
		return nil, commonerrors.New(codes.ErrInvalidParameter, "oidc_token", "token is not valid")
	}

	claims, ok := token.Claims.(*GitHubOIDCClaims)
	if !ok {
		return nil, commonerrors.New(codes.ErrInvalidParameter, "oidc_token", "invalid claims")
	}

	// 验证issuer
	if claims.Issuer != GitHubOIDCIssuer {
		return nil, commonerrors.New(codes.ErrInvalidParameter, "oidc_token", "invalid issuer")
	}

	// 验证audience (通常是GitHub App的client ID或者自定义的audience)
	// 这里可以根据需要进行配置
	
	// 验证repository (如果指定了expectedRepo)
	if expectedRepo != "" && claims.Repository != expectedRepo {
		return nil, commonerrors.New(codes.ErrInvalidParameter, "oidc_token", "repository mismatch")
	}

	// 验证token是否过期
	if time.Now().After(claims.ExpiresAt.Time) {
		return nil, commonerrors.New(codes.ErrInvalidParameter, "oidc_token", "token expired")
	}

	ctx.GetLogger().Info("OIDC token validated successfully", 
		"repository", claims.Repository,
		"actor", claims.Actor,
		"workflow", claims.Workflow,
		"ref", claims.Ref)

	return claims, nil
}

// getPublicKey 获取用于验证JWT的公钥
func (v *OIDCValidator) getPublicKey(ctx base.Context, kid string) (*rsa.PublicKey, error) {
	// 检查缓存
	if time.Since(v.cacheTime) < v.cacheTTL {
		if key, exists := v.jwksCache[kid]; exists {
			return key, nil
		}
	}

	// 从GitHub获取JWKS
	if err := v.refreshJWKS(ctx); err != nil {
		return nil, err
	}

	// 再次检查缓存
	if key, exists := v.jwksCache[kid]; exists {
		return key, nil
	}

	return nil, fmt.Errorf("public key with kid %s not found", kid)
}

// refreshJWKS 刷新JWKS缓存
func (v *OIDCValidator) refreshJWKS(ctx base.Context) error {
	resp, err := http.Get(GitHubJWKSEndpoint)
	if err != nil {
		return errors.WithStack(err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to fetch JWKS: status %d", resp.StatusCode)
	}

	var jwks GitHubJWKS
	if err := json.NewDecoder(resp.Body).Decode(&jwks); err != nil {
		return errors.WithStack(err)
	}

	// 清空旧缓存
	v.jwksCache = make(map[string]*rsa.PublicKey)

	// 解析并缓存公钥
	for _, key := range jwks.Keys {
		if key.Kty == "RSA" && key.Use == "sig" {
			publicKey, err := parseRSAPublicKey(key)
			if err != nil {
				ctx.GetLogger().Warn("failed to parse RSA public key", "kid", key.Kid, "err", err)
				continue
			}
			v.jwksCache[key.Kid] = publicKey
		}
	}

	v.cacheTime = time.Now()
	ctx.GetLogger().Info("JWKS cache refreshed", "keys_count", len(v.jwksCache))

	return nil
}

// parseRSAPublicKey 从JWK解析RSA公钥
func parseRSAPublicKey(githubJWK GitHubJWK) (*rsa.PublicKey, error) {
	// 构造JWK JSON
	jwkData := map[string]interface{}{
		"kty": githubJWK.Kty,
		"use": githubJWK.Use,
		"kid": githubJWK.Kid,
		"n":   githubJWK.N,
		"e":   githubJWK.E,
	}

	jwkJSON, err := json.Marshal(jwkData)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 使用jwx库解析JWK
	key, err := jwk.ParseKey(jwkJSON)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 转换为RSA公钥
	var rsaKey rsa.PublicKey
	if err := key.Raw(&rsaKey); err != nil {
		return nil, errors.WithStack(err)
	}

	return &rsaKey, nil
}
