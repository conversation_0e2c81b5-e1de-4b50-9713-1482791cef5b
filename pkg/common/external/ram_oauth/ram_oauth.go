package ram_oauth

import (
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/callback"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/component"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"golang.org/x/oauth2"
	"net/url"
)

var (
	oauthConfig = &oauth2.Config{
		//ClientID:     "",
		//ClientSecret: "",
		Endpoint: oauth2.Endpoint{
			AuthURL:  "https://signin.aliyun.com/oauth2/v1/auth",
			TokenURL: "https://oauth.aliyun.com/v1/token",
		},
	}
)

func QueryUserInfoByAuthCode(ctx base.Context, appId string, code string) (*callback.UserInfoResponse, error) {
	// 使用授权码换取访问令牌
	path := "/v1/github/callback/ram"
	endpoint := config.GetSystemEndpoint()
	if endpoint == "" {
		ctx.GetLogger().Error("failed to get agent webhook endpoint")
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	oauthConfig.RedirectURL, _ = url.JoinPath(endpoint, path, appId)
	oauthConfig.ClientID = appId
	oauthConfig.ClientSecret = config.GetRamOAuthClientSecret(appId)
	if oauthConfig.ClientSecret == "" {
		ctx.GetLogger().Error("failed to get ram oauth client secret", "appId", appId)
		return nil, commonerrors.New(codes.ServerInternalError)
	}
	token, err := oauthConfig.Exchange(ctx, code)
	if err != nil {
		ctx.GetLogger().Error("failed to exchange token", "authorization code", code, "err", err)
		return nil, errors.WithStack(err)
	}
	//curl "https://oauth.aliyun.com/v1/userinfo" --header "Authorization: Bearer token"
	//{"sub":"zAhEcc1moWDsTYnYHQmAiw==","uid":"****************","login_name":"<EMAIL>","requestid":"4bcafabe-c9f5-48a9-91b5-4e9f74f660d0","name":"root","bid":"26842","type":"Account","aid":"****************"}
	queryUrl := "https://oauth.aliyun.com/v1/userinfo"
	headers := map[string]string{
		"Authorization": "Bearer " + token.AccessToken,
	}
	userInfo, err := component.GetUserInfoByOauth(ctx, headers, queryUrl)
	return userInfo, nil
}

func GetRamCallbackUrl(ctx base.Context, state string) string {
	path := "/v1/github/callback/ram/"
	oauthURL := oauthConfig.Endpoint.AuthURL
	appId := config.Get(config.KeyRamOAuthAppId)
	if config.Get(config.KeyRamOAuthAppId) == "" {
		ctx.GetLogger().Error("failed to get ram Oauth app id")
		return ""
	}
	clientId := appId
	endpoint := config.GetSystemEndpoint()
	if endpoint == "" {
		ctx.GetLogger().Error("failed to get agent webhook endpoint")
		return ""
	}
	redirectUri, _ := url.JoinPath(endpoint, path, appId)
	accessType := "online"
	callbackURL := fmt.Sprintf("%s?%s&%s&%s&%s", oauthURL, "client_id="+clientId, "redirect_uri="+redirectUri,
		"access_type="+accessType, "state="+state)
	return callbackURL
}
