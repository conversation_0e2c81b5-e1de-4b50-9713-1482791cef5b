package code_aone

import (
	"encoding/json"
	"time"
)

type UserBasic struct {
	AvatarURL string `json:"avatar_url"`
	Email     string `json:"email"`
	ExternUID string `json:"extern_uid"`
	ID        int    `json:"id"`
	Name      string `json:"name"`
	State     string `json:"state"`
	Username  string `json:"username"`
	WebURL    string `json:"web_url"`
}

type UserLogin struct {
	AvatarURL        string     `json:"avatar_url"`
	Bio              string     `json:"bio"`
	CanCreateGroup   bool       `json:"can_create_group"`
	CanCreateProject bool       `json:"can_create_project"`
	ColorSchemeId    int32      `json:"color_scheme_id"`
	CreatedAt        *time.Time `json:"created_at"`
	CurrentSignInAt  *time.Time `json:"current_sign_in_at"`
	DeptPath         string     `json:"deptPath"`
	Email            string     `json:"email"`
	ExternUid        string     `json:"extern_uid"`
	Id               int64      `json:"id"`
	Identities       []struct {
		ExternUid string `json:"extern_uid"`
		Provider  string `json:"provider"`
	}
	IsAdmin       bool   `json:"is_admin"`
	Linkedin      string `json:"linkedin"`
	Name          string `json:"name"`
	PrivateToken  string `json:"private_token"`
	ProjectsLimit int32  `json:"projects_limit"`
	Skype         string `json:"skype"`
	State         string `json:"state"`
	ThemeId       int32  `json:"theme_id"`
	Twitter       string `json:"twitter"`
	UserName      string `json:"username"`
	WebURL        string `json:"web_url"`
	WebsiteURL    string `json:"website_url"`
}

type Pattern struct {
}

type CodeOwnerRule struct {
	GlobRule     string      `json:"globRule"`
	Owners       []UserBasic `json:"owners"`
	RegexPattern Pattern     `json:"regexPattern"`
	RegexRule    string      `json:"regexRule"`
}

type CheckResult struct {
	CheckName                 string          `json:"check_name"`
	CheckStatus               string          `json:"check_status"`
	CheckType                 string          `json:"check_type"`
	ExtraUsers                []UserBasic     `json:"extra_users"`
	SatisfiedCodeOwnerRules   []CodeOwnerRule `json:"satisfied_code_owner_rules"`
	SatisfiedItems            []string        `json:"satisfied_items"`
	UnsatisfiedCodeOwnerRules []CodeOwnerRule `json:"unsatisfied_code_owner_rules"`
	UnsatisfiedItems          []string        `json:"unsatisfied_items"`
}

type ApproveCheckChainResult struct {
	SatisfiedCheckResults   []CheckResult `json:"satisfied_check_results"`
	TotalCheckResult        string        `json:"total_check_result"`
	UnsatisfiedCheckResults []CheckResult `json:"unsatisfied_check_results"`
}

type Attribute struct {
	AttributeInfo  string `json:"attributeInfo"`
	AttributeType  string `json:"attributeType"`
	GmtCreate      string `json:"gmtCreate"`
	GmtModified    string `json:"gmtModified"`
	ID             int    `json:"id"`
	IsDeleted      bool   `json:"isDeleted"`
	OrganizationID string `json:"organizationId"`
	ProjectID      int    `json:"projectId"`
	SourceID       int    `json:"sourceId"`
	SourceType     string `json:"sourceType"`
}

type AutoMergeConfig struct {
	CommitMessage           string    `json:"commit_message"`
	CreatedAt               string    `json:"created_at"`
	DeleteBranchAfterMerged bool      `json:"delete_branch_after_merged"`
	ID                      int       `json:"id"`
	MergeRequestID          int       `json:"merge_request_id"`
	MergeType               string    `json:"merge_type"`
	Operator                UserBasic `json:"operator"`
	OperatorID              int       `json:"operator_id"`
}

type Milestone struct {
	CreatedAt   string `json:"created_at"`
	Description string `json:"description"`
	DueDate     string `json:"due_date"`
	ID          int    `json:"id"`
	IID         int    `json:"iid"`
	ProjectID   int    `json:"project_id"`
	State       string `json:"state"`
	Title       string `json:"title"`
	UpdatedAt   string `json:"updated_at"`
}

type PatchCommands struct {
	Checkout   string `json:"checkout"`
	Cherrypick string `json:"cherrypick"`
}

type MergeRequestDetailDTO struct {
	AcceptedRevision          string                  `json:"accepted_revision"`
	ApproveCheckResult        ApproveCheckChainResult `json:"approve_check_result"`
	Assignee                  UserBasic               `json:"assignee"`
	Assignees                 []UserBasic             `json:"assignees"`
	Attributes                []Attribute             `json:"attributes"`
	Author                    UserBasic               `json:"author"`
	AutoMergeConfig           AutoMergeConfig         `json:"auto_merge_config"`
	AutoMergeNotSupportReason string                  `json:"auto_merge_not_support_reason"`
	ClosedComments            int                     `json:"closedComments"`
	CodeOwnerMode             string                  `json:"code_owner_mode"`
	Comments                  int                     `json:"comments"`
	CreatedAt                 *time.Time              `json:"created_at"`
	CreationMethod            string                  `json:"creation_method"`
	Description               string                  `json:"description"`
	DetailURL                 string                  `json:"detail_url"`
	DiffTooLarge              bool                    `json:"diff_too_large"`
	Downvotes                 int                     `json:"downvotes"`
	EmailGroups               []interface{}           `json:"email_groups"`
	EnableAutoMerge           bool                    `json:"enable_auto_merge"`
	ID                        int                     `json:"id"`
	IID                       int                     `json:"iid"`
	IsSupportAutoMerge        bool                    `json:"is_support_auto_merge"`
	IsSupportChangeState      bool                    `json:"is_support_change_state"`
	IsSupportClose            bool                    `json:"is_support_close"`
	IsSupportMerge            bool                    `json:"is_support_merge"`
	IsSupportModify           bool                    `json:"is_support_modify"`
	IsSupportReassign         bool                    `json:"is_support_reassign"`
	IsSupportReopen           bool                    `json:"is_support_reopen"`
	IsSupportWorkitem         bool                    `json:"is_support_workitem"`
	IsUsePushBlock            bool                    `json:"is_use_push_block"`
	Labels                    []interface{}           `json:"labels"`
	LocalBranch               string                  `json:"local_branch"`
	MergeStatus               string                  `json:"mergeStatus"`
	MergeBase                 string                  `json:"merge_base"`
	MergeError                string                  `json:"merge_error"`
	MergeParams               string                  `json:"merge_params"`
	MergeWhenBuildSucceeds    bool                    `json:"merge_when_build_succeeds"`
	MergedRevision            string                  `json:"merged_revision"`
	Milestone                 Milestone               `json:"milestone"`
	PatchCommands             PatchCommands           `json:"patch_commands"`
	ProjectID                 int                     `json:"project_id"`
	ProjectPath               string                  `json:"project_path"`
	RefOldOid                 string                  `json:"ref_old_oid"`
	SourceBranch              string                  `json:"source_branch"`
	SourceBranchExist         bool                    `json:"source_branch_exist"`
	SourceProjectID           int                     `json:"source_project_id"`
	SourceType                string                  `json:"source_type"`
	SshURLToRepo              string                  `json:"ssh_url_to_repo"`
	State                     string                  `json:"state"`
	Subscribers               []UserBasic             `json:"subscribers"`
	SupportMergeFfOnly        bool                    `json:"support_merge_ff_only"`
	TargetBranch              string                  `json:"target_branch"`
	TargetBranchExist         bool                    `json:"target_branch_exist"`
	TargetProjectID           int64                   `json:"target_project_id"`
	TargetType                string                  `json:"target_type"`
	Title                     string                  `json:"title"`
	UpdatedAt                 *time.Time              `json:"updated_at"`
	Upvotes                   int32                   `json:"upvotes"`
	WarningMessage            string                  `json:"warning_message"`
	WebURL                    string                  `json:"web_url"`
	WorkInProgress            bool                    `json:"work_in_progress"`
}

type CodeReviewChangesetDTO struct {
	AddLines          int32  `json:"add_lines"`
	Amode             string `json:"amode"`
	Bmode             string `json:"bmode"`
	DelLines          int32  `json:"del_lines"`
	DeletedFile       bool   `json:"deleted_file"`
	HasLock           bool   `json:"hasLock"`
	IsBinary          bool   `json:"is_binary"`
	NewFile           bool   `json:"new_file"`
	NewObjectID       string `json:"new_object_id"`
	NewPath           string `json:"new_path"`
	OldObjectID       string `json:"old_object_id"`
	OldPath           string `json:"old_path"`
	Read              bool   `json:"read"`
	RebaseImpactLevel string `json:"rebase_impact_level"`
	Ref               string `json:"ref"`
	RenamedFile       bool   `json:"renamed_file"`
	TotalNote         int32  `json:"total_note"`
	UnresolvedNote    int32  `json:"unresolved_note"`
}

type CodeReviewChangeTreeRspDTO struct {
	ChangesetDTOS        []CodeReviewChangesetDTO `json:"changesetDTOS"`
	Count                int32                    `json:"count"`
	RebaseWarningTooltip string                   `json:"rebaseWarningTooltip"`
	TotalAddLines        int32                    `json:"totalAddLines"`
	TotalDelLines        int32                    `json:"totalDelLines"`
}

type Range struct {
	LineCount int `json:"lineCount"`
	LineStart int `json:"lineStart"`
}

type Line struct {
	Content  string `json:"content"`
	LineType string `json:"lineType"`
}

type Hunk struct {
	FromFileRange Range  `json:"fromFileRange"`
	Lines         []Line `json:"lines"`
	ToFileRange   Range  `json:"toFileRange"`
}

type CodeReviewDiffDTO struct {
	Diff                  string `json:"diff"`
	RebaseIntroducedDiffs []Hunk `json:"rebaseIntroducedDiffs"`
	ShowDiff              bool   `json:"showDiff"`
}

type MergeRequestCommentInputDTO struct {
	Closed             int32  `json:"closed,omitempty"`
	CommitID           string `json:"commit_id,omitempty"`
	ComparisonCommitID string `json:"comparison_commit_id,omitempty"`
	IsDraft            bool   `json:"is_draft,omitempty"`
	LabelIDs           string `json:"labelIds,omitempty"`
	Line               int64  `json:"line,omitempty"`
	Note               string `json:"note,omitempty"`
	ParentNoteID       int64  `json:"parent_note_id,omitempty"`
	Path               string `json:"path,omitempty"`
	RangeContext       string `json:"rangeContext,omitempty"`
	Side               string `json:"side,omitempty"`
	SpecificLineCode   string `json:"specificLineCode,omitempty"`
	ToLine             int64  `json:"to_line,omitempty"`
}

type MergeRequestCommentParams struct {
	MergeRequestID int64                       `json:"mergeRequestId" uri:"mergeRequestId"`
	PrivateToken   string                      `json:"private_token" form:"private_token"`
	ProjectID      int64                       `json:"projectId" uri:"projectId"`
	InputDTO       MergeRequestCommentInputDTO `json:"inputDTO" form:"inputDTO"`
}

type MergeRequestCommentLineRange struct {
	FromLine int64 `json:"from_line"`
	ToLine   int64 `json:"to_line"`
}

type MergeRequestDraftsParams struct {
	MergeRequestID  int64           `json:"mergeRequestId" uri:"mergeRequestId"`
	PrivateToken    string          `json:"private_token" form:"private_token"`
	ProjectID       int64           `json:"projectId" uri:"projectId"`
	CommitDraftsDTO CommitDraftsDTO `json:"commitDraftsDTO"`
}

type CommitDraftsDTO struct {
	LabelIds string `json:"labelIds"`
	Note     string `json:"note"`
}

type MergeRequestDraftOutputDTO struct {
	Success bool `json:"success"`
}

func (d *MergeRequestDraftOutputDTO) UnmarshalJSON(data []byte) error {
	// 直接将JSON数据解析到Value字段
	return json.Unmarshal(data, &d.Success)
}

type UpdateMergeRequestCommentResponse struct {
	Success bool `json:"success"`
}

func (d *UpdateMergeRequestCommentResponse) UnmarshalJSON(data []byte) error {
	// 直接将JSON数据解析到Value字段
	return json.Unmarshal(data, &d.Success)
}

type EmojiActionDTO struct {
	ID   int       `json:"id"`
	Time string    `json:"time"`
	User UserBasic `json:"user"`
}

type EmojiOutputDTO struct {
	Actions []EmojiActionDTO `json:"actions"`
	Content string           `json:"content"`
	Count   int32            `json:"count"`
}

type LabelRecordDetailDTO struct {
	Color        string `json:"color"`
	ID           int64  `json:"id"`
	Name         string `json:"name"`
	NotesLabelID int64  `json:"notesLabelId"`
	ObjectID     int64  `json:"objectId"`
	ObjectType   string `json:"objectType"`
}

type Suggestion struct {
	Applied bool   `json:"applied"`
	Diff    string `json:"diff"`
	ID      int64  `json:"id"`
	NoteID  int64  `json:"noteId"`
}

type MergeRequestCommentOutputDTO struct {
	Adopted              int32                        `json:"adopted"`
	AIComment            bool                         `json:"aiComment"`
	AISummary            bool                         `json:"aiSummary"`
	Author               UserBasic                    `json:"author"`
	Closed               int32                        `json:"closed"`
	CreatedAt            *time.Time                   `json:"created_at"`
	Emojis               []EmojiOutputDTO             `json:"emojis"`
	ID                   int64                        `json:"id"`
	IsDraft              bool                         `json:"is_draft"`
	LabelRecords         []LabelRecordDetailDTO       `json:"labelRecords"`
	Line                 int64                        `json:"line"`
	LineRange            MergeRequestCommentLineRange `json:"lineRange"`
	Note                 string                       `json:"note"`
	NotesCodeDiffContext string                       `json:"notesCodeDiffContext"`
	OriginLineRange      MergeRequestCommentLineRange `json:"originLineRange"`
	Outdated             bool                         `json:"outdated"`
	ParentNoteID         int64                        `json:"parent_note_id"`
	Path                 string                       `json:"path"`
	ProjectID            int64                        `json:"project_id"`
	RangeContext         string                       `json:"range_context"`
	Side                 string                       `json:"side"`
	Suggestion           Suggestion                   `json:"suggestion"`
	UpdatedAt            *time.Time                   `json:"updated_at"`
}

type ListMergeRequestAllCommentsResponse struct {
	Comments []MergeRequestCommentOutputDTO `json:"comments"`
}

type GetRepositoryFilesParams struct {
	ProjectID    int64  `json:"projectId" uri:"projectId"`
	Ref          string `json:"ref" form:"ref"`
	FilePath     string `json:"file_path" form:"file_path"`
	PrivateToken string `json:"private_token" form:"private_token"`
}

type RepositoryFileDTO struct {
	BlobId        string `json:"blob_id"`
	CommitId      string `json:"commit_id"`
	Content       string `json:"content"`
	DetectCharset string `json:"detect_charset"`
	Encoding      string `json:"encoding"`
	FileName      string `json:"file_name"`
	FilePath      string `json:"file_path"`
	LastCommitId  string `json:"last_commit_id"`
	Ref           string `json:"ref"`
	Size          int64  `json:"size"`
}

type UpdateMergeRequestNoteDTO struct {
	Body      string `json:"body"`
	Closed    int32  `json:"closed"`
	DraftFlag bool   `json:"draftFlag"`
}

type CodeReviewPushRecordDTO struct {
	ApproveRecords []CodeReviewApproveRecordDTO `json:"approveRecords"`
	BaseRevision   string                       `json:"baseRevision"`
	Commits        []CodeReviewCommitRecordDTO  `json:"commits"`
	ID             int64                        `json:"id"`
	LineCount      int32                        `json:"line_count"`
	NewRevision    string                       `json:"new_revision"`
	OldRevision    string                       `json:"old_revision"`
	PushTime       *time.Time                   `json:"push_time"`
	Pusher         UserBasic                    `json:"pusher"`
}

type CodeReviewApproveRecordDTO struct {
	ApproveUser         UserBasic  `json:"approveUser"`
	CodeReviewApproveAt *time.Time `json:"codeReviewApproveAt"`
	MergeRequestID      int64      `json:"mergeRequestId"`
	UserID              int64      `json:"userId"`
}

type CodeReviewCommitRecordDTO struct {
	Author              UserBasic  `json:"author"`
	AuthoredDate        *time.Time `json:"authored_date"`
	Committer           UserBasic  `json:"committer"`
	CommittedDate       *time.Time `json:"committed_date"`
	DisappearFromPushID int64      `json:"disappear_from_push_id"`
	ID                  int64      `json:"id"`
	Message             string     `json:"message"`
	SHA                 string     `json:"sha"`
}
