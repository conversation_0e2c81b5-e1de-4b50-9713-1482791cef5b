package code_aone

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	http2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"net/url"
	"strconv"
	"time"
)

type CodeAoneClient struct {
	PrivateToken string
	Endpoint     string
	Timeout      time.Duration
}

func NewClient() *CodeAoneClient {
	endpoint := config.Get(config.KeyCodeaoneEndpoint)
	return &CodeAoneClient{
		Endpoint: endpoint,
		Timeout:  10 * time.Second,
	}
}

func (c *CodeAoneClient) GetMergeRequest(ctx base.Context, projectId, mrId int64, platformToken string) (*MergeRequestDetailDTO, error) {
	uri := fmt.Sprintf("/api/v4/projects/%d/merge_request/%d", projectId, mrId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
	}
	return http2.InvokeHttp[MergeRequestDetailDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "GET",
		Url:     fullUrl,
		Query:   queryData,
		Timeout: c.Timeout,
	})
}

func (c *CodeAoneClient) ListMergeRequestChanges(ctx base.Context, projectId, mrId int64, platformToken string) (*CodeReviewChangeTreeRspDTO, error) {
	uri := fmt.Sprintf("/api/v4/projects/%d/merge_request/%d/push_record/change_tree", projectId, mrId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
	}
	return http2.InvokeHttp[CodeReviewChangeTreeRspDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "GET",
		Url:     fullUrl,
		Query:   queryData,
		Timeout: c.Timeout,
	})
}

func (c *CodeAoneClient) GetMergeRequestChangeDiff(ctx base.Context, projectId, mrId int64, ref string, platformToken string) (*CodeReviewDiffDTO, error) {
	uri := fmt.Sprintf("/api/v4/projects/%d/merge_request/%d/push_record/changeset/diff", projectId, mrId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
		"ref":           ref,
	}
	return http2.InvokeHttp[CodeReviewDiffDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "GET",
		Url:     fullUrl,
		Query:   queryData,
		Timeout: c.Timeout,
	})
}

func (c *CodeAoneClient) CreateMergeRequestComment(ctx base.Context, req *MergeRequestCommentParams, platformToken string) (*MergeRequestCommentOutputDTO, error) {
	uri := fmt.Sprintf("/api/v4/projects/%d/merge_request/%d/comments", req.ProjectID, req.MergeRequestID)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
	}
	bodyBytes, err := json.Marshal(req.InputDTO)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	headers := map[string]string{
		"Accept":       "application/json",
		"Content-Type": "application/json",
	}
	return http2.InvokeHttp[MergeRequestCommentOutputDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "POST",
		Url:     fullUrl,
		Headers: headers,
		Query:   queryData,
		Body:    bodyBytes,
		Timeout: c.Timeout,
	})
}

func (c *CodeAoneClient) PostMergeRequestDrafts(ctx base.Context, req *MergeRequestDraftsParams, platformToken string) (*MergeRequestDraftOutputDTO, error) {
	uri := fmt.Sprintf("/api/v4/projects/%d/merge_request/%d/drafts", req.ProjectID, req.MergeRequestID)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
	}
	bodyBytes, err := json.Marshal(req.CommitDraftsDTO)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	headers := map[string]string{
		"Accept":       "application/json",
		"Content-Type": "application/json",
	}
	return http2.InvokeHttp[MergeRequestDraftOutputDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "POST",
		Url:     fullUrl,
		Headers: headers,
		Query:   queryData,
		Body:    bodyBytes,
		Timeout: c.Timeout,
	})
}

func (c *CodeAoneClient) GetRepositoryFile(ctx base.Context, req *GetRepositoryFilesParams, platformToken string) (*RepositoryFileDTO, error) {
	uri := fmt.Sprintf("/api/v3/projects/%d/repository/files", req.ProjectID)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
		"file_path":     req.FilePath,
		"ref":           req.Ref,
	}
	return http2.InvokeHttp[RepositoryFileDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "GET",
		Url:     fullUrl,
		Query:   queryData,
		Timeout: c.Timeout,
	})
}

func (c *CodeAoneClient) ListMergeRequestAllComments(ctx base.Context, projectId, mrId int64, platformToken string) (*[]MergeRequestCommentOutputDTO, error) {
	uri := fmt.Sprintf("/api/v4/projects/%d/merge_request/%d/comments", projectId, mrId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
	}
	result, err := http2.InvokeHttp[[]MergeRequestCommentOutputDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "GET",
		Url:     fullUrl,
		Query:   queryData,
		Timeout: c.Timeout,
	})
	return result, nil
}

func (c *CodeAoneClient) ListMergeRequestPushRecords(ctx base.Context, projectId, mrId int64, platformToken string) (*[]CodeReviewPushRecordDTO, error) {
	uri := fmt.Sprintf("/api/v4/projects/%d/merge_request/%d/push_records", projectId, mrId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
	}
	result, err := http2.InvokeHttp[[]CodeReviewPushRecordDTO](ctx, &http2.InvokeHttpRequest{
		Method:  "GET",
		Url:     fullUrl,
		Query:   queryData,
		Timeout: c.Timeout,
	})
	return result, nil
}

func (c *CodeAoneClient) UpdateMergeRequestComment(ctx base.Context, projectId, mrId int64, platformToken string, opts *codeplatform.UpdateMergeRequestCommentOpts) (*UpdateMergeRequestCommentResponse, error) {
	commentId, err := strconv.Atoi(opts.CommentId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	uri := fmt.Sprintf("/api/v3/projects/%d/merge_requests/%d/notes/%d", projectId, mrId, int64(commentId))
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"private_token": platformToken,
	}
	body := &UpdateMergeRequestNoteDTO{}
	if opts.Content != nil {
		body.Body = *opts.Content
	}
	if opts.Resolved != nil {
		if *opts.Resolved {
			body.Closed = 1
		} else {
			body.Closed = 0
		}
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	headers := map[string]string{
		"Accept":       "application/json",
		"Content-Type": "application/json",
	}
	return http2.InvokeHttp[UpdateMergeRequestCommentResponse](ctx, &http2.InvokeHttpRequest{
		Method:  "PUT",
		Url:     fullUrl,
		Headers: headers,
		Query:   queryData,
		Body:    bodyBytes,
		Timeout: c.Timeout,
	})
}

func GetAuthenticatedUser(ctx base.Context, endpoint string, token string) (*UserLogin, error) {
	uri := fmt.Sprintf("%s/api/v3/user", endpoint)
	queryData := map[string]string{
		"private_token": token,
	}
	return http2.InvokeHttp[UserLogin](ctx, &http2.InvokeHttpRequest{
		Method: "GET",
		Url:    uri,
		Query:  queryData,
	})
}
