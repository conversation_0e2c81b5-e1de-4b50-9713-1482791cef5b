package gitlab

import (
	"crypto/sha1"
	"crypto/tls"
	"fmt"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	sdk "gitlab.com/gitlab-org/api/client-go"
	"k8s.io/utils/ptr"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"
)

func newClient(ctx base.Context, idInfo *identity.IdentityInfo) (*sdk.Client, error) {
	sni := ""
	if idInfo.PlatformProperty != nil {
		sni = idInfo.PlatformProperty.Sni
	}
	return newClientWithEndpointAndToken(ctx, idInfo.PlatformEndpoint, idInfo.PlatformToken, sni)
}

func newClientWithEndpointAndToken(ctx base.Context, endpoint string, token string, sni string) (*sdk.Client, error) {
	options := []sdk.ClientOptionFunc{
		sdk.WithBaseURL(endpoint),
	}
	if sni != "" {
		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
					ServerName:         sni,
				},
			},
			Timeout: 60 * time.Second,
			// 关闭301重定向功能
			CheckRedirect: func(req *http.Request, via []*http.Request) error {
				return http.ErrUseLastResponse
			},
		}
		options = append(options, sdk.WithHTTPClient(client))
	}
	client, err := sdk.NewClient(token, options...)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return client, nil
}

func GetMergeRequest(ctx base.Context, identityInfo *identity.IdentityInfo, projectId, mrId string) (*sdk.MergeRequest, error) {
	client, err := newClient(ctx, identityInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, errors.WithStack(err)
	}

	// 将 string 转换为 int
	mrIDInt, err := strconv.Atoi(mrId)
	if err != nil {
		ctx.GetLogger().Error("invalid merge request id", "mrId", mrId, "err", err.Error())
		return nil, commonerrors.New(codes.ErrInvalidParameter, "mrId", mrId)
	}

	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(identityInfo.PlatformProperty),
	}
	mr, resp, err := client.MergeRequests.GetMergeRequestChanges(projectId, mrIDInt, nil, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to get merge request",
			"projectId", projectId,
			"mrId", mrId,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return mr, nil
}

func CreateMergeRequestDiscussion(ctx base.Context, identityInfo *identity.IdentityInfo, projectId string, mrId int, opts *codeplatform.CreateMergeRequestCommentOpts) (*sdk.Discussion, error) {
	discussionOpts := &sdk.CreateMergeRequestDiscussionOptions{
		Body: sdk.Ptr(opts.Content),
	}

	client, err := newClient(ctx, identityInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, errors.WithStack(err)
	}

	mr, err := GetMergeRequest(ctx, identityInfo, projectId, strconv.Itoa(mrId))
	if err != nil {
		ctx.GetLogger().Error("failed to get merge request", "projectId", projectId, "mrId", mrId, "err", err.Error())
		return nil, errors.WithStack(err)
	}

	position, err := convertPositionOption(ctx, &codeplatform.CodePosition{
		StartLine: opts.StartLine,
		EndLine:   opts.EndLine,
		LineType:  opts.LineType,
		Path:      opts.Path,
	}, mr)
	if err != nil {
		ctx.GetLogger().Error("failed to convert position option", "err", err.Error())
		return nil, errors.WithStack(err)
	}
	discussionOpts.Position = position

	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(identityInfo.PlatformProperty),
	}
	d, resp, err := client.Discussions.CreateMergeRequestDiscussion(projectId, mrId, discussionOpts, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to create merge request discussion",
			"projectId", projectId,
			"mrId", mrId,
			"opts", discussionOpts,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return d, nil
}

func CreateMergeRequestNote(ctx base.Context, identityInfo *identity.IdentityInfo, projectId string, mrId int, opts *codeplatform.CreateMergeRequestCommentOpts) (*sdk.Note, error) {
	noteOpts := &sdk.CreateMergeRequestNoteOptions{
		Body: sdk.Ptr(opts.Content),
	}
	client, err := newClient(ctx, identityInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(identityInfo.PlatformProperty),
	}
	note, resp, err := client.Notes.CreateMergeRequestNote(projectId, mrId, noteOpts, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to create merge request note",
			"projectId", projectId,
			"mrId", mrId,
			"opts", noteOpts,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return note, nil
}

func AddMergeRequestDiscussionNote(ctx base.Context, idInfo *identity.IdentityInfo, projectId string, mrId int, discussionId, content string) (*sdk.Note, error) {
	noteOpts := &sdk.AddMergeRequestDiscussionNoteOptions{
		Body: sdk.Ptr(content),
	}
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	n, resp, err := client.Discussions.AddMergeRequestDiscussionNote(projectId, mrId, discussionId, noteOpts, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to add merge request discussion note",
			"projectId", projectId,
			"mrId", mrId,
			"discussionId", discussionId,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return n, nil
}

func GetMergeRequestNote(ctx base.Context, idInfo *identity.IdentityInfo, projectId string, mergeRequestId string, noteId string) (*sdk.Note, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return nil, errors.WithStack(err)
	}
	noteIdInt, err := cast.ToIntE(noteId)
	if err != nil {
		ctx.GetLogger().Error("convert note id from string to int error", "noteId", noteId, "err", err)
		return nil, errors.WithStack(err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	note, res, err := client.Notes.GetMergeRequestNote(projectId, mrId, noteIdInt, reqOptions...)
	if err != nil {
		if res != nil && res.StatusCode == http.StatusNotFound {
			return nil, commonerrors.New(codes.ErrMergeRequestCommentNotFound, noteId)
		}
		ctx.GetLogger().Error("failed to GetMergeRequestNote",
			"projectId", projectId,
			"mrId", mrId,
			"noteId", noteId,
			"resp", res,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return note, nil
}

func ListMergeRequestComments(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, req *codeplatform.ListMergeRequestCommentsRequest) (*sdk.Discussion, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return nil, errors.WithStack(err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	comments, resp, err := client.Discussions.GetMergeRequestDiscussion(projectId, mrId, req.CommentId, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to GetMergeRequestDiscussion",
			"projectId", projectId,
			"mrId", mrId,
			"discussionId", req.CommentId,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return comments, nil
}

func ListMergeRequestAllComments(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string) ([]*sdk.Discussion, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return nil, errors.WithStack(err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	var results []*sdk.Discussion

	opt := &sdk.ListMergeRequestDiscussionsOptions{
		PerPage: 50,
		Page:    1,
	}

	comments, resp, err := client.Discussions.ListMergeRequestDiscussions(projectId, mrId, opt, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to ListMergeRequestDiscussions",
			"projectId", projectId,
			"mrId", mrId,
			"opt", opt,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	results = append(results, comments...)

	totalPage := resp.TotalPages
	for page := 2; page <= totalPage; page++ {
		opt.Page = page
		comments, resp, err = client.Discussions.ListMergeRequestDiscussions(projectId, mrId, opt, reqOptions...)
		if err != nil {
			ctx.GetLogger().Error("failed to ListMergeRequestDiscussions",
				"projectId", projectId,
				"mrId", mrId,
				"opt", opt,
				"resp", resp,
				"err", err.Error(),
			)
		}
		results = append(results, comments...)
	}

	return results, nil
}

func GetCurrentUser(ctx base.Context, endpoint string, token string, property *identity.PlatformProperty) (*sdk.User, error) {
	sni := ""
	if property != nil {
		sni = property.Sni
	}
	client, err := newClientWithEndpointAndToken(ctx, endpoint, token, sni)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, errors.WithStack(err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHost(sni),
	}
	user, resp, err := client.Users.CurrentUser(reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to get current user",
			"resp", resp,
			"err", err.Error(),
		)
		if resp != nil && resp.StatusCode == 401 {
			return nil, commonerrors.New(codes.ErrInvalidGitlabToken)
		} else if resp == nil {
			return nil, commonerrors.New(codes.ErrInvalidGitlabHost)
		}
		return nil, errors.WithStack(err)
	}
	return user, nil
}

func GetCommitDiff(ctx base.Context, idInfo *identity.IdentityInfo, projectId string, sha string) ([]*sdk.Diff, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	diffs, resp, err := client.Commits.GetCommitDiff(projectId, sha, nil, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to GetMergeRequestCommits",
			"projectId", projectId,
			"sha", sha,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return diffs, nil
}

func ListMergeRequestCommits(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, req *codeplatform.ListAllMergeRequestCommitDiffsRequest) ([]*sdk.Commit, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return nil, errors.WithStack(err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	var results []*sdk.Commit
	opt := &sdk.GetMergeRequestCommitsOptions{
		PerPage: 50,
		Page:    1,
	}

	commits, resp, err := client.MergeRequests.GetMergeRequestCommits(projectId, mrId, opt, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to GetMergeRequestCommits",
			"projectId", projectId,
			"mrId", mrId,
			"opt", opt,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	results = append(results, commits...)
	totalPage := resp.TotalPages
	for page := 2; page <= totalPage; page++ {
		opt.Page = page
		commits, resp, err = client.MergeRequests.GetMergeRequestCommits(projectId, mrId, opt, reqOptions...)
		if err != nil {
			ctx.GetLogger().Error("failed to GetMergeRequestCommits",
				"projectId", projectId,
				"mrId", mrId,
				"opt", opt,
				"resp", resp,
				"err", err.Error(),
			)
		}
		results = append(results, commits...)
	}

	return commits, nil
}

func withHost(host string) sdk.RequestOptionFunc {
	return func(req *retryablehttp.Request) error {
		if host == "" {
			return nil
		}
		req.Host = host
		req.Header.Set("Host", host)
		return nil
	}
}

func withHostFromPlatformProperty(property *identity.PlatformProperty) sdk.RequestOptionFunc {
	return func(req *retryablehttp.Request) error {
		if property == nil {
			return nil
		}
		req.Host = property.Sni
		req.Header.Set("Host", property.Sni)
		return nil
	}
}

func UpdateMergeRequestDiscussionNote(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, discussionId string, opts *codeplatform.UpdateMergeRequestCommentOpts) (*sdk.Note, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	body := sdk.UpdateMergeRequestDiscussionNoteOptions{
		Resolved: opts.Resolved,
		Body:     opts.Content,
	}
	noteId, err := cast.ToIntE(opts.CommentId)
	if err != nil {
		ctx.GetLogger().Error("convert note id from string to int error", "note", opts.CommentId, "err", err)
		return nil, errors.WithStack(err)
	}
	note, resp, err := client.Discussions.UpdateMergeRequestDiscussionNote(projectId, mrId, discussionId, noteId, &body, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to UpdateMergeRequestDiscussionNote",
			"projectId", projectId,
			"mrId", mrId,
			"discussionId", discussionId,
			"noteId", noteId,
			"resp", resp,
			"err", err.Error(),
		)
		return nil, errors.WithStack(err)
	}
	return note, nil
}

func UpdateMergeRequestNote(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, opts *codeplatform.UpdateMergeRequestCommentOpts) (*sdk.Note, error) {
	ctx.GetLogger().Info("start to update gitlab merge request comment", "projectId", projectId,
		"mrId", mergeRequestId, "commentId", opts.CommentId)
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return nil, errors.WithStack(err)
	}
	noteId, err := cast.ToIntE(opts.CommentId)
	if err != nil {
		ctx.GetLogger().Error("convert comment id from string to int error", "commentId", opts.CommentId, "err", err)
		return nil, errors.WithStack(err)
	}
	body := &sdk.UpdateMergeRequestNoteOptions{
		Body: opts.Content,
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	note, resp, err := client.Notes.UpdateMergeRequestNote(projectId, mrId, noteId, body, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to update gitlab merge request note",
			"projectId", projectId,
			"mrId", mrId,
			"commentId", opts.CommentId,
			"resp", resp,
			"err", err.Error(),
		)
	}
	return note, nil
}

func DeleteMergeRequestNote(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, noteId string) error {
	ctx.GetLogger().Info("start to delete gitlab merge request note", "projectId", projectId,
		"mrId", mergeRequestId, "noteId", noteId)
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return errors.WithStack(err)
	}
	noteIdInt, err := cast.ToIntE(noteId)
	if err != nil {
		ctx.GetLogger().Error("convert comment id from string to int error", "noteId", noteId, "err", err)
		return errors.WithStack(err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	resp, err := client.Notes.DeleteMergeRequestNote(projectId, mrId, noteIdInt, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to delete gitlab merge request note",
			"projectId", projectId,
			"mrId", mrId,
			"noteId", noteId,
			"resp", resp,
			"err", err.Error(),
		)
		return errors.WithStack(err)
	}
	return nil
}

func DeleteMergeRequestDiscussionNote(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, discussionId string, noteId string) error {
	ctx.GetLogger().Info("start to delete gitlab merge request discussion note", "projectId", projectId,
		"mrId", mergeRequestId, "discussionId", discussionId, "noteId", noteId)
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return errors.WithStack(err)
	}
	noteIdInt, err := cast.ToIntE(noteId)
	if err != nil {
		ctx.GetLogger().Error("convert note id from string to int error", "noteId", noteId, "err", err)
		return errors.WithStack(err)
	}
	reqOptions := []sdk.RequestOptionFunc{
		withHostFromPlatformProperty(idInfo.PlatformProperty),
	}
	resp, err := client.Discussions.DeleteMergeRequestDiscussionNote(projectId, mrId, discussionId, noteIdInt, reqOptions...)
	if err != nil {
		ctx.GetLogger().Error("failed to delete gitlab merge request discussion note",
			"projectId", projectId,
			"mrId", mrId,
			"discussionId", discussionId,
			"noteId", noteId,
			"resp", resp,
			"err", err.Error(),
		)
		return errors.WithStack(err)
	}
	return nil
}

func ResolveMergeRequestDiscussion(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, opts *codeplatform.ResolveMergeRequestCommentOpts) error {
	ctx.GetLogger().Info("start to resolve gitlab merge request discussion", "projectId", projectId,
		"mrId", mergeRequestId, "discussionId", opts.CommentId)
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return errors.WithStack(err)
	}
	_, resp, err := client.Discussions.ResolveMergeRequestDiscussion(
		projectId,
		mrId,
		opts.CommentId,
		&sdk.ResolveMergeRequestDiscussionOptions{
			Resolved: sdk.Ptr(opts.Resolved),
		},
	)
	if err != nil {
		ctx.GetLogger().Error("failed to resolve merge request discussion", "projectId", projectId,
			"mrId", mrId, "discussionId", opts.CommentId, "resp", resp, "err", err.Error())
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("successfully resolve discussion", "projectId", projectId, "mrId", mrId, "discussionId", opts.CommentId)
	return nil
}

func GetRepositoryFile(ctx base.Context, idInfo *identity.IdentityInfo, projectId string, opts *codeplatform.GetRepositoryFileOpts) (*sdk.File, error) {
	ctx.GetLogger().Info("start to get repository file from gitlab", "projectId", projectId,
		"ref", opts.Ref, "path", opts.Path)
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}

	file, resp, err := client.RepositoryFiles.GetFile(
		projectId,
		opts.Path,
		&sdk.GetFileOptions{
			Ref: opts.Ref,
		},
	)
	if err != nil {
		ctx.GetLogger().Error("failed to get repository file", "resp", resp, "err", err.Error())
		return nil, errors.WithStack(err)
	}
	ctx.GetLogger().Info("successfully get repository file")
	return file, nil
}

func GetVersion(ctx base.Context, idInfo *identity.IdentityInfo) (string, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return "", err
	}
	version, resp, err := client.Version.GetVersion()
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab version", "resp", resp, "err", err.Error())
		return "", errors.WithStack(err)
	}
	ctx.GetLogger().Info("get gitlab version successfully", "version", version.Version)
	return version.Version, nil
}

func CreateDraftNotes(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string, draftNotes []codeplatform.ReviewComment) error {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return errors.WithStack(err)
	}

	ctx.GetLogger().Info("start to create draft note", "count", len(draftNotes), "mrId", mrId)
	mr, err := GetMergeRequest(ctx, idInfo, projectId, mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("failed to get mr", "err", err.Error())
		return errors.WithStack(err)
	}

	for _, note := range draftNotes {
		position, err := convertPositionOption(ctx, note.Position, mr)
		if err != nil {
			ctx.GetLogger().Error("failed to convert position option", "err", err.Error(), "draftNote", note)
			// 不要影响发送其他评论
			continue
		}
		res, resp, err := client.DraftNotes.CreateDraftNote(projectId, mrId, &sdk.CreateDraftNoteOptions{
			Note:     sdk.Ptr(note.Content),
			Position: position,
		})
		if err != nil {
			ctx.GetLogger().Error("failed to create draft note", "draftNote", note, "err", err.Error(), "resp", resp, "draftNote", note)
			//return errors.WithStack(err)
			// 不要影响发送其他评论
			continue
		}
		ctx.GetLogger().Info("successfully create draft note", "resp", resp, "result", res)
	}
	return nil
}

func PublishAllDraftNotes(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string) error {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("start to publish draft notes", "mrId", mrId, "projectId", projectId)
	resp, err := client.DraftNotes.PublishAllDraftNotes(projectId, mrId)
	if err != nil {
		ctx.GetLogger().Error("failed to publish draft notes", "err", err.Error(), "resp", resp)
		return errors.WithStack(err)
	}
	ctx.GetLogger().Info("successfully publish all draft notes", "mrId", mrId, "projectId", projectId)
	return nil
}

func ListNotes(ctx base.Context, iidInfo *identity.IdentityInfo, projectId, mergeRequestId string) ([]*sdk.Note, error) {
	client, err := newClient(ctx, iidInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return nil, errors.WithStack(err)
	}
	ctx.GetLogger().Info("start to list notes", "mrId", mrId, "projectId", projectId)
	var allNotes []*sdk.Note
	page := 1
	perPage := 100 // GitLab API 每页最多返回 100 条记录

	// 按创建时间升序排列，确保分页稳定性
	opts := &sdk.ListMergeRequestNotesOptions{
		ListOptions: sdk.ListOptions{
			Page:    page,
			PerPage: perPage,
		},
		OrderBy: sdk.Ptr("created_at"), // 按创建时间排序
		Sort:    sdk.Ptr("asc"),        // 升序排列（从旧到新）
	}

	for {
		ctx.GetLogger().Info("fetching notes", "page", opts.Page, "perPage", opts.PerPage)
		notes, resp, err := client.Notes.ListMergeRequestNotes(projectId, mrId, opts)
		if err != nil {
			ctx.GetLogger().Error("failed to list notes", "err", err.Error(), "resp", resp)
			return nil, errors.WithStack(err)
		}

		allNotes = append(allNotes, notes...)

		// 如果当前页返回数量小于请求量，说明已获取全部数据
		if len(notes) < perPage {
			break
		}

		// 更新下一页参数
		opts.Page++
	}

	ctx.GetLogger().Info("successfully listed all notes", "mrId", mrId, "projectId", projectId, "count", len(allNotes))
	return allNotes, nil
}

func GetMergeRequestCommits(ctx base.Context, idInfo *identity.IdentityInfo, projectId, mergeRequestId string) ([]*sdk.Commit, error) {
	client, err := newClient(ctx, idInfo)
	if err != nil {
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}
	mrId, err := cast.ToIntE(mergeRequestId)
	if err != nil {
		ctx.GetLogger().Error("convert merge request id from string to int error", "mrId", mergeRequestId, "err", err)
		return nil, errors.WithStack(err)
	}
	ctx.GetLogger().Info("start to get merge request commits", "mrId", mrId, "projectId", projectId)

	var allCommits []*sdk.Commit
	opts := &sdk.GetMergeRequestCommitsOptions{
		PerPage: 20,
		Page:    1,
	}

	for {
		commits, resp, err := client.MergeRequests.GetMergeRequestCommits(projectId, mrId, opts)
		if err != nil {
			ctx.GetLogger().Error("failed to get merge request commits", "err", err.Error(), "resp", resp)
			return nil, errors.WithStack(err)
		}
		allCommits = append(allCommits, commits...)

		if len(commits) < opts.PerPage {
			break
		}
		opts.Page++
	}
	ctx.GetLogger().Info("successfully get all merge request commits", "mrId", mrId, "projectId", projectId, "count", len(allCommits))
	return allCommits, nil
}

// Compare 获取两个提交之间的差异比较
// 该函数使用 GitLab 客户户端，根据指定的项目ID、起始提交和目标提交来获取代码差异
// 参数:
//
//	ctx base.Context - 上下文对象，用于日志记录和错误处理
//	idInfo *identity.IdentityInfo - 身份信息，用于创建 GitLab 客户端
//	projectId string - 项目ID，标识要比较代码的项目
//	from string - 起始提交的引用，例如提交哈希或分支名
//	to string - 目标提交的引用，例如提交哈希或分支名
//
// 返回值:
//
//	*sdk.Compare - 比较结果对象，包含从起始提交到目标提交的代码差异
//	error - 如果发生错误，则返回错误对象
func Compare(ctx base.Context, idInfo *identity.IdentityInfo, projectId, from, to string) (*sdk.Compare, error) {
	// 创建 GitLab 客户端
	client, err := newClient(ctx, idInfo)
	if err != nil {
		// 如果创建客户端失败，记录错误并返回
		ctx.GetLogger().Error("failed to get gitlab client", "err", err.Error())
		return nil, err
	}

	// 使用客户端比较两个提交之间的差异
	compare, resp, err := client.Repositories.Compare(projectId, &sdk.CompareOptions{
		From:    ptr.To(from),
		To:      ptr.To(to),
		Unidiff: ptr.To(true),
	})
	if err != nil {
		// 如果比较失败，记录错误和响应信息，然后返回包装过的错误
		ctx.GetLogger().Error("failed to compare", "err", err.Error(), "resp", resp)
		return nil, errors.WithStack(err)
	}

	// 如果比较成功，记录相关信息并返回比较结果
	ctx.GetLogger().Info("successfully compare", "projectId", projectId, "from", from, "to", to)
	return compare, nil
}

func convertPositionOption(ctx base.Context, position *codeplatform.CodePosition, mr *sdk.MergeRequest) (*sdk.PositionOptions, error) {
	opts := &sdk.PositionOptions{
		PositionType: sdk.Ptr("text"), // 仅支持代码块评论，不支持代码文件评论，有版本兼容问题
		BaseSHA:      &mr.DiffRefs.BaseSha,
		StartSHA:     &mr.DiffRefs.StartSha,
		HeadSHA:      &mr.DiffRefs.HeadSha,
	}

	if position.Path == nil || position.EndLine == nil {
		ctx.GetLogger().Error("path or endLine is nil", "position", position)
		return nil, fmt.Errorf("path or endLine is nil")
	}

	for _, c := range mr.Changes {
		if c.NewPath == *position.Path {
			opts.NewPath = &c.NewPath
			break
		}
	}
	// 生成的path不对
	if opts.NewPath == nil {
		ctx.GetLogger().Error("failed to find target path", "path", *position.Path)
		return nil, fmt.Errorf(fmt.Sprintf("failed to find target path: %s", *position.Path))
	}

	if position.StartLine == nil {
		// 如果 start line 为空，默认为 end line
		position.StartLine = position.EndLine
	}
	opts.NewLine = position.EndLine

	// 评论到多行，需要指定 LineRange
	if *position.StartLine != *position.EndLine {

		var fileDiff string
		for _, change := range mr.Changes {
			if change.NewPath == *position.Path {
				fileDiff = change.Diff
				break
			}
		}
		// 建立新行到旧行的映射
		lineMapping := parseDiffForLineMapping(fileDiff)
		fileNameHash := getSHA1Hash(*position.Path)

		opts.LineRange = &sdk.LineRangeOptions{
			Start: &sdk.LinePositionOptions{
				Type:     sdk.Ptr("new"), // 只支持评论新行
				LineCode: sdk.Ptr(generateLineCode(lineMapping, fileNameHash, *position.StartLine)),
			},
			End: &sdk.LinePositionOptions{
				Type:     sdk.Ptr("new"),
				LineCode: sdk.Ptr(generateLineCode(lineMapping, fileNameHash, *position.EndLine)),
			},
		}
	}
	return opts, nil
}

// parseDiffForLineMapping 解析git diff内容，生成新行到旧行的映射关系
func parseDiffForLineMapping(diff string) map[int]int {
	lineMapping := make(map[int]int)
	re := regexp.MustCompile(`(?m)^@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@.*\n([^@]*)`)

	matches := re.FindAllStringSubmatch(diff, -1)
	for _, match := range matches {
		oldStart, _ := strconv.Atoi(match[1])
		newStart, _ := strconv.Atoi(match[2])
		lines := strings.Split(match[3], "\n")

		currentOld := oldStart
		currentNew := newStart
		var lastDeleted *int

		for _, line := range lines {
			if line == "" {
				continue
			}

			switch line[0] {
			case ' ':
				lineMapping[currentNew] = currentOld
				currentOld++
				currentNew++
				lastDeleted = nil
			case '+':
				if lastDeleted != nil {
					lineMapping[currentNew] = *lastDeleted
				} else {
					lineMapping[currentNew] = 0
				}
				currentNew++
			case '-':
				lastDeleted = &currentOld
				currentOld++
			}
		}
	}
	return lineMapping
}

// generateLineCode A line code is of the form <SHA>_<old>_<new>, like this: adc83b19e793491b1c6ea0fd8b46cd9f32e292fc_5_5
func generateLineCode(lineMapping map[int]int, fileSha string, newLine int) string {
	var lineCode string
	if oldLine, exists := lineMapping[newLine]; exists {
		lineCode = fmt.Sprintf("%s_%d_%d", fileSha, oldLine, newLine)
	} else {
		// 新文件的新增行，oldLine 为 0
		lineCode = fmt.Sprintf("%s_0_%d", fileSha, newLine)
	}
	return lineCode
}

func getSHA1Hash(input string) string {
	hasher := sha1.New()
	hasher.Write([]byte(input))

	return fmt.Sprintf("%x", hasher.Sum(nil))
}
