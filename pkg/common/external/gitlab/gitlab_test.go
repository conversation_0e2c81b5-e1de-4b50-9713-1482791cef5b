package gitlab

import (
	"reflect"
	"testing"
)

func Test_parseDiffForLineMapping(t *testing.T) {
	tests := []struct {
		name     string
		diff     string
		expected map[int]int
	}{
		{
			name:     "空diff",
			diff:     "",
			expected: map[int]int{},
		},
		{
			name: "只有上下文行",
			diff: `@@ -1,3 +1,3 @@
 上下文1
 上下文2
 上下文3`,
			expected: map[int]int{
				1: 1,
				2: 2,
				3: 3,
			},
		},
		{
			name: "只有新增行",
			diff: `@@ -1,0 +1,3 @@
+新增行1
+新增行2
+新增行3`,
			expected: map[int]int{
				1: 0,
				2: 0,
				3: 0,
			},
		},
		{
			name: "只有删除行",
			diff: `@@ -1,3 +1,0 @@
-删除行1
-删除行2
-删除行3`,
			expected: map[int]int{},
		},
		{
			name: "删除后新增（替换）",
			diff: `@@ -1,3 +1,3 @@
-删除行1
-删除行2
-删除行3
+新增行A
+新增行B
+新增行C`,
			expected: map[int]int{
				1: 1, // 新增行A 对应 删除行1
				2: 2, // 新增行B 对应 删除行2
				3: 3, // 新增行C 对应 删除行3
			},
		},
		{
			name: "混合场景：上下文、删除和新增",
			diff: `@@ -10,5 +10,6 @@
 上下文1
 上下文2
-删除行
+新增行A
+新增行B
 上下文3
 上下文4`,
			expected: map[int]int{
				10: 10, // 上下文1
				11: 11, // 上下文2
				12: 12, // 新增行A 对应 删除行
				13: 0,  // 新增行B 没有对应的删除行
				14: 13, // 上下文3
				15: 14, // 上下文4
			},
		},
		{
			name: "多个hunk",
			diff: `@@ -10,3 +10,4 @@
 上下文1
-删除行1
+新增行A
+新增行B
 上下文2
@@ -20,3 +21,2 @@
 上下文3
-删除行2
 上下文4`,
			expected: map[int]int{
				10: 10, // 上下文1
				11: 11, // 新增行A 对应 删除行1
				12: 0,  // 新增行B 没有对应的删除行
				13: 12, // 上下文2
				21: 20, // 上下文3
				22: 22, // 上下文4
			},
		},
		{
			name: "实际案例：重命名类及其字段",
			diff: `@@ -12,22 +12,22 @@ import java.io.Serializable;
 @Setter
 @EqualsAndHashCode
 @ToString
-public class SkuSpec implements Serializable  {
+public class ItemAttributeAndValue implements Serializable {
     @Serial
-    private static final long serialVersionUID = -3686283498974131636L;
+    private static final long serialVersionUID = 4489146596381270494L;
+    /**
+     * 当前处理的属性对象，用于存储业务相关的属性元数据
+     */
+    private Attribute attribute;
 
     /**
-     * 存储键值对中的键
-     * <p>
-     * 用于标识数据项的唯一键值，通常作为数据检索的依据
+     * 经过解析处理的属性值对象，包含类型转换后的属性值信息
      */
-    private String key;
+    private AttributeValue attributeValue;
 
     /**
-     * 存储键值对中的值
-     * <p>
-     * 表示与键相关联的具体数据内容，通过键可以快速访问对应的值
+     * 用户输入的原始字符串值，需要经过验证和格式化处理
      */
-    private String value;
+    private String inputValue;
 
 }`,
			expected: map[int]int{
				12: 12, // @Setter
				13: 13, // @EqualsAndHashCode
				14: 14, // @ToString
				15: 15, // class声明行（修改）
				16: 16, // @Serial
				17: 17, // serialVersionUID行（修改）
				18: 18, // 新增注释1
				19: 18, // 新增注释2
				20: 18, // 新增注释3
				21: 18, // 新增字段attribute
				22: 18, // 空行
				23: 19, // 注释行1（修改）
				24: 22, // 注释行2（修改）
				25: 23, // 注释行3（修改）
				26: 24, // 字段声明行（修改）
				27: 0,  // 空行
				28: 22, // 注释行1（修改）
				29: 23, // 注释行2（修改）
				30: 24, // 注释行3（修改）
				31: 25, // 注释行4（修改）
				32: 26, // 字段声明行（修改）
				33: 0,  // 空行
				34: 28, // }结尾
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseDiffForLineMapping(tt.diff)

			// 检查结果是否符合预期
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("parseDiffForLineMapping()\n获得: %v\n期望: %v", result, tt.expected)
			}

			// 打印结果（便于调试）
			t.Logf("结果映射: %v", result)
		})
	}
}

// 测试特定的行号映射
func TestSpecificMappings(t *testing.T) {
	// 实际案例中的类重命名测试
	diff := `@@ -12,22 +12,22 @@ import java.io.Serializable;
 @Setter
 @EqualsAndHashCode
 @ToString
-public class SkuSpec implements Serializable  {
+public class ItemAttributeAndValue implements Serializable {
     @Serial
-    private static final long serialVersionUID = -3686283498974131636L;
+    private static final long serialVersionUID = 4489146596381270494L;
+    /**
+     * 当前处理的属性对象，用于存储业务相关的属性元数据
+     */
+    private Attribute attribute;
 
     /**
-     * 存储键值对中的键
-     * <p>
-     * 用于标识数据项的唯一键值，通常作为数据检索的依据
+     * 经过解析处理的属性值对象，包含类型转换后的属性值信息
      */
-    private String key;
+    private AttributeValue attributeValue;
 
     /**
-     * 存储键值对中的值
-     * <p>
-     * 表示与键相关联的具体数据内容，通过键可以快速访问对应的值
+     * 用户输入的原始字符串值，需要经过验证和格式化处理
      */
-    private String value;
+    private String inputValue;
 
 }`

	mapping := parseDiffForLineMapping(diff)

	// 测试特定关注的行号映射
	testCases := []struct {
		newLine     int
		expectedOld int
		description string
	}{
		{15, 15, "类名行应该相互映射"},
		{17, 17, "序列化ID行应该相互映射"},
		{21, 0, "新属性行应该没有旧行对应"},
		{26, 21, "第一个字段声明行应该相互映射"},
		{32, 26, "第二个字段声明行应该相互映射"},
	}

	for _, tc := range testCases {
		t.Run(tc.description, func(t *testing.T) {
			if oldLine, exists := mapping[tc.newLine]; !exists || oldLine != tc.expectedOld {
				t.Errorf("新行 %d 的映射错误: 期望旧行为 %d, 实际为 %v",
					tc.newLine, tc.expectedOld, oldLine)
			}
		})
	}
}
