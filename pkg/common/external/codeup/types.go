package codeup

import "time"

type GetChangeRequestResponse struct {
	Ahead               int  `json:"ahead"`
	AllRequirementsPass bool `json:"allRequirementsPass"`
	Author              struct {
		Avatar   string `json:"avatar"`
		Email    string `json:"email"`
		Id       int    `json:"id"`
		Name     string `json:"name"`
		State    string `json:"state"`
		UserId   string `json:"userId"`
		Username string `json:"username"`
	} `json:"author"`
	Behind                int    `json:"behind"`
	BizId                 string `json:"bizId"`
	CanRevertOrCherryPick bool   `json:"canRevertOrCherryPick"`
	ChangeSizeBucket      string `json:"changeSizeBucket"`
	CheckList             struct {
		NeedAttentionItems   []interface{} `json:"needAttentionItems"`
		RequirementRuleItems []struct {
			ItemType string `json:"itemType"`
			Pass     bool   `json:"pass"`
		} `json:"requirementRuleItems"`
	} `json:"checkList"`
	CreateFrom     string     `json:"createFrom"`
	CreateTime     *time.Time `json:"createTime"`
	Description    string     `json:"description"`
	DetailUrl      string     `json:"detailUrl"`
	HasReverted    bool       `json:"hasReverted"`
	LocalId        int        `json:"localId"`
	MergedRevision string     `json:"mergedRevision"`
	MrType         string     `json:"mrType"`
	ProjectId      int        `json:"projectId"`
	Reviewers      []struct {
		Avatar              string     `json:"avatar"`
		Email               string     `json:"email"`
		HasCommented        bool       `json:"hasCommented"`
		HasReviewed         bool       `json:"hasReviewed"`
		Id                  int        `json:"id"`
		Name                string     `json:"name"`
		ReviewTime          *time.Time `json:"reviewTime"`
		State               string     `json:"state"`
		UserId              string     `json:"userId"`
		Username            string     `json:"username"`
		ReviewOpinionStatus string     `json:"reviewOpinionStatus,omitempty"`
	} `json:"reviewers"`
	SourceBranch                   *string       `json:"sourceBranch"`
	SourceCommitId                 *string       `json:"sourceCommitId"`
	SourceProjectId                int           `json:"sourceProjectId"`
	Status                         string        `json:"status"`
	Subscribers                    []interface{} `json:"subscribers"`
	SupportMergeFastForwardOnly    bool          `json:"supportMergeFastForwardOnly"`
	TargetBranch                   string        `json:"targetBranch"`
	TargetProjectId                int           `json:"targetProjectId"`
	TargetProjectNameWithNamespace string        `json:"targetProjectNameWithNamespace"`
	TargetProjectPathWithNamespace string        `json:"targetProjectPathWithNamespace"`
	Title                          string        `json:"title"`
	TotalCommentCount              int           `json:"totalCommentCount"`
	UnResolvedCommentCount         int           `json:"unResolvedCommentCount"`
	UpdateTime                     *time.Time    `json:"updateTime"`
	WebUrl                         string        `json:"webUrl"`
}

type GetCompareResponse struct {
	Commits []struct {
		AuthorEmail    string      `json:"authorEmail"`
		AuthorName     string      `json:"authorName"`
		AuthoredDate   time.Time   `json:"authoredDate"`
		CommittedDate  time.Time   `json:"committedDate"`
		CommitterEmail string      `json:"committerEmail"`
		CommitterName  string      `json:"committerName"`
		Id             string      `json:"id"`
		Message        string      `json:"message"`
		ParentIds      []string    `json:"parentIds"`
		ShortId        string      `json:"shortId"`
		Stats          interface{} `json:"stats"`
		Title          string      `json:"title"`
	} `json:"commits"`
	Diffs []struct {
		AMode       string `json:"aMode"`
		BMode       string `json:"bMode"`
		Binary      bool   `json:"binary"`
		DeletedFile bool   `json:"deletedFile"`
		Diff        string `json:"diff"`
		IsNewLfs    bool   `json:"isNewLfs"`
		IsOldLfs    bool   `json:"isOldLfs"`
		NewFile     bool   `json:"newFile"`
		NewId       string `json:"newId"`
		NewPath     string `json:"newPath"`
		OldId       string `json:"oldId"`
		OldPath     string `json:"oldPath"`
		RenamedFile bool   `json:"renamedFile"`
	} `json:"diffs"`
	Messages []interface{} `json:"messages"`
}

type ChangeRequestPatch struct {
	CommitId             string    `json:"commitId"`
	CreateTime           time.Time `json:"createTime"`
	MrBizId              string    `json:"mrBizId"`
	PatchSetBizId        string    `json:"patchSetBizId"`
	PatchSetName         string    `json:"patchSetName"`
	Ref                  string    `json:"ref"`
	RelatedMergeItemType string    `json:"relatedMergeItemType"`
	ShortId              string    `json:"shortId"`
	UserId               int       `json:"user_id"`
	VersionNo            int       `json:"versionNo"`
}

type ListChangeRequestPatchSetsResponse []ChangeRequestPatch

type ChangeRequestFromToInfo struct {
	// FromCommitId baseCommitId
	FromCommitId      string
	FromPatchSetBizId string
	// ToCommitId headCommitId
	ToCommitId      string
	ToPatchSetBizId string
}

type CreateChangeRequestCommentRequest struct {
	CommentType        ChangeRequestCommentType `json:"comment_type"`
	Content            string                   `json:"content"`
	Draft              bool                     `json:"draft"`
	Resolved           bool                     `json:"resolved"`
	FilePath           *string                  `json:"file_path,omitempty"`
	LineNumber         *int                     `json:"line_number,omitempty"`
	FromPatchSetBizId  *string                  `json:"from_patchset_biz_id,omitempty"`
	ToPatchSetBizId    *string                  `json:"to_patchset_biz_id,omitempty"`
	PatchSetBizId      *string                  `json:"patchset_biz_id,omitempty"`
	ParentCommentBizId *string                  `json:"parent_comment_biz_id,omitempty"`
}

type ChangeRequestCommentType string

const (
	InlineComment ChangeRequestCommentType = "INLINE_COMMENT"
	GlobalComment ChangeRequestCommentType = "GLOBAL_COMMENT"
)

type CreateChangeRequestCommentResponse struct {
	Author struct {
		Avatar   string `json:"avatar"`
		Email    string `json:"email"`
		Id       int    `json:"id"`
		Name     string `json:"name"`
		State    string `json:"state"`
		UserId   string `json:"userId"`
		Username string `json:"username"`
	} `json:"author"`
	ChildCommentsList            []interface{} `json:"child_comments_list"`
	CommentBizId                 string        `json:"comment_biz_id"`
	CommentTime                  time.Time     `json:"comment_time"`
	CommentType                  string        `json:"comment_type"`
	Content                      string        `json:"content"`
	ExpressionReplyList          []interface{} `json:"expression_reply_list"`
	IsDeleted                    bool          `json:"is_deleted"`
	LastEditTime                 interface{}   `json:"last_edit_time"`
	LastEditUser                 interface{}   `json:"last_edit_user"`
	LastResolvedStatusChangeTime interface{}   `json:"last_resolved_status_change_time"`
	LastResolvedStatusChangeUser interface{}   `json:"last_resolved_status_change_user"`
	Location                     interface{}   `json:"location"`
	OutDated                     bool          `json:"out_dated"`
	ProjectId                    int           `json:"project_id"`
	RelatedPatchSet              struct {
		CommitId             string    `json:"commitId"`
		CreateTime           time.Time `json:"createTime"`
		MrBizId              string    `json:"mrBizId"`
		PatchSetBizId        string    `json:"patchSetBizId"`
		PatchSetName         string    `json:"patchSetName"`
		Ref                  string    `json:"ref"`
		RelatedMergeItemType string    `json:"relatedMergeItemType"`
		ShortId              string    `json:"shortId"`
		UserId               int       `json:"user_id"`
		VersionNo            int       `json:"versionNo"`
	} `json:"related_patchset"`
	Resolved bool   `json:"resolved"`
	State    string `json:"state"`
}

type GetFileBlobsResponse struct {
	BlobId       string `json:"blobId"`
	CommitId     string `json:"commitId"`
	Content      string `json:"content"`
	Encoding     string `json:"encoding"`
	FileName     string `json:"fileName"`
	FilePath     string `json:"filePath"`
	LastCommitId string `json:"lastCommitId"`
	Ref          string `json:"ref"`
	Size         string `json:"size"`
}

type ReviewChangeRequestRequest struct {
	SubmitDraftCommentIds []string `json:"submitDraftCommentIds"`
}

type ReviewChangeRequestResponse struct {
	Result bool `json:"result"`
}

type GetAppTokenResponse struct {
	TokenId        string    `json:"token_id"`
	AppId          string    `json:"app_id"`
	OrganizationId string    `json:"organization_id"`
	Token          string    `json:"token"`
	ExpiredAt      time.Time `json:"expired_at"`
	CreatedAt      time.Time `json:"created_at"`
}

type MergeRequestCommentType string

type MergeRequestComment struct {
	Author struct {
		Avatar string `json:"avatar"`
		Id     int    `json:"id"`
		Name   string `json:"name"`
		UserId string `json:"userId"`
	} `json:"author"`
	ChildCommentsList   []MergeRequestComment    `json:"child_comments_list"`
	CommentBizId        string                   `json:"comment_biz_id"`
	CommentTime         time.Time                `json:"comment_time"`
	CommentType         ChangeRequestCommentType `json:"comment_type"`
	Content             string                   `json:"content"`
	ExpressionReplyList []struct {
		Emoji         string `json:"emoji"`
		ReplyUserList []struct {
			ReplyBizId string `json:"reply_biz_id"`
			ReplyUser  struct {
				AliyunPk   string `json:"aliyunPk"`
				Avatar     string `json:"avatar"`
				AvatarUrl  string `json:"avatar_url"`
				Email      string `json:"email"`
				ExternUid  string `json:"extern_uid"`
				Id         int    `json:"id"`
				Name       string `json:"name"`
				State      string `json:"state"`
				TbUserId   string `json:"tb_user_id"`
				UserType   string `json:"user_type"`
				Username   string `json:"username"`
				WebUrl     string `json:"web_url"`
				WebsiteUrl string `json:"website_url"`
			} `json:"reply_user"`
		} `json:"reply_user_list"`
	} `json:"expression_reply_list"`
	IsDeleted                    bool        `json:"is_deleted"`
	LastEditTime                 interface{} `json:"last_edit_time"`
	LastEditUser                 interface{} `json:"last_edit_user"`
	LastResolvedStatusChangeTime *time.Time  `json:"last_resolved_status_change_time"`
	LastResolvedStatusChangeUser *struct {
		Avatar   string `json:"avatar"`
		Email    string `json:"email"`
		Id       int    `json:"id"`
		Name     string `json:"name"`
		State    string `json:"state"`
		UserId   string `json:"userId"`
		Username string `json:"username"`
	} `json:"last_resolved_status_change_user"`
	Location        interface{} `json:"location"`
	OutDated        bool        `json:"out_dated"`
	ProjectId       int64       `json:"project_id"`
	RelatedPatchset struct {
		CommitId             string    `json:"commitId"`
		CreateTime           time.Time `json:"createTime"`
		MrBizId              string    `json:"mrBizId"`
		PatchSetBizId        string    `json:"patchSetBizId"`
		PatchSetName         string    `json:"patchSetName"`
		Ref                  string    `json:"ref"`
		RelatedMergeItemType string    `json:"relatedMergeItemType"`
		ShortId              string    `json:"shortId"`
		UserId               int       `json:"user_id"`
		VersionNo            int       `json:"versionNo"`
	} `json:"related_patchset"`
	Resolved          bool   `json:"resolved"`
	State             string `json:"state"`
	FilePath          string `json:"filePath,omitempty"`
	FromPatchsetBizId string `json:"from_patchset_biz_id,omitempty"`
	LineNumber        int    `json:"line_number,omitempty"`
	ToPatchsetBizId   string `json:"to_patchset_biz_id,omitempty"`
}

type ListMergeRequestCommentsRequest struct {
	CommentIds []string `json:"comment_biz_id_list"`
}

type ListMergeRequestCommentsResponse []MergeRequestComment

type UpdateMergeRequestResponse struct {
	Result bool `json:"result"`
}
