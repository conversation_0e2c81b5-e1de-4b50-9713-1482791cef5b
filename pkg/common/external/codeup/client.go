package codeup

import (
	"encoding/json"
	"fmt"
	"github.com/golang-jwt/jwt/v4"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	errors2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	http2 "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/http"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"math"
	"net/url"
	"time"
)

type CodeupClient struct {
	AppToken  string
	AppSecret string
	AppId     string
	Endpoint  string
}

func NewClient(appId string) *CodeupClient {
	return &CodeupClient{
		AppId:     appId,
		AppSecret: config.GetCodeupAppSecret(appId),
		AppToken:  config.GetCodeupAppToken(appId),
		Endpoint:  config.GetCodeupEndpoint(appId),
	}
}

func (c *CodeupClient) loadAuthHeaders(ctx base.Context, orgId string) (map[string]string, error) {
	token := c.AppToken
	if token == "" {
		// 如果配置了个人Token，则优先使用Token，否则通过AppSecret交换
		t, err := c.getAppTokenWithCache(ctx, orgId)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		token = t
	}
	return map[string]string{
		"x-yunxiao-token": token,
	}, nil
}

func (c *CodeupClient) withOrganizationAuthLoader(orgId string) func(ctx base.Context) (map[string]string, error) {
	return func(ctx base.Context) (map[string]string, error) {
		return c.loadAuthHeaders(ctx, orgId)
	}
}

func (c *CodeupClient) appTokenCacheKey(orgId string) string {
	return fmt.Sprintf("codeup_app_token_%s_%s", c.AppId, orgId)
}

func (c *CodeupClient) getAppTokenWithCache(ctx base.Context, orgId string) (string, error) {
	key := c.appTokenCacheKey(orgId)
	redisCli := helper.GetRedis()
	if existToken, err := redisCli.Get(ctx, key).Result(); err == nil {
		return existToken, nil
	} else if !errors.Is(err, redis.Nil) {
		ctx.GetLogger().Error("load app token failed", "key", key, "err", err)
		return "", errors.WithStack(err)
	}
	resp, err := c.GetAppToken(ctx, orgId)
	if err != nil {
		ctx.GetLogger().Error("Failed to get app token", "orgId", orgId, "err", err)
		return "", errors.WithStack(err)
	}
	d := resp.ExpiredAt.Sub(time.Now())
	if d >= 5*time.Minute {
		// 至少还有5分钟才过期，先缓存
		expiration := d - 5*time.Minute
		err := redisCli.Set(ctx, key, resp.Token, expiration).Err()
		if err != nil {
			errors2.AlertError(ctx, errors2.AlertScopeRedis, []string{key, "codeup-app-token"}, "Failed to set app token", err)
			// 不阻塞
		}
	}

	return resp.Token, nil
}

func (c *CodeupClient) GetChangeRequest(ctx base.Context, orgId, repoId string, localId int) (*GetChangeRequestResponse, error) {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/changeRequests/%d", orgId, repoId, localId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return http2.InvokeHttp[GetChangeRequestResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "GET",
		Url:        fullUrl,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
}

// GetCompare 获取两个commit之间的diff代码块，from源分支，to目标分支
func (c *CodeupClient) GetCompare(ctx base.Context, orgId, repoId string, from string, to string) (*GetCompareResponse, error) {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/compares", orgId, repoId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"from": from,
		"to":   to,
	}
	return http2.InvokeHttp[GetCompareResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "GET",
		Url:        fullUrl,
		Query:      queryData,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
}

func (c *CodeupClient) ListChangeRequestPatchSets(ctx base.Context, orgId, repoId string, localId int) (*ListChangeRequestPatchSetsResponse, error) {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/changeRequests/%d/diffs/patches", orgId, repoId, localId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return http2.InvokeHttp[ListChangeRequestPatchSetsResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "GET",
		Url:        fullUrl,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
}

func (c *CodeupClient) GetChangeRequestLatestFromToCommitId(ctx base.Context, orgId, repoId string, localId int) (*ChangeRequestFromToInfo, error) {
	patchSets, err := c.ListChangeRequestPatchSets(ctx, orgId, repoId, localId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if patchSets == nil || len(*patchSets) == 0 {
		return nil, errors2.New(codes.ServerInternalError, "empty patchSets")
	}
	if len(*patchSets) < 2 {
		return nil, errors2.New(codes.ServerInternalError, "only 1 patchSet")
	}
	headCommitId := ""
	headPatch := ""
	baseCommitId := ""
	basePatch := ""

	getLatestSourcePatch := func(patchSets *ListChangeRequestPatchSetsResponse) *ChangeRequestPatch {
		// 获取最新的Source Patch
		versionNo := -1
		index := -1
		for i := range *patchSets {
			patchSet := (*patchSets)[i]
			if patchSet.RelatedMergeItemType != "MERGE_SOURCE" {
				continue
			}
			if patchSet.VersionNo > versionNo {
				versionNo = patchSet.VersionNo
				index = i
			}
		}
		if index >= 0 {
			return &(*patchSets)[index]
		}
		return nil
	}
	getOldestTargetPatch := func(patchSets *ListChangeRequestPatchSetsResponse) *ChangeRequestPatch {
		// 获取最旧的Target Patch
		versionNo := math.MaxInt
		index := -1
		for i := range *patchSets {
			patchSet := (*patchSets)[i]
			if patchSet.RelatedMergeItemType != "MERGE_TARGET" {
				continue
			}
			if patchSet.VersionNo < versionNo {
				versionNo = patchSet.VersionNo
				index = i
			}
		}
		if index >= 0 {
			return &(*patchSets)[index]
		}
		return nil
	}
	if latestSource := getLatestSourcePatch(patchSets); latestSource != nil {
		headCommitId = latestSource.CommitId
		headPatch = latestSource.PatchSetBizId
	}
	if oldestTarget := getOldestTargetPatch(patchSets); oldestTarget != nil {
		baseCommitId = oldestTarget.CommitId
		basePatch = oldestTarget.PatchSetBizId
	}
	if headCommitId == "" {
		return nil, errors2.New(codes.ServerInternalError, "empty headCommitId")
	}
	if baseCommitId == "" {
		return nil, errors2.New(codes.ServerInternalError, "empty baseCommitId")
	}

	return &ChangeRequestFromToInfo{
		FromCommitId:      baseCommitId,
		FromPatchSetBizId: basePatch,
		ToCommitId:        headCommitId,
		ToPatchSetBizId:   headPatch,
	}, nil
}

func (c *CodeupClient) CreateChangeRequestComment(ctx base.Context, orgId, repoId string, localId int, req *CreateChangeRequestCommentRequest) (*CreateChangeRequestCommentResponse, error) {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/changeRequests/%d/comments", orgId, repoId, localId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	bodyBytes, err := json.Marshal(req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return http2.InvokeHttp[CreateChangeRequestCommentResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "POST",
		Url:        fullUrl,
		Body:       bodyBytes,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
}

func (c *CodeupClient) GetFileBlobs(ctx base.Context, orgId, repoId, filePath, ref string) (*GetFileBlobsResponse, error) {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/files/%s", orgId, repoId, url.QueryEscape(filePath))
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	queryData := map[string]string{
		"ref": ref,
	}
	return http2.InvokeHttp[GetFileBlobsResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "GET",
		Url:        fullUrl,
		Query:      queryData,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
}

func (c *CodeupClient) ReviewChangeRequest(ctx base.Context, orgId, repoId string, localId int, req *ReviewChangeRequestRequest) (*ReviewChangeRequestResponse, error) {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/changeRequests/%d/review", orgId, repoId, localId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	bodyBytes, err := json.Marshal(req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return http2.InvokeHttp[ReviewChangeRequestResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "POST",
		Url:        fullUrl,
		Body:       bodyBytes,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
}

func (c *CodeupClient) DeleteChangeRequestComment(ctx base.Context, orgId, repoId string, localId int, commentId string) error {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/changeRequests/%d/comments/%s", orgId, repoId, localId, commentId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return errors.WithStack(err)
	}
	if _, err := http2.InvokeHttp[struct{}](ctx, &http2.InvokeHttpRequest{
		Method:     "DELETE",
		Url:        fullUrl,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	}); err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (c *CodeupClient) GetAppToken(ctx base.Context, orgId string) (*GetAppTokenResponse, error) {
	uri := fmt.Sprintf("/open/v1/organizations/%s/apps/%s/tokens", orgId, c.AppId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	body := map[string]any{
		"appId": c.AppId,
		"orgId": orgId,
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return http2.InvokeHttp[GetAppTokenResponse](ctx, &http2.InvokeHttpRequest{
		Method: "POST",
		Url:    fullUrl,
		Body:   bodyBytes,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		AuthLoader: authWithAppSecretHS256(body, c.AppSecret),
	})
}

func (c *CodeupClient) ListMergeRequestComments(ctx base.Context, orgId, repoId string, localId int, req *ListMergeRequestCommentsRequest) (*ListMergeRequestCommentsResponse, error) {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/changeRequests/%d/comments/list", orgId, repoId, localId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	bodyBytes, err := json.Marshal(req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return http2.InvokeHttp[ListMergeRequestCommentsResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "POST",
		Url:        fullUrl,
		Body:       bodyBytes,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
}

func (c *CodeupClient) GetMergeRequestComment(ctx base.Context, orgId, repoId string, localId int, commentId string) (*MergeRequestComment, error) {
	comments, err := c.ListMergeRequestComments(ctx, orgId, repoId, localId, &ListMergeRequestCommentsRequest{CommentIds: []string{commentId}})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for i := range *comments {
		comment := (*comments)[i]
		if found := findCommentRecursively(&comment, commentId); found != nil {
			return found, nil
		}
	}
	ctx.GetLogger().Error("comment not found", "commentId", commentId)
	return nil, errors2.New(codes.ServerInternalError, "comment not found")
}

func (c *CodeupClient) UpdateMergeRequestComment(ctx base.Context, orgId, repoId string, localId int, opts *codeplatform.UpdateMergeRequestCommentOpts) error {
	uri := fmt.Sprintf("/oapi/v1/codeup/organizations/%s/repositories/%s/changeRequests/%d/comments/%s", orgId, repoId, localId, opts.CommentId)
	fullUrl, err := url.JoinPath(c.Endpoint, uri)
	if err != nil {
		return errors.WithStack(err)
	}
	body := map[string]any{
		"content": opts.Content,
	}
	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return errors.WithStack(err)
	}
	resp, err := http2.InvokeHttp[UpdateMergeRequestResponse](ctx, &http2.InvokeHttpRequest{
		Method:     "PUT",
		Url:        fullUrl,
		Body:       bodyBytes,
		AuthLoader: c.withOrganizationAuthLoader(orgId),
	})
	if err != nil {
		return errors.WithStack(err)
	}
	if !resp.Result {
		return errors2.New(codes.ServerInternalError, "update comment failed")
	}
	return nil
}

func findCommentRecursively(comment *MergeRequestComment, commentId string) *MergeRequestComment {
	if comment.CommentBizId == commentId {
		return comment
	}
	for i := range comment.ChildCommentsList {
		child := &comment.ChildCommentsList[i]
		if child != nil {
			if found := findCommentRecursively(child, commentId); found != nil {
				return found
			}
		}
	}
	return nil
}

func authWithAppSecretHS256(body map[string]any, appSecret string) func(base.Context) (map[string]string, error) {
	return func(ctx base.Context) (map[string]string, error) {
		headers := map[string]string{}
		mc := jwt.MapClaims{}
		for k, v := range body {
			mc[k] = v
		}
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, mc)
		tokenString, err := token.SignedString([]byte(appSecret))
		if err != nil {
			return headers, errors.WithStack(err)
		}
		headers["Authorization"] = fmt.Sprintf("Bearer %s", tokenString)
		return headers, nil
	}

}
