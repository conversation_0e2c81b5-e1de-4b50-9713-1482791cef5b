package cronjob

import (
	"k8s.io/utils/ptr"
	"time"
)

type JobOptions struct {

	// 任务名称, 同一个类型的任务需要唯一
	Name string

	// Cron表达式类型的任务
	CronExpression *string

	//Duration 固定周期性任务
	Duration *time.Duration
	// 任务函数
	Task func() error

	// SingletonMode  job限制为单次并发执行，会采用分布式锁来实现，默认为true
	// 在获取锁的时候，默认不会重试， 如果当前获取锁失败，则不会执行任务。
	//如果需要重试，将LockerRetryable设置为true，并配置合理的LockerRetries, LockerRetryDelay, 推荐整体的retry时间要大于任务执行时间。
	SingletonMode bool

	// LockerRetryable  在job为SingletonMode的时候, 会加入一个锁, 这个表示获取锁失败的,是否重试，默认为false
	LockerRetryable bool

	// LockerTimeout  任务锁定超时时间
	LockerTimeout time.Duration

	// LockerRetries  在job为SingletonMode的时候, 会加入一个锁, 这个表示获取锁失败的,重试次数， 默认为3
	LockerRetries int

	// LockerRetryDelay  在job为SingletonMode的时候, 会加入一个锁, 这个表示获取锁失败的,重试间隔，默认为50ms
	LockerRetryDelay time.Duration
}

type Job struct {
	// 任务名称, 同一个类型的任务需要唯一
	Name string
	// Cron表达式类型的任务
	CronExpression *string
	//Duration 固定周期性任务
	Duration *time.Duration
	// 任务函数
	Task func() error

	// SingletonMode  job限制为单次并发执行，会采用分布式锁来实现，默认为true
	// 在获取锁的时候，默认不会重试， 如果当前获取锁失败，则不会执行任务。
	//如果需要重试，将LockerRetryable设置为true，并配置合理的LockerRetries, LockerRetryDelay, 推荐整体的retry时间要大于任务执行时间。
	SingletonMode bool

	// LockerTimeout  任务锁定超时时间, 默认为2s, 仅在SingletonMode为true下有效
	LockerTimeout time.Duration

	// LockerRetryable  在job为SingletonMode的时候, 会加入一个锁, 这个表示获取锁失败的,是否重试，默认为false， 仅在SingletonMode为true下有效
	LockerRetryable bool

	// LockerRetries  在job为SingletonMode的时候, 会加入一个锁, 这个表示获取锁失败的,重试次数， 默认为3， 仅在SingletonMode为true下有效
	LockerRetries int

	// LockerRetryDelay  在job为SingletonMode的时候, 会加入一个锁, 这个表示获取锁失败的,重试间隔，默认为50ms，  仅在SingletonMode为true下有效
	LockerRetryDelay time.Duration
}

func NewJob(opts ...JobOption) *Job {
	jobOptions := newDefaultJobOptions()
	for _, opt := range opts {
		opt(jobOptions)
	}

	return &Job{
		Name:             jobOptions.Name,
		CronExpression:   jobOptions.CronExpression,
		Duration:         jobOptions.Duration,
		SingletonMode:    jobOptions.SingletonMode,
		Task:             jobOptions.Task,
		LockerTimeout:    jobOptions.LockerTimeout,
		LockerRetries:    jobOptions.LockerRetries,
		LockerRetryDelay: jobOptions.LockerRetryDelay,
		LockerRetryable:  jobOptions.LockerRetryable,
	}
}

type JobOption func(opts *JobOptions)

func WithName(name string) JobOption {
	return func(opts *JobOptions) {
		opts.Name = name
	}
}

func WithCronExpression(cronExpression string) JobOption {
	return func(opts *JobOptions) {
		opts.CronExpression = ptr.To(cronExpression)
	}
}
func WithDuration(duration time.Duration) JobOption {
	return func(opts *JobOptions) {
		opts.Duration = ptr.To(duration)
	}
}

func WithSingletonMode(SingletonMode bool) JobOption {
	return func(opts *JobOptions) {
		opts.SingletonMode = SingletonMode
	}
}

func WithTask(task func() error) JobOption {
	return func(opts *JobOptions) {
		opts.Task = task
	}
}

func WithLockerTimeout(timeout time.Duration) JobOption {
	return func(opts *JobOptions) {
		opts.LockerTimeout = timeout
	}
}

func WithLockerRetries(retries int) JobOption {
	return func(opts *JobOptions) {
		opts.LockerRetries = retries
	}
}

func WithLockerRetryable(retryable bool) JobOption {
	return func(opts *JobOptions) {
		opts.LockerRetryable = retryable
	}
}

func WithLockerRetryDelay(delay time.Duration) JobOption {
	return func(opts *JobOptions) {
		opts.LockerRetryDelay = delay
	}
}

func newDefaultJobOptions() *JobOptions {
	return &JobOptions{
		SingletonMode:    true,
		LockerTimeout:    2 * time.Second,
		LockerRetries:    3,
		LockerRetryDelay: time.Millisecond * 50,
		LockerRetryable:  false,
	}
}
