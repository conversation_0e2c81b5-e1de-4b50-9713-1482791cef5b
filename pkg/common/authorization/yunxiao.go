package authorization

type Operation string

const (
	ViewAgentConfiguration Operation = "agents_configuration_view"
	ViewAgentLogs          Operation = "agents_logs_view"

	ManageAgentConfiguration Operation = "agents_configuration_manage"
	ToggleAgent              Operation = "agents_configuration_enable_disable"

	DownloadAgentLogs Operation = "agents_logs_download"

	AgentAdmin Operation = "agents_admin_set"
)

const (
	// AgentOverviewPermission 概览页面
	AgentOverviewPermission = "agents.overview"
	// AgentListPermission 智能体列表页面
	AgentListPermission = "agents.list.view"
	// AgentLogsPermission 查看Agent执行记录
	AgentLogsPermission = "agents.logs.view"
	// AgentLogsExportPermission 导出Agent执行记录
	AgentLogsExportPermission = "agents.logs.export"
	// AgentAuditPermission 查看操作日志
	AgentAuditPermission = "agents.audit.view"
	// AgentConfigurationPermission 查看智能体配置
	AgentConfigurationPermission = "agents.configuration.view"
	// AgentConfigurationManagePermission 管理智能体配置
	AgentConfigurationManagePermission = "agents.configuration.manage"
	// AgentConfigurationTogglePermission 开关智能体
	AgentConfigurationTogglePermission = "agents.configuration.toggle"
	// AgentCodeBaseConfigurationPermission 查看代码仓库配置
	AgentCodeBaseConfigurationPermission = "agents.code-base.configuration.view"
	// AgentCodeBaseConfigurationManagePermission 管理代码仓库配置
	AgentCodeBaseConfigurationManagePermission = "agents.code-base.configuration.manage"
	// AgentCodeReviewAgentGithubPermission 代码评审智能体 Github平台
	AgentCodeReviewAgentGithubPermission = "agents.code-review-agent.github"
	// AgentCodeReviewAgentGitlabPermission 代码评审智能体 Gitlab平台
	AgentCodeReviewAgentGitlabPermission = "agents.code-review-agent.gitlab"
)

func (o Operation) ToFrontPagePermissions() []string {
	switch o {
	case ViewAgentLogs:
		return []string{
			AgentAuditPermission,
			AgentOverviewPermission,
			AgentLogsPermission,
		}
	case ViewAgentConfiguration:
		return []string{
			AgentAuditPermission,
			AgentOverviewPermission,
			AgentListPermission,
			AgentConfigurationPermission,
			AgentCodeBaseConfigurationPermission,
			AgentCodeReviewAgentGitlabPermission,
		}
	case ManageAgentConfiguration:
		return []string{
			AgentConfigurationManagePermission,
			AgentCodeBaseConfigurationManagePermission,
		}
	case ToggleAgent:
		return []string{
			AgentConfigurationTogglePermission,
		}
	case DownloadAgentLogs:
		return []string{
			AgentLogsExportPermission,
		}
	case AgentAdmin:
		return []string{
			AgentAuditPermission,
			AgentOverviewPermission,
			AgentListPermission,
			AgentLogsPermission,
			AgentConfigurationPermission,
			AgentCodeBaseConfigurationPermission,
			AgentCodeReviewAgentGitlabPermission,
			AgentConfigurationManagePermission,
			AgentCodeBaseConfigurationManagePermission,
			AgentConfigurationTogglePermission,
			AgentLogsExportPermission,
		}
	}
	return nil
}
