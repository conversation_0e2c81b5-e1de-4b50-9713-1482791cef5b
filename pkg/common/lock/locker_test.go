package lock

import (
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"testing"
	"time"
)

func TestWithExpiry(t *testing.T) {
	mockey.PatchConvey("WithExpiry", t, func() {
		opt := newDefaultLockOption()
		convey.So(opt.expire, convey.ShouldEqual, 5*time.Second)
		f := WithExpiry(1 * time.Second)
		f.Apply(&opt)
		convey.So(opt.expire, convey.ShouldEqual, 1*time.Second)
	})
}
func TestWithRetryDelay(t *testing.T) {
	mockey.PatchConvey("WithRetryDelay", t, func() {
		opt := newDefaultLockOption()
		convey.So(opt.retryDelay, convey.ShouldEqual, 600*time.Millisecond)
		f := WithRetryDelay(2 * time.Second)
		f.Apply(&opt)
		convey.So(opt.retryDelay, convey.ShouldEqual, 2*time.Second)
	})
}

func TestWithRetry(t *testing.T) {
	mockey.<PERSON><PERSON>onvey("WithRetry", t, func() {
		opt := newDefaultLockOption()
		convey.So(opt.retry, convey.ShouldEqual, 10)
		f := WithRetry(5)
		f.Apply(&opt)
		convey.So(opt.retry, convey.ShouldEqual, 5)
	})
}
