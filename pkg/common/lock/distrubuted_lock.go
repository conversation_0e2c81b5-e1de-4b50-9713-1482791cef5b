package lock

import (
	"context"
	"github.com/go-redsync/redsync/v4"
	goredis "github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"sync"
	"time"
)

type DistributedLock interface {
	// Lock acquires a lock once.
	Lock() error
	LockContext(ctx context.Context) error
	TryLock() error
	TryLockContext(ctx context.Context) error
	Unlock() (bool, error)
	UnlockContext(ctx context.Context) (bool, error)
	Value() string
	Name() string
}
type DistributedLockContextOptions struct {
}

var NewRedisDistributedLock = sync.OnceValue(func() *redsync.Redsync {
	return redsync.New(goredis.NewPool(helper.GetRedis()))
})

func NewLockerWithValue(name string, value string) DistributedLock {
	r := NewRedisDistributedLock()
	// redis中5秒过期，如果结合Lock()方法则是每隔300ms尝试获取锁，尝试10次，也就是3秒内如果拿不到锁就报错。
	// 这个值暂时拍脑袋定的，按需修改
	return r.NewMutex(
		name,
		redsync.WithExpiry(5*time.Second),
		redsync.WithRetryDelay(600*time.Millisecond),
		redsync.WithTries(10),
		redsync.WithValue(value),
	)
}
