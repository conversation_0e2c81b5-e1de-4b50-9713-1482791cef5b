package lock

import (
	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"time"
)

type LockFunc func() error
type UnlockFunc func()

type LockOption struct {
	expire     time.Duration
	retryDelay time.Duration
	retry      int
}

func newDefaultLockOption() LockOption {
	return LockOption{
		expire:     60 * time.Second,
		retryDelay: 600 * time.Millisecond,
		retry:      10,
	}
}

type ApplyLockOption interface {
	Apply(*LockOption)
}

type ApplyLockOptionFunc func(*LockOption)

func (f ApplyLockOptionFunc) Apply(opt *LockOption) {
	f(opt)
}

func WithExpiry(expire time.Duration) ApplyLockOption {
	return ApplyLockOptionFunc(func(opt *LockOption) {
		opt.expire = expire
	})
}
func WithRetryDelay(retryDelay time.Duration) ApplyLockOption {
	return ApplyLockOptionFunc(func(opt *LockOption) {
		opt.retryDelay = retryDelay
	})
}
func WithRetry(retry int) ApplyLockOption {
	return ApplyLockOptionFunc(func(opt *LockOption) {
		opt.retry = retry
	})
}

// NewLockFunc 获得分布式锁，返回上锁函数和解锁函数，简化日志记录与解锁错误处理
func NewLockFunc(ctx base.Context, lockName string, opts ...ApplyLockOption) (LockFunc, UnlockFunc) {
	opt := newDefaultLockOption()
	for _, fn := range opts {
		fn.Apply(&opt)
	}
	return newLockFunc(ctx, lockName, opt.expire, opt.retryDelay, opt.retry)
}

func NewLock(lockName string, opts ...ApplyLockOption) DistributedLock {
	opt := newDefaultLockOption()
	for _, fn := range opts {
		fn.Apply(&opt)
	}
	r := NewRedisDistributedLock()
	l := r.NewMutex(lockName,
		redsync.WithExpiry(opt.expire),
		redsync.WithRetryDelay(opt.retryDelay),
		redsync.WithTries(opt.retry))
	return l
}

func newLockFunc(ctx base.Context, lockName string, expiry time.Duration, retryDelay time.Duration, retry int) (LockFunc, UnlockFunc) {
	r := NewRedisDistributedLock()
	l := r.NewMutex(lockName,
		redsync.WithExpiry(expiry),
		redsync.WithRetryDelay(retryDelay),
		redsync.WithTries(retry))
	lockFn := func() error {
		if err := l.Lock(); err != nil {
			ctx.GetLogger().Error("lock failed", "lockName", lockName, "err", err)
			return errors.WithStack(err)
		}
		ctx.GetLogger().Info("lock success", "lockName", lockName)
		return nil
	}
	unlockFn := func() {
		if succ, err := l.Unlock(); err != nil {
			ctx.GetLogger().Error("unlock failed", "lockName", lockName, "err", err)
		} else if !succ {
			ctx.GetLogger().Error("unlock failed, without err", "lockName", lockName)
		}
		ctx.GetLogger().Info("unlock success", "lockName", lockName)
	}
	return lockFn, unlockFn
}
