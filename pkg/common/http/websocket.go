package http

import (
	"github.com/gorilla/websocket"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/identity"
	"log/slog"
	"sync"
)

type SafeConn struct {
	conn      *websocket.Conn
	ConnectId string
	wl        *sync.Mutex
	IdInfo    *identity.IdentityInfo
}

func (c *SafeConn) WriteText(msg string) error {
	c.wl.Lock()
	defer c.wl.Unlock()
	slog.Info("--> " + msg)
	return c.conn.WriteMessage(websocket.TextMessage, []byte(msg))
}

func (c *SafeConn) Read() (messageType int, p []byte, err error) {
	mt, p, err := c.conn.ReadMessage()
	if err != nil {
		slog.Error("fail to read from connect", "err", err)
	} else {
		slog.Info("<-- " + string(p))
	}
	return mt, p, err
}
func NewSafeConn(connId string, conn *websocket.Conn, idInfo *identity.IdentityInfo) *SafeConn {
	conn.SetPingHandler(nil)
	return &SafeConn{
		conn:      conn,
		ConnectId: connId,
		wl:        &sync.Mutex{},
		IdInfo:    idInfo,
	}
}
