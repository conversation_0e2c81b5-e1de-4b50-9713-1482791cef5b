package http

import (
	"bytes"
	"encoding/json"
	mapset "github.com/deckarep/golang-set/v2"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"io"
	"net/http"
	"net/url"
	"time"
)

const (
	defaultTimeout = 5 * time.Second
)

var (
	sensitiveKeys = mapset.NewThreadUnsafeSet[string](
		"x-yunxiao-token",
		"Authorization",
	)
)

// InvokeHttp 调用http请求，默认超时时间5秒，埋入X-Trace-ID与X-Request-ID请求头，埋入X-User-ID头表征用户身份，支持传入一个AuthLoader函数，用于加载认证相关的请求头
func InvokeHttp[T any](ctx base.Context, req *InvokeHttpRequest) (*T, error) {
	if req.Timeout == 0 {
		req.Timeout = defaultTimeout
	}
	if req.AuthLoader != nil {
		authHeaders, err := req.AuthLoader(ctx)
		if err != nil {
			ctx.GetLogger().Error("auth loader failed", "err", err)
			return nil, errors.WithStack(err)
		}
		if req.Headers == nil {
			req.Headers = make(map[string]string)
		}
		for k, v := range authHeaders {
			req.Headers[k] = v
		}
	}

	return invokeHttpWithTimeout[T](ctx, req.Method, req.Url, req.Headers, req.Query, req.Body, req.Timeout)
}

func invokeHttpWithTimeout[T any](ctx base.Context, method string, fullUrl string, headers map[string]string, queryData map[string]string, bodyBytes []byte, timeout time.Duration) (*T, error) {
	// 日志打印时移除掉敏感的headers 与 query
	logHeaders := make(map[string]string)
	logQuery := make(map[string]string)
	for k, v := range headers {
		logHeaders[k] = v
		if sensitiveKeys.Contains(k) {
			logHeaders[k] = "******"
		}
	}
	for k, v := range queryData {
		logQuery[k] = v
		if sensitiveKeys.Contains(k) {
			logQuery[k] = "******"
		}
	}
	ctx.GetLogger().Info("invoke http", "method", method, "fullUrl", fullUrl, "headers", logHeaders, "queryData", logQuery, "timeout", timeout)
	// bodyBytes中可能包含敏感数据
	ctx.GetLogger().Debug("invoke http body", "bodyBytes", string(bodyBytes))
	result := new(T)
	queryValues := url.Values{}
	for key, value := range queryData {
		queryValues.Add(key, value)
	}
	if len(queryData) > 0 {
		fullUrl += "?" + queryValues.Encode()
	}

	req, err := http.NewRequest(method, fullUrl, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return result, errors.WithStack(err)
	}
	for k, v := range headers {
		req.Header.Set(k, v)
	}
	req.Header.Set("X-Trace-ID", ctx.GetTraceId())
	reqId := helper.NewRequestId()
	req.Header.Set("X-Request-ID", reqId)
	req.Header.Set("X-User-ID", ctx.GetUid())

	client := &http.Client{Timeout: timeout}
	resp, err := client.Do(req)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	defer resp.Body.Close()
	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		ctx.GetLogger().Error("read response body failed", "err", err)
		return nil, errors.WithStack(err)
	}
	if resp.StatusCode != 200 && resp.StatusCode != 201 {
		// 记录一下非正常响应
		ctx.GetLogger().Warn("invoke http failed", "statusCode", resp.StatusCode, "fullUrl", fullUrl, "method", method, "respBody", string(respBody))
		if resp.StatusCode == 404 {
			return nil, commonerrors.New(codes.ErrExternalResourceNotFound, reqId)
		}
		return nil, commonerrors.New(codes.ErrRequestExternalFailed, reqId)
	}
	// todo 过滤敏感数据
	ctx.GetLogger().Info("invoke http success", "method", method, "fullUrl", fullUrl, "respBody", string(respBody))
	if err := json.Unmarshal(respBody, result); err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}
