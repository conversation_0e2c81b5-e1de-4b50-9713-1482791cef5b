package http

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"time"
)

// ApiObject 所有对外暴露的实体均需要实现此类
// 为什么不直接用json注解？因为可能会涉及到特定的业务逻辑，用json无法处理，只能单独写VO对象，所以这里统一这个接口
type ApiObject interface {
	ToMap() map[string]any
}

type Response struct {
	HttpCode           int     `json:"-"`
	Error              error   `json:"-"`
	Code               string  `json:"code"`
	Message            *string `json:"message,omitempty"`
	Data               any     `json:"data,omitempty"`
	RequestId          *string `json:"requestId,omitempty"`
	AccessDeniedDetail any     `json:"accessDeniedDetail,omitempty"`
}

// CssResult 南天门 SPI 固定返回格式
type CssResult struct {
	HttpStatusCode int32   `json:"httpStatusCode" xml:"httpStatusCode"`
	Success        bool    `json:"success" xml:"success"`
	RequestId      *string `json:"requestId" xml:"requestId"`
	Code           *string `json:"code,omitempty" xml:"code"`
	Data           *string `json:"data,omitempty" xml:"data"`
	Message        *string `json:"message,omitempty" xml:"message"`
	Synchro        *bool   `json:"synchro,omitempty" xml:"synchro"`
}

type InvokeHttpRequest struct {
	Method     string            `json:"method"`
	Url        string            `json:"url"`
	Headers    map[string]string `json:"headers"`
	Query      map[string]string `json:"query"`
	Body       []byte            `json:"body"`
	Timeout    time.Duration     `json:"timeout"`
	AuthLoader AuthLoaderFunc    `json:"-"`
}

type AuthLoaderFunc func(ctx base.Context) (map[string]string, error)
