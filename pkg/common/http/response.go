package http

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"k8s.io/utils/ptr"
	"net/http"
)

const (
	CodeOk                   = "Ok"
	CodeUnauthorized         = "Unauthorized"
	CodeServiceUnavailable   = "ServiceUnavailable"
	CodeServiceInternalError = "ServiceInternalError"
	CodeInvalidParameter     = "InvalidParameter"
	CodeAccessDenied         = "Forbidden.AccessDenied"
	CodeNotFound             = "NotFound"
	CodeStatusConflict       = "Conflict"
	CodeBadRequest           = "BadRequest"
	CodeCloudProductInactive = "CloudProductInactive"
)

func Ok(ctx *gin.Context, data any) {
	ctx.JSON(http.StatusOK, toResponse(ctx, CodeOk, data, nil))
}

func SSEvent(ctx *gin.Context, data any) {
	ctx.SSEvent("data", data)
}

func ServerFail(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusInternalServerError, toResponse(ctx, errCode, data, &errMsg))
}

func NoContent(ctx *gin.Context) {
	ctx.Status(http.StatusNoContent)
}

func InvalidParameter(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusBadRequest, toResponse(ctx, errCode, data, &errMsg))
}

func NotFound(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusNotFound, toResponse(ctx, errCode, data, &errMsg))
}

func Conflict(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusConflict, toResponse(ctx, errCode, data, &errMsg))
}

func Unauthorized(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusUnauthorized, toResponse(ctx, errCode, data, &errMsg))
}

func Forbidden(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusForbidden, toResponse(ctx, errCode, data, &errMsg))
}

func CloudProductInactiveResponse(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusBadRequest, toResponse(ctx, errCode, data, &errMsg))
}

func AccessDenied(ctx *gin.Context, errMsg string, accessDeniedDetail any) {
	ctx.JSON(http.StatusForbidden, AccessDeniedResponse(ctx, errMsg, accessDeniedDetail))
}

func TooManyRequests(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.JSON(http.StatusTooManyRequests, toResponse(ctx, errCode, data, &errMsg))
}

func Redirect(ctx *gin.Context, errCode string, errMsg string, data any) {
	ctx.Redirect(http.StatusFound, errMsg)
}

func toMap(data any) any {
	if o, ok := data.(ApiObject); ok {
		return o.ToMap()
	}
	return data
}

func toResponse(ctx *gin.Context, code string, data any, errMsg *string) *Response {
	return &Response{
		RequestId: context.GetRequestId(ctx),
		Code:      code,
		Data:      toMap(data),
		Message:   errMsg,
	}
}

func UnauthorizedResponse(ctx *gin.Context) *Response {
	msg := CodeUnauthorized
	return &Response{
		HttpCode:  http.StatusUnauthorized,
		Code:      CodeUnauthorized,
		Message:   &msg,
		RequestId: context.GetRequestId(ctx),
	}
}

func ServiceUnavailableResponse(ctx *gin.Context, message string) *Response {
	return &Response{
		HttpCode:  http.StatusServiceUnavailable,
		Code:      CodeServiceUnavailable,
		Message:   &message,
		RequestId: context.GetRequestId(ctx),
	}
}

func BadRequestResponse(ctx *gin.Context, message string) *Response {
	return &Response{
		HttpCode:  http.StatusBadRequest,
		Code:      CodeBadRequest,
		Message:   &message,
		RequestId: context.GetRequestId(ctx),
	}
}

func AccessDeniedResponse(ctx *gin.Context, message string, accessDeniedDetail any) *Response {
	return &Response{
		HttpCode:           http.StatusForbidden,
		Code:               CodeAccessDenied,
		Message:            &message,
		RequestId:          context.GetRequestId(ctx),
		AccessDeniedDetail: toMap(accessDeniedDetail),
	}
}

// CssRespond 南天门SPI Response
func CssRespond(ctx *gin.Context, data *string, synchro *bool, err error) {
	res := &CssResult{
		Data:           data,
		Synchro:        synchro,
		RequestId:      context.GetRequestId(ctx),
		HttpStatusCode: http.StatusOK,
	}

	if err != nil {
		res.Success = false

		var bizErr *comnerrors.BizError
		if errors.As(err, &bizErr) {
			res.Code = ptr.To(bizErr.ErrorCode)
			res.Message = ptr.To(bizErr.ErrorMessage)
		} else {
			// 没有明确错误码的err都是调用出错
			res.HttpStatusCode = http.StatusServiceUnavailable
			// 不要抛原始错误
			res.Message = ptr.To("unknown error, please contact us")
		}

	} else {
		res.Success = true
	}

	// 调通了就只返回200
	ctx.JSON(http.StatusOK, res)

}
