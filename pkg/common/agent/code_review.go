package agent

import (
	"encoding/json"
	"errors"
	"fmt"
	mapset "github.com/deckarep/golang-set/v2"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	i18n "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/in18"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"path/filepath"
	"strings"
)

type CodeReviewTaskParameterConvertor interface {
	ToCodeReviewTaskParameters() map[string]any
}

func loadCodeReviewTaskParameters(m *map[string]any, convertor CodeReviewTaskParameterConvertor) {
	if convertor == nil {
		return
	}
	params := convertor.ToCodeReviewTaskParameters()
	if len(params) == 0 {
		return
	}
	for k, v := range params {
		(*m)[k] = v
	}
}

type RepositoryAgentRule struct {
	ReviewRules *RepositoryReviewRule `json:"reviewRules" yaml:"reviewRules"`
}

type RepositoryReviewRule struct {
	Enable              bool `json:"enable" yaml:"enable"`
	*CodeReviewProperty `yaml:",inline"`
}

func BuildRepositoryAgentRule(enable bool, property *CodeReviewProperty) *RepositoryAgentRule {
	return &RepositoryAgentRule{
		ReviewRules: &RepositoryReviewRule{
			Enable:             enable,
			CodeReviewProperty: property,
		},
	}
}

type CodeReviewProperty struct {
	OutputSetting    *CodeReviewOutputSetting    `json:"outputSetting" yaml:"outputSetting"`
	PathInstructions []CodeReviewPathInstruction `json:"pathInstructions" yaml:"pathInstructions"`
	TriggerFilter    *CodeReviewTriggerFilter    `json:"triggerFilter" yaml:"triggerFilter"`
	ContentFilter    *CodeReviewContentFilter    `json:"contentFilter" yaml:"contentFilter"`
	ToolSetting      *CodeReviewToolSetting      `json:"toolSetting" yaml:"toolSetting"`
	ContextSetting   *CodeReviewContextSetting   `json:"contextSetting" yaml:"contextSetting"`
}

func (crp *CodeReviewProperty) Validate() error {
	if crp == nil {
		return comnerrors.New(codes.ErrInvalidParameterWithEmpty, "codeReview")
	}
	if crp.OutputSetting == nil {
		crp.OutputSetting = NewDefaultOutputSetting()
	}
	if err := crp.OutputSetting.Validate(); err != nil {
		return err
	}
	if crp.ContentFilter == nil {
		crp.ContentFilter = NewCodeReviewContentFilter()
	}
	if err := crp.ContentFilter.Validate(); err != nil {
		return err
	}
	if crp.TriggerFilter == nil {
		crp.TriggerFilter = NewDefaultTriggerFilter()
	}
	if err := crp.TriggerFilter.Validate(); err != nil {
		return err
	}
	for _, v := range crp.PathInstructions {
		if err := v.Validate(); err != nil {
			return err
		}
	}
	if crp.ToolSetting == nil {
		crp.ToolSetting = NewCodeReviewToolSetting()
	}
	if err := crp.ToolSetting.Validate(); err != nil {
		return err
	}
	if crp.ContextSetting == nil {
		crp.ContextSetting = NewDefaultContextSetting()
	}
	if err := crp.ContextSetting.Validate(); err != nil {
		return err
	}
	return nil
}

func (crp *CodeReviewProperty) ToCodeReviewTaskParameters() map[string]any {
	if crp == nil {
		p := NewDefaultCodeReviewProperty()
		return p.ToCodeReviewTaskParameters()
	}
	m := map[string]any{}
	fields := []CodeReviewTaskParameterConvertor{
		crp.OutputSetting,
		crp.ContentFilter,
		crp.ToolSetting,
	}
	for _, convertor := range fields {
		loadCodeReviewTaskParameters(&m, convertor)
	}

	var pathInstructions []map[string]any
	for _, v := range crp.PathInstructions {
		val := v.ToCodeReviewTaskParameters()
		if len(val) == 0 {
			continue
		}
		pathInstructions = append(pathInstructions, val)
	}
	if len(pathInstructions) > 0 {
		m["path_instructions"] = pathInstructions
	}

	return m
}

func (crp *CodeReviewProperty) MarshalJSON() ([]byte, error) {
	if crp == nil {
		return []byte("{}"), nil
	}
	type Alias CodeReviewProperty
	temp := (*Alias)(crp)
	if len(temp.PathInstructions) == 0 {
		temp.PathInstructions = make([]CodeReviewPathInstruction, 0)
	}
	if temp.ContentFilter == nil {
		temp.ContentFilter = NewCodeReviewContentFilter()
	}
	if temp.OutputSetting == nil {
		temp.OutputSetting = NewDefaultOutputSetting()
	}
	if temp.ToolSetting == nil {
		temp.ToolSetting = NewCodeReviewToolSetting()
	}
	if temp.TriggerFilter == nil {
		temp.TriggerFilter = NewDefaultTriggerFilter()
	}
	if temp.ContextSetting == nil {
		temp.ContextSetting = NewDefaultContextSetting()
	}
	return json.Marshal(temp)
}

type CodeReviewOutputSetting struct {
	Language     CodeReviewLanguage     `json:"language,omitempty" yaml:"language"`
	ProblemLevel CodeReviewProblemLevel `json:"problemLevel,omitempty" yaml:"problemLevel"`
	WalkThrough  *CodeReviewWalkThrough `json:"walkThrough" yaml:"walkThrough"`
	Report       *CodeReviewReport      `json:"report" yaml:"report"`
}

func (cros *CodeReviewOutputSetting) ToCodeReviewTaskParameters() map[string]any {
	if cros == nil {
		return map[string]any{}
	}
	m := map[string]any{
		"language":      cros.Language.ToCodeReviewTaskValue(),
		"problem_level": cros.ProblemLevel,
	}
	fields := []CodeReviewTaskParameterConvertor{
		cros.WalkThrough,
		cros.Report,
	}
	for _, convertor := range fields {
		loadCodeReviewTaskParameters(&m, convertor)
	}
	return m
}

type CodeReviewLanguage string

const (
	Chinese CodeReviewLanguage = "zh-CN"
	English CodeReviewLanguage = "en-US"
)

func (crl CodeReviewLanguage) ToCodeReviewTaskValue() i18n.Locale {
	switch crl {
	case Chinese:
		return i18n.SimplifiedChinese
	case English:
		return i18n.English
	}
	return i18n.SimplifiedChinese
}

func (cros *CodeReviewOutputSetting) Validate() error {
	switch cros.Language {
	case Chinese, English:
	default:
		return comnerrors.New(codes.ErrInvalidParameter, "language", cros.Language)
	}
	switch cros.ProblemLevel {
	case Blocker, Critical, Major, Minor:
	default:
		return comnerrors.New(codes.ErrInvalidParameter, "problemLevel", cros.ProblemLevel)
	}
	return nil
}

type CodeReviewProblemLevel string

const (
	Blocker  CodeReviewProblemLevel = "BLOCKER"
	Critical CodeReviewProblemLevel = "CRITICAL"
	Major    CodeReviewProblemLevel = "MAJOR"
	Minor    CodeReviewProblemLevel = "MINOR"
)

type CodeReviewWalkThrough struct {
	Enable     bool `json:"enable" yaml:"enable"`
	Collapse   bool `json:"collapse" yaml:"collapse"`
	Summary    bool `json:"summary" yaml:"summary"`
	Changes    bool `json:"changes" yaml:"changes"`
	Diagram    bool `json:"diagram" yaml:"diagram"`
	Statistics bool `json:"statistics" yaml:"statistics"`
}

func (c *CodeReviewWalkThrough) Validate() error {
	return nil
}

func (c *CodeReviewWalkThrough) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"walkthrough_enable":     c.Enable,
		"walkthrough_collapse":   c.Collapse,
		"walkthrough_summary":    c.Summary,
		"walkthrough_changes":    c.Changes,
		"walkthrough_diagram":    c.Diagram,
		"walkthrough_statistics": c.Statistics,
	}
}

type CodeReviewReport struct {
	Enable            bool `json:"enable" yaml:"enable"`
	Collapse          bool `json:"collapse" yaml:"collapse"`
	Summary           bool `json:"summary" yaml:"summary"`
	CodeSuggestion    bool `json:"codeSuggestion" yaml:"codeSuggestion"`
	HighLevelFeedback bool `json:"highLevelFeedback" yaml:"highLevelFeedback"`
	Scan              bool `json:"scan" yaml:"scan"`
	UnitTest          bool `json:"unitTest" yaml:"unitTest"`
}

func (c *CodeReviewReport) Validate() error {
	return nil
}

func (c *CodeReviewReport) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"report_enable":              c.Enable,
		"report_collapse":            c.Collapse,
		"report_summary":             c.Summary,
		"report_code_suggestion":     c.CodeSuggestion,
		"report_high_level_feedback": c.HighLevelFeedback,
		"report_scan":                c.Scan,
		"report_unit_test":           c.UnitTest,
	}
}

type CodeReviewPathInstruction struct {
	PathSelectors []string `json:"pathSelectors" yaml:"pathSelectors"`
	Text          string   `json:"text" yaml:"text"`
}

func (c *CodeReviewPathInstruction) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	if len(c.PathSelectors) == 0 {
		return map[string]any{}
	}
	return map[string]any{
		"path_selectors": c.PathSelectors,
		"text":           c.Text,
	}
}

func (c *CodeReviewPathInstruction) Validate() error {
	if len(c.PathSelectors) == 0 {
		return comnerrors.New(codes.ErrInvalidParameterWithEmpty, "pathSelectors")
	}
	for i := range c.PathSelectors {
		c.PathSelectors[i] = util.CleanInvisibleChars(c.PathSelectors[i])
	}
	if c.Text == "" {
		return comnerrors.New(codes.ErrInvalidParameterWithEmpty, "text")
	}
	if len(c.Text) > 10240 {
		return comnerrors.New(codes.ErrInvalidParameterStringTooLong, "text", 10240)
	}
	return nil
}

type CodeReviewTriggerFilter struct {
	TitleKeywords     []string `json:"titleKeywords" yaml:"titleKeywords"`
	SourceBranchGlobs []string `json:"sourceBranchGlobs" yaml:"sourceBranchGlobs"`
	TargetBranchGlobs []string `json:"targetBranchGlobs" yaml:"targetBranchGlobs"`
	FileLimit         int      `json:"fileLimit" yaml:"fileLimit"`
	CommitLimit       int      `json:"commitLimit" yaml:"commitLimit"`
	LineLimit         int      `json:"lineLimit" yaml:"lineLimit"`
}

func (c *CodeReviewTriggerFilter) Validate() error {
	if c.FileLimit > 100 || c.FileLimit < 0 {
		return comnerrors.New(codes.ErrInvalidParameter, "fileLimit", c.FileLimit)
	}
	if c.CommitLimit > 100 || c.CommitLimit < 0 {
		return comnerrors.New(codes.ErrInvalidParameter, "commitLimit", c.CommitLimit)
	}
	if c.LineLimit > 10000 || c.LineLimit < 0 {
		return comnerrors.New(codes.ErrInvalidParameter, "lineLimit", c.LineLimit)
	}
	titleKeywordSet := mapset.NewThreadUnsafeSet[string]()
	for i, keyword := range c.TitleKeywords {
		if titleKeywordSet.Contains(keyword) {
			return comnerrors.New(codes.ErrInvalidParameterWithDetail, fmt.Sprintf("titleKeywords[%d]=%s", i, keyword), "duplicated")
		}
		titleKeywordSet.Add(keyword)
	}
	sourceBranchSet := mapset.NewThreadUnsafeSet[string]()
	for i, branch := range c.SourceBranchGlobs {
		if sourceBranchSet.Contains(branch) {
			return comnerrors.New(codes.ErrInvalidParameterWithDetail, fmt.Sprintf("sourceBranchGlobs[%d]=%s", i, branch), "duplicated")
		}
		if err := validateBranchGlob(branch); err != nil {
			return comnerrors.New(codes.ErrInvalidParameterWithDetail, fmt.Sprintf("sourceBranchGlobs[%d]=%s", i, branch), err.Error())
		}
		sourceBranchSet.Add(branch)
	}
	targetBranchSet := mapset.NewThreadUnsafeSet[string]()
	for i, branch := range c.TargetBranchGlobs {
		if targetBranchSet.Contains(branch) {
			return comnerrors.New(codes.ErrInvalidParameterWithDetail, fmt.Sprintf("targetBranchGlobs[%d]=%s", i, branch), "duplicated")
		}
		if err := validateBranchGlob(branch); err != nil {
			return comnerrors.New(codes.ErrInvalidParameterWithDetail, fmt.Sprintf("targetBranchGlobs[%d]=%s", i, branch), err.Error())
		}
		targetBranchSet.Add(branch)
	}
	return nil
}

func (c *CodeReviewTriggerFilter) MarshalJSON() ([]byte, error) {
	if c == nil {
		return []byte("{}"), nil
	}
	type Alias CodeReviewTriggerFilter
	temp := (*Alias)(c)
	if len(temp.SourceBranchGlobs) == 0 {
		temp.SourceBranchGlobs = make([]string, 0)
	}
	if len(temp.TargetBranchGlobs) == 0 {
		temp.TargetBranchGlobs = make([]string, 0)
	}
	if len(temp.TitleKeywords) == 0 {
		temp.TitleKeywords = make([]string, 0)
	}
	return json.Marshal((*Alias)(c))
}

func NewDefaultTriggerFilter() *CodeReviewTriggerFilter {
	return &CodeReviewTriggerFilter{
		FileLimit:         100,
		CommitLimit:       100,
		LineLimit:         10000,
		SourceBranchGlobs: make([]string, 0),
		TargetBranchGlobs: make([]string, 0),
		TitleKeywords:     make([]string, 0),
	}
}

type CodeReviewContentFilter struct {
	FileGlobs []string `json:"fileGlobs,omitempty" yaml:"fileGlobs"`
}

func NewCodeReviewContentFilter() *CodeReviewContentFilter {
	return &CodeReviewContentFilter{
		FileGlobs: make([]string, 0),
	}
}

func (c *CodeReviewContentFilter) Validate() error {
	for i := range c.FileGlobs {
		c.FileGlobs[i] = util.CleanInvisibleChars(c.FileGlobs[i])
	}
	return nil
}

func (c *CodeReviewContentFilter) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	if c.FileGlobs == nil {
		return map[string]any{}
	}
	return map[string]any{
		"filter_files": c.FileGlobs,
	}
}

type CodeReviewToolSetting struct {
	SonarQube    *SonarQubeToolSetting `json:"sonarQube" yaml:"sonarQube"`
	MvnTest      *MvnTest              `json:"mvnTest" yaml:"mvnTest"`
	Biome        *Biome                `json:"biome" yaml:"biome"`
	Eslint       *Eslint               `json:"eslint" yaml:"eslint"`
	GolangciLint *GolangciLint         `json:"golangciLint" yaml:"golangciLint"`
	Ruff         *Ruff                 `json:"ruff" yaml:"ruff"`
	ShellCheck   *ShellCheck           `json:"shellCheck" yaml:"shellCheck"`
	YamlLint     *YamlLint             `json:"yamlLint" yaml:"yamlLint"`
	GitLeaks     *GitLeaks             `json:"gitLeaks" yaml:"gitLeaks"`
}

func (c *CodeReviewToolSetting) Validate() error {
	if err := c.SonarQube.Validate(); err != nil {
		return err
	}
	return nil
}

func (c *CodeReviewToolSetting) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	fields := []CodeReviewTaskParameterConvertor{
		c.SonarQube,
		c.MvnTest,
		c.Biome,
		c.Eslint,
		c.GolangciLint,
		c.Ruff,
		c.ShellCheck,
		c.YamlLint,
		c.GitLeaks,
	}
	m := map[string]any{}
	for _, convert := range fields {
		loadCodeReviewTaskParameters(&m, convert)
	}
	return m
}

type SonarQubeToolSetting struct {
	Enable       bool   `json:"enable" yaml:"enable"`
	ProblemLevel string `json:"problemLevel,omitempty" yaml:"problemLevel"`
}

func (c *SonarQubeToolSetting) Validate() error {
	if c == nil {
		return nil
	}
	if c.ProblemLevel != "" {
		switch CodeReviewProblemLevel(c.ProblemLevel) {
		case Blocker, Critical, Major, Minor:
		default:
			return comnerrors.New(codes.ErrInvalidParameter, "problemLevel", c.ProblemLevel)
		}
	}
	return nil
}

func (c *SonarQubeToolSetting) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"sonar_qube_enable":        c.Enable,
		"sonar_qube_problem_level": c.ProblemLevel,
	}
}

type MvnTest struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func (c *MvnTest) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"mvn_test_enable": c.Enable,
	}
}

type Biome struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func (c *Biome) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"biome_enable": c.Enable,
	}
}

type Eslint struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func (c *Eslint) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"eslint_enable": c.Enable,
	}
}

type GolangciLint struct {
	Enable     bool   `json:"enable" yaml:"enable"`
	ConfigFile string `json:"configFile" yaml:"configFile"`
}

func (c *GolangciLint) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"golangci_lint_enable":      c.Enable,
		"golangci_lint_config_file": c.ConfigFile,
	}
}

type Ruff struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func (c *Ruff) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"ruff_enable": c.Enable,
	}
}

type ShellCheck struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func (c *ShellCheck) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"shell_check_enable": c.Enable,
	}
}

type YamlLint struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func (c *YamlLint) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"yaml_lint_enable": c.Enable,
	}
}

type GitLeaks struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func (c *GitLeaks) ToCodeReviewTaskParameters() map[string]any {
	if c == nil {
		return map[string]any{}
	}
	return map[string]any{
		"gitleaks_enable": c.Enable,
	}
}

type CodeReviewContextSetting struct {
	Issue *CodeReviewIssueSetting `json:"issue" yaml:"issue"`
}

func (c *CodeReviewContextSetting) Validate() error {
	return nil
}

type CodeReviewIssueSetting struct {
	Enable bool `json:"enable" yaml:"enable"`
}

func NewDefaultCodeReviewProperty() *CodeReviewProperty {
	return &CodeReviewProperty{
		OutputSetting:    NewDefaultOutputSetting(),
		PathInstructions: make([]CodeReviewPathInstruction, 0),
		TriggerFilter:    NewDefaultTriggerFilter(),
		ContentFilter:    NewCodeReviewContentFilter(),
		ToolSetting:      NewCodeReviewToolSetting(),
		ContextSetting:   NewDefaultContextSetting(),
	}
}

func NewDefaultOutputSetting() *CodeReviewOutputSetting {
	return &CodeReviewOutputSetting{
		Language:     Chinese,
		ProblemLevel: Minor,
		WalkThrough: &CodeReviewWalkThrough{
			Enable:     true,
			Collapse:   true,
			Summary:    true,
			Changes:    true,
			Diagram:    true,
			Statistics: false,
		},
		Report: &CodeReviewReport{
			Enable:            true,
			Collapse:          false,
			Summary:           true,
			CodeSuggestion:    true,
			HighLevelFeedback: true,
			Scan:              false,
			UnitTest:          false,
		},
	}
}

func NewCodeReviewToolSetting() *CodeReviewToolSetting {
	return &CodeReviewToolSetting{
		SonarQube:    &SonarQubeToolSetting{Enable: true, ProblemLevel: "MAJOR"},
		MvnTest:      &MvnTest{Enable: true},
		Biome:        &Biome{Enable: true},
		Eslint:       &Eslint{Enable: true},
		GolangciLint: &GolangciLint{Enable: true},
		Ruff:         &Ruff{Enable: true},
		ShellCheck:   &ShellCheck{Enable: true},
		YamlLint:     &YamlLint{Enable: true},
		GitLeaks:     &GitLeaks{Enable: true},
	}
}

func NewDefaultContextSetting() *CodeReviewContextSetting {
	return &CodeReviewContextSetting{
		Issue: NewDefaultIssueSetting(),
	}
}

func NewDefaultIssueSetting() *CodeReviewIssueSetting {
	return &CodeReviewIssueSetting{
		Enable: true,
	}
}

func validateBranchGlob(s string) error {
	if s == "" {
		return errors.New("glob pattern can not be empty")
	}
	if strings.Contains(s, " ") {
		return errors.New("glob pattern can not contain spaces")
	}
	_, err := filepath.Match(s, "")
	return err
}
