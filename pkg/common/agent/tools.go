package agent

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"

type CodeReviewTool struct {
	Name        string                   `json:"name"`
	<PERSON><PERSON>       string                   `json:"alias"`
	Version     string                   `json:"version"`
	Description string                   `json:"description"`
	Type        CodeReviewToolType       `json:"type"`
	Properties  []CodeReviewToolProperty `json:"properties"`
}

type CodeReviewToolType string

const (
	ToolTypeCodeQuality   CodeReviewToolType = "code-quality"
	ToolTypeConfigQuality CodeReviewToolType = "config-quality"
	ToolTypeSecurity      CodeReviewToolType = "security"
)

type CodeReviewToolProperty struct {
	Name        string `json:"name"`
	Alias       string `json:"alias"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Required    bool   `json:"required"`
	Enum        []any  `json:"enum,omitempty"`
}

type ListToolDescriptionsRequest struct {
	Language string `json:"language"`
	page.PagesParams
}

func (r *ListToolDescriptionsRequest) Validate() error {
	if r.Language == "" {
		r.Language = "zh-CN"
	}
	if err := r.PagesParams.Validate(); err != nil {
		return err
	}
	return nil
}
