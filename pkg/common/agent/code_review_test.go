package agent

import (
	"fmt"
	"github.com/stretchr/testify/assert"
	"gopkg.in/yaml.v3"
	"testing"
)

func TestRepositoryAgentRuleUnmarshal(t *testing.T) {
	yamlContent := `# 评审配置
reviewRules:
  # 启用代码评审
  enable: true
  # 输出设置
  outputSetting:
    # 评审后输出语言 zh-CN / en-US
    language: zh-CN
    # 问题等级设置为 BLOCKER/CRITICAL/MAJOR/MINOR
    problemLevel: BLOCKER
    
    walkThrough:
      enable: true
      collapse: true
      summary: true
      changes: true
      diagram: true
      statistics: true
    
    report:
      enable: true
      collapse: true
      summary: true
      codeSuggestion: true
      highLevelFeedback: true
      scan: true
      unitTest: true

  # 自定义指令
  pathInstructions:
    - pathSelectors:
        - "**/README.md"
      text: "请认真阅读README，注意是否有typo。"

  # 触发器过滤配置
  triggerFilter:
    # 标题关键词匹配
    titleKeywords:
      - dev
      - daily
    # 源分支匹配模式
    sourceBranchGlobs:
      - dev*
      - daily-*
    # 目标分支匹配模式
    targetBranchGlobs:
      - autotest-*
      - cicd-*
    # 文件数量限制，上限100
    fileLimit: 100
    # 提交数量限制，上限100
    commitLimit: 100
    # 代码行数限制，上限10000
    lineLimit: 10000

  # 内容过滤配置
  contentFilter:
    fileGlobs:
      - vendor/*
      - bin/*

  # 工具设置
  toolSetting:
    # SonarQube配置
    sonarQube:
      enable: true
      problemLevel: MAJOR
    
    # Maven测试配置
    mvnTest:
      enable: true
    
    # Biome配置
    biome:
      enable: true
    
    # ESLint配置
    eslint:
      enable: false
    
    # Golang CI Lint配置
    golangciLint:
      enable: true
      configFile: ""
    
    # Ruff配置
    ruff:
      enable: true
    
    # Shell检查配置
    shellCheck:
      enable: true
    
    # YAML检查配置
    yamlLint:
      enable: true
    
    # GitLeaks配置
    gitLeaks:
      enable: false

  # 上下文设置
  contextSetting:
    # issue集成
    issue:
      enable: true
`

	var agentRule RepositoryAgentRule
	if err := yaml.Unmarshal([]byte(yamlContent), &agentRule); err != nil {
		t.Error(err)
	}
	assert.True(t, agentRule.ReviewRules.Enable)
	assert.True(t, agentRule.ReviewRules.CodeReviewProperty != nil)
	assert.True(t, agentRule.ReviewRules.CodeReviewProperty.OutputSetting != nil)
}

func Test_validateBranchGlob(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "*.go",
			args: args{
				s: "*.go",
			},
			wantErr: func(t assert.TestingT, err error, i ...interface{}) bool {
				return assert.NoError(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.wantErr(t, validateBranchGlob(tt.args.s), fmt.Sprintf("validateBranchGlob(%v)", tt.args.s))
		})
	}
}
