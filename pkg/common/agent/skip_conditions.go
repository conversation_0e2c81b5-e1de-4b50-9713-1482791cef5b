package agent

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
)

type SkipConditions interface {
	Check(ctx base.Context, sessionId string) (bool, *SkipReason, error)
}
type SkipReason struct {
	Reason      string        `json:"reason"`
	Description string        `json:"description"`
	Details     []DetailGroup `json:"details"`
}

type SkipConditionType string

const (
	SkipConditionTypeSkipReview SkipConditionType = "SkipReview"
)

type SkipConditionsFactory func() SkipConditions

var skipConditionRegistry = make(map[SkipConditionType]SkipConditionsFactory)

func RegisterSkipConditionType(t SkipConditionType, factory SkipConditionsFactory) {
	skipConditionRegistry[t] = factory
}
