package agent

import (
	"encoding/json"
	"fmt"
	agentruntime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/agent-runtime"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
)

type Type string

const (
	// TypeCodeReview 表示代码审核Agent
	TypeCodeReview Type = "CodeReview"
)

func (t Type) Validate() error {
	switch t {
	case TypeCodeReview:
	default:
		return comnerrors.New(codes.ErrInvalidParameter, "agentType", t)
	}
	return nil
}

// LLMConfig is the configuration for the LLM agent
type LLMConfig struct {
}

// Agent 用户的一类Agent
type Agent struct {
}

type Class struct {
	AgentType Type
	ClassName string
}

type ListAgentOptions struct {
	AgentName AgentName `json:"agentName,omitempty"`
	page.PagesParams
	// UserId 不允许从外部注入
	// Deprecated
	UserId string `json:"-"`
}

type ListAgentTaskOptions struct {
	AgentId                 string `json:"agentId"`
	SessionId               string `json:"sessionId"`
	WithIdDesc              bool   `json:"withIdDesc"`
	States                  []TaskState
	WithTaskParameterValues map[string][]any
	page.PagesParams
}

type TaskState string

const (
	TaskStatePending   TaskState = "pending"
	TaskStateTriggered TaskState = "triggered"
	TaskStateFailed    TaskState = "failed"
)

type AgentProperty struct {
	// 所有智能体公共配置
	Enable        bool          `json:"enable"`
	SkipSandbox   bool          `json:"skipSandbox,omitempty"`
	ModelName     string        `json:"modelName,omitempty"`
	ModelProvider ModelProvider `json:"modelProvider,omitempty"`
	// 根据智能体类型，子配置
	CodeReview       *CodeReviewProperty `json:"codeReview"`
	GithubCodeReview *CodeReviewProperty `json:"githubCodeReview"`

	SkipConditions SkipConditions `json:"skipConditions,omitempty"`
}

type DetailGroup struct {
	Title string   `json:"title"`
	Items []string `json:"items"`
}

func (a *AgentProperty) Validate() error {
	if a.ModelProvider != "" {
		switch a.ModelProvider {
		case ModelProviderSystem:
		default:
			return comnerrors.New(codes.ErrInvalidParameter, "modelProvider", a.ModelProvider)
		}
	}

	if a.CodeReview != nil {
		if err := a.CodeReview.Validate(); err != nil {
			return err
		}
	}
	return nil
}

func (a *AgentProperty) LoadCodeReviewTaskParameters() map[string]any {
	if a == nil {
		return map[string]any{}
	}
	return a.CodeReview.ToCodeReviewTaskParameters()
}

func (a *AgentProperty) LoadGithubCodeReviewTaskParameters() map[string]any {
	if a == nil {
		return map[string]any{}
	}
	return a.GithubCodeReview.ToCodeReviewTaskParameters()
}

func (a *AgentProperty) UnmarshalJSON(data []byte) error {
	type Alias AgentProperty
	aux := struct {
		SkipConditionsRaw json.RawMessage `json:"skipConditions,omitempty"`
		*Alias
	}{
		Alias: (*Alias)(a),
	}
	// 解析标准字段
	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 如果 SkipConditions 不存在，直接返回
	if aux.SkipConditionsRaw == nil {
		return nil
	}

	// 解析 type 字段以确定具体类型
	var typeInfo struct {
		Type SkipConditionType `json:"type"`
	}
	if len(aux.SkipConditionsRaw) == 0 {
		return nil
	}
	if err := json.Unmarshal(aux.SkipConditionsRaw, &typeInfo); err != nil {
		return err
	}

	// 根据 type 字段从注册表中获取对应的工厂函数
	factory, exists := skipConditionRegistry[typeInfo.Type]
	if !exists {
		return fmt.Errorf("unknown SkipConditions type: %s", typeInfo.Type)
	}

	// 使用工厂函数创建实例
	instance := factory()

	// 反序列化到具体实例
	if err := json.Unmarshal(aux.SkipConditionsRaw, instance); err != nil {
		return err
	}

	a.SkipConditions = instance
	return nil
}

type ModelProvider string

const (
	ModelProviderSystem ModelProvider = "system"
)

type AgentInfo struct {
	AgentId         string         `json:"agentId"`
	AgentName       AgentName      `json:"agentName"`
	AgentProperty   *AgentProperty `json:"agentProperty,omitempty"`
	CreateTimestamp int64          `json:"createTimestamp"`
	UpdateTimestamp int64          `json:"updateTimestamp"`
}

func (ai *AgentInfo) Enabled() bool {
	return ai.AgentProperty != nil && ai.AgentProperty.Enable
}

func (ai *AgentInfo) GetCodeReviewProperty() *CodeReviewProperty {
	if ai.AgentProperty == nil {
		return nil
	}
	return ai.AgentProperty.CodeReview
}
func (ai *AgentInfo) GetGithubCodeReviewProperty() *CodeReviewProperty {
	if ai.AgentProperty == nil {
		return nil
	}
	return ai.AgentProperty.GithubCodeReview
}

type Snapshot struct {
	AgentId         string         `json:"agentId"`
	SessionId       string         `json:"sessionId"`
	Data            map[string]any `json:"data"`
	CreateTimestamp int64          `json:"createTimestamp"`
	UpdateTimestamp int64          `json:"updateTimestamp"`
}

type ListSnapshotOptions struct {
	SessionId string `json:"sessionId"`
	AgentId   string `json:"agentId"`
	Page      page.PagesParams
}

type AgentName string

const (
	CodeReviewAgent  AgentName = "CodeReviewAgent"
	AiDeveloperAgent AgentName = "AiDeveloperAgent"
)

func (an AgentName) ToChineseName() string {
	switch an {
	case CodeReviewAgent:
		return "代码评审智能体"
	}
	return string(an)
}

type CreateAgentRequest struct {
	AgentName     AgentName      `json:"agentName"`
	AgentProperty *AgentProperty `json:"agentProperty,omitempty"`
}

type UpdateAgentRequest struct {
	// 不允许外部反序列化注入，仅内部使用
	UserId        *string        `json:"-"`
	AgentProperty *AgentProperty `json:"agentProperty,omitempty"`
}

func (r *CreateAgentRequest) Validate() error {
	switch r.AgentName {
	case CodeReviewAgent:
	case AiDeveloperAgent:
	default:
		return comnerrors.New(codes.ErrInvalidParameter, "agentName", r.AgentName)
	}
	if r.AgentProperty != nil {
		if err := r.AgentProperty.Validate(); err != nil {
			return err
		}
	}
	return nil
}

type PatchAgentRequest struct {
	Enable           *bool               `json:"enable,omitempty"`
	CodeReview       *CodeReviewProperty `json:"codeReview,omitempty"`
	GithubCodeReview *CodeReviewProperty `json:"githubCodeReview,omitempty"`
}

func (r *PatchAgentRequest) Validate() error {
	if r.CodeReview != nil {
		if err := r.CodeReview.Validate(); err != nil {
			return err
		}
	}
	if r.GithubCodeReview != nil {
		if err := r.GithubCodeReview.Validate(); err != nil {
			return err
		}
	}
	return nil
}

type AgentTaskInfo struct {
	TaskId          string                   `json:"taskId"`
	AgentId         string                   `json:"agentId"`
	SessionId       string                   `json:"sessionId"`
	State           TaskState                `json:"state"`
	TaskConfig      *agentruntime.TaskConfig `json:"taskConfig"`
	CreateTimestamp int64                    `json:"createTimestamp"`
	UpdateTimestamp int64                    `json:"updateTimestamp"`
}
