package identity

import (
	"fmt"
	"github.com/pkg/errors"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/util"
	"net/url"
	"strings"
)

type IdentityInfo struct {
	IdentityId       string            `json:"identityId"`
	Source           Source            `json:"source"`
	PlatformEndpoint string            `json:"platformEndpoint"`
	PlatformUsername string            `json:"platformUsername"`
	PlatformToken    string            `json:"platformToken"`
	WebhookToken     string            `json:"webhookToken"`
	CreateTimestamp  int64             `json:"createTimestamp"`
	UserId           string            `json:"userId"`
	AgentId          string            `json:"agentId"`
	WebhookUrl       string            `json:"webhookUrl"`
	PlatformProperty *PlatformProperty `json:"platformProperty,omitempty"`
	Description      string            `json:"description"`
}

// GetSandboxEntrypoint 获取沙箱访问Git的入口地址，例如https://gitlab.com
func (ii *IdentityInfo) GetSandboxEntrypoint() (string, error) {
	if ii.PlatformProperty == nil {
		return ii.PlatformEndpoint, nil
	}
	sni := ii.PlatformProperty.Sni
	entrypoint := ii.PlatformProperty.SandboxEntrypoint
	if entrypoint == "" {
		// 没有单独的沙箱entrypoint，则和Agent使用相同入口
		entrypoint = ii.PlatformEndpoint
	}
	if sni != "" {
		// 如果配置了SNI，则需要将替换Host
		u, err := url.Parse(entrypoint)
		if err != nil {
			return "", errors.WithStack(err)
		}
		// 需要保留entrypoint的端口信息
		targetHost := sni
		if portStr := u.Port(); portStr != "" {
			targetHost = fmt.Sprintf("%s:%s", targetHost, portStr)
		}
		u.Host = targetHost
		return u.String(), nil
	} else {
		return entrypoint, nil
	}
}

func (ii *IdentityInfo) ToApiResponse() map[string]any {
	return map[string]any{
		"identityId":       ii.IdentityId,
		"source":           ii.Source,
		"agentId":          ii.AgentId,
		"platformEndpoint": ii.PlatformEndpoint,
		"platformToken":    util.MaskSecret(ii.PlatformToken),
		"userId":           ii.UserId,
		"webhookUrl":       ii.WebhookUrl,
		"webhookToken":     ii.WebhookToken,
		"createTimestamp":  ii.CreateTimestamp,
	}
}

type CreateIdentityRequest struct {
	AgentId          string            `json:"agentId"`
	Source           Source            `json:"source"`
	Description      *string           `json:"description"`
	PlatformEndpoint string            `json:"platformEndpoint"`
	PlatformToken    string            `json:"platformToken"`
	PlatformProperty *PlatformProperty `json:"platformProperty,omitempty"`
}

func (r *CreateIdentityRequest) Validate() error {
	if err := r.Source.Validate(); err != nil {
		return err
	}
	if r.Description != nil && len(*r.Description) > 1024 {
		return commonerrors.New(codes.ErrInvalidParameterWithDetail, "description", r.Description, "length should be less than 1024")
	}

	if r.AgentId == "" {
		return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "agentId")
	}

	// 清理前后空格
	r.PlatformToken = strings.TrimSpace(r.PlatformToken)
	switch r.Source {
	case SourceAgentConnect:
	// do nothing
	case SourceGitLab, SourceCodeaone:
		if r.PlatformEndpoint == "" {
			return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "platformEndpoint")
		}
		if r.PlatformToken == "" {
			return commonerrors.New(codes.ErrInvalidParameterWithEmpty, "platformToken")
		}
	default:
		return commonerrors.New(codes.ErrInvalidParameter, "source", r.Source)
	}

	if r.PlatformProperty != nil {
		if err := r.PlatformProperty.Validate(); err != nil {
			return errors.WithStack(err)
		}
	}
	return nil
}

type UpdateIdentityRequest struct {
	PlatformToken    *string
	SyncAssociated   *bool
	Description      *string
	PlatformUsername *string
	UserId           *string
}

type Source string

const (
	SourceGitLab       Source = "gitlab"
	SourceCodeup       Source = "codeup"
	SourceCodeaone     Source = "codeaone"
	SourceGithub       Source = "github"
	SourceAgentConnect Source = "agent-connect"
	SourceAiDeveloper  Source = "ai-developer"
)

func (s Source) Validate() error {
	switch s {
	case SourceGitLab, SourceCodeup, SourceGithub, SourceAgentConnect, SourceAiDeveloper, SourceCodeaone:
		return nil
	default:
		return commonerrors.New(codes.ErrInvalidParameter, "source", s)
	}
}

type ListIdentityOptions struct {
	AgentId     string
	SourceTypes []string
	page.PagesParams
}

type PlatformProperty struct {
	Sni                  string `json:"sni,omitempty"`
	SandboxEntrypoint    string `json:"sandboxEntrypoint,omitempty"`
	GithubAppId          int64  `json:"githubAppId,omitempty"`
	GithubInstallationId int64  `json:"githubInstallationId,omitempty"`
	GithubUsername       string `json:"githubUsername,omitempty"`
	GithubUserId         int    `json:"githubUserId,omitempty"`
	GithubAppSlug        string `json:"githubAppSlug,omitempty"`
	GithubAccountType    string `json:"githubAccountType,omitempty"`
	CodeupOrganizationId string `json:"codeupOrganizationId,omitempty"`
	CodeupAppId          string `json:"codeupAppId,omitempty"`
}

func (p *PlatformProperty) Validate() error {
	if p == nil {
		return nil
	}
	if p.SandboxEntrypoint != "" {
		_, err := url.Parse(p.SandboxEntrypoint)
		if err != nil {
			return commonerrors.New(codes.ErrInvalidParameterWithDetail, "sandboxEntrypoint", p.SandboxEntrypoint, err.Error())
		}
	}
	return nil
}
