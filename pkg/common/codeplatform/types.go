package codeplatform

import (
	"encoding/base64"
	mapset "github.com/deckarep/golang-set/v2"
	"strings"
	"time"
)

type MergeRequest struct {
	// ID 是合并请求的唯一标识符。
	ID string `json:"id"`
	// ProjectId 是项目在仓库中的唯一标识符。
	ProjectId string `json:"project_id"`
	// TargetBranch 是合并请求的目标分支名称。
	TargetBranch string `json:"target_branch"`
	// SourceBranch 是合并请求的源分支名称。
	SourceBranch string `json:"source_branch"` // Deprecated 请使用 SourceRef
	// SourceRef 是合并请求的源引用，指来源分支或者commit。
	SourceRef string `json:"source_ref"`
	// Title 是合并请求的标题。
	Title string `json:"title"`
	// State 表示合并请求的状态。
	State string `json:"state"`
	// CreatedAt 记录合并请求的创建时间。
	CreatedAt *time.Time `json:"created_at"`
	// UpdatedAt 记录合并请求的最后更新时间。
	UpdatedAt *time.Time `json:"updated_at"`
	// SourceProjectID 是源项目的标识符。
	SourceProjectID int `json:"source_project_id"`
	// TargetProjectID 是目标项目的标识符。
	TargetProjectID int `json:"target_project_id"`
	// Description 包含合并请求的详细描述。
	Description string `json:"description"`
	// WebURL 是合并请求在网页上的地址。
	WebURL string `json:"web_url"`
	// SHA 是合并请求中最新提交的SHA哈希值。
	SHA string `json:"sha"`
	// MergeCommitSHA 是合并提交的SHA哈希值。
	MergeCommitSHA string `json:"merge_commit_sha"`
	// SquashCommitSHA 是将所有提交压缩为一个提交后的SHA哈希值。
	SquashCommitSHA string `json:"squash_commit_sha"`
	// Changes 包含合并请求的所有更改细节。
	Changes []*MergeRequestDiff `json:"changes"`
	// AuthorName 是合并请求作者的名称。
	AuthorName string `json:"author_name"`

	// DiffRefs 包含合并请求的差异引用信息。
	DiffRefs *DiffRefs `json:"diff_refs,omitempty"`

	// CommitRanges 包含合并请求的提交范围信息。
	CommitRanges []*Commit `json:"commit_ranges,omitempty"`
}

type DiffRefs struct {
	BaseSHA string `json:"base_sha"`
	HeadSHA string `json:"head_sha"`
}

func (mr *MergeRequest) GetDiffLines() int {
	lines := 0
	for _, change := range mr.Changes {
		for _, line := range strings.Split(change.Diff, "\n") {
			// 统计以 '+' 或 '-' 开头的行（排除文件信息行）
			if strings.HasPrefix(line, "+++ ") || strings.HasPrefix(line, "--- ") {
				// 云效代码片段头会有如下标识
				//--- a/tt.js
				//+++ b/tt.js
				//@@ -1 +1,3 @@
				continue
			}
			if strings.HasPrefix(line, "+") || strings.HasPrefix(line, "-") {
				lines++
			}
		}
	}
	return lines
}

func (mr *MergeRequest) GetFileCount() int {
	s := mapset.NewThreadUnsafeSet[string]()
	for _, ch := range mr.Changes {
		s.Add(ch.NewPath)
	}
	return len(s.ToSlice())
}

type MergeRequestDiff struct {
	OldPath     string `json:"old_path"`
	NewPath     string `json:"new_path"`
	AMode       string `json:"a_mode"`
	BMode       string `json:"b_mode"`
	Diff        string `json:"diff"`
	NewFile     bool   `json:"new_file"`
	RenamedFile bool   `json:"renamed_file"`
	DeletedFile bool   `json:"deleted_file"`
}

type CreateMergeRequestCommentOpts struct {
	Content string `json:"content" ` // 评论内容
	//Type             *string // 评论类型："Comment" 或 "DiffComment"
	Path             *string `json:"path"`                // 要评论的文件路径
	StartLine        *int    `json:"start_line"`          // 评论开始行号
	EndLine          *int    `json:"end_line"`            // 评论结束行号
	LineType         *string `json:"line_type"`           // 行类型："new" 或 "old"
	ReplyToCommentID *string `json:"reply_to_comment_id"` // 回复的讨论ID
	Resolved         *bool   `json:"resolved"`            // 问题已解决
	Draft            *bool   `json:"draft"`               // 草稿评论
	CommitId         *string `json:"commit_id"`           // 该评论基于哪个commitId进行，属于统计使用，非代码平台必须
}

type Comment struct {
	ID              string `json:"id,omitempty"`
	CommitId        string `json:"commitId,omitempty"`
	Content         string `json:"content,omitempty"`
	Username        string `json:"username,omitempty"`
	CreateTimestamp int64  `json:"createTimestamp"`
}

type ListMergeRequestCommentsRequest struct {
	CommentId string
}

type Commit struct {
	Sha              string            `json:"sha,omitempty"`
	Title            string            `json:"title,omitempty"`
	AuthorName       string            `json:"author_name,omitempty"`
	AuthorEmail      string            `json:"author_email,omitempty"`
	AuthoredDate     *time.Time        `json:"authored_date,omitempty"`
	CommitterName    string            `json:"committer_name,omitempty"`
	CommitterEmail   string            `json:"committer_email,omitempty"`
	CommittedDate    *time.Time        `json:"committed_date,omitempty"`
	CreatedAt        *time.Time        `json:"created_at,omitempty"`
	Message          string            `json:"message,omitempty"`
	ParentIDs        []string          `json:"parent_ids,omitempty"`
	ProjectID        int               `json:"project_id,omitempty"`
	Trailers         map[string]string `json:"trailers,omitempty"`
	ExtendedTrailers map[string]string `json:"extended_trailers,omitempty"`
	URL              string            `json:"url,omitempty"`
}

type ListMergeRequestCommentsResponse struct {
	Comments     []*Comment    `json:"comments"`
	Type         CommentType   `json:"type"`
	CodePosition *CodePosition `json:"code_position,omitempty"`
}

type CommentType string

const (
	CommentTypeCommon         CommentType = "Common"
	CommentTypeDiscussionNote CommentType = "DiscussionNote"
)

type CodePosition struct {
	Path      *string `json:"path"`       // 要评论的文件路径
	StartLine *int    `json:"start_line"` // 评论开始行号
	EndLine   *int    `json:"end_line"`   // 评论结束行号
	LineType  *string `json:"line_type"`  // 行类型："new" 或 "old"
}

type ResolveMergeRequestCommentOpts struct {
	CommentId string `json:"comment_id"`
	Resolved  bool   `json:"resolved"`
}

type UpdateMergeRequestCommentOpts struct {
	CommentId string  `json:"comment_id"`
	Resolved  *bool   `json:"resolved"`
	Content   *string `json:"content"`
}

type DeleteMergeRequestCommentOpts struct {
	CommentId string `json:"comment_id"`
}

type LineRange struct {
	Start *int `json:"start"`
	End   *int `json:"end"`
}

type GetRepositoryFileOpts struct {
	Ref       *string
	Path      string
	LineRange *LineRange
}

type RepositoryFile struct {
	FileName        string `json:"file_name"`
	FilePath        string `json:"file_path"`
	Size            int    `json:"size"`
	Encoding        string `json:"encoding"`
	Content         string `json:"content"`
	ExecuteFileMode bool   `json:"execute_file_mode"`
	Ref             string `json:"ref"`
	BlobID          string `json:"blob_id"`
	CommitID        string `json:"commit_id"`
	SHA256          string `json:"content_sha256"`
	LastCommitID    string `json:"last_commit_id"`
}

func (rf *RepositoryFile) GetContent() string {
	if rf.Encoding == "base64" {
		content, err := base64.StdEncoding.DecodeString(rf.Content)
		if err != nil {
			return ""
		}
		return string(content)
	}
	return rf.Content
}

type ListMergeRequestAllCommentsRequest struct {
}

type Discussion struct {
	Comments     []*Comment    `json:"comments"`
	Type         CommentType   `json:"type"`
	Resolved     bool          `json:"resolved"`
	CodePosition *CodePosition `json:"code_position,omitempty"`
}

type ListAllMergeRequestCommitDiffsRequest struct {
}

type CommitDiff struct {
	CommitId    string `json:"commit_id"`
	Diff        string `json:"diff"`
	NewPath     string `json:"new_path"`
	OldPath     string `json:"old_path"`
	NewFile     bool   `json:"new_file"`
	RenamedFile bool   `json:"renamed_file"`
	DeletedFile bool   `json:"deleted_file"`
}

type CreateReviewOpts struct {
	Comments []ReviewComment `json:"comments,omitempty"`  // 可选，评审评论列表
	CommitId string          `json:"commit_id,omitempty"` // 可选，评审基于哪个commitId进行，属于统计使用，非代码平台必须
}

type CreateReviewResponse struct {
	ReviewId *string    `json:"review_id,omitempty"`
	Comments []*Comment `json:"comments"`
}

type ReviewComment struct {
	Position *CodePosition `json:"position"`
	Content  string        `json:"content"` // 评论内容
}

type UpdateReviewOpts struct {
	ReviewId string `json:"review_id"`
	Content  string `json:"content"`
}

type CreateCheckRunOpts struct {
	Name   string  `json:"name"`
	Status *string `json:"status"`
	Output *string `json:"output"`
}

type CreateCheckRunResponse struct {
	Id     string  `json:"id"`
	Status *string `json:"status"`
}

type UpdateCheckRunOpts struct {
	Id         string  `json:"id"`
	Name       string  `json:"name"`
	Output     *string `json:"output"`
	DetailUrl  *string `json:"detail_url"`
	Status     *string `json:"status"`
	Conclusion *string `json:"conclusion"`
}

type FeedbackType string

const (
	FeedbackTypeHelpful    FeedbackType = "helpful"    // 点赞
	FeedbackTypeNeutral    FeedbackType = "neutral"    // 无用
	FeedbackTypeMisleading FeedbackType = "misleading" // 点踩
)

// CreateFeedbackOpts 数据结构
type CreateFeedbackOpts struct {
	SessionId    string       `json:"session_id"`
	SuggestionId string       `json:"suggestion_id"` // ar 里的原始 suggestion id，记录下来方便在 ar 追溯日志
	FeedbackType FeedbackType `json:"feedback_type"`
}

type UpdateFeedbackOpts struct {
	SessionId       string  `json:"session_id"`
	SuggestionId    string  `json:"suggestion_id"`
	FeedbackContent *string `json:"feedback_content"`
}

// CompareCommitsOpts 数据结构, 支持支持两个CommitId间的比较
type CompareCommitsOpts struct {
	Head        string `json:"head"`
	Base        string `json:"base"`
	WithCommits bool   `json:"with_commits"`
}

type CompareCommitResult struct {
	CommitDiffs []*MergeRequestDiff `json:"commit_diffs,omitempty"`
}

type IncrementalMergeRequest struct {
	// CommitDiffs 包含增量合并请求的提交差异信息。
	CommitDiffs []*MergeRequestDiff `json:"commit_diffs,omitempty"`

	// CommitRanges 包含合并请求的提交范围信息。
	CommitRanges []*Commit `json:"commit_ranges,omitempty"`
}
