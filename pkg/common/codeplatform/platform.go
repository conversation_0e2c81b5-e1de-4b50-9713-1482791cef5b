package codeplatform

import (
	"fmt"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
)

// Platform 代码托管平台类型
type Platform string

const (
	PlatformGitlab       Platform = "gitlab"
	PlatformGithub       Platform = "github"
	PlatformAgentConnect Platform = "agent-connect"
	PlatformCodeup       Platform = "codeup"
	PlatformCodeaone     Platform = "codeaone"
)

func (p Platform) String() string {
	return string(p)
}

func (p Platform) IsValid() bool {
	switch p {
	case PlatformGitlab:
		return true
	default:
		return false
	}
}

func AllPlatforms() []Platform {
	return []Platform{
		PlatformGitlab,
	}
}

func PlatformFromString(s string) (Platform, error) {
	p := Platform(s)
	if !p.IsValid() {
		return "", fmt.Errorf("invalid platform: %s", s)
	}
	return p, nil
}

// GetMergeRequestOptions 定义了获取合并请求时的选项。
// 该结构体可以配置是否一起获取合并请求相关的提交信息。
type GetMergeRequestOptions struct {
	// WithCommits 指示是否包括合并请求的所有提交信息。
	// 设置为true时，将获取合并请求的详细提交记录。
	// 设置为false时，仅获取合并请求的基本信息，不包括提交记录。
	WithCommits bool
}

// GetIncrementalMergeRequestOptions 定义了获取增量合并请求的参数结构体
// 用于指定比较的提交范围和返回数据的详细程度
type GetIncrementalMergeRequestOptions struct {
	// BaseSha 表示基准分支的提交哈希值
	// 通常是目标分支（如 master）的最新提交，用于作为比较的起始点
	BaseSha string

	// HeadSha 表示特性分支的最新提交哈希值
	// 通常是要合并的开发分支的最新提交，用于作为比较的终点
	HeadSha string

	// WithCommits 表示是否在响应中包含具体的提交记录信息
	// 当设置为 true 时，返回结果会包括从 BaseSha 到 HeadSha 的所有提交信息
	// 当设置为 false 时，仅返回基础的差异信息
	WithCommits bool
}

type Interface interface {
	// GetMergeRequest 获取合并请求的详细信息
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - opts: 获取合并请求的选项，包含请求所需的参数
	// 返回值:
	//   - *MergeRequest: 合并请求的详细信息
	//   - error: 如果获取过程中发生错误，则返回错误信息
	GetMergeRequest(ctx base.Context, sessionId string, opts *GetMergeRequestOptions) (*MergeRequest, error)

	// GetIncrementalMergeRequest 获取增量合并请求的详细信息
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - opts: 获取增量合并请求的选项，包含请求所需的参数
	// 返回值:
	//   - *IncrementalMergeRequest: 增量合并请求的详细信息
	//   - error: 如果获取过程中发生错误，则返回错误信息
	GetIncrementalMergeRequest(ctx base.Context, sessionId string, opts *GetIncrementalMergeRequestOptions) (*IncrementalMergeRequest, error)

	// CreateMergeRequestComment 创建合并请求的评论
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - opts: 创建评论的选项，包含评论内容和相关参数
	// 返回值:
	//   - *Comment: 创建的评论对象
	//   - error: 如果创建过程中发生错误，则返回错误信息
	CreateMergeRequestComment(ctx base.Context, sessionId string, opts *CreateMergeRequestCommentOpts) (*Comment, error)

	// ListMergeRequestComments 列出合并请求的评论列表
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - req: 列出评论的请求参数，包含分页和过滤条件
	// 返回值:
	//   - *ListMergeRequestCommentsResponse: 评论列表的响应对象
	//   - error: 如果列出过程中发生错误，则返回错误信息
	ListMergeRequestComments(ctx base.Context, sessionId string, req *ListMergeRequestCommentsRequest) (*ListMergeRequestCommentsResponse, error)

	// ResolveMergeRequestComment 解决合并请求的评论
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - opts: 解决评论的选项，包含评论ID和解决状态
	// 返回值:
	//   - error: 如果解决过程中发生错误，则返回错误信息
	ResolveMergeRequestComment(ctx base.Context, sessionId string, opts *ResolveMergeRequestCommentOpts) error

	// UpdateMergeRequestComment 更新合并请求的评论
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - opts: 更新评论的选项，包含评论ID和更新内容
	// 返回值:
	//   - *Comment: 更新后的评论对象
	//   - error: 如果更新过程中发生错误，则返回错误信息
	UpdateMergeRequestComment(ctx base.Context, sessionId string, opts *UpdateMergeRequestCommentOpts) (*Comment, error)

	// DeleteMergeRequestComment 删除合并请求的评论
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - opts: 删除评论的选项，包含评论ID
	// 返回值:
	//   - error: 如果删除过程中发生错误，则返回错误信息
	DeleteMergeRequestComment(ctx base.Context, sessionId string, opts *DeleteMergeRequestCommentOpts) error

	// ListMergeRequestAllComments 列出合并请求的所有评论（包括讨论）
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - req: 列出所有评论的请求参数，包含分页和过滤条件
	// 返回值:
	//   - []*Discussion: 讨论列表，包含所有评论和回复
	//   - error: 如果列出过程中发生错误，则返回错误信息
	ListMergeRequestAllComments(ctx base.Context, sessionId string, req *ListMergeRequestAllCommentsRequest) ([]*Discussion, error)

	// ListAllMergeRequestCommitDiffs 列出合并请求的所有提交差异
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	//   - req: 列出提交差异的请求参数，包含分页和过滤条件
	// 返回值:
	//   - []*CommitDiff: 提交差异列表
	//   - error: 如果列出过程中发生错误，则返回错误信息
	ListAllMergeRequestCommitDiffs(ctx base.Context, sessionId string, req *ListAllMergeRequestCommitDiffsRequest) ([]*CommitDiff, error)

	// ListMergeRequestCommits 列出合并请求的所有提交
	// 参数:
	//   - ctx: 基础上下文，用于请求的取消和超时控制
	//   - sessionId: 会话ID，用于标识当前用户会话
	// 返回值:
	//   - []*Commit: 提交列表
	//   - error: 如果列出过程中发生错误，则返回错误信息
	ListMergeRequestCommits(ctx base.Context, sessionId string) ([]*Commit, error)

	// CompareCommits 比较两个提交之间的差异。
	CompareCommits(ctx base.Context, sessionId string, opts *CompareCommitsOpts) (*CompareCommitResult, error)

	// CreateReview 创建评审，一次性发送所有文件级别的代码建议。
	// 在 GitHub 的实现中，直接使用了其内置的 "review" 概念，将所有评论作为 review comment 包含在一个 review 中。
	// 在 GitLab 的实现中，则使用了 "draftNote" 的概念，先创建多条 draftNote，然后一次性发布，模拟单次 review 的效果。
	CreateReview(ctx base.Context, sessionId string, opts *CreateReviewOpts) (*CreateReviewResponse, error)

	// UpdateReview 更新 review 内容。
	// 此接口仅在 GitHub 中支持， Github Review 可以更新内容显示在 Thread 开头处，Agent会把最终的 overall report 更新到 Review 中。
	UpdateReview(ctx base.Context, sessionId string, opts *UpdateReviewOpts) error

	CreateCheckRun(ctx base.Context, sessionId string, opts *CreateCheckRunOpts) (*CreateCheckRunResponse, error)
	UpdateCheckRun(ctx base.Context, sessionId string, opts *UpdateCheckRunOpts) error

	GetRepositoryFile(ctx base.Context, sessionId string, opts *GetRepositoryFileOpts) (*RepositoryFile, error)

	GetVersion(ctx base.Context, sessionId string) (string, error)
}

type FeedbackService interface {
	Feedback(ctx base.Context, opts *CreateFeedbackOpts) error

	UpdateFeedback(ctx base.Context, opts *UpdateFeedbackOpts) error
}
