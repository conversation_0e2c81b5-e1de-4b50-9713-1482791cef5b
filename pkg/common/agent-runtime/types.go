package agent_runtime

import (
	"github.com/spf13/cast"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
)

type SandboxConfig struct {
	Endpoint string `json:"endpoint,omitempty"`
}

type LlmConfig struct {
	BaseUrl   string `json:"base_url,omitempty"`
	ModelName string `json:"model_name,omitempty"`
	ApiKey    string `json:"api_key,omitempty"`
}

type TaskConfig struct {
	Parameters map[string]any `json:"parameters,omitempty"`
}

func (tc *TaskConfig) GetCodeReviewHeadCommitId() string {
	if tc.Parameters == nil {
		return ""
	}
	return cast.ToString(tc.Parameters["head_commit_id"])
}

func (tc *TaskConfig) GetCodeReviewTaskType() string {
	if tc.Parameters == nil {
		return ""
	}
	event := cast.ToString(tc.Parameters["event"])
	switch event {
	case "MERGE_REQUEST", "MERGE_REQUEST_INCREMENTAL_REVIEW":
		return "MERGE_REQUEST"
	case "MERGE_REQUEST_COMMENT":
		return "MERGE_REQUEST_COMMENT"
	}
	return event
}

type StepSessionRequest struct {
	TaskConfig    *TaskConfig    `json:"task_config,omitempty"`
	AgentName     string         `json:"agent_name,omitempty"`
	AgentId       string         `json:"agent_id,omitempty"`
	SandboxConfig *SandboxConfig `json:"sandbox_config,omitempty"`
	LLMConfig     *LlmConfig     `json:"llm_config,omitempty"`
}

func (r *StepSessionRequest) SetCodeReviewFetchCode(fetchCode *bool) {
	if fetchCode == nil {
		return
	}
	if r.TaskConfig == nil {
		r.TaskConfig = &TaskConfig{Parameters: map[string]any{}}
	}
	if r.TaskConfig.Parameters == nil {
		r.TaskConfig.Parameters = make(map[string]any)
	}
	r.TaskConfig.Parameters["enable_fetch_code"] = *fetchCode
}

func (r *StepSessionRequest) SetSandboxInitFailed() {
	r.SandboxConfig = nil
	if r.TaskConfig == nil {
		r.TaskConfig = &TaskConfig{Parameters: map[string]any{}}
	}
	if r.TaskConfig.Parameters == nil {
		r.TaskConfig.Parameters = make(map[string]any)
	}
	r.TaskConfig.Parameters["enable_fetch_code"] = false
	r.TaskConfig.Parameters["sandbox_init_failure"] = true
}

type StepSessionResponse struct {
	TraceId string `json:"trace_id"`
	Data    struct {
		SessionId string `json:"session_id"`
		AgentId   string `json:"agent_id"`
		AbortUrl  string `json:"abort_url"`
	} `json:"data"`
}

type AbortSessionRequest struct {
	// AgentId 需要中断的AgentId，若为空，则中断当前Session下所有Agent，目前设计中AgentRuntime上不会同时出现一个Session下多个AgentTask，在AgentController侧排队处理
	AgentId string `json:"agent_id,omitempty"`
}

type AbortSessionCallbackFn func(ctx base.Context, sessionId string) error

type AbortSessionOptions struct {
	MatchAgentName      string
	MatchTaskParameters map[string]any
	// MatchTaskParametersAnyValue 支持按照key匹配 []value 中任意一个
	MatchTaskParametersAnyValue map[string][]any
	// CallbackOnSuccessFns 真正执行了Abort调用后，回调函数。 如果需要扩展到其他执行点，请自行重构设计。
	CallbackOnSuccessFns []AbortSessionCallbackFn
}

type AbortSessionResponse struct {
	TraceId string `json:"trace_id"`
}
