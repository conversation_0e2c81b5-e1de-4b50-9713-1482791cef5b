package dao

import (
	"context"
	"fmt"
	daojson "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao/json"
	"gorm.io/gorm/schema"
	"reflect"
)

func init() {
	schema.RegisterSerializer("json_aes_field_serializer", &JsonAesFieldSerializer{serializer: &daojson.CustomJSONSerializer{}})
}

// JsonAesFieldSerializer 支持对结构体中带有标签encrypt的string或[]string字段进行加密解密，其中aes表示使用aes进行加密解密
// 示例
//
//	type myStruct struct {
//		Password     string   `json:"password" encrypt:"aes"`
//		PasswordList []string `json:"password_list" encrypt:"aes"`
//		Name         string   `json:"name"`
//	}
//
//	type myEntity struct {
//		StructA *myStruct `gorm:"column:struct_a;serializer:json_aes_field_serializer"`
//	}
type JsonAesFieldSerializer struct {
	serializer *daojson.CustomJSONSerializer
}

func (s JsonAesFieldSerializer) Scan(ctx context.Context, field *schema.Field, dst reflect.Value, dbValue any) (err error) {
	fieldValue := reflect.New(field.FieldType)

	if dbValue != nil {
		var bytes []byte
		switch v := dbValue.(type) {
		case []byte:
			bytes = v
		case string:
			bytes = []byte(v)
		default:
			return fmt.Errorf("failed to unmarshal JSONB value: %#v", dbValue)
		}

		if len(bytes) > 0 {
			err = s.serializer.Unmarshal(bytes, fieldValue.Interface())
		}
	}

	field.ReflectValueOf(ctx, dst).Set(fieldValue.Elem())
	return
}

func (s JsonAesFieldSerializer) Value(ctx context.Context, field *schema.Field, dst reflect.Value, fieldValue any) (any, error) {
	result, err := s.serializer.Marshal(fieldValue)
	if string(result) == "null" {
		if field.TagSettings["NOT NULL"] != "" {
			return "", nil
		}
		return nil, err
	}
	return string(result), err
}
