package dao

import (
	"fmt"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gorm.io/gorm"
	"time"
)

type DbWhereOption func(tx *gorm.DB) (*gorm.DB, error)

type DbPageOption func(tx *gorm.DB) (*gorm.DB, error)

type DbAction func(ctx base.Context, tx *gorm.DB) error

type DbOptions struct {
	Wheres []DbWhereOption
	Pages  []DbPageOption
}

func NewDbOptions(opts ...DbWhereOption) *DbOptions {
	return &DbOptions{
		Wheres: opts,
	}
}

func (o *DbOptions) AddWhere(opts ...DbWhereOption) {
	o.Wheres = append(o.Wheres, opts...)
}

func (o *DbOptions) AddPage(opts ...DbPageOption) {
	o.Pages = append(o.Pages, opts...)
}

func WithDistinctFields(fieldNames ...string) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(fieldNames) == 0 {
			return nil, errors.New("empty field name")
		}
		return tx.Distinct(fieldNames), nil
	}
}

func WithFieldNotNull(fieldName string) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(fieldName) == 0 {
			return nil, errors.New("empty field name")
		}
		return tx.Where(fmt.Sprintf(`%s IS NOT NULL AND %s != ""`, fieldName, fieldName)), nil
	}
}

func WithFieldNull(fieldName string) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(fieldName) == 0 {
			return nil, errors.New("empty field name")
		}
		return tx.Where(fmt.Sprintf(`%s IS NULL`, fieldName)), nil
	}
}

func WithSelect(selectContent string) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(selectContent) == 0 {
			return nil, errors.New("empty select content")
		}
		return tx.Select(selectContent), nil
	}
}

func WithGroup(fieldName string) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(fieldName) == 0 {
			return nil, errors.New("empty fieldName")
		}
		return tx.Group(fieldName), nil
	}
}

func WithPage(pageNumber, pageSize int) DbPageOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		return tx.Offset((pageNumber - 1) * pageSize).Limit(pageSize), nil
	}
}

func withOrder(order string) DbPageOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		return tx.Order(order), nil
	}
}

func WithFieldOrderDesc(fieldName string) DbPageOption {
	return withOrder(fmt.Sprintf("%s DESC", fieldName))
}

func WithFieldOrderAsc(fieldName string) DbPageOption {
	return withOrder(fmt.Sprintf("%s ASC", fieldName))
}

func WithUserId(uid string) DbWhereOption {
	return WithKV("user_id", uid)
}

func WithResourceGroupId(rgId string) DbWhereOption {
	return WithKV("resource_group_id", rgId)
}

func WithKV(key string, value any) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(key) == 0 {
			return nil, errors.New("empty key")
		}
		return tx.Where(fmt.Sprintf("%s = ?", key), value), nil
	}
}

func WithKeyNotEqualValue(key string, value any) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(key) == 0 {
			return nil, errors.New("empty key")
		}
		return tx.Where(fmt.Sprintf("%s <> ?", key), value), nil
	}
}

func WithKeyValues[T any](key string, values ...T) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(values) == 0 {
			return nil, errors.New("empty values")
		}
		if len(key) == 0 {
			return nil, errors.New("empty key")
		}
		return tx.Where(fmt.Sprintf("%s IN ?", key), values), nil
	}
}

func WithKeyNotInValues[T any](key string, values ...T) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(values) == 0 {
			return nil, errors.New("empty values")
		}
		if len(key) == 0 {
			return nil, errors.New("empty key")
		}
		return tx.Where(fmt.Sprintf("%s NOT IN ?", key), values), nil
	}
}

func WithLike(fieldName string, value string) DbWhereOption {
	return func(db *gorm.DB) (*gorm.DB, error) {
		if len(value) == 0 {
			return nil, errors.New("empty value")
		}
		if len(fieldName) == 0 {
			return nil, errors.New("empty filedName")
		}
		return db.Where(fmt.Sprintf("%s LIKE ?", fieldName), fmt.Sprintf("%%%s%%", value)), nil
	}
}

func WithLeftLike(fieldName string, value string) DbWhereOption {
	return func(db *gorm.DB) (*gorm.DB, error) {
		if len(value) == 0 {
			return nil, errors.New("empty value")
		}
		if len(fieldName) == 0 {
			return nil, errors.New("empty filedName")
		}
		return db.Where(fmt.Sprintf("%s LIKE ?", fieldName), fmt.Sprintf("%%%s", value)), nil
	}
}

func WithRightLike(fieldName string, value string) DbWhereOption {
	return func(db *gorm.DB) (*gorm.DB, error) {
		if len(value) == 0 {
			return nil, errors.New("empty value")
		}
		if len(fieldName) == 0 {
			return nil, errors.New("empty filedName")
		}
		return db.Where(fmt.Sprintf("%s LIKE ?", fieldName), fmt.Sprintf("%s%%", value)), nil
	}
}

func WithGreaterThan(key string, value any) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(key) == 0 {
			return nil, errors.New("empty key")
		}
		return tx.Where(fmt.Sprintf("%s > ?", key), value), nil
	}
}

func WithGmtCreateAfter(t time.Time) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if t.IsZero() {
			return nil, errors.New("empty time")
		}
		return tx.Where(fmt.Sprintf("%s > ?", "gmt_create"), t), nil
	}
}

func WithGmtCreateBefore(t time.Time) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if t.IsZero() {
			return nil, errors.New("empty time")
		}
		return tx.Where(fmt.Sprintf("%s < ?", "gmt_create"), t), nil
	}
}

func WithGmtModifiedBefore(t time.Time) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if t.IsZero() {
			return nil, errors.New("empty time")
		}
		return tx.Where(fmt.Sprintf("%s < ?", "gmt_modified"), t), nil
	}
}

func WithLessThan(key string, value any) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(key) == 0 {
			return nil, errors.New("empty key")
		}
		return tx.Where(fmt.Sprintf("%s < ?", key), value), nil
	}
}

func WithNotEqual(key string, value any) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(key) == 0 {
			return nil, errors.New("empty key")
		}
		return tx.Where(fmt.Sprintf("%s <> ?", key), value), nil
	}
}

func WithJsonFieldNotEmpty(fieldName string) DbWhereOption {
	return func(tx *gorm.DB) (*gorm.DB, error) {
		if len(fieldName) == 0 {
			return nil, errors.New("empty field")
		}
		return tx.Where(fmt.Sprintf(`%s IS NOT NULL AND %s <> "" AND %s <> "[]" AND %s <> "{}"`, fieldName, fieldName, fieldName, fieldName)), nil
	}
}
