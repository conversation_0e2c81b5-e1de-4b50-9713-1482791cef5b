package dao

import (
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	daojson "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/dao/json"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/test/dbmock"
	"os"
	"regexp"
	"testing"
)

type sensitiveEntity struct {
	Id   uint32 `gorm:"column:id;primaryKey"`
	Name string `gorm:"column:name"`
	User *User  `gorm:"column:user;serializer:json_aes_field_serializer"`
}

type User struct {
	Name             string           `json:"name"`
	UserType         UserType         `json:"userType"`
	Email            string           `json:"email,omitempty" encrypt:"aes"`
	Nicknames        []string         `json:"nicknames,omitempty" encrypt:"aes"`
	Age              int              `json:"age"`
	PrivateData      *UserPrivateData `json:"privateData,omitempty"`
	TestForOmitEmpty UserPrivateData  `json:"testForOmitEmpty,omitempty"`
}

type UserType string

type UserPrivateData struct {
	Password         string       `json:"password,omitempty" encrypt:"aes"`
	PassPhases       []string     `json:"passPhases,omitempty" encrypt:"aes"`
	PrivateData      *PrivateData `json:"privateData,omitempty"`
	PrivateData2     PrivateData  `json:"privateData2,omitempty"`
	TestForOmitEmpty *PrivateData `json:"testForOmitEmpty,omitempty"`
}

type PrivateData struct {
	AccountId       string `json:"accountId" encrypt:"aes"`
	AccountPassword string `json:"accountPassword" encrypt:"aes"`
}

func (se *sensitiveEntity) TableName() string {
	return "t_sensitive_entity"
}

func TestJsonAesFieldSerializer(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		err := os.Setenv("AES_KEY", "****************")
		assert.Nil(tt, err, "set aes key")

		name := "test"

		se := &sensitiveEntity{
			Name: name,
			User: &User{
				Name:     name,
				UserType: UserType("test"),
				Email:    "emails",
				Nicknames: []string{
					"nickname1",
					"nickname2",
				},
				Age: 18,
				PrivateData: &UserPrivateData{
					Password: "passwd",
					PassPhases: []string{
						"phase1",
						"phase2",
					},
				},
			},
		}
		serializer := daojson.CustomJSONSerializer{}
		userBytes, err := serializer.Marshal(se.User)
		assert.Nil(tt, err, "custom marshal err")

		mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `t_sensitive_entity` WHERE name = ? ORDER BY `t_sensitive_entity`.`id` LIMIT ?")).
			WithArgs(name, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "user"}).
				AddRow(1, name, string(userBytes)))
		entity, err := GetEntity(newCtx(), &sensitiveEntity{}, NewDbOptions(WithKV("name", name)))
		assert.Nil(tt, err, "get entity")

		assert.Equal(tt, se.Name, entity.Name)
		assert.Equal(tt, se.User, entity.User)
	})
}
