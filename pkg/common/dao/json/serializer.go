package json

import (
	"encoding/json"
	"fmt"
	"github.com/pkg/errors"
	"reflect"
	"strings"
	"time"
)

// CustomJSONSerializer 提供自定义的 JSON 序列化和反序列化功能
type CustomJSONSerializer struct{}

// Marshal 将结构体序列化为 JSON，自动加密带有 `encrypt:"aes"` 标`签的string或[]string字段，其中aes表示使用aes进行加密解密
// 后续可以根据需要扩展加密算法
func (c *CustomJSONSerializer) Marshal(v any) ([]byte, error) {
	return c.marshalValue(reflect.ValueOf(v))
}

func (c *CustomJSONSerializer) marshalValue(val reflect.Value) ([]byte, error) {
	typ := val.Type()

	// 如果是指针，获取其指向的元素
	if typ.Kind() == reflect.Ptr {
		if val.IsNil() {
			return json.Marshal(nil)
		}
		val = val.Elem()
		typ = val.Type()
	}

	if typ.Kind() != reflect.Struct {
		return json.Marshal(val.Interface())
	}

	// 创建一个映射来存储序列化后的数据
	result := make(map[string]any)

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		fieldVal := val.Field(i)

		// 跳过未导出字段
		if !field.IsExported() {
			continue
		}

		// 获取JSON标签的名称
		jsonTag := field.Tag.Get("json")
		if jsonTag == "-" { // 跳过不需要序列化的字段
			continue
		}
		jsonKey := field.Name
		omitempty := false
		if jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			if len(parts) > 0 {
				jsonKey = parts[0]
				for _, opt := range parts[1:] {
					if opt == "omitempty" {
						omitempty = true
						break
					}
				}
			}
		}

		// 处理 omitempty
		if omitempty {
			if isEmptyValue(fieldVal) {
				continue
			}
		}

		// 检查是否需要加密
		encryptTag := field.Tag.Get("encrypt")
		if encryptTag != "" {
			alg := getAlgorithm(encryptTag)
			if alg == nil {
				return nil, errors.Errorf("unsupported encryption algorithm %s", encryptTag)
			}
			switch field.Type.Kind() {
			case reflect.String:
				encrypted, err := alg.encryptString(fieldVal.String())
				if err != nil {
					return nil, err
				}
				result[jsonKey] = encrypted
			case reflect.Slice:
				if field.Type.Elem().Kind() != reflect.String {
					return nil, errors.Errorf("unsupported slice element type for field %s", field.Name)
				}
				encryptedSlice := []string{}
				for j := 0; j < fieldVal.Len(); j++ {
					enc, err := alg.encryptString(fieldVal.Index(j).String())
					if err != nil {
						return nil, err
					}
					encryptedSlice = append(encryptedSlice, enc)
				}
				result[jsonKey] = encryptedSlice
			default:
				return nil, errors.Errorf("unsupported field type %s for encryption", field.Type.Kind())
			}
		} else {
			// 处理结构体和指针结构体
			if field.Type == reflect.TypeOf(time.Time{}) {
				result[jsonKey] = fieldVal.Interface()
			} else if fieldKind := field.Type.Kind(); fieldKind == reflect.Struct || (fieldKind == reflect.Ptr && field.Type.Elem().Kind() == reflect.Struct) {
				nestedJSON, err := c.marshalValue(fieldVal)
				if err != nil {
					return nil, err
				}
				var nestedMap map[string]any
				err = json.Unmarshal(nestedJSON, &nestedMap)
				if err != nil {
					return nil, err
				}
				result[jsonKey] = nestedMap
			} else {
				// 非加密字段直接赋值
				result[jsonKey] = fieldVal.Interface()
			}
		}
	}

	return json.Marshal(result)
}

// Unmarshal 将 JSON 数据反序列化为结构体，自动解密带有  `encrypt:"aes"` 标`签的string或[]string字段，其中aes表示使用aes进行加密解密
// 后续可以根据需要扩展加密算法
func (c *CustomJSONSerializer) Unmarshal(data []byte, v any) error {
	return c.unmarshalValue(data, reflect.ValueOf(v))
}

func (c *CustomJSONSerializer) unmarshalValue(data []byte, val reflect.Value) error {
	if val.Kind() != reflect.Ptr || val.IsNil() {
		return errors.New("Unmarshal requires a non-nil pointer")
	}

	// 解析JSON到临时的map
	tempMap := make(map[string]any)
	if err := json.Unmarshal(data, &tempMap); err != nil {
		return err
	}

	return c.unmarshalToValue(tempMap, val.Elem())
}

func (c *CustomJSONSerializer) unmarshalToValue(tempMap map[string]any, val reflect.Value) error {
	// 如果是指针，获取其指向的元素
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			val.Set(reflect.New(val.Type().Elem()))
		}
		val = val.Elem()
	}

	if val.Kind() != reflect.Struct {
		return fmt.Errorf("cannot unmarshal into non-struct type %v", val.Type())
	}

	typ := val.Type()

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		fieldVal := val.Field(i)

		if !field.IsExported() {
			continue
		}

		jsonTag := field.Tag.Get("json")
		if jsonTag == "-" {
			continue
		}

		jsonKey := field.Name
		if jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			if len(parts) > 0 {
				jsonKey = parts[0]
			}
		}

		rawValue, exists := tempMap[jsonKey]
		if !exists {
			continue
		}

		encryptTag := field.Tag.Get("encrypt")
		if encryptTag != "" {
			alg := getAlgorithm(encryptTag)
			if alg == nil {
				return errors.Errorf("unsupported encryption algorithm %s", encryptTag)
			}
			if err := c.handleEncryptedField(alg, field, fieldVal, rawValue); err != nil {
				return err
			}
		} else {
			if err := c.handleRegularField(field, fieldVal, rawValue); err != nil {
				return err
			}
		}
	}

	return nil
}

func (c *CustomJSONSerializer) handleEncryptedField(alg *algorithm, field reflect.StructField, fieldVal reflect.Value, rawValue any) error {
	switch field.Type.Kind() {
	case reflect.String:
		cipherText, ok := rawValue.(string)
		if !ok {
			return fmt.Errorf("expected string for encrypted field %s", field.Name)
		}
		decrypted, err := alg.decryptString(cipherText)
		if err != nil {
			return err
		}
		fieldVal.SetString(decrypted)
	case reflect.Slice:
		if field.Type.Elem().Kind() != reflect.String {
			return errors.Errorf("unsupported slice element type for encrypted field %s", field.Name)
		}
		cipherSlice, ok := rawValue.([]any)
		if !ok {
			return errors.Errorf("expected slice for encrypted field %s", field.Name)
		}
		decryptedSlice := reflect.MakeSlice(field.Type, len(cipherSlice), len(cipherSlice))
		for j, item := range cipherSlice {
			cipherText, ok := item.(string)
			if !ok {
				return errors.Errorf("expected string in slice for encrypted field %s", field.Name)
			}
			decrypted, err := alg.decryptString(cipherText)
			if err != nil {
				return err
			}
			decryptedSlice.Index(j).SetString(decrypted)
		}
		fieldVal.Set(decryptedSlice)
	default:
		return errors.Errorf("unsupported field type %s for encryption", field.Type.Kind())
	}
	return nil
}

func (c *CustomJSONSerializer) handleRegularField(field reflect.StructField, fieldVal reflect.Value, rawValue any) error {
	if field.Type == reflect.TypeOf(time.Time{}) {
		return c.setTimeField(fieldVal, rawValue)
	}

	switch field.Type.Kind() {
	case reflect.Struct:
		return c.setStructField(field, fieldVal, rawValue)
	case reflect.Ptr:
		return c.setPtrField(field, fieldVal, rawValue)
	case reflect.Slice:
		return c.setSliceField(field, fieldVal, rawValue)
	case reflect.Map:
		return c.setMapField(field, fieldVal, rawValue)
	default:
		return c.setBasicField(field, fieldVal, rawValue)
	}
}

func (c *CustomJSONSerializer) setTimeField(fieldVal reflect.Value, rawValue any) error {
	timeStr, ok := rawValue.(string)
	if !ok {
		return fmt.Errorf("expected string for time.Time field")
	}
	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return err
	}
	fieldVal.Set(reflect.ValueOf(t))
	return nil
}

func (c *CustomJSONSerializer) setStructField(field reflect.StructField, fieldVal reflect.Value, rawValue any) error {
	nestedMap, ok := rawValue.(map[string]any)
	if !ok {
		return fmt.Errorf("expected map for struct field %s", field.Name)
	}
	return c.unmarshalToValue(nestedMap, fieldVal)
}

func (c *CustomJSONSerializer) setPtrField(field reflect.StructField, fieldVal reflect.Value, rawValue any) error {
	if rawValue == nil {
		fieldVal.Set(reflect.Zero(field.Type))
		return nil
	}

	if fieldVal.IsNil() {
		fieldVal.Set(reflect.New(field.Type.Elem()))
	}

	return c.handleRegularField(reflect.StructField{Type: field.Type.Elem()}, fieldVal.Elem(), rawValue)
}

func (c *CustomJSONSerializer) setSliceField(field reflect.StructField, fieldVal reflect.Value, rawValue any) error {
	slice, ok := rawValue.([]any)
	if !ok {
		return fmt.Errorf("expected slice for field %s", field.Name)
	}

	sliceVal := reflect.MakeSlice(field.Type, len(slice), len(slice))
	for i, elem := range slice {
		if err := c.handleRegularField(reflect.StructField{Type: field.Type.Elem()}, sliceVal.Index(i), elem); err != nil {
			return err
		}
	}

	fieldVal.Set(sliceVal)
	return nil
}

func (c *CustomJSONSerializer) setMapField(field reflect.StructField, fieldVal reflect.Value, rawValue any) error {
	mapData, ok := rawValue.(map[string]any)
	if !ok {
		return fmt.Errorf("expected map for field %s", field.Name)
	}

	mapVal := reflect.MakeMap(field.Type)
	for key, value := range mapData {
		keyVal := reflect.ValueOf(key)
		elemVal := reflect.New(field.Type.Elem()).Elem()
		if err := c.handleRegularField(reflect.StructField{Type: field.Type.Elem()}, elemVal, value); err != nil {
			return err
		}
		mapVal.SetMapIndex(keyVal, elemVal)
	}

	fieldVal.Set(mapVal)
	return nil
}

func (c *CustomJSONSerializer) setBasicField(field reflect.StructField, fieldVal reflect.Value, rawValue any) error {
	rv := reflect.ValueOf(rawValue)

	// 处理数字类型的特殊情况（如 JSON 中的数字可能被解析为 float64）
	if field.Type.Kind() >= reflect.Int && field.Type.Kind() <= reflect.Float64 {
		if rv.Type().Kind() == reflect.Float64 {
			rv = reflect.ValueOf(convertNumber(rawValue.(float64), field.Type.Kind()))
		}
	}

	if !rv.Type().ConvertibleTo(field.Type) {
		return fmt.Errorf("cannot convert %v to %v for field %s", rv.Type(), field.Type, field.Name)
	}

	fieldVal.Set(rv.Convert(field.Type))
	return nil
}

// convertNumber 辅助函数，用于将 float64 转换为适当的数字类型
func convertNumber(f float64, kind reflect.Kind) any {
	switch kind {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return int64(f)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return uint64(f)
	case reflect.Float32:
		return float32(f)
	default:
		return f
	}
}

func isEmptyValue(v reflect.Value) bool {
	switch v.Kind() {
	case reflect.Array, reflect.Map, reflect.Slice, reflect.String:
		return v.Len() == 0
	case reflect.Bool:
		return !v.Bool()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return v.Int() == 0
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64, reflect.Uintptr:
		return v.Uint() == 0
	case reflect.Float32, reflect.Float64:
		return v.Float() == 0
	case reflect.Interface, reflect.Ptr:
		return v.IsNil()
	case reflect.Struct:
		return reflect.DeepEqual(v.Interface(), reflect.Zero(v.Type()).Interface())
	}
	return false
}
