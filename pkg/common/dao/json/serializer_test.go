package json

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"os"
	"reflect"
	"testing"
	"time"
)

type User struct {
	Name             string           `json:"name"`
	UserType         UserType         `json:"userType"`
	Email            string           `json:"email,omitempty" encrypt:"aes"`
	Nicknames        []string         `json:"nicknames,omitempty" encrypt:"aes"`
	Age              int              `json:"age"`
	PrivateData      *UserPrivateData `json:"privateData,omitempty"`
	TestForOmitEmpty UserPrivateData  `json:"testForOmitEmpty,omitempty"`
}

type UserType string

type UserPrivateData struct {
	Password         string       `json:"password,omitempty" encrypt:"aes"`
	PassPhases       []string     `json:"passPhases,omitempty" encrypt:"aes"`
	PrivateData      *PrivateData `json:"privateData,omitempty"`
	PrivateData2     PrivateData  `json:"privateData2,omitempty"`
	TestForOmitEmpty *PrivateData `json:"testForOmitEmpty,omitempty"`
}

type PrivateData struct {
	AccountId       string `json:"accountId" encrypt:"aes"`
	AccountPassword string `json:"accountPassword" encrypt:"aes"`
}

type TimeStruct struct {
	CurrentTime time.Time `json:"currentTime"`
}

func TestCustomJSONSerializer(t *testing.T) {
	u := User{
		Name:     "name",
		Email:    "email",
		UserType: UserType("test"),
		Nicknames: []string{
			"nickname1",
			"nickname2",
		},
		Age: 18,
		PrivateData: &UserPrivateData{
			Password: "123",
			PassPhases: []string{
				"passPhase1",
				"passPhase2",
			},
			PrivateData: &PrivateData{
				AccountId: "accountId1",
			},
			PrivateData2: PrivateData{
				AccountPassword: "accPwd",
			},
		},
	}
	err := os.Setenv("AES_KEY", "****************")
	assert.Nil(t, err, "set aes key")

	serializer := CustomJSONSerializer{}
	bytes, err := serializer.Marshal(u)
	assert.Nil(t, err, "marshal user struct")
	t.Log(string(bytes))
	bytes2, err2 := serializer.Marshal(&u)
	assert.Nil(t, err2, "marshal user struct ptr")
	t.Log(string(bytes2))

	byte3, err := json.Marshal(u)
	assert.Nil(t, err, "json marshal user")
	t.Log(string(byte3))

	u1 := User{}
	u2 := User{}

	err = serializer.Unmarshal(bytes, &u1)
	assert.Nil(t, err, "marshal to u1")
	err = serializer.Unmarshal(bytes2, &u2)
	assert.Nil(t, err, "marshal to u2")
	assert.True(t, reflect.DeepEqual(u, u1))
	assert.True(t, reflect.DeepEqual(u, u2))

}

func TestCustomJSONSerializerForTime(t *testing.T) {
	err := os.Setenv("AES_KEY", "****************")
	assert.Nil(t, err, "set aes key")
	ts := TimeStruct{CurrentTime: time.Now()}
	serializer := CustomJSONSerializer{}
	bytes, err := serializer.Marshal(ts)
	assert.Nil(t, err, "marshal time struct")
	bytes2, err := json.Marshal(ts)
	assert.Nil(t, err, "json marshal time struct")
	assert.Equal(t, string(bytes), string(bytes2))
	t.Log(string(bytes))

	ts2 := &TimeStruct{}
	err = serializer.Unmarshal(bytes, ts2)
	assert.Equal(t, ts.CurrentTime.Unix(), ts2.CurrentTime.Unix())

}
