package json

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/aes"

func init() {
	registerAlgorithm(&algorithm{
		name:          "aes",
		encryptString: aes.EncryptString,
		decryptString: aes.DecryptString})
}

var algorithmMap = map[string]*algorithm{}

func registerAlgorithm(a *algorithm) {
	algorithmMap[a.name] = a
}

func getAlgorithm(name string) *algorithm {
	return algorithmMap[name]
}

type encryptFn func(s string) (string, error)
type decryptFn func(s string) (string, error)

type algorithm struct {
	name          string
	encryptString encryptFn
	decryptString decryptFn
}
