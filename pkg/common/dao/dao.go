package dao

import (
	"context"
	"reflect"
	"strings"

	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	comnerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/database"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/sql"

	"log/slog"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"gorm.io/gorm/utils"
)

func getDataBase(ctx base.Context) *gorm.DB {
	db := database.GetDatabase()
	// 使用特定的Logger记录日志
	return db.Session(&gorm.Session{Logger: newDbLogger(ctx)})
}

// getEntityFields 通过反射获取实体的所有数据库字段名
func getEntityFields[T schema.Tabler](entity T) []string {
	var fields []string

	// 获取实体类型
	entityType := reflect.TypeOf(entity)

	// 如果是指针，获取其指向的元素类型
	if entityType.Kind() == reflect.Ptr {
		entityType = entityType.Elem()
	}

	// 遍历结构体字段
	for i := 0; i < entityType.NumField(); i++ {
		field := entityType.Field(i)

		// 跳过未导出的字段
		if !field.IsExported() {
			continue
		}

		// 获取gorm column标签
		gormTag := field.Tag.Get("gorm")
		if gormTag == "-" {
			continue // 跳过不映射的字段
		}

		// 解析column名称
		columnName := ""
		if gormTag != "" {
			parts := strings.Split(gormTag, ";")
			for _, part := range parts {
				part = strings.TrimSpace(part)
				if strings.HasPrefix(part, "column:") {
					columnName = strings.TrimPrefix(part, "column:")
					break
				}
			}
		}

		// 如果没有指定column名称，使用字段名的蛇形命名
		if columnName == "" {
			columnName = toSnakeCase(field.Name)
		}

		fields = append(fields, columnName)
	}

	return fields
}

// toSnakeCase 将驼峰命名转换为蛇形命名
// 正确处理连续大写字母，例如 HTTPServer -> http_server 而不是 h_t_t_p_server
func toSnakeCase(camelCase string) string {
	if len(camelCase) == 0 {
		return ""
	}

	var result []rune
	runes := []rune(camelCase)

	for i, r := range runes {
		isUpper := r >= 'A' && r <= 'Z'

		if i > 0 {
			prevIsUpper := runes[i-1] >= 'A' && runes[i-1] <= 'Z'
			prevIsLower := runes[i-1] >= 'a' && runes[i-1] <= 'z'
			prevIsDigit := runes[i-1] >= '0' && runes[i-1] <= '9'

			// 检查下一个字符（如果存在）
			nextIsLower := i+1 < len(runes) && runes[i+1] >= 'a' && runes[i+1] <= 'z'

			// 需要添加下划线的情况：
			// 1. 当前字符是大写，前一个字符是小写或数字
			// 2. 当前字符是大写，前一个字符也是大写，但下一个字符是小写（连续大写字母边界）
			if isUpper && (prevIsLower || prevIsDigit) {
				result = append(result, '_')
			} else if isUpper && prevIsUpper && nextIsLower {
				result = append(result, '_')
			}
		}

		if isUpper {
			result = append(result, r-'A'+'a')
		} else {
			result = append(result, r)
		}
	}

	return string(result)
}

// GetEntity 获取单个实体
// T 根据该类型反射构建返回值与寻找表名
// DbOptions 构建查询条件
// 若找不到满足条件实体，则返回 gorm.ErrRecordNotFound错误
// SQL类似于
// SELECT field1, field2, ... FROM table_name WHERE ... ORDER BY ID LIMIT 1
func GetEntity[T schema.Tabler](ctx base.Context, entity T, o *DbOptions) (T, error) {
	db := getDataBase(ctx)

	e := new(T)
	tx := db.Table(entity.TableName())

	// 获取实体的所有字段并设置Select
	fields := getEntityFields(entity)
	if len(fields) > 0 {
		tx = tx.Select(strings.Join(fields, ", "))
	}

	var err error
	for _, opt := range o.Wheres {
		if tx, err = opt(tx); err != nil {
			return *e, dbErrorWrapper(ctx, err)
		}
	}
	for _, opt := range o.Pages {
		if tx, err = opt(tx); err != nil {
			return *e, dbErrorWrapper(ctx, err)
		}
	}
	if err := tx.First(e).Error; err != nil {
		return *e, dbErrorWrapper(ctx, err)
	}
	return *e, nil
}

// ListEntitiesWithPage 列出所有条件的实体信息，遵守分页行为，必须传递Page参数，否则要报错
// T 根据该类型反射构建返回值与寻找表名
// DbOptions 构建查询条件
func ListEntitiesWithPage[T schema.Tabler](ctx base.Context, entity T, o *DbOptions) ([]T, int64, error) {
	db := getDataBase(ctx)
	tx := db.Table(entity.TableName())
	var err error
	entities := make([]T, 0)
	for _, opt := range o.Wheres {
		if tx, err = opt(tx); err != nil {
			return nil, -1, dbErrorWrapper(ctx, err)
		}
	}
	total := int64(-1)
	if tx := tx.Count(&total); tx.Error != nil {
		return nil, -1, dbErrorWrapper(ctx, tx.Error)
	}
	if total <= 0 {
		return entities, total, nil
	}
	if len(o.Pages) == 0 {
		return nil, -1, comnerrors.New(codes.ErrInvalidParameterWithDetail, "query", "there is no page option in list query")
	}

	// 获取实体的所有字段并设置Select
	fields := getEntityFields(entity)
	if len(fields) > 0 {
		tx = tx.Select(strings.Join(fields, ", "))
	}

	for _, opt := range o.Pages {
		if tx, err = opt(tx); err != nil {
			return nil, -1, dbErrorWrapper(ctx, err)
		}
	}
	if tx := tx.Find(&entities); tx.Error != nil {
		return nil, -1, dbErrorWrapper(ctx, tx.Error)
	}
	return entities, total, nil
}

func listEntityWithoutTotal[T schema.Tabler](ctx base.Context, entity T, o *DbOptions) ([]T, error) {
	db := getDataBase(ctx)
	tx := db.Table(entity.TableName())
	var err error
	entities := make([]T, 0)
	for _, opt := range o.Wheres {
		if tx, err = opt(tx); err != nil {
			return nil, dbErrorWrapper(ctx, err)
		}
	}

	// 获取实体的所有字段并设置Select
	fields := getEntityFields(entity)
	if len(fields) > 0 {
		tx = tx.Select(strings.Join(fields, ", "))
	}

	for _, opt := range o.Pages {
		if tx, err = opt(tx); err != nil {
			return nil, dbErrorWrapper(ctx, err)
		}
	}
	if tx := tx.Find(&entities); tx.Error != nil {
		return nil, dbErrorWrapper(ctx, tx.Error)
	}
	return entities, nil
}

// ListEntitiesAutoPage 列出所有满足条件的实体，自动按照每页50个查询，避免过大影响网络IO
// T 根据该类型反射构建返回值与寻找表名
// DbOptions 构建查询条件，其中Page参数不生效
func ListEntitiesAutoPage[T schema.Tabler](ctx base.Context, entity T, o *DbOptions) ([]T, int64, error) {
	pageSize := config.GetOrDefaultInt(config.DBListAutoPageSize, 50)

	pageNumber := 1
	entities := make([]T, 0)
	newO := DbOptions{
		Wheres: o.Wheres,
		Pages:  []DbPageOption{WithPage(pageNumber, pageSize)},
	}
	results, total, err := ListEntitiesWithPage(ctx, entity, &newO)
	if err != nil {
		return nil, -1, err
	}
	entities = append(entities, results...)

	for page := 2; int64(page)*int64(pageSize) <= total || int64(page-1)*int64(pageSize) < total; page += 1 {
		r, err := listEntityWithoutTotal(ctx, entity, &DbOptions{
			Wheres: o.Wheres,
			Pages:  []DbPageOption{WithPage(page, pageSize)},
		})
		if err != nil {
			return nil, -1, err
		}
		entities = append(entities, r...)
	}
	return entities, total, nil
}

func DBActionWithTransaction(ctx base.Context, actions ...DbAction) error {
	db := getDataBase(ctx)
	tx := db.Begin()
	for _, act := range actions {
		if err := act(ctx, tx); err != nil {
			tx.Rollback()
			return err
		}
	}
	tx.Commit()
	return nil
}

func InsertEntitiesTransaction[T any](entities ...T) DbAction {
	return func(ctx base.Context, tx *gorm.DB) error {
		if len(entities) == 0 {
			return nil
		}
		batchSize := config.GetOrDefaultInt(config.DbInsertBatchSize, 50)
		if db := tx.CreateInBatches(entities, batchSize); db.Error != nil {
			return dbErrorWrapper(ctx, db.Error)
		}
		return nil
	}
}

func SaveEntitiesWithTransaction[T any](entities ...T) DbAction {
	return func(ctx base.Context, tx *gorm.DB) error {
		if len(entities) == 0 {
			return nil
		}
		batchSize := config.GetOrDefaultInt(config.DbSaveBatchSize, 50)
		saveInBatch := func(ctx base.Context, batchEntities ...T) error {
			if db := tx.Save(entities); db.Error != nil {
				return dbErrorWrapper(ctx, db.Error)
			}
			return nil
		}
		for i := 0; i < len(entities); i += batchSize {
			if err := saveInBatch(ctx, entities[i:min(i+batchSize, len(entities))]...); err != nil {
				return err
			}
		}
		return nil
	}
}

func DeleteEntitiesWithTransaction[T any](emptyEntity T, o *DbOptions) DbAction {
	return func(ctx base.Context, tx *gorm.DB) error {
		if o == nil {
			return errors.New("empty options")
		}
		if len(o.Wheres) == 0 {
			return errors.New("empty wheres")
		}
		var err error
		for _, opt := range o.Wheres {
			if tx, err = opt(tx); err != nil {
				return errors.WithStack(err)
			}
		}
		if db := tx.Delete(emptyEntity); db.Error != nil {
			return dbErrorWrapper(ctx, db.Error)
		}
		return nil
	}
}

func UpdateEntityWithTransaction[T any](updateEntity T, o *DbOptions) DbAction {
	return func(ctx base.Context, tx *gorm.DB) error {
		if o == nil {
			return errors.New("empty options")
		}
		if len(o.Wheres) == 0 {
			return errors.New("empty wheres")
		}
		var err error
		for _, opt := range o.Wheres {
			if tx, err = opt(tx); err != nil {
				return errors.WithStack(err)
			}
		}
		if db := tx.Updates(updateEntity); db.Error != nil {
			return dbErrorWrapper(ctx, db.Error)
		}
		return nil
	}
}

// InsertEntity 插入一条DB记录
func InsertEntity(ctx base.Context, entity any) error {
	db := getDataBase(ctx)
	if tx := db.Create(entity); tx.Error != nil {
		return dbErrorWrapper(ctx, tx.Error)
	}
	return nil
}

// InsertEntities 插入一系列DB记录，按照批大小为10，分批插入
func InsertEntities[T any](ctx base.Context, entities []T) error {
	if len(entities) == 0 {
		return nil
	}
	batchSize := config.GetOrDefaultInt(config.DbInsertBatchSize, 50)
	db := getDataBase(ctx)
	if tx := db.CreateInBatches(entities, batchSize); tx.Error != nil {
		return dbErrorWrapper(ctx, tx.Error)
	}
	return nil
}

// Save 保存1个DB实体，传入的实体需要包含主键ID，否则走创建流程
// SQL类似于下方
// INSERT INTO {表名} (字段...) VALUES (字段值...) ON DUPLICATE KEY UPDATE (字段1)=(值1), (字段2)=(值2) ...
func Save[T any](ctx base.Context, entity T) error {
	if any(entity) == nil {
		return nil
	}
	db := getDataBase(ctx)
	if tx := db.Save(entity); tx.Error != nil {
		return dbErrorWrapper(ctx, tx.Error)
	}
	return nil
}

// Saves 报错DB实体，传入的实体需要包含主键ID，否则走创建流程
// SQL类似于下方
// INSERT INTO {表名} (字段...) VALUES (字段值...) ON DUPLICATE KEY UPDATE (字段1)=(值1), (字段2)=(值2) ...
func Saves[T any](ctx base.Context, entities []T) error {
	if len(entities) == 0 {
		return nil
	}
	db := getDataBase(ctx)
	if tx := db.Save(entities); tx.Error != nil {
		return dbErrorWrapper(ctx, tx.Error)
	}
	return nil
}

// UpdateEntity 部份更新实体字段
// entity 传入需要修改的部份，指针类型字段为nil, 值类型字段为零值，则会忽略字段更新
// DbOptions 来筛选被更新的条目
// SQL类似于
// UPDATE {表名} SET {字段1}={值1}, {字段2}={值2} ... WHERE {条件}
func UpdateEntity[T schema.Tabler](ctx base.Context, entity T, o *DbOptions) error {
	if o == nil {
		return errors.New("empty options")
	}
	if len(o.Wheres) == 0 {
		return errors.New("empty wheres")
	}
	db := getDataBase(ctx)

	var err error
	for _, w := range o.Wheres {
		db, err = w(db)
		if err != nil {
			return dbErrorWrapper(ctx, err)
		}
	}
	if tx := db.Table(entity.TableName()).Updates(entity); tx.Error != nil {
		return dbErrorWrapper(ctx, tx.Error)
	}
	return nil
}

// DeleteEntities 删除多条DB记录
// T 根据该类型查找表名
// DbOptions 来筛选被删除的条目
// SQL类似于
// DELETE FROM {表名} WHERE {条件}
func DeleteEntities[T schema.Tabler](ctx base.Context, emptyEntity T, o *DbOptions) error {
	if o == nil {
		return dbErrorWrapper(ctx, errors.New("empty options"))
	}
	if len(o.Wheres) == 0 {
		return dbErrorWrapper(ctx, errors.New("empty wheres"))
	}
	db := getDataBase(ctx)
	tx := db.Table(emptyEntity.TableName())
	var err error
	for _, opt := range o.Wheres {
		if tx, err = opt(tx); err != nil {
			return dbErrorWrapper(ctx, err)
		}
	}
	if tx := tx.Delete(emptyEntity); tx.Error != nil {
		return dbErrorWrapper(ctx, tx.Error)
	}
	return nil
}

// Count 计算条目数量
// DbOptions page参数不生效
func Count[T schema.Tabler](ctx base.Context, emptyEntity T, o *DbOptions) (int64, error) {
	db := getDataBase(ctx)
	tx := db.Table(emptyEntity.TableName())

	var err error
	if o != nil {
		for _, opt := range o.Wheres {
			if tx, err = opt(tx); err != nil {
				return -1, dbErrorWrapper(ctx, err)
			}
		}
	}
	var cnt int64
	if tx := tx.Count(&cnt); tx.Error != nil {
		return -1, dbErrorWrapper(ctx, tx.Error)
	}
	return cnt, nil
}

// RawScan 执行原生sql，不要在sql中拼接参数的值，使用values进行传参
func RawScan[T any](ctx base.Context, sql sql.Template[T], values ...any) (T, error) {
	db := getDataBase(ctx)
	results := new(T)
	tx := db.Raw(sql.Sql, values...).Scan(&results)
	if tx.Error != nil {
		return *results, dbErrorWrapper(ctx, tx.Error)
	}

	return *results, nil
}

func dbErrorWrapper(ctx base.Context, err error) error {
	if err == nil {
		return nil
	}
	if errors.Is(err, gorm.ErrRecordNotFound) {
		ctx.GetLogger().Warn("record not found")
		return errors.WithStack(comnerrors.New(codes.ErrRecordNotFound))
	}
	ctx.GetLogger().Error("database error", "err", err)
	if comnerrors.IsDatabaseError(err) {
		return errors.WithStack(err)
	}
	return errors.WithStack(comnerrors.New(codes.ErrDatabaseError))
}

type dbLogger struct {
	log *slog.Logger
}

func (l *dbLogger) LogMode(level logger.LogLevel) logger.Interface {
	return l
}

func (l *dbLogger) Info(ctx context.Context, s string, i ...any) {
	l.log.Info(s, i...)
}

func (l *dbLogger) Warn(ctx context.Context, s string, i ...any) {
	l.log.Warn(s, i...)
}

func (l *dbLogger) Error(ctx context.Context, s string, i ...any) {
	l.log.Error(s, i...)
}

func (l *dbLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {

	elapsed := time.Since(begin)
	sql, rows := fc()

	logFn := l.log.Debug
	if err != nil {
		logFn = l.log.Error
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logFn = l.log.Warn
		}
	}
	logFn("sql trace", "line", utils.FileWithLineNum(),
		"elapsed(ms)", elapsed.Milliseconds(),
		"rows", rows,
		"sql", sql,
		"err", err)
}

func newDbLogger(ctx base.Context) logger.Interface {
	return &dbLogger{log: ctx.GetLogger()}
}
