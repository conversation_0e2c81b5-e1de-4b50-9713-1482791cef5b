package dao

import (
	"database/sql/driver"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/aes"
	"strings"
)

const (
	encryptedStringPrefix = "aes:::"
)

// AESStringField DB实体使用的数据结构，该数据结构表征一个加密的字符串，在落库时自动加密，加载内存时自动解密。
// 该数据结构做了后向兼容性处理，即DB中存在未加密数据，读取时将不进行解密动作，但重新写入DB时，将转换成加密数据。
// 注意：更新DB字段 string -> AESStringField 操作是不可逆的。请斟酌后再改动。
type AESStringField string

func (s AESStringField) Value() (driver.Value, error) {
	if !strings.HasPrefix(string(s), encryptedStringPrefix) {
		encrypted, err := aes.EncryptString(string(s))
		if err != nil {
			return nil, err
		}
		return "aes:::" + encrypted, nil
	}
	return string(s), nil
}

func (s *AESStringField) Scan(value interface{}) error {
	if val, ok := value.(string); ok {
		if strings.HasPrefix(val, encryptedStringPrefix) {
			decrypted, err := aes.DecryptString(strings.TrimPrefix(val, "aes:::"))
			if err != nil {
				return err
			}
			*s = AESStringField(decrypted)
		} else {
			*s = AESStringField(val)
		}
	}
	return nil
}
