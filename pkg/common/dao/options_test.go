package dao

import (
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"reflect"
	"testing"
	"time"
)

func TestDbWhereOption(t *testing.T) {
	tests := []struct {
		sql  string
		args []any
		typ  string
		f    DbWhereOption
	}{
		{
			sql:  "a LIKE ?",
			args: []any{"b%"},
			f:    WithRightLike("a", "b"),
		},
		{
			sql:  "a LIKE ?",
			args: []any{"%b%"},
			f:    WithLike("a", "b"),
		},
		{
			sql:  "a LIKE ?",
			args: []any{"%b"},
			f:    WithLeftLike("a", "b"),
		},
		{
			sql:  "user_id = ?",
			args: []any{"a"},
			f:    WithUserId("a"),
		},
		{
			sql:  "k = ?",
			args: []any{"v"},
			f:    WithKV("k", "v"),
		},
		{
			sql:  "k IN ?",
			args: []any{[]any{[]string{"v1", "v2"}}},
			f:    With<PERSON>eyValues("k", []string{"v1", "v2"}),
		},
		{
			sql:  "created_at > ?",
			args: []any{time.Date(2023, 10, 1, 0, 0, 0, 0, time.UTC)},
			f:    WithGreaterThan("created_at", time.Date(2023, 10, 1, 0, 0, 0, 0, time.UTC)),
		},
		{
			sql:  "created_at < ?",
			args: []any{time.Date(2023, 10, 2, 0, 0, 0, 0, time.UTC)},
			f:    WithLessThan("created_at", time.Date(2023, 10, 2, 0, 0, 0, 0, time.UTC)),
		},
	}

	db, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	// 使用mock的数据库连接初始化gorm数据库对象
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true, // 跳过数据库初始化
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("an error '%s' was not expected when initiating mock database", err)
	}

	for i, tt := range tests {
		t.Run(fmt.Sprintf("case-%d", i), func(t *testing.T) {
			tx, err := tt.f(gormDB)
			if err != nil {
				t.Error(err)
			}
			c := tx.Statement.Clauses["WHERE"]
			w, ok := c.Expression.(clause.Where)
			if !ok {
				t.Fatalf("Clause must be clause.Where")
			}
			expr, ok := w.Exprs[0].(clause.Expr)
			if !ok {
				t.Fatalf("Expr must be clause.Expr")
			}
			if expr.SQL != tt.sql {
				t.Fatalf("SQL must be %s, got: %s", "a LIKE ?", expr.SQL)
			}
			if !reflect.DeepEqual(fmt.Sprintf("%v", expr.Vars), fmt.Sprintf("%v", tt.args)) {
				t.Fatalf("Vars must be %v, got: %v", tt.args, expr.Vars)
			}
		})
	}
}

func TestDbWhereOptionError(t *testing.T) {
	var emptyStrings []string = nil
	tests := []struct {
		f       DbWhereOption
		wantErr bool
	}{
		{
			wantErr: true,
			f:       WithKV("", ""),
		},
		{
			wantErr: false,
			f:       WithKV("a", ""),
		},
		{
			wantErr: true,
			f:       WithKeyValues("", ""),
		},
		//{
		//	wantErr: true,
		//	f:       WithKeyValues("a", ""),
		//},
		{
			wantErr: false,
			f:       WithKeyValues("a", ""),
		},
		{
			wantErr: true,
			f:       WithLike("", ""),
		},
		{
			wantErr: true,
			f:       WithLike("a", ""),
		},
		{
			wantErr: false,
			f:       WithLike("a", "b"),
		},
		{
			wantErr: true,
			f:       WithLeftLike("", ""),
		},
		{
			wantErr: true,
			f:       WithLeftLike("a", ""),
		},
		{
			wantErr: false,
			f:       WithLeftLike("a", "b"),
		},
		{
			wantErr: true,
			f:       WithRightLike("", ""),
		},
		{
			wantErr: true,
			f:       WithRightLike("a", ""),
		},
		{
			wantErr: false,
			f:       WithRightLike("a", "b"),
		},
		{
			wantErr: true,
			f:       WithGreaterThan("", ""),
		},
		{
			wantErr: false,
			f:       WithGreaterThan("a", ""),
		},
		{
			wantErr: true,
			f:       WithLessThan("", ""),
		},
		{
			wantErr: false,
			f:       WithLessThan("a", ""),
		},
		{
			wantErr: false,
			f:       WithKeyNotInValues("a", "v1", "v2"),
		},
		{
			wantErr: true,
			f:       WithKeyNotInValues("", "v1", "v2"),
		},
		{
			wantErr: false,
			f:       WithKeyNotInValues("a", ""),
		},
		{
			wantErr: true,
			f:       WithKeyNotInValues("a", emptyStrings...),
		},
		{
			wantErr: true,
			f:       WithSelect(""),
		},
		{
			wantErr: false,
			f:       WithSelect("api_id"),
		},
		{
			wantErr: true,
			f:       WithGroup(""),
		},
		{
			wantErr: false,
			f:       WithGroup("api_id"),
		},
		{
			wantErr: true,
			f:       WithDistinctFields(emptyStrings...),
		},
		{
			wantErr: true,
			f:       WithLeftLike("", "a"),
		},
		{
			wantErr: true,
			f:       WithRightLike("", "a"),
		},
		{
			wantErr: true,
			f:       WithLike("", "a"),
		},
		{
			wantErr: true,
			f:       WithKeyValues("a", emptyStrings...),
		},
		{
			wantErr: true,
			f:       WithFieldNotNull(""),
		},
		{
			wantErr: false,
			f:       WithFieldNotNull("api_id"),
		},
		{
			wantErr: true,
			f:       WithFieldNotNull(""),
		},
		{
			wantErr: false,
			f:       WithFieldNotNull("api_id"),
		},
	}
	db, _, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	// 使用mock的数据库连接初始化gorm数据库对象
	gormDB, err := gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true, // 跳过数据库初始化
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("an error '%s' was not expected when initiating mock database", err)
	}
	for i, tt := range tests {
		t.Run(fmt.Sprintf("case-%d", i), func(t *testing.T) {
			_, err := tt.f(gormDB)
			if (err != nil) != tt.wantErr {
				t.Errorf("DbWhereOption() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDbOption(t *testing.T) {
	o := NewDbOptions(WithKV("k", 1))
	if len(o.Wheres) != 1 {
		t.Fatalf("Wheres must be 1, got: %d", len(o.Wheres))
	}
	o.AddWhere(WithKV("k2", 2), WithKeyValues("k3", "v31", "v32"))
	if len(o.Wheres) != 3 {
		t.Fatalf("Wheres must be 3, got: %d", len(o.Wheres))
	}
	o.AddPage(WithPage(1, 10))
	if len(o.Pages) != 1 {
		t.Fatalf("Pages must be 1, got: %d", len(o.Pages))
	}
}
