package dao

import (
	"errors"
	"fmt"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/sql"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/test/basemock"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/test/dbmock"

	"reflect"
	"regexp"
	"testing"

	"gorm.io/gorm"
)

type mockEntity struct {
	Id      uint32 `gorm:"column:id;primaryKey"`
	Name    string `gorm:"column:name"`
	Deleted bool   `gorm:"column:is_delete"`
}

func (*mockEntity) TableName() string {
	return "t_test"
}

func newCtx() base.Context {
	return basemock.NewMockContext("test")
}

func TestRawScan(t *testing.T) {
	sql1 := sql.Template[[]mockEntity]{
		Sql: "SELECT * FROM `t_test` WHERE name = ?",
	}

	sql2 := sql.Template[int]{
		Sql: "SELECT count(*) FROM `t_test` WHERE name = ?",
	}

	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		want := mockEntity{
			Id:      1,
			Name:    "test",
			Deleted: false,
		}
		getValue := want.Name
		mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `t_test` WHERE name = ?")).
			WithArgs(getValue).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "is_delete"}).
				AddRow(want.Id, want.Name, want.Deleted))

		var got []mockEntity
		got, err := RawScan(newCtx(), sql1, want.Name)
		if err != nil {
			t.Fatalf("RawScan error: %v", err)
		}
		if !reflect.DeepEqual(want, got[0]) {
			t.Fatalf("want: %v, got: %v", want, got[0])
		}
	})

	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		want := mockEntity{
			Id:      1,
			Name:    "test",
			Deleted: false,
		}
		getValue := want.Name
		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE name = ?")).
			WithArgs(getValue).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		got, err := RawScan(newCtx(), sql2, want.Name)
		if err != nil {
			t.Fatalf("RawScan error: %v", err)
		}

		if got != 1 {
			t.Fatalf("want: %v, got: %v", 1, got)
		}
	})
}

func TestGetEntity(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		want := mockEntity{
			Id:      1,
			Name:    "test",
			Deleted: false,
		}
		getValue := want.Name
		mock.ExpectQuery(regexp.QuoteMeta("SELECT id, name, is_delete FROM `t_test` WHERE name = ? ORDER BY `t_test`.`id` LIMIT ?")).
			WithArgs(getValue, 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "is_delete"}).
				AddRow(want.Id, want.Name, want.Deleted))

		me, err := GetEntity(newCtx(), &mockEntity{}, NewDbOptions(WithKV("name", getValue)))
		if err != nil {
			t.Fatalf("GetEntity error: %v", err)
		}
		if !reflect.DeepEqual(&want, me) {
			t.Fatalf("want: %v, got: %v", &want, me)
		}
	})
}

func TestListEntities(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		wants := []mockEntity{
			{
				Id:      1,
				Name:    "test",
				Deleted: false,
			},
			{
				Id:      2,
				Name:    "test2",
				Deleted: false,
			},
			{
				Id:      3,
				Name:    "test3",
				Deleted: false,
			},
		}

		pageSize := 1
		pageNumber := 2
		want := wants[1]

		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE id IN (?,?,?)")).
			WithArgs(wants[0].Id, wants[1].Id, wants[2].Id).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(len(wants)))

		mock.ExpectQuery(regexp.QuoteMeta("SELECT id, name, is_delete FROM `t_test` WHERE id IN (?,?,?) LIMIT ? OFFSET ?")).
			WithArgs(wants[0].Id, wants[1].Id, wants[2].Id, pageSize, (pageNumber-1)*pageSize).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "is_delete"}).
				AddRow(want.Id, want.Name, want.Deleted))

		opt := NewDbOptions(WithKeyValues("id", 1, 2, 3))
		opt.AddPage(WithPage(pageNumber, pageSize))
		entities, total, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt)
		if err != nil {
			t.Fatalf("ListEntitiesWithPage error: %v", err)
		}
		if int(total) != len(wants) {
			t.Fatalf("want: %d, got: %d", len(wants), total)
		}
		if len(entities) > pageSize {
			t.Fatalf("want: <= %d, got: %d", pageSize, len(entities))
		}
		if !reflect.DeepEqual(&want, entities[0]) {
			t.Fatalf("want: %v, got: %v", &want, entities[0])
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		wants := []mockEntity{
			{
				Id:      1,
				Name:    "test",
				Deleted: false,
			},
			{
				Id:      2,
				Name:    "test2",
				Deleted: false,
			},
			{
				Id:      3,
				Name:    "test3",
				Deleted: false,
			},
		}

		pageSize := 1
		pageNumber := 2

		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE id IN (?,?,?)")).
			WithArgs(wants[0].Id, wants[1].Id, wants[2].Id).
			WillReturnError(errors.New("mock err"))

		opt := NewDbOptions(WithKeyValues("id", 1, 2, 3))
		opt.AddPage(WithPage(pageNumber, pageSize))
		if _, _, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		wants := []mockEntity{
			{
				Id:      1,
				Name:    "test",
				Deleted: false,
			},
			{
				Id:      2,
				Name:    "test2",
				Deleted: false,
			},
			{
				Id:      3,
				Name:    "test3",
				Deleted: false,
			},
		}

		pageSize := 1
		pageNumber := 2

		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE id IN (?,?,?)")).
			WithArgs(wants[0].Id, wants[1].Id, wants[2].Id).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(0))

		opt := NewDbOptions(WithKeyValues("id", 1, 2, 3))
		opt.AddPage(WithPage(pageNumber, pageSize))
		if entities, count, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt); err != nil {
			t.Fatal(err)
		} else if count != 0 {
			t.Fatalf("want: %d, got: %d", 0, count)
		} else if len(entities) != 0 {
			t.Fatalf("want: %d, got: %d", 0, len(entities))
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		wants := []mockEntity{
			{
				Id:      1,
				Name:    "test",
				Deleted: false,
			},
			{
				Id:      2,
				Name:    "test2",
				Deleted: false,
			},
			{
				Id:      3,
				Name:    "test3",
				Deleted: false,
			},
		}

		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE id IN (?,?,?)")).
			WithArgs(wants[0].Id, wants[1].Id, wants[2].Id).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(len(wants)))

		opt := NewDbOptions(WithKeyValues("id", 1, 2, 3))
		opt.AddPage(func(tx *gorm.DB) (*gorm.DB, error) {
			return nil, errors.New("mock err")
		})
		if _, _, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		wants := []mockEntity{
			{
				Id:      1,
				Name:    "test",
				Deleted: false,
			},
			{
				Id:      2,
				Name:    "test2",
				Deleted: false,
			},
			{
				Id:      3,
				Name:    "test3",
				Deleted: false,
			},
		}
		pageSize := 1
		pageNumber := 2

		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE id IN (?,?,?)")).
			WithArgs(wants[0].Id, wants[1].Id, wants[2].Id).
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(len(wants)))

		mock.ExpectQuery(regexp.QuoteMeta("SELECT id, name, is_delete FROM `t_test` WHERE id IN (?,?,?) LIMIT ? OFFSET ?")).
			WithArgs(wants[0].Id, wants[1].Id, wants[2].Id, pageSize, (pageNumber-1)*pageSize).
			WillReturnError(errors.New("mock err"))

		opt := NewDbOptions(WithKeyValues("id", 1, 2, 3))
		opt.AddPage(WithPage(pageNumber, pageSize))

		if _, _, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
}

func TestListEntitiesAutoPage(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		var wants []mockEntity
		defaultPageSize := 50

		for i := 0; i < defaultPageSize+1; i++ {
			wants = append(wants, mockEntity{
				Id:      uint32(i),
				Name:    fmt.Sprintf("test%d", i),
				Deleted: false,
			})
		}

		queryName := "test"

		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE name LIKE ?")).
			WithArgs(queryName + "%").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(len(wants)))

		firstQueryRows := sqlmock.NewRows([]string{"id", "name", "is_delete"})
		for i := 0; i < defaultPageSize; i++ {
			firstQueryRows = firstQueryRows.AddRow(wants[i].Id, wants[i].Name, wants[i].Deleted)
		}
		mock.ExpectQuery(regexp.QuoteMeta("SELECT id, name, is_delete FROM `t_test` WHERE name LIKE ? LIMIT ?")).
			WithArgs(queryName+"%", defaultPageSize).
			WillReturnRows(firstQueryRows)

		secondQueryRows := sqlmock.NewRows([]string{"id", "name", "is_delete"})
		for i := defaultPageSize; i < len(wants); i++ {
			secondQueryRows = secondQueryRows.AddRow(wants[i].Id, wants[i].Name, wants[i].Deleted)
		}
		mock.ExpectQuery(regexp.QuoteMeta("SELECT id, name, is_delete FROM `t_test` WHERE name LIKE ? LIMIT ? OFFSET ?")).
			WithArgs(queryName+"%", defaultPageSize, defaultPageSize).
			WillReturnRows(secondQueryRows)

		opt := NewDbOptions(WithRightLike("name", queryName))
		entities, total, err := ListEntitiesAutoPage(newCtx(), &mockEntity{}, opt)
		if err != nil {
			t.Fatalf("ListEntitiesWithPage error: %v", err)
		}
		if int(total) != len(wants) {
			t.Fatalf("want: %d, got: %d", len(wants), total)
		}
		if len(entities) != len(wants) {
			t.Fatalf("want: %d, got: %d", len(wants), len(entities))
		}
		for i := range entities {
			if !reflect.DeepEqual(&wants[i], entities[i]) {
				t.Fatalf("want: %v, got: %v", &wants[i], entities[i])
			}
		}
	})
}

func TestDbActionWithTransaction(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		entities := []mockEntity{
			{
				Id:      1,
				Name:    "test",
				Deleted: false,
			},
			{
				Id:      2,
				Name:    "test2",
				Deleted: false,
			},
			{
				Id:      3,
				Name:    "test3",
				Deleted: false,
			},
		}
		me := mockEntity{Id: 4, Name: "test"}
		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `t_test` (`name`,`is_delete`,`id`) VALUES (?,?,?),(?,?,?),(?,?,?)")).
			WithArgs(entities[0].Name, entities[0].Deleted, entities[0].Id, entities[1].Name, entities[1].Deleted, entities[1].Id, entities[2].Name, entities[2].Deleted, entities[2].Id).
			WillReturnResult(sqlmock.NewResult(1, 3))
		mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `t_test` (`name`,`is_delete`,`id`) VALUES (?,?,?)")).
			WithArgs(me.Name, me.Deleted, me.Id).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		ctx := basemock.NewMockContext("test")
		if err := DBActionWithTransaction(ctx,
			InsertEntitiesTransaction(entities...),
			SaveEntitiesWithTransaction(mockEntity{Id: 4, Name: "test"})); err != nil {
			t.Fatal(err)
		}

		mock.ExpectBegin()
		mock.ExpectRollback()

		if err := DBActionWithTransaction(ctx, func(ctx base.Context, tx *gorm.DB) error {
			return errors.New("mock err")
		}); err == nil {
			t.Fatal("should return error")
		}

		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `t_test` WHERE name IN (?,?,?)")).
			WithArgs(entities[0].Name, entities[1].Name, entities[2].Name).
			WillReturnResult(sqlmock.NewResult(1, 3))
		mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `t_test` WHERE id = ?")).
			WithArgs(me.Id).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()
		if err := DBActionWithTransaction(ctx,
			DeleteEntitiesWithTransaction(&mockEntity{}, NewDbOptions(WithKeyValues("name", entities[0].Name, entities[1].Name, entities[2].Name))),
			DeleteEntitiesWithTransaction(&mockEntity{}, NewDbOptions(WithKV("id", me.Id)))); err != nil {
			t.Fatal(err)
		}
	})
}

func TestInsertEntity(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		me := mockEntity{Name: "test"}

		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `t_test` (`name`,`is_delete`) VALUES (?,?)")).
			WithArgs(me.Name, me.Deleted).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()
		if err := InsertEntity(newCtx(), &me); err != nil {
			t.Fatal(err)
		}
	})
}

func TestInsertEntities(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		mes := []mockEntity{
			{Name: "test"},
			{Name: "test2"},
		}

		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `t_test` (`name`,`is_delete`) VALUES (?,?),(?,?)")).
			WithArgs(mes[0].Name, mes[0].Deleted, mes[1].Name, mes[1].Deleted).
			WillReturnResult(sqlmock.NewResult(2, 2))
		mock.ExpectCommit()
		if err := InsertEntities(newCtx(), mes); err != nil {
			t.Fatal(err)
		}
	})
}

func TestSaves(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		mes := []mockEntity{
			{Id: 1, Name: "test"},
			{Id: 2, Name: "test2"},
		}

		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `t_test` (`name`,`is_delete`,`id`) VALUES (?,?,?),(?,?,?) ON DUPLICATE KEY UPDATE `name`=VALUES(`name`),`is_delete`=VALUES(`is_delete`)")).
			WithArgs(mes[0].Name, mes[0].Deleted, mes[0].Id, mes[1].Name, mes[1].Deleted, mes[1].Id).
			WillReturnResult(sqlmock.NewResult(2, 2))
		mock.ExpectCommit()
		if err := Saves(newCtx(), mes); err != nil {
			t.Fatal(err)
		}
	})
}

func TestDeleteEntities(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		queryName := "test"

		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `t_test` WHERE name LIKE ?")).
			WithArgs(fmt.Sprintf("%%%s%%", queryName)).
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()
		if err := DeleteEntities(newCtx(), &mockEntity{}, NewDbOptions(WithLike("name", queryName))); err != nil {
			t.Fatal(err)
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		if err := DeleteEntities(newCtx(), &mockEntity{}, nil); err == nil {
			t.Fatal("expected err, but no err")
		}
		if err := DeleteEntities(newCtx(), &mockEntity{}, NewDbOptions()); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		queryName := "test"
		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("DELETE FROM `t_test` WHERE name LIKE ?")).
			WithArgs(fmt.Sprintf("%%%s%%", queryName)).
			WillReturnError(errors.New("mock err"))
		mock.ExpectRollback()
		if err := DeleteEntities(newCtx(), &mockEntity{}, NewDbOptions(WithLike("name", queryName))); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
}

func TestWithOrderAndDistinct(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		mock.ExpectQuery(regexp.QuoteMeta("SELECT COUNT(DISTINCT(`name`)) FROM `t_test` WHERE name LIKE ?")).
			WithArgs("%test%").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
		mock.ExpectQuery(regexp.QuoteMeta("SELECT DISTINCT `name` FROM `t_test` WHERE name LIKE ? ORDER BY name ASC")).
			WithArgs("%test%").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("test1").AddRow("test2"))
		opt := NewDbOptions(WithLike("name", "test"), WithDistinctFields("name"))
		opt.AddPage(WithFieldOrderAsc("name"))
		entities, count, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt)
		if err != nil {
			t.Fatal(err)
		}
		if count != 2 {
			t.Fatalf("want: %d, got: %d", 2, count)
		}
		if len(entities) != 2 {
			t.Fatalf("want: %d, got: %d", 2, len(entities))
		}
		assert.Equal(tt, "test1", entities[0].Name)
		assert.Equal(tt, "test2", entities[1].Name)
	})

	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		mock.ExpectQuery(regexp.QuoteMeta("SELECT COUNT(DISTINCT(`name`)) FROM `t_test` WHERE name LIKE ?")).
			WithArgs("%test%").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(2))
		mock.ExpectQuery(regexp.QuoteMeta("SELECT DISTINCT `name` FROM `t_test` WHERE name LIKE ? ORDER BY name DESC")).
			WithArgs("%test%").
			WillReturnRows(sqlmock.NewRows([]string{"name"}).AddRow("test2").AddRow("test1"))
		opt := NewDbOptions(WithLike("name", "test"), WithDistinctFields("name"))
		opt.AddPage(WithFieldOrderDesc("name"))
		entities, count, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt)
		if err != nil {
			t.Fatal(err)
		}
		if count != 2 {
			t.Fatalf("want: %d, got: %d", 2, count)
		}
		if len(entities) != 2 {
			t.Fatalf("want: %d, got: %d", 2, len(entities))
		}
		assert.Equal(tt, "test2", entities[0].Name)
		assert.Equal(tt, "test1", entities[1].Name)
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		opt := NewDbOptions(WithLike("name", "test"), WithDistinctFields(""))
		if _, _, err := ListEntitiesWithPage(newCtx(), &mockEntity{}, opt); err == nil {
			t.Fatal("expected err, but no err")
		}
	})

}

func TestUpdateEntity(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		me := mockEntity{Name: "test-updated"}
		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("UPDATE `t_test` SET `name`=? WHERE name = ?")).
			WithArgs(me.Name, "test").
			WillReturnResult(sqlmock.NewResult(1, 1))
		mock.ExpectCommit()

		err := UpdateEntity(newCtx(), &me, NewDbOptions(WithKV("name", "test")))
		assert.Nil(tt, err)
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		if err := UpdateEntity(newCtx(), &mockEntity{}, nil); err == nil {
			t.Fatal("expected err, but no err")
		}
		if err := UpdateEntity(newCtx(), &mockEntity{}, NewDbOptions()); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		me := mockEntity{Name: "test-updated"}
		mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `t_test` WHERE name = ? ORDER BY `t_test`.`id` LIMIT ?")).
			WithArgs("test", 1).
			WillReturnError(errors.New("mock err"))

		if err := UpdateEntity(newCtx(), &me, NewDbOptions(WithKV("name", "test"))); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		me := mockEntity{Name: "test-updated"}
		mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `t_test` WHERE name = ? ORDER BY `t_test`.`id` LIMIT ?")).
			WithArgs("test", 1).
			WillReturnRows(sqlmock.NewRows([]string{"id", "name", "is_delete"}).AddRow(1, "test", 0))
		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("UPDATE `t_test` SET `name`=? WHERE `id` = ?")).
			WithArgs(me.Name, 1).
			WillReturnError(errors.New("mock err"))
		mock.ExpectRollback()

		if err := UpdateEntity(newCtx(), &me, NewDbOptions(WithKV("name", "test"))); err == nil {
			t.Fatal("expected err, but no err")
		}
	})
}

func TestCount(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		mock.ExpectQuery(regexp.QuoteMeta("SELECT count(*) FROM `t_test` WHERE name = ?")).
			WithArgs("test").
			WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

		v, err := Count(newCtx(), &mockEntity{}, NewDbOptions(WithKV("name", "test")))
		assert.Nil(tt, err)
		assert.Equal(tt, int64(1), v)
	})
}

func TestSave(t *testing.T) {
	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		me := &mockEntity{
			Id:   1,
			Name: "test",
		}
		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("UPDATE `t_test` SET `name`=?,`is_delete`=? WHERE `id` = ?")).
			WithArgs(me.Name, me.Deleted, 1).
			WillReturnResult(sqlmock.NewResult(1, 1))

		mock.ExpectCommit()
		err := Save(newCtx(), me)
		assert.Nil(tt, err)
	})

	dbmock.WithMockDb(t, func(tt *testing.T, mock sqlmock.Sqlmock) {
		me := &mockEntity{
			Name: "test",
		}
		mock.ExpectBegin()
		mock.ExpectExec(regexp.QuoteMeta("INSERT INTO `t_test` (`name`,`is_delete`) VALUES (?,?)")).
			WithArgs(me.Name, me.Deleted).
			WillReturnResult(sqlmock.NewResult(1, 1))

		mock.ExpectCommit()
		err := Save(newCtx(), me)
		assert.Nil(tt, err)
	})
}

func TestGetEntityFields(t *testing.T) {
	entity := &mockEntity{}
	fields := getEntityFields(entity)

	// 验证字段包含预期的数据库列名
	expectedFields := []string{"id", "name", "is_delete"}

	if len(fields) != len(expectedFields) {
		t.Fatalf("expected %d fields, got %d", len(expectedFields), len(fields))
	}

	// 验证每个字段都存在
	fieldMap := make(map[string]bool)
	for _, field := range fields {
		fieldMap[field] = true
	}

	for _, expected := range expectedFields {
		if !fieldMap[expected] {
			t.Fatalf("expected field %s not found in fields: %v", expected, fields)
		}
	}
}

func TestToSnakeCase(t *testing.T) {
	testCases := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "连续大写字母 - HTTPServer",
			input:    "HTTPServer",
			expected: "http_server",
		},
		{
			name:     "连续大写字母 - XMLHttpRequest",
			input:    "XMLHttpRequest",
			expected: "xml_http_request",
		},
		{
			name:     "连续大写字母 - APIKey",
			input:    "APIKey",
			expected: "api_key",
		},
		{
			name:     "连续大写字母 - JSONData",
			input:    "JSONData",
			expected: "json_data",
		},
		{
			name:     "连续大写字母 - UserID",
			input:    "UserID",
			expected: "user_id",
		},
		{
			name:     "普通驼峰命名 - CamelCase",
			input:    "CamelCase",
			expected: "camel_case",
		},
		{
			name:     "普通驼峰命名 - SimpleWord",
			input:    "SimpleWord",
			expected: "simple_word",
		},
		{
			name:     "全大写 - HTML",
			input:    "HTML",
			expected: "html",
		},
		{
			name:     "全大写 - UPPERCASE",
			input:    "UPPERCASE",
			expected: "uppercase",
		},
		{
			name:     "单个字符 - A",
			input:    "A",
			expected: "a",
		},
		{
			name:     "空字符串",
			input:    "",
			expected: "",
		},
		{
			name:     "全小写 - lowercase",
			input:    "lowercase",
			expected: "lowercase",
		},
		{
			name:     "混合复杂情况 - URLHTTPSConnection",
			input:    "URLHTTPSConnection",
			expected: "urlhttps_connection",
		},
		{
			name:     "以小写开头 - myHTTPServer",
			input:    "myHTTPServer",
			expected: "my_http_server",
		},
		{
			name:     "包含数字 - API2Key",
			input:    "API2Key",
			expected: "api2_key",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := toSnakeCase(tc.input)
			assert.Equal(t, tc.expected, result, "输入: %s, 期望: %s, 实际: %s", tc.input, tc.expected, result)
		})
	}
}
