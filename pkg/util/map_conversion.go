package util

import (
	"encoding/json"
	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
)

func ConvertMapTo[T any](ctx base.Context, data map[string]any) (*T, error) {
	v := new(T)
	marshal, err := json.Marshal(data)
	if err != nil {
		ctx.GetLogger().Error("marshal data failed", "err", err)
		return nil, errors.WithStack(err)
	}
	if err := json.Unmarshal(marshal, v); err != nil {
		ctx.GetLogger().Error("unmarshal data failed", "err", err)
		return nil, errors.WithStack(err)
	}
	return v, nil
}
