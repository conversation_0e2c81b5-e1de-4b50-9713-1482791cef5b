package util

import "testing"

func TestAreIntervalsOverlapping(t *testing.T) {
	// 定义测试用例
	tests := []struct {
		name            string
		firstStart      int
		firstEnd        int
		secondStart     int
		secondEnd       int
		expectedOverlap bool
	}{
		{
			name:            "部分重叠_第一个区间在左",
			firstStart:      1,
			firstEnd:        5,
			secondStart:     3,
			secondEnd:       7,
			expectedOverlap: true,
		},
		{
			name:            "部分重叠_第一个区间在右",
			firstStart:      3,
			firstEnd:        7,
			secondStart:     1,
			secondEnd:       5,
			expectedOverlap: true,
		},
		{
			name:            "完全不重叠_第一个区间在左",
			firstStart:      1,
			firstEnd:        3,
			secondStart:     4,
			secondEnd:       6,
			expectedOverlap: false,
		},
		{
			name:            "完全不重叠_第一个区间在右",
			firstStart:      4,
			firstEnd:        6,
			secondStart:     1,
			secondEnd:       3,
			expectedOverlap: false,
		},
		{
			name:            "边界相接",
			firstStart:      1,
			firstEnd:        4,
			secondStart:     4,
			secondEnd:       6,
			expectedOverlap: true,
		},
		{
			name:            "第一个区间包含第二个区间",
			firstStart:      1,
			firstEnd:        10,
			secondStart:     2,
			secondEnd:       5,
			expectedOverlap: true,
		},
		{
			name:            "第二个区间包含第一个区间",
			firstStart:      2,
			firstEnd:        5,
			secondStart:     1,
			secondEnd:       10,
			expectedOverlap: true,
		},
		{
			name:            "相同区间",
			firstStart:      1,
			firstEnd:        5,
			secondStart:     1,
			secondEnd:       5,
			expectedOverlap: true,
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := AreIntervalsOverlapping(
				tt.firstStart,
				tt.firstEnd,
				tt.secondStart,
				tt.secondEnd,
			)
			if got != tt.expectedOverlap {
				t.Errorf("AreIntervalsOverlapping(%d, %d, %d, %d) = %v, want %v",
					tt.firstStart, tt.firstEnd, tt.secondStart, tt.secondEnd,
					got, tt.expectedOverlap)
			}
		})
	}
}
