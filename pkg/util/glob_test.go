package util

import "testing"

func TestIsGlobMatch(t *testing.T) {
	tests := []struct {
		pattern string
		text    string
		want    bool
	}{
		{"src/*.go", "src/main.go", true},
		{"src/*.go", "src/dir/main.go", false},
		{"test?", "test1", true},
		{"test?", "test/", false},
		{"*.txt", "file.txt", true},
		{"src/test.go", "src/test.go", true},
		{"a*b", "a/b", false},
		{"a*b", "ab", true},
		{"a*b", "axb", true},
	}

	for _, tt := range tests {
		t.Run(tt.pattern+"_"+tt.text, func(t *testing.T) {
			got := IsGlobMatch(tt.pattern, tt.text)
			if got != tt.want {
				t.Errorf("IsGlobMatch(%q, %q) = %v, want %v",
					tt.pattern, tt.text, got, tt.want)
			}
		})
	}
}
