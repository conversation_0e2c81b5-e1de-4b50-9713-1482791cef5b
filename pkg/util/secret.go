package util

import "strings"

func MaskSecret(secret string) string {
	if secret == "" {
		return "****"
	}

	const (
		prefixLen = 4
		suffixLen = 4
		maskLen   = 4
		minLength = 8
	)

	runes := []rune(secret)
	length := len(runes)

	if length < minLength {
		switch {
		case length <= 2:
			return "****"
		default:
			return string(runes[0]) + "****"
		}
	}

	var builder strings.Builder
	builder.WriteString(string(runes[:prefixLen]))
	builder.WriteString(strings.Repeat("*", maskLen))
	builder.WriteString(string(runes[length-suffixLen:]))

	return builder.String()
}
