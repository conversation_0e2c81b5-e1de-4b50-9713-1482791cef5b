package util

import (
	"reflect"
	"testing"
	"time"
)

func TestParseDateTimeWithZone(t *testing.T) {
	type args struct {
		timeStr string
	}
	tests := []struct {
		name    string
		args    args
		want    time.Time
		wantErr bool
	}{
		{
			name: "normal case",
			args: args{
				timeStr: "2025-03-06 07:41:17 UTC",
			},
			want:    time.Date(2025, 3, 6, 7, 41, 17, 0, time.UTC),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseDateTimeWithZone(tt.args.timeStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("ParseDateTimeWithZone() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.<PERSON><PERSON><PERSON>("ParseDateTimeWithZone() got = %v, want %v", got, tt.want)
			}
		})
	}
}
