package util

import (
	"fmt"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
)

// SafeGoroutineFunc 使用这个函数包装需要使用go func启动的函数，防止协程panic导致整个服务挂掉
func SafeGoroutineFunc(ctx base.Context, f func()) {
	defer func() {
		if r := recover(); r != nil {
			errors.AlertError(ctx, errors.AlertScopeGoroutine, []string{ctx.GetUid(), ctx.GetTraceId()}, "panic in goroutine", fmt.Errorf("%v", r))
		}
	}()
	f()
}
