package util

import (
	"fmt"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	commonerrors "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/errors/codes"
	"strconv"
)

const (
	githubIssuePrefix     string = "issueComment-"
	githubPrCommentPrefix string = "prComment-"
)

func GenerateGithubCommentIdStr(ctx base.Context, commentId int64, commentType codeplatform.CommentType) (string, error) {
	switch commentType {
	case codeplatform.CommentTypeDiscussionNote:
		return fmt.Sprintf("%s%s", githubPrCommentPrefix, strconv.FormatInt(commentId, 10)), nil
	case codeplatform.CommentTypeCommon:
		return fmt.Sprintf("%s%s", githubIssuePrefix, strconv.FormatInt(commentId, 10)), nil
	default:
		ctx.GetLogger().Error("Invalid comment type", "comment type", commentType)
		return "", commonerrors.New(codes.ServerInternalError)
	}
}
