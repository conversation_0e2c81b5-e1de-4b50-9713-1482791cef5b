package util

import (
	"fmt"
	"testing"
)

func TestMaskSecret(t *testing.T) {
	testCases := []string{
		"",                  // 空字符串
		"a",                 // 单字符
		"ab",                // 两个字符
		"abc",               // 三个字符
		"abcd",              // 四个字符
		"abcde",             // 五个字符
		"abcdef",            // 六个字符
		"abcdefgh",          // 八个字符
		"abcdefghi",         // 九个字符
		"abcdefghijklmn",    // 较长字符串
		"1234567890123456",  // 数字字符串
		"<EMAIL>", // 邮箱格式
	}

	for _, tc := range testCases {
		masked := MaskSecret(tc)
		fmt.Printf("Original: %-20s | Masked: %s\n", tc, masked)
	}
}
