package redis

import (
	"context"
	"github.com/redis/go-redis/v9"
	"sync"
	"time"
)

var (
	redisClient *redis.Client
	once        sync.Once
)

func GetClient() *redis.Client {
	return redisClient
}
func Setup(opts *redis.Options) (*redis.Client, error) {
	var err error
	once.Do(func() {
		redisClient, err = initRedis(opts)
	})
	return redisClient, err
}

func initRedis(options *redis.Options) (*redis.Client, error) {
	if options.PoolSize == 0 {
		options.PoolSize = 1000
	}

	if options.MinIdleConns == 0 {
		options.MinIdleConns = 100
	}

	if options.MaxIdleConns == 0 {
		options.MaxIdleConns = 200
	}

	if options.ConnMaxIdleTime == 0 {
		options.ConnMaxIdleTime = 2 * time.Minute
	}

	if options.MaxRetries == 0 {
		options.MaxRetries = 3
	}
	client := redis.NewClient(options)
	if _, err := client.Ping(context.Background()).Result(); err != nil {
		return nil, err
	}
	return client, nil
}
