package dao

import (
	"strings"

	"github.com/pkg/errors"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/github"
	"gorm.io/gorm"
)

// CreateOIDCInstallation 创建OIDC安装记录
func CreateOIDCInstallation(ctx base.Context, req *github.CreateOIDCInstallationRequest) (*github.OIDCInstallation, error) {
	installation := &github.OIDCInstallation{
		AppId:          req.AppId,
		InstallationId: req.InstallationId,
		AccountId:      req.AccountId,
		AccountLogin:   req.AccountLogin,
		AccountType:    req.AccountType,
		AppSlug:        req.AppSlug,
		Repositories:   req.Repositories,
		Permissions:    req.Permissions,
		Status:         github.OIDCInstallationStatusActive,
	}

	if err := GetDB(ctx).Create(installation).Error; err != nil {
		ctx.GetLogger().Error("failed to create OIDC installation", "err", err, "req", req)
		return nil, errors.WithStack(err)
	}

	ctx.GetLogger().Info("OIDC installation created", "installationId", installation.InstallationId, "appId", installation.AppId)
	return installation, nil
}

// GetOIDCInstallationByInstallationId 根据Installation ID获取记录
func GetOIDCInstallationByInstallationId(ctx base.Context, installationId int64) (*github.OIDCInstallation, error) {
	var installation github.OIDCInstallation
	err := GetDB(ctx).Where("installation_id = ? AND status = ?", installationId, github.OIDCInstallationStatusActive).First(&installation).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		ctx.GetLogger().Error("failed to get OIDC installation", "installationId", installationId, "err", err)
		return nil, errors.WithStack(err)
	}
	return &installation, nil
}

// GetOIDCInstallationByRepository 根据仓库获取安装记录
func GetOIDCInstallationByRepository(ctx base.Context, appId int64, owner, repo string) (*github.OIDCInstallation, error) {
	var installations []github.OIDCInstallation
	err := GetDB(ctx).Where("app_id = ? AND status = ?", appId, github.OIDCInstallationStatusActive).Find(&installations).Error
	if err != nil {
		ctx.GetLogger().Error("failed to query OIDC installations", "appId", appId, "err", err)
		return nil, errors.WithStack(err)
	}

	// 检查哪个安装记录包含目标仓库
	for _, installation := range installations {
		if installation.IsRepositoryAuthorized(owner, repo) {
			return &installation, nil
		}
	}

	return nil, nil
}

// UpdateOIDCInstallation 更新OIDC安装记录
func UpdateOIDCInstallation(ctx base.Context, installationId int64, req *github.UpdateOIDCInstallationRequest) error {
	updates := make(map[string]interface{})
	
	if req.Repositories != nil {
		updates["repositories"] = req.Repositories
	}
	if req.Permissions != nil {
		updates["permissions"] = req.Permissions
	}
	if req.Status != "" {
		updates["status"] = req.Status
	}

	if len(updates) == 0 {
		return nil
	}

	err := GetDB(ctx).Model(&github.OIDCInstallation{}).Where("installation_id = ?", installationId).Updates(updates).Error
	if err != nil {
		ctx.GetLogger().Error("failed to update OIDC installation", "installationId", installationId, "err", err)
		return errors.WithStack(err)
	}

	ctx.GetLogger().Info("OIDC installation updated", "installationId", installationId, "updates", updates)
	return nil
}

// DeleteOIDCInstallation 软删除OIDC安装记录
func DeleteOIDCInstallation(ctx base.Context, installationId int64) error {
	err := UpdateOIDCInstallation(ctx, installationId, &github.UpdateOIDCInstallationRequest{
		Status: github.OIDCInstallationStatusDeleted,
	})
	if err != nil {
		return err
	}

	ctx.GetLogger().Info("OIDC installation soft deleted", "installationId", installationId)
	return nil
}

// ListOIDCInstallations 查询OIDC安装记录列表
func ListOIDCInstallations(ctx base.Context, query *github.OIDCInstallationQuery) ([]*github.OIDCInstallation, error) {
	db := GetDB(ctx).Model(&github.OIDCInstallation{})

	// 构建查询条件
	if query.AppId != nil {
		db = db.Where("app_id = ?", *query.AppId)
	}
	if query.InstallationId != nil {
		db = db.Where("installation_id = ?", *query.InstallationId)
	}
	if query.AccountId != nil {
		db = db.Where("account_id = ?", *query.AccountId)
	}
	if query.AccountLogin != nil {
		db = db.Where("account_login = ?", *query.AccountLogin)
	}
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}

	var installations []*github.OIDCInstallation
	err := db.Order("gmt_create DESC").Find(&installations).Error
	if err != nil {
		ctx.GetLogger().Error("failed to list OIDC installations", "query", query, "err", err)
		return nil, errors.WithStack(err)
	}

	// 如果指定了仓库过滤条件，需要进一步过滤
	if query.Repository != nil {
		parts := strings.Split(*query.Repository, "/")
		if len(parts) == 2 {
			owner, repo := parts[0], parts[1]
			var filtered []*github.OIDCInstallation
			for _, installation := range installations {
				if installation.IsRepositoryAuthorized(owner, repo) {
					filtered = append(filtered, installation)
				}
			}
			installations = filtered
		}
	}

	return installations, nil
}

// CountOIDCInstallations 统计OIDC安装记录数量
func CountOIDCInstallations(ctx base.Context, query *github.OIDCInstallationQuery) (int64, error) {
	db := GetDB(ctx).Model(&github.OIDCInstallation{})

	// 构建查询条件
	if query.AppId != nil {
		db = db.Where("app_id = ?", *query.AppId)
	}
	if query.AccountId != nil {
		db = db.Where("account_id = ?", *query.AccountId)
	}
	if query.AccountLogin != nil {
		db = db.Where("account_login = ?", *query.AccountLogin)
	}
	if query.Status != nil {
		db = db.Where("status = ?", *query.Status)
	}

	var count int64
	err := db.Count(&count).Error
	if err != nil {
		ctx.GetLogger().Error("failed to count OIDC installations", "query", query, "err", err)
		return 0, errors.WithStack(err)
	}

	return count, nil
}

// IsRepositoryAuthorizedForOIDC 检查仓库是否已授权给OIDC App
func IsRepositoryAuthorizedForOIDC(ctx base.Context, appId int64, owner, repo string) (bool, *github.OIDCInstallation, error) {
	installation, err := GetOIDCInstallationByRepository(ctx, appId, owner, repo)
	if err != nil {
		return false, nil, err
	}
	if installation == nil {
		return false, nil, nil
	}
	return true, installation, nil
}

// GetOIDCInstallationStats 获取OIDC安装统计信息
func GetOIDCInstallationStats(ctx base.Context, appId int64) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总安装数
	totalCount, err := CountOIDCInstallations(ctx, &github.OIDCInstallationQuery{
		AppId:  &appId,
		Status: &github.OIDCInstallationStatusActive,
	})
	if err != nil {
		return nil, err
	}
	stats["total_installations"] = totalCount

	// 按账户类型统计
	var accountTypeStats []struct {
		AccountType string `json:"account_type"`
		Count       int64  `json:"count"`
	}
	err = GetDB(ctx).Model(&github.OIDCInstallation{}).
		Select("account_type, COUNT(*) as count").
		Where("app_id = ? AND status = ?", appId, github.OIDCInstallationStatusActive).
		Group("account_type").
		Find(&accountTypeStats).Error
	if err != nil {
		ctx.GetLogger().Error("failed to get account type stats", "appId", appId, "err", err)
		return nil, errors.WithStack(err)
	}
	stats["by_account_type"] = accountTypeStats

	// 最近安装的记录
	var recentInstallations []*github.OIDCInstallation
	err = GetDB(ctx).Where("app_id = ? AND status = ?", appId, github.OIDCInstallationStatusActive).
		Order("gmt_create DESC").
		Limit(5).
		Find(&recentInstallations).Error
	if err != nil {
		ctx.GetLogger().Error("failed to get recent installations", "appId", appId, "err", err)
		return nil, errors.WithStack(err)
	}
	stats["recent_installations"] = recentInstallations

	return stats, nil
}
