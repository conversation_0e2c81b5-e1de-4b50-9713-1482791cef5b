package logger

import (
	"github.com/gin-gonic/gin"
	"gopkg.in/natefinch/lumberjack.v2"
	"io"
	"log/slog"
	"os"
	"os/user"
	"path/filepath"
	"sync"
)

var Level = new(slog.LevelVar)

var InitLog = sync.OnceFunc(func() {
	appName := os.Getenv("APP_NAME")
	if appName == "" {
		appName = "agent-controller"
	}
	appHome := os.Getenv("APP_HOME")
	if appHome == "" {
		u, err := user.Current()
		if err != nil {
			appHome = "/home/<USER>/agent-controller"
		} else {
			appHome = filepath.Join(u.HomeDir, appName)
		}
	}

	logDir := filepath.Join(appHome, "logs")
	//确保日志目录存在
	_ = os.MkdirAll(logDir, 0755)
	logFileName := filepath.Join(logDir, appName+".log")

	logFile := &lumberjack.Logger{
		Filename:   logFileName,
		MaxSize:    100, //单日志文件最大100MB
		MaxBackups: 3,   //最多保留3个备份
		MaxAge:     7,   //最多保留7天
		Compress:   true,
		LocalTime:  true,
	}

	//定义MultiWriter，将日志同时输出到标准输出与日志文件
	multiWriter := io.MultiWriter(os.Stdout, logFile)
	//创建 slog 的 JSONHandler
	jsonHandler := slog.NewJSONHandler(multiWriter, &slog.HandlerOptions{Level: Level})
	//设置默认的slog日志记录器，输出日志到文件
	slog.SetDefault(slog.New(jsonHandler))

	//gin日志输出到文件
	gin.DisableConsoleColor()
	gin.DefaultWriter = multiWriter
	gin.DefaultErrorWriter = multiWriter
})

func init() {
	InitLog()
}
