package cmd

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/cobra"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/database"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"log/slog"
	"net/http"
	"net/url"
	"os"
	"strings"
	"text/template"
	"time"
)

var (
	agentRuntimeImage                   string
	agentRuntimeDeployTemplateFilePath  string
	agentRuntimeServiceTemplateFilePath string
	agentRuntimeNamespace               string
	podPort                             = 8000
	sessionCheckInterval                = 30 * time.Second
	sessionCheckTimeout                 = 30 * time.Minute
	podReadyTimeout                     = 10 * time.Minute
)

// 约束条件： deployment / service / pod 都具备标签 app={{ .Name }}，三者名字相同
// service以80端口对外提供服务，pod以8000端口对外提供服务

/*	 模版示例
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: {{ .Name }}
  name: {{ .Name }}
  namespace: {{ .Namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Name }}
  template:
    metadata:
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: '8000'
        prometheus.io/scrape: 'true'
      labels:
        app: {{ .Name }}
    spec:
      containers:
        - env:
            - name: SELF_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.podIP
          envFrom:
            - secretRef:
                name: agent-runtime
            - configMapRef:
                name: agent-runtime
          image: {{ .Image }}
          imagePullPolicy: Always
          name: agent-runtime
          ports:
            - containerPort: 8000
              name: agent-port
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /health
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
          resources:
            limits:
              cpu: '1'
              memory: 2Gi
            requests:
              cpu: '1'
              memory: 2Gi
          startupProbe:
            failureThreshold: 5
            httpGet:
              path: /health
              port: 8000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: {{ .Name }}
  name: {{ .Name }}
  namespace: {{ .Namespace }}
spec:
  internalTrafficPolicy: Cluster
  ipFamilies:
    - IPv4
  ipFamilyPolicy: SingleStack
  ports:
    - name: agent-port
      port: 80
      protocol: TCP
      targetPort: 8000
  selector:
    app: {{ .Name }}
  sessionAffinity: None
  type: ClusterIP
*/

// newARDeployer 创建新的 Cobra 命令
func newARDeployer() *cobra.Command {
	serverCmd := &cobra.Command{
		Use:   "ar-deploy",
		Short: "Deploy Agent Runtime to specified image",
		Run:   arDeploy,
	}

	serverCmd.Flags().StringVarP(&agentRuntimeImage, "image", "i", "", "agent runtime镜像")
	serverCmd.Flags().StringVarP(&configFile, "config", "c", "", "agent controller的配置文件")
	serverCmd.Flags().StringVarP(&agentRuntimeNamespace, "namespace", "", "lingma-agents", "AgentRuntime的命名空间")
	serverCmd.Flags().StringVarP(&agentRuntimeDeployTemplateFilePath, "deploy-template", "", "/tmp/deploy/agent-runtime/deployment.yaml", "deployment模版文件")
	serverCmd.Flags().StringVarP(&agentRuntimeServiceTemplateFilePath, "service-template", "", "/tmp/deploy/agent-runtime/service.yaml", "service模版文件")
	return serverCmd
}

func arDeploy(cmd *cobra.Command, args []string) {
	l := slog.Default()
	ctx := context.TODO()

	initDatabaseOrExit(ctx, l)
	cfg, err := rest.InClusterConfig()
	if err != nil {
		exitWithError(l, "load kubernetes config failed", err)
	}
	cs := kubernetes.NewForConfigOrDie(cfg)
	dynamicClient := dynamic.NewForConfigOrDie(cfg)

	newSvcName, err := createNewDeployAndService(ctx, cs, dynamicClient, l, agentRuntimeNamespace, agentRuntimeImage, agentRuntimeDeployTemplateFilePath, agentRuntimeServiceTemplateFilePath)
	if err != nil {
		exitWithError(l, "load deployment and service failed", err)
	}

	oldEndpoint := config.Get(config.KeyAgentRuntimeEndpoint)
	newEndpoint := fmt.Sprintf("http://%s", newSvcName)
	if err := config.Set(database.GetDatabase(), config.GroupComponent, config.KeyAgentRuntimeEndpoint, newEndpoint, "*"); err != nil {
		exitWithError(l, "set agent runtime endpoint failed", err)
	}
	l.Info("agent runtime endpoint update success", "endpoint", newEndpoint, "oldEndpoint", oldEndpoint)

	if oldEndpoint == "" {
		l.Warn("old agent runtime endpoint not found")
		os.Exit(0)
	}

	oldSvcName := getOldServiceName(l, oldEndpoint)
	if oldSvcName == "" {
		l.Warn("old agent runtime service name not found", "endpoint", oldEndpoint)
		os.Exit(0)
	}

	if waitForAllSessionsEmpty(ctx, cs, oldSvcName, agentRuntimeNamespace, l) {
		scaleDeployment(ctx, cs, agentRuntimeNamespace, oldSvcName, 0, l)
		printAndDeleteOldResources(ctx, cs, agentRuntimeNamespace, oldSvcName, l)
	}
}

func exitWithError(l *slog.Logger, message string, err error) {
	l.Error(message, "error", err)
	os.Exit(1)
}

func scaleDeployment(ctx context.Context, cs *kubernetes.Clientset, namespace, deploymentName string, replicas int32, l *slog.Logger) {
	scale, err := cs.AppsV1().Deployments(namespace).GetScale(ctx, deploymentName, metav1.GetOptions{})
	if err != nil {
		l.Error("failed to get scale for deployment", "deployment", deploymentName, "error", err)
		return
	}

	scale.Spec.Replicas = replicas
	_, err = cs.AppsV1().Deployments(namespace).UpdateScale(ctx, deploymentName, scale, metav1.UpdateOptions{})
	if err != nil {
		l.Error("failed to update scale for deployment", "deployment", deploymentName, "error", err)
		return
	}

	l.Info("scaled deployment to zero replicas", "deployment", deploymentName)
}

func createNewDeployAndService(ctx context.Context, cs *kubernetes.Clientset, dynamicClient dynamic.Interface, l *slog.Logger,
	namespace, image string, deploymentTemplateFilePath string, serviceTemplateFilePath string) (string, error) {

	deployTmpl, err := template.ParseFiles(deploymentTemplateFilePath)
	if err != nil {
		return "", fmt.Errorf("parse deployment template failed: %w", err)
	}
	serviceTmpl, err := template.ParseFiles(serviceTemplateFilePath)
	if err != nil {
		return "", fmt.Errorf("parse service template failed: %w", err)
	}
	suffix := time.Now().Unix()
	name := fmt.Sprintf("agent-runtime-%d", suffix)
	deployData := map[string]string{
		"Image":     image,
		"Name":      name,
		"Namespace": namespace,
	}
	var deploy bytes.Buffer
	if err := deployTmpl.Execute(&deploy, deployData); err != nil {
		return "", fmt.Errorf("execute deployment template failed: %w", err)
	}
	svcData := map[string]string{
		"Name":      name,
		"Namespace": namespace,
	}
	var service bytes.Buffer
	if err := serviceTmpl.Execute(&service, svcData); err != nil {
		return "", fmt.Errorf("execute service template failed: %w", err)
	}
	l.Info("agent runtime deployment and service", "deployment", deploy.String(), "service", service.String())
	deployYaml := deploy.String()
	serviceYaml := service.String()

	if err := applyYAML(ctx, dynamicClient, deployYaml); err != nil {
		return "", fmt.Errorf("apply deployment yaml failed: %w", err)
	}
	if err := applyYAML(ctx, dynamicClient, serviceYaml); err != nil {
		return "", fmt.Errorf("apply service yaml failed: %w", err)
	}

	if err := waitForPodsReady(ctx, cs, name, namespace, l); err != nil {
		return "", fmt.Errorf("waiting for pods ready failed: %w", err)
	}
	l.Info("agent runtime deployment and service created", "name", name)
	return name, nil
}

func applyYAML(ctx context.Context, dynamicClient dynamic.Interface, yamlContent string) error {
	decoder := yaml.NewYAMLOrJSONDecoder(strings.NewReader(yamlContent), 4096)
	var rawObj map[string]interface{}
	if err := decoder.Decode(&rawObj); err != nil {
		return fmt.Errorf("failed to decode YAML: %w", err)
	}

	unstructuredObj := &unstructured.Unstructured{Object: rawObj}
	kind := unstructuredObj.GetKind()
	namespace := unstructuredObj.GetNamespace()
	if namespace == "" {
		namespace = agentRuntimeNamespace
	}

	var gvr schema.GroupVersionResource
	switch kind {
	case "Deployment":
		gvr = schema.GroupVersionResource{Group: "apps", Version: "v1", Resource: "deployments"}
	case "Service":
		gvr = schema.GroupVersionResource{Group: "", Version: "v1", Resource: "services"}
	default:
		return fmt.Errorf("unsupported resource kind: %s", kind)
	}

	_, err := dynamicClient.Resource(gvr).Namespace(namespace).Create(ctx, unstructuredObj, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("failed to apply %s: %w", kind, err)
	}

	return nil
}

func waitForPodsReady(ctx context.Context, cs *kubernetes.Clientset, name, namespace string, l *slog.Logger) error {
	timeoutCtx, cancel := context.WithTimeout(ctx, podReadyTimeout)
	defer cancel()

	labelSelector := fmt.Sprintf("app=%s", name)

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("timed out waiting for pods to be ready: %w", timeoutCtx.Err())
		default:
			pods, err := cs.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{LabelSelector: labelSelector})
			if err != nil {
				return fmt.Errorf("failed to list pods: %w", err)
			}
			if len(pods.Items) == 0 {
				l.Info("No pods found, waiting for pods to be ready", "name", name)
				time.Sleep(5 * time.Second)
				continue
			}

			allReady := true
			for _, pod := range pods.Items {
				ready := false
				containersReady := false
				for _, condition := range pod.Status.Conditions {
					if condition.Type == corev1.PodReady && condition.Status == corev1.ConditionTrue {
						ready = true
					}
					if condition.Type == corev1.ContainersReady && condition.Status == corev1.ConditionTrue {
						containersReady = true
					}
				}
				if !ready || !containersReady {
					allReady = false
					break
				}
			}

			if allReady {
				l.Info("All pods are ready and have passed readiness probe", "name", name)
				return nil
			}

			l.Info("Waiting for pods to be ready and pass readiness probe", "name", name)
			time.Sleep(5 * time.Second)
		}
	}
}

func getOldServiceName(l *slog.Logger, arEndpoint string) string {
	if arEndpoint == "" {
		return ""
	}
	parsedURL, err := url.Parse(arEndpoint)
	if err != nil {
		l.Error("Error parsing URL", "err", err)
		return ""
	}
	hostParts := strings.Split(parsedURL.Hostname(), ".")
	if len(hostParts) > 0 {
		return hostParts[0]
	}
	return ""
}

func waitForAllSessionsEmpty(ctx context.Context, cs *kubernetes.Clientset, oldSvcName, namespace string, l *slog.Logger) bool {
	timeoutCtx, cancel := context.WithTimeout(ctx, sessionCheckTimeout)
	defer cancel()

	for {
		select {
		case <-timeoutCtx.Done():
			l.Error("timed out waiting for all sessions to be empty")
			return false
		default:
			pods, err := cs.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{
				LabelSelector: fmt.Sprintf("app=%s", oldSvcName),
			})
			if err != nil {
				l.Error("failed to list pods for old service", "error", err)
				return false
			}

			allSessionsEmpty := true
			for _, pod := range pods.Items {
				if pod.Status.Phase == corev1.PodRunning {
					if !checkPodSessionsEmpty(pod, l) {
						allSessionsEmpty = false
						break
					}
				}
			}

			if allSessionsEmpty {
				l.Info("All sessions are empty for old service", "service", oldSvcName)
				return true
			}

			l.Info("Waiting for all sessions to be empty", "service", oldSvcName)
			time.Sleep(sessionCheckInterval)
		}
	}
}

func checkPodSessionsEmpty(pod corev1.Pod, l *slog.Logger) bool {
	podIP := pod.Status.PodIP
	url := fmt.Sprintf("http://%s:%d/v1/agent-runtime/sessions", podIP, podPort)

	// 创建一个带有超时的 HTTP 客户端
	client := &http.Client{
		Timeout: 5 * time.Second, // 设置超时时间为5秒
	}

	resp, err := client.Get(url)
	if err != nil {
		l.Error("failed to get sessions from pod", "pod", pod.Name, "error", err)
		return false
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		l.Error("failed to get sessions from pod", "pod", pod.Name, "statusCode", resp.StatusCode)
		return false
	}

	var result struct {
		Code string `json:"code"`
		Data struct {
			Sessions []struct {
				SessionID string `json:"session_id"`
				AgentID   string `json:"agent_id"`
				AbortURL  string `json:"abort_url"`
			} `json:"sessions"`
		} `json:"data"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		l.Error("failed to decode response", "pod", pod.Name, "error", err)
		return false
	}

	return len(result.Data.Sessions) == 0
}

func initDatabaseOrExit(ctx context.Context, l *slog.Logger) {
	cfg, err := initConfig(configFile)
	if err != nil {
		exitWithError(l, "Init config failed. Exit.", err)
	}
	databaseConfig := cfg.Database
	db, err := database.SetupDB(&database.Config{
		Host:                   databaseConfig.Host,
		Port:                   databaseConfig.Port,
		User:                   databaseConfig.User,
		Password:               databaseConfig.Password,
		Driver:                 databaseConfig.Driver,
		Name:                   databaseConfig.Name,
		MaxIdleConns:           databaseConfig.MaxIdleConns,
		MaxOpenConns:           databaseConfig.MaxOpenConns,
		ConnMaxLifetimeSeconds: databaseConfig.ConnMaxLifetimeSeconds,
	})
	if err != nil {
		exitWithError(l, "fail to init database", err)
	}
	if err = config.Init(ctx, db, time.Second*30); err != nil {
		exitWithError(l, "fail to reload config from db", err)
	}
}

func printAndDeleteOldResources(ctx context.Context, cs *kubernetes.Clientset, namespace, name string, l *slog.Logger) {
	// 获取并打印 Deployment
	deployment, err := cs.AppsV1().Deployments(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		l.Error("failed to get deployment", "name", name, "error", err)
	} else {
		deploymentJSON, _ := json.MarshalIndent(deployment, "", "  ")
		l.Info("Deployment JSON", "deployment", string(deploymentJSON))
	}

	// 获取并打印 Service
	service, err := cs.CoreV1().Services(namespace).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		l.Error("failed to get service", "name", name, "error", err)
	} else {
		serviceJSON, _ := json.MarshalIndent(service, "", "  ")
		l.Info("Service JSON", "service", string(serviceJSON))
	}

	// 删除 Deployment
	if err := cs.AppsV1().Deployments(namespace).Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
		l.Error("failed to delete deployment", "name", name, "error", err)
	} else {
		l.Info("Deleted deployment", "name", name)
	}

	// 删除 Service
	if err := cs.CoreV1().Services(namespace).Delete(ctx, name, metav1.DeleteOptions{}); err != nil {
		l.Error("failed to delete service", "name", name, "error", err)
	} else {
		l.Info("Deleted service", "name", name)
	}
}
