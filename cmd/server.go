package cmd

import (
	"context"
	"fmt"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/apiserver"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/agent"
	agent_connect "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/agent-connect"
	agent_runtime "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/agent-runtime"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/authorization"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/callback"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/codeplatform"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/cronjob"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/event"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/feature"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/identity"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/overview"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/repository"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/sandbox"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/stats"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/user"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/core/webhook"
	"log/slog"
	"os"
)

var configFile string

func newServerCommand() *cobra.Command {
	serverCmd := &cobra.Command{
		Use:   "server",
		Short: "Start a lingma ai agent  controller",
		Run:   run,
	}

	serverCmd.Flags().StringVarP(&configFile, "config", "c", "", "server的配置文件")
	return serverCmd
}

func run(cmd *cobra.Command, args []string) {
	c := NewCoreWithConfig(configFile)

	slog.Info("Start newCore...")
	if err := c.Start(context.Background()); err != nil {
		slog.Error("Start newCore failed.", "error", err)
		os.Exit(1)
	}
}

func NewCoreWithConfig(configFile string) comncore.Core {
	cfg, err := initConfig(configFile)
	if err != nil {
		slog.Error("Init config failed. Exit.", "error", err)
		os.Exit(1)
	}

	//初始化core
	slog.Info("New newCore...")
	return core.NewCore(
		core.WithConfig(cfg),
		core.WithApiServer(apiserver.New(cfg.Server.Address, cfg.Server.InnerAddress, cfg.Server.MetricsAddress, cfg.Server.PprofAddress)),
		core.WithAgentService(agent.New()),
		core.WithCodePlatformService(codeplatform.New()),
		core.WithSessionService(session.New()),
		core.WithWebhookService(webhook.New()),
		core.WithAgentRuntimeService(agent_runtime.New()),
		core.WithSandboxService(sandbox.New()),
		core.WithIdentityService(identity.New()),
		core.WithEventService(event.New()),
		core.WithCronjobService(cronjob.New()),
		core.WithCallbackService(callback.New()),
		core.WithAgentConnectService(agent_connect.New()),
		core.WithStatsService(stats.New()),
		core.WithRepositoryService(repository.New()),
		core.WithFeatureService(feature.New()),
		core.WithOverviewService(overview.New()),
		core.WithAuthorizationService(authorization.New()),
		core.WithUserService(user.New()),
	)
}

func initConfig(configFile string) (*config.Config, error) {
	if len(configFile) == 0 {
		return nil, fmt.Errorf("there is not a cfg file")
	}

	viper.SetConfigFile(configFile)
	viper.AutomaticEnv()

	cfg := &config.Config{}
	if err := viper.ReadInConfig(); err == nil {
		slog.Info("Read cfg from file:", "file", configFile)
		// Unmarshal 将配置文件转成对象
		err = viper.Unmarshal(cfg)
		return cfg, err
	} else {
		return nil, err
	}
}
