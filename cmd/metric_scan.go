package cmd

import (
	"context"
	"github.com/spf13/cobra"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/page"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/session"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper"
	"log/slog"
	"os"
)

var (
	scanMetricUserId         string
	scanSessionFromTimestamp int64
	scanSessionToTimestamp   int64
)

/*
    Usage:
		./agent-controller metric-scan -c config.yaml [--user-id 12345678] [--from 1748248879338] [--to 1748248879338]
	其中[]表示可选参数，
		- --user-id表示扫描指定用户的merge request
		- --from表示扫描指定时间戳之后的merge request
		- --to表示扫描指定时间戳之前的merge request
*/

func newMetricScanCommand() *cobra.Command {
	metricScanCmd := &cobra.Command{
		Use:   "metric-scan",
		Short: "Scan merge request metrics for CodeReviewAgent",
		Run:   metricScan,
	}
	metricScanCmd.Flags().StringVarP(&configFile, "config", "c", "", "agent controller的配置文件")
	metricScanCmd.Flags().StringVarP(&scanMetricUserId, "user-id", "", "", "扫描指定用户的merge request")
	metricScanCmd.Flags().Int64VarP(&scanSessionFromTimestamp, "from", "", 0, "扫描指定时间戳之后的merge request")
	metricScanCmd.Flags().Int64VarP(&scanSessionToTimestamp, "to", "", 0, "扫描指定时间戳之前的merge request")
	return metricScanCmd
}

func metricScan(cmd *cobra.Command, args []string) {
	c := NewCoreWithConfig(configFile)

	slog.Info("Start newCore...")
	go func() {
		if err := c.Start(context.Background()); err != nil {
			slog.Error("Start newCore failed.", "error", err)
			os.Exit(1)
		}
	}()
	<-c.Started()
	defer c.Stop()
	ctx := base.NewContextForAdmin(helper.NewTraceId(), "cmd-metric-scan")
	sessionIds, err := loadInterestedSessionIds(ctx)
	if err != nil {
		ctx.GetLogger().Error("loadInterestedSessionIds failed.", "error", err)
		os.Exit(1)
	}
	ctx.GetLogger().Info("loadInterestedSessionIds", "sessionIds", sessionIds)
	var failedSessionIds []string
	for _, sid := range sessionIds {
		ctx = base.NewContextWithUserOverride(ctx, sid, ctx.GetSiteType())
		ctx.GetLogger().Info("ScanMergeRequestMetrics", "sessionId", sid)
		if err := core.GetCoreService().GetSessionService().ScanMergeRequestMetrics(ctx, sid, true); err != nil {
			ctx.GetLogger().Error("ScanMergeRequestMetrics failed.", "sessionId", sid, "error", err)
			failedSessionIds = append(failedSessionIds, sid)
			continue
		}
		ctx.GetLogger().Info("ScanMergeRequestMetrics completed.", "sessionId", sid)
	}
	if len(failedSessionIds) > 0 {
		ctx.GetLogger().Info("ScanMergeRequestMetrics finished with some failure", "sessionIds", failedSessionIds)
	} else {
		ctx.GetLogger().Info("ScanMergeRequestMetrics finished")
	}
}

func loadInterestedSessionIds(ctx base.Context) ([]string, error) {
	listSessionReq := &session.ListSessionsRequest{}
	if scanMetricUserId != "" {
		ctx = base.NewContextForBareUser(scanMetricUserId, ctx.GetTraceId(), "cmd-metric-scan", ctx.GetSiteType())
	}
	if scanSessionFromTimestamp > 0 {
		listSessionReq.FromTimestamp = scanSessionFromTimestamp
	}
	if scanSessionToTimestamp > 0 {
		listSessionReq.ToTimestamp = scanSessionToTimestamp
	}
	resp, err := core.GetCoreService().GetSessionService().ListSessions(ctx, listSessionReq)
	if err != nil {
		return nil, err
	}
	return page.TransferSlice(resp.Items, func(item *session.Info) string { return item.SessionId }), nil
}
