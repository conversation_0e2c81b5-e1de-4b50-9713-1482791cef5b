skip-review:
    file-limit:
        reason: "Skip reason: Number of changed files exceeds limit"
        description: "The agent supports setting a maximum number of files for smart review. Changes exceeding this limit are automatically skipped. Contact administrator for settings adjustment if smart review is needed."
        item-title: "Changed Files"
        item-description: "%d (Maximum limit %d files)"
    line-limit:
        reason: "Skip reason: Number of changed lines exceeds limit"
        description: "The agent supports setting a maximum number of lines for smart review. Changes exceeding this limit are automatically skipped. Contact administrator for settings adjustment if smart review is needed."
        item-title: "Changed Lines"
        item-description: "%d (Maximum limit %d lines)"
    commit-limit:
        reason: "Skip reason: Number of commits exceeds limit"
        description: "The agent supports setting a maximum number of commits for smart review. Changes exceeding this limit are automatically skipped. Contact administrator for settings adjustment if smart review is needed."
        item-title: "Commit Count"
        item-description: "%d (Maximum limit %d)"
    title-keyword-filter:
        reason: "Skip reason: Title contains filtered keywords"
        description: "The agent supports title keyword filtering. Reviews are automatically skipped when keywords are detected. Contact administrator for settings adjustment if smart review is needed."
        item-title: "Keywords"
    target-branch-filter:
        reason: "Skip reason: Target branch matches filter"
        description: "The agent supports target branch filtering. Reviews are automatically skipped when target branch matches. Contact administrator for settings adjustment if smart review is needed."
        item-title: "Target Branch Pattern"
    source-branch-filter:
        reason: "Skip reason: Source branch matches filter"
        description: "The agent supports source branch filtering. Reviews are automatically skipped when source branch matches. Contact administrator for settings adjustment if smart review is needed."
        item-title: "Source Branch Pattern"
    comment-title: "### ⚠️ Code Review Skipped"
skip-review-codeup:
    file-limit:
        reason: "Skip reason: Number of changed files exceeds limit"
        description: "Too many code changes to review. Please reduce the merge request size and try again."
        item-title: "Changed Files"
        item-description: "%d (Maximum limit %d files)"
    line-limit:
        reason: "Skip reason: Number of changed lines exceeds limit"
        description: "Too many code changes to review. Please reduce the merge request size and try again."
        item-title: "Changed Lines"
        item-description: "%d (Maximum limit %d lines)"
    commit-limit:
        reason: "Skip reason: Number of commits exceeds limit"
        description: "Too many code changes to review. Please reduce the merge request size and try again."
        item-title: "Commit Count"
        item-description: "%d (Maximum limit %d)"
    title-keyword-filter:
        reason: "Skip reason: Title contains filtered keywords"
        description: "Title keyword filtering triggered, review automatically skipped. Please adjust review settings if smart review is needed."
        item-title: "Keywords"
    target-branch-filter:
        reason: "Skip reason: Target branch matches filter"
        description: "Target branch filtering triggered, review automatically skipped. Please adjust review settings if smart review is needed."
        item-title: "Target Branch Pattern"
    source-branch-filter:
        reason: "Skip reason: Source branch matches filter"
        description: "Source branch filtering triggered, review automatically skipped. Please adjust review settings if smart review is needed."
        item-title: "Source Branch Pattern"
    comment-title: "### ⚠️ Code Review Skipped"