#!/usr/bin/env bash
set -e

APP_NAME_TEMP=lingma-agent-controller
PWD=`pwd`

COMPILE_GOOS=$1
COMPILE_GOARCH=$2

build_time=$(date '+%Y-%m-%d %T')
build_info="${build_time} $(go version | cut -d ' ' -f 3- | sed 's#/#_#g')"

echo "build_info : $build_info"
echo "current_dir: $PWD "

# 这里先默认去Aone的应用名称，若没有再通过用户设置
if [[ $APP_NAME"X" == "X" ]]; then
   APP_NAME=$APP_NAME_TEMP
   echo "==========  no aone appName,manual setting appName: $APP_NAME ==================="
else
   echo "==========  use aone appName: $APP_NAME ==================="
fi

if [[ $BUILD_WORK_PATH"X" != "X" ]]; then
   echo "========== change dir to（compile dir,aone only） : $BUILD_WORK_PATH ==================="
   cd $BUILD_WORK_PATH
fi

echo "========== go build... ==================="

if [ -f ./APP-META/docker-config/environment/target/$APP_NAME ]; then
  rm -f ./APP-META/docker-config/environment/target/$APP_NAME
fi

go env -w GO111MODULE=on
git config --global url."**************************:".insteadOf "https://gitlab.alibaba-inc.com/"
export GOPROXY="http://gomodule-repository.aone.alibaba-inc.com,https://mirrors.aliyun.com/goproxy/,https://goproxy.cn/,http://goproxy.alibaba-inc.com,direct"
go env -w GOPRIVATE="*.alibaba-inc.com"
go env -w GONOPROXY="*.alibaba-inc.com"
go env -w GONOSUMDB="*.alibaba-inc.com"
go mod tidy

go env

#go build -o ./APP-META/docker-config/environment/target/$APP_NAME ./main.go

# set default go arch amd64, other params arm64
SYSTEM_DEFAULT_GOARCH=$(go env GOARCH)

if [ $COMPILE_GOARCH"X" == "X" ]; then
  if [ $SYSTEM_DEFAULT_GOARCH"X" == "X" ]; then
    COMPILE_GOARCH="amd64"
  else
    COMPILE_GOARCH=$SYSTEM_DEFAULT_GOARCH
  fi
fi

# GOOS in (Darwin,linux,windows)
if [[ $COMPILE_GOOS"X" != "X" ]]; then
  echo "========GOOS: $COMPILE_GOOS , Set GOARCH: $COMPILE_GOARCH ,SYSTEM GOARCH(Default): $SYSTEM_DEFAULT_GOARCH =========="
  CGO_ENABLED=0 GOOS=$COMPILE_GOOS GOARCH=$COMPILE_GOARCH go build -o ./APP-META/docker-config/environment/target/$APP_NAME ./main.go
else
  echo "========GOOS: $(uname) ,SYSTEM GOARCH(Default): $SYSTEM_DEFAULT_GOARCH =========="
  go build -o ./APP-META/docker-config/environment/target/$APP_NAME ./main.go
fi

echo "========== grant to golang-web-sample    ==================="
chmod +x ./APP-META/docker-config/environment/target/$APP_NAME
echo "========== finished    ==================="
exit 0