# 基于基础镜像
# 本地测试拉取，平台包含linux/amd64、linux/arm64，docker pull --platform=linux/arm64  {镜像地址}
# 检查是包含的平台，docker manifest inspect {镜像地址} ，查看platform数据即可
FROM reg.docker.alibaba-inc.com/amwp/kubeone-base-amd:2.4_b63b97d5_2025-03-16

# dockerfile查找顺序，'Dockerfile_daily_testing', 'Dockerfile_daily', 'Dockerfile_testing', 'Dockerfile'
# 指定运行时的系统环境变量(APP_NAME)，注意这里用的是对接Aone的应用名称，才能复制
ENV APP_NAME=lingma-agent-controller
ENV ENV_TYPE=testing
ENV APP_HOME=/home/<USER>/$APP_NAME
ENV TENGINE_HOME="/opt/taobao/tengine"
# 指定预发环境域名，创建身份时返回完整的webhook url
ENV SYSTEM_DOMAIN=pre-lingma-agent-controller.aliyun-inc.com
# OXS区不使用控制台，用户身份可以mock
ENV MOCK_USER_HEADER=true

RUN yum update --exclude=kernel -y && yum install -y tengine-proxy-2.5.12 taobao-cronolog-1.6.2 -b current && \
    [ -f "${TENGINE_HOME}/bin/setup_services.sh" ] && sudo -u admin -E -H bash ${TENGINE_HOME}/bin/setup_services.sh /home/<USER>/cai

COPY environment/ $APP_HOME/
COPY config/server.toml.example /tmp/config/server.toml
COPY --chown=admin:admin config/nginx-proxy.conf /home/<USER>/cai/conf/nginx-proxy.conf

RUN chmod +x $APP_HOME/bin/appctl.sh && \
    mkdir -p $APP_HOME/logs && \
    mkdir -p $APP_HOME/templates && \
    mkdir -p $APP_HOME/locales

# 设置工作目录
WORKDIR $APP_HOME

COPY config/templates/ ./target/templates/
COPY config/locales/ ./target/locales/


# 运行应用
#CMD ["./bin/agent-controller", "server", "run", "-c", "/tmp/config/server.toml"]