skip-review:
    file-limit:
        reason: 跳过原因：改动文件数量超出限定
        description: 智能体支持设置智能评审最大改动文件数量，超过限定数量自动跳过。如需触发智能评审，可联系管理员进行设置。
        item-title: 改动文件数量
        item-description: "%d（限制最大文件数量 %d）"
    line-limit:
        reason: 跳过原因：改动行数超出限定
        description: 智能体支持设置智能评审最大改动行数，超过限定数量自动跳过。如需触发智能评审，可联系管理员进行设置。
        item-title: 改动行数
        item-description: "%d（限制最大行数 %d）"
    commit-limit:
        reason: 跳过原因：Commit 数量超出限定
        description: 智能体支持设置智能评审最大Commit数，超过限定数量自动跳过。如需触发智能评审，可联系管理员进行设置。
        item-title: Commit 数
        item-description: "%d（限制最大数量 %d）"
    title-keyword-filter:
        reason: 跳过原因：标题包含关键词
        description: 智能体支持设置标题关键词过滤，触发关键字自动跳过。如需触发智能评审，可联系管理员进行设置。
        item-title: "关键词"
    target-branch-filter:
        reason: 跳过原因：目标分支匹配
        description: 智能体支持设置目标分支过滤，触发目标分支自动跳过。如需触发智能评审，可联系管理员进行设置。
        item-title: 目标分支匹配规则
    source-branch-filter:
        reason: 跳过原因：来源分支匹配
        description: 智能体支持设置来源分支过滤，触发来源分支自动跳过。如需触发智能评审，可联系管理员进行设置。
        item-title: 来源分支匹配规则
    comment-title: "### ⚠️ 智能评审已跳过"

