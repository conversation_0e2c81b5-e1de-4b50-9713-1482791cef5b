<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会话详情 - 智能体排查系统</title>
    <style>
        /* 保留原有的基础样式 */
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .info-section {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .info-item {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .info-label {
            width: 120px;
            font-weight: bold;
            color: #666;
        }

        .info-value {
            flex: 1;
            padding: 8px 12px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #333;
        }

        .readonly {
            background-color: #eee;
            cursor: not-allowed;
        }

        .back-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-button:hover {
            background-color: #1976D2;
        }

        /* 新增主Tab样式 */
        .main-tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }

        .main-tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            position: relative;
        }

        .main-tab.active {
            color: #2196F3;
            font-weight: bold;
        }

        .main-tab.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: #2196F3;
        }

        /* 子Tab样式 */
        .sub-tabs {
            display: flex;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .sub-tab {
            padding: 8px 15px;
            cursor: pointer;
            border: 1px solid #ddd;
            background-color: white;
            margin-right: 10px;
            border-radius: 3px;
        }

        .sub-tab.active {
            background-color: #2196F3;
            color: white;
            border-color: #2196F3;
        }

        /* 内容区域样式 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .snapshot-content {
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        /* 刷新按钮 */
        .refresh-button {
            float: right;
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .refresh-button:hover {
            background-color: #45a049;
        }

        .snapshot-content {
            padding: 20px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .action-item {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .action-header {
            cursor: pointer;
            padding: 5px;
            user-select: none;
        }

        .action-header:hover {
            background-color: #f0f0f0;
        }

        .toggle-icon {
            float: right;
            font-size: 12px;
            color: #666;
        }

        .action-detail {
            margin-top: 10px;
        }

        pre {
            background: #fff;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 10px 0;
        }

        /* 各个区块的基础样式 */
        .section-block {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }

        /* 基本信息区块 */
        .basic-info {
            background-color: #e3f2fd;
        }

        /* 元数据区块 */
        .metadata-info {
            background-color: #f3e5f5;
        }

        /* 动作记录区块 */
        .actions-info {
            background-color: #f1f8e9;
        }

        /* 上下文信息区块 */
        .context-info {
            background-color: #fff3e0;
        }

        /* JSON 展示区域 */
        .json-section {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 4px;
            margin-top: 10px;
        }

        .json-section pre {
            margin: 0;
            padding: 15px;
            background: transparent;
        }

        /* 动作记录项样式更新 */
        .action-item {
            background: rgba(255, 255, 255, 0.7);
            margin: 10px 0;
            border-radius: 4px;
        }

        .action-header {
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 4px;
        }

        .action-header:hover {
            background: rgba(255, 255, 255, 0.9);
        }

        /* 标题样式 */
        h3 {
            margin: 0;
            color: #333;
            font-size: 16px;
        }
        /* 事件表格样式 */
        .events-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .events-table th,
        .events-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .events-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .events-table tr:hover {
            background-color: #f8f9fa;
        }

        /* 分页器样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px 0;
        }

        .pagination button {
            padding: 8px 15px;
            border: 1px solid #ddd;
            background-color: white;
            cursor: pointer;
            border-radius: 4px;
        }

        .pagination button:disabled {
            background-color: #f5f5f5;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .pagination button:hover:not(:disabled) {
            background-color: #e9ecef;
        }

        .page-info {
            padding: 0 15px;
            color: #666;
        }

        /* 事件详情模态框样式 */
        .event-detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .detail-button {
            padding: 4px 8px;
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .detail-button:hover {
            background-color: #1976D2;
        }
        /* 简要信息样式 */
        .brief-info {
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }

        /* 错误状态 */
        .brief-info-error {
            background-color: #ffebee;
            color: #d32f2f;
        }

        /* 生命周期状态样式 */
        .brief-info-connected {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .brief-info-disconnected {
            background-color: #fafafa;
            color: #757575;
        }

        .brief-info-saved {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        /* 其他可能的生命周期状态样式 */
        .brief-info-running {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        .brief-info-completed {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        /* 大模型消耗相关样式 */
        .brief-info-usage {
            background-color: #fff3e0;
            color: #f57c00;
        }
        /* 移除之前的简要信息样式，添加类型样式 */
        .events-table tr {
            transition: background-color 0.3s;
        }

        /* 类型样式 */
        .type-runtime {
            background-color: #f3f6f9;
        }

        .type-agent {
            background-color: #fdf7ef;
        }

        .type-action {
            background-color: #f5f3fd;
        }

        .type-agent-task {
            background-color: #f0f7f7;
        }

        /* 鼠标悬停效果 */
        .events-table tr:hover {
            background-color: rgba(0, 0, 0, 0.02) !important;
        }

        /* 类型标签样式 */
        .type-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 为不同类型设置不同的标签颜色 */
        .type-runtime .type-badge {
            background-color: #e3f2fd;
            color: #1976d2;
        }

        .type-agent .type-badge {
            background-color: #fff3e0;
            color: #f57c00;
        }

        .type-action .type-badge {
            background-color: #f3e5f5;
            color: #7b1fa2;
        }

        .type-agent-task .type-badge {
            background-color: #e0f2f1;
            color: #00796b;
        }
        /* 行背景色 */
        .type-llm-message {
            background-color: #f2f8ef;  /* 使用偏绿色调，表示消息交互 */
        }

        /* 类型标签样式 */
        .type-llm-message .type-badge {
            background-color: #e8f5e9;
            color: #2e7d32;
        }

        /* 保持其他样式不变 */
        .events-table tr {
            transition: background-color 0.3s;
        }

        .events-table tr:hover {
            background-color: rgba(0, 0, 0, 0.02) !important;
        }
        .back-button {
            display: inline-block;
            padding: 8px 15px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .back-button:hover {
            background-color: #1976D2;
        }
        .json-section {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 4px;
            margin-top: 10px;
        }

        .json-section pre {
            margin: 0;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #333;
        }

        .action-detail pre {
            background: rgba(255, 255, 255, 0.7);
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.5;
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #333;
        }

        /* 保持原有的区块样式 */
        .section-block {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
        }

        .basic-info {
            background-color: #e3f2fd;
        }

        .metadata-info {
            background-color: #f3e5f5;
        }

        .actions-info {
            background-color: #f1f8e9;
        }

        .context-info {
            background-color: #fff3e0;
        }
    </style>
</head>
<body>
<div class="header">
    <h1>会话详情</h1>
</div>

<a href="/admin/ops" class="back-button">返回智能体排查</a>

<div class="info-section">
    <div class="info-item">
        <div class="info-label">用户ID：</div>
        <div class="info-value readonly">{{.UserId}}</div>
    </div>

    <div class="info-item">
        <div class="info-label">会话ID：</div>
        <div class="info-value readonly">{{.SessionId}}</div>
    </div>
</div>

<div class="main-tabs">
    <button class="main-tab active" onclick="switchMainTab('snapshots')">智能体快照数据</button>
    <button class="main-tab" onclick="switchMainTab('events')">事件</button>
    <button class="refresh-button" onclick="refreshData()">刷新数据</button>
</div>

<div id="snapshots" class="tab-content active">
    <div class="sub-tabs" id="snapshotTabs">
        <!-- 子标签页将通过JavaScript动态生成 -->
    </div>
    <div id="snapshotContents">
        <!-- 快照内容将通过JavaScript动态生成 -->
    </div>
</div>

<div id="events" class="tab-content">
    <!-- 事件相关内容将在后续开发 -->
    <div style="padding: 20px;">事件内容开发中...</div>
</div>

<script>
    // 全局变量存储快照数据
    let snapshotData = [];

    // 切换主标签页
    function switchMainTab(tabId) {
        document.querySelectorAll('.main-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        event.target.classList.add('active');
        document.getElementById(tabId).classList.add('active');
    }

    // 切换子标签页
    function switchSubTab(index) {
        document.querySelectorAll('.sub-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.snapshot-content').forEach(content => {
            content.style.display = 'none';
        });

        document.querySelector(`.sub-tab[data-index="${index}"]`).classList.add('active');
        document.querySelector(`.snapshot-content[data-index="${index}"]`).style.display = 'block';
    }

    // 刷新数据
    async function refreshData() {
        try {
            const sessionId = '{{.SessionId}}';
            const userId = '{{.UserId}}';
            const response = await fetch(`/admin/agent-snapshots?sessionId=${sessionId}&pageNumber=1&pageSize=100`, {
                headers: {
                    'X-User-ID': userId
                }
            });
            const data = await response.json();

            if (data.code === 'Ok') {
                snapshotData = data.data.items;
                renderSnapshots();
            }
        } catch (error) {
            console.error('获取数据失败:', error);
        }
    }

    // 渲染快照数据
    function renderSnapshots() {
        if (!snapshotData || snapshotData.length === 0) {
            document.getElementById('snapshotTabs').innerHTML = '<div>暂无数据</div>';
            document.getElementById('snapshotContents').innerHTML = '';
            return;
        }

        const tabsHtml = snapshotData.map((snapshot, index) => `
        <div class="sub-tab ${index === 0 ? 'active' : ''}"
             onclick="switchSubTab(${index})"
             data-index="${index}">
            ${snapshot.agentId}
            <br>
            <small>${formatTimestamp(snapshot.createTimestamp)}</small>
        </div>
    `).join('');

        const contentsHtml = snapshotData.map((snapshot, index) => {
            const metadata = snapshot.data.metadata || {};
            const state = snapshot.data.state || {};
            const actions = state.actions || [];
            const context = state.context || {};

            // 构建基本信息对象
            const basicInfo = {
                agentId: snapshot.agentId,
                sessionId: snapshot.sessionId,
                createTimestamp: formatTimestamp(snapshot.createTimestamp),
                updateTimestamp: formatTimestamp(snapshot.updateTimestamp)
            };

            // 处理元数据
            const metadataForDisplay = {
                agent_name: metadata.agent_name || '-',
                saved_at: formatTimestamp(metadata.saved_at)
            };

            return `
            <div class="snapshot-content"
                 data-index="${index}"
                 style="display: ${index === 0 ? 'block' : 'none'}">

                <div class="section-block basic-info">
                    <h3>基本信息</h3>
                    <div class="json-section">
                        <pre>${formatJsonContent(basicInfo)}</pre>
                    </div>
                </div>

                <div class="section-block metadata-info">
                    <h3>元数据</h3>
                    <div class="json-section">
                        <pre>${formatJsonContent(metadataForDisplay)}</pre>
                    </div>
                </div>

                <div class="section-block actions-info">
                    <h3>动作记录</h3>
                    ${actions.map((action, actionIndex) => {
                const keyInfo = {
                    'Action Type': action.action_type,
                    'Time': formatTimestamp(action.executed_at),
                    'Status': action.status,
                    'Action ID': action.action_id
                };

                const fullAction = {...action};
                delete fullAction.action_type;
                delete fullAction.executed_at;
                delete fullAction.status;
                delete fullAction.action_id;

                return `
                            <div class="action-item">
                                <div class="action-header" onclick="toggleAction(${index}, ${actionIndex})">
                                    ${Object.entries(keyInfo)
                    .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                    .join(' | ')}
                                    <span class="toggle-icon">▶</span>
                                </div>
                                <div class="action-detail" id="action-${index}-${actionIndex}" style="display: none;">
                                    <pre>${formatJsonContent(fullAction)}</pre>
                                </div>
                            </div>
                        `;
            }).join('')}
                </div>

                <div class="section-block context-info">
                    <h3>上下文信息</h3>
                    <div class="json-section">
                        <pre>${formatJsonContent(context)}</pre>
                    </div>
                </div>
            </div>
        `;
        }).join('');

        document.getElementById('snapshotTabs').innerHTML = tabsHtml;
        document.getElementById('snapshotContents').innerHTML = contentsHtml;
    }

    // 添加折叠切换函数
    function toggleAction(snapshotIndex, actionIndex) {
        const detailElement = document.getElementById(`action-${snapshotIndex}-${actionIndex}`);
        const headerElement = detailElement.previousElementSibling;
        const toggleIcon = headerElement.querySelector('.toggle-icon');

        if (detailElement.style.display === 'none') {
            detailElement.style.display = 'block';
            toggleIcon.textContent = '▼';
        } else {
            detailElement.style.display = 'none';
            toggleIcon.textContent = '▶';
        }
    }

    // 格式化时间戳
    function formatTimestamp(timestamp) {
        // 如果是毫秒级时间戳
        if (timestamp > 1000000000000) {
            timestamp = Math.floor(timestamp / 1000);
        }
        return new Date(timestamp * 1000).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    // 事件分页数据
    let eventData = {
        currentPage: 1,
        pageSize: 100,
        totalSize: 0,
        items: []
    };

    // 切换到事件标签页时加载数据
    function switchMainTab(tabId) {
        document.querySelectorAll('.main-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        event.target.classList.add('active');
        document.getElementById(tabId).classList.add('active');

        // 切换到事件标签页时加载事件数据
        if (tabId === 'events') {
            loadEvents(1);
        }
    }

    // 加载事件数据
    async function loadEvents(pageNumber) {
        // 验证页码
        if (pageNumber < 1) pageNumber = 1;
        const totalPages = Math.ceil(eventData.totalSize / eventData.pageSize);
        if (totalPages > 0 && pageNumber > totalPages) {
            pageNumber = totalPages;
        }

        try {
            const sessionId = '{{.SessionId}}';
            const userId = '{{.UserId}}';
            const response = await fetch(
                `/admin/sessions/${sessionId}/events?withIdAsc=true&pageNumber=${pageNumber}&pageSize=${eventData.pageSize}`,
                {
                    headers: {
                        'X-User-ID': userId
                    }
                }
            );
            const data = await response.json();

            if (data.code === 'Ok') {
                eventData.items = data.data.items;
                eventData.currentPage = data.data.pageNumber;
                eventData.totalSize = data.data.totalSize;
                renderEvents();
            } else {
                console.error('加载事件数据失败:', data);
                alert('加载事件数据失败');
            }
        } catch (error) {
            console.error('获取事件数据失败:', error);
            alert('获取事件数据失败');
        }
    }
    // 更新渲染事件列表的函数
    function renderEvents() {
        const eventsContainer = document.getElementById('events');
        const totalPages = Math.ceil(eventData.totalSize / eventData.pageSize);

        const html = `
        <div class="events-container">
            <table class="events-table">
                <thead>
                    <tr>
                        <th>时间</th>
                        <th>事件ID</th>
                        <th>类型</th>
                        <th>简要信息</th>
                        <th>详细数据</th>
                    </tr>
                </thead>
                <tbody>
                    ${eventData.items.map(event => {
            const eventTime = formatTimestamp(event.createTimestamp);
            const eventType = event.property?.type || '-';
            const eventData = event.property?.data || {};
            const briefInfo = getBriefInfo(event);
            const typeClass = getTypeClass(event);

            return `
                            <tr class="${typeClass}">
                                <td>${eventTime}</td>
                                <td>${event.eventId}</td>
                                <td><span class="type-badge">${eventType}</span></td>
                                <td>${briefInfo}</td>
                                <td>
                                    <button onclick="toggleEventData(this)" class="detail-button">
                                        查看数据
                                    </button>
                                    <div class="event-data" style="display: none;">
                                        <pre style="white-space: pre-wrap;">${formatJsonContent(eventData)}</pre>
                                    </div>
                                </td>
                            </tr>
                        `;
        }).join('')}
                </tbody>
            </table>

            <div class="pagination">
                <div class="pagination-controls">
                    <button
                        onclick="loadEvents(1)"
                        ${eventData.currentPage === 1 ? 'disabled' : ''}>
                        首页
                    </button>
                    <button
                        onclick="loadEvents(${eventData.currentPage - 1})"
                        ${eventData.currentPage === 1 ? 'disabled' : ''}>
                        上一页
                    </button>
                    <span class="page-info">
                        第 ${eventData.currentPage} 页 / 共 ${totalPages} 页
                        (总记录数: ${eventData.totalSize})
                    </span>
                    <button
                        onclick="loadEvents(${eventData.currentPage + 1})"
                        ${eventData.currentPage === totalPages ? 'disabled' : ''}>
                        下一页
                    </button>
                    <button
                        onclick="loadEvents(${totalPages})"
                        ${eventData.currentPage === totalPages ? 'disabled' : ''}>
                        末页
                    </button>
                </div>
                <div class="page-size-selector">
                    每页显示：
                    <select onchange="changePageSize(this.value)">
                        <option value="10" ${eventData.pageSize === 10 ? 'selected' : ''}>10条</option>
                        <option value="20" ${eventData.pageSize === 20 ? 'selected' : ''}>20条</option>
                        <option value="50" ${eventData.pageSize === 50 ? 'selected' : ''}>50条</option>
                        <option value="100" ${eventData.pageSize === 100 ? 'selected' : ''}>100条</option>
                    </select>
                </div>
            </div>
        </div>
    `;

        eventsContainer.innerHTML = html;
    }
    // 处理页面大小变化
    function changePageSize(newSize) {
        eventData.pageSize = parseInt(newSize);
        loadEvents(1); // 切换到第一页
    }


    // 切换事件数据显示/隐藏
    function toggleEventData(button) {
        const dataDiv = button.nextElementSibling;
        if (dataDiv.style.display === 'none') {
            dataDiv.style.display = 'block';
            button.textContent = '隐藏数据';
        } else {
            dataDiv.style.display = 'none';
            button.textContent = '查看数据';
        }
    }

    // 关闭事件详情
    function closeEventDetail() {
        const modal = document.querySelector('.event-detail-modal');
        if (modal) {
            modal.remove();
        }
    }

    // 更新获取简要信息的函数
    function getBriefInfo(event) {
        const type = event.property?.type;
        const data = event.property?.data || {};

        switch (type) {
            case 'runtime':
            case 'agent':
                const lifecycle = data.lifecycle || '-';
                const hasError = data.error ? '有错误' : '无错误';
                return `${lifecycle} | ${hasError}`;

            case 'action':
                return `${data.action_type || '-'} ｜ ${data.lifecycle || '-'}`;

            case 'agent_task':
                switch(data.type) {
                    case 'accumulate_llm_usage':
                        return '累计大模型消耗';
                    case 'one_task_llm_usage':
                        return '本次大模型消耗';
                    case 'mr_statistic':
                        return 'MR变更统计';
                    default:
                        return '-';
                }

            default:
                return '-';
        }
    }

    // 获取类型样式
    function getTypeClass(event) {
        const type = event.property?.type;

        switch (type) {
            case 'runtime':
                return 'type-runtime';
            case 'agent':
                return 'type-agent';
            case 'action':
                return 'type-action';
            case 'agent_task':
                return 'type-agent-task';
            case 'llm_message':
                return 'type-llm-message';
            default:
                return '';
        }
    }
    function escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') return '';
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;");
    }

    // 格式化JSON显示，处理HTML内容
    function formatJsonContent(obj) {
        try {
            const formatted = JSON.stringify(obj, (key, value) => {
                if (typeof value === 'string') {
                    return escapeHtml(value);
                }
                return value;
            }, 2);
            return formatted;
        } catch (error) {
            console.error('JSON格式化失败:', error);
            return '格式化失败';
        }
    }
    // 页面加载完成后自动加载数据
    window.onload = function () {
        refreshData();
    };
</script>
</body>
</html>
