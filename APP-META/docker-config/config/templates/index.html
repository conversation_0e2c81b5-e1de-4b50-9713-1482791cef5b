<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/html">
<head>
    <title>{{.PageTitle}}</title>
    <meta charset="UTF-8"/>
    <meta http-equiv="x-dns-prefetch-control" content="on">
    <meta name="keywords" content="{{.AppDesc}}">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="aplus-waiting" content="MAN">
    <link rel="shortcut icon" href type="image/x-icon">
    <link rel="stylesheet" href="{{.CDNPath}}/yunxiao-fe/teamix-ui/{{.TeamixUIVersion}}/style/style/yunxiao-v5.min.css"/>
    <link rel="stylesheet" href="{{.CDNPath}}/yunxiao-fe/agent-fe/{{.HomePageVersion}}/index.css"/>
    <link rel="stylesheet" href="{{.CDNPath}}/ais-fed/navigation-sdk/{{.NavigationSDKVersion}}/next-indexWithoutTeamixUI.css"/>
        <style id="base-theme-color"> :root {
            --color-brand1-1: #eae9fd;
            --color-brand1-2: #e0dffc;
            --color-brand1-3: #aca9f6;
            --color-brand1-4: #7b77f0;
            --color-brand1-5: #645fed;
            --color-brand1-6: #615CED;
            --color-brand1-7: #4e4abf;
            --color-brand1-8: #39368b;
            --color-brand1-9: #2e2b6f;
            --color-brand1-10: #222053
        } </style>
</head>
<body>
<script nonce="{{.Nonce}}">
    window.YUNXIAO_GLOBAL = {
        orgId: "{{.OrganizationId}}",
        host: "//{{.Domain}}",
        userId: "{{.UserId}}",
        username: "{{.Username}}",
        appId: "{{.AppId}}",
        defaultAppId: "{{.AppId}}",
        defaultCredentialType: "self",
        installedAppsIdentities: ["workbench", "lingma", "base"],
    }
    window.cdnPath = '//{{.Domain}}/public/yunxiao-fe/agent-fe/{{.HomePageVersion}}/';
    window.baseLibCdnPath = "//{{.Domain}}/public";
    window.csrfToken = "";
    window.UILessConfig = {
        host: '//{{.Domain}}/uiless',
        appId: "{{.AppId}}",
        env: '{{.Env}}',
        orgId: "{{.OrganizationId}}",
    };
</script>
<script nonce="{{.Nonce}}" src="{{.CDNPath}}/yunxiao-fe/base-lib/{{.CodeLibVersion}}/libs.min.js"></script>
<script nonce="{{.Nonce}}" src="{{.CDNPath}}/yunxiao-fe/teamix-ui/{{.TeamixUIVersion}}/dist/dist/teamix-ui.min.js"></script>
<script nonce="{{.Nonce}}" src="{{.CDNPath}}/ais-fed/navigation-sdk/{{.NavigationSDKVersion}}/next-indexWithoutTeamixUI.js"></script>

<div id="container">
</div>
<script nonce="{{.Nonce}}" src="{{.CDNPath}}/yunxiao-fe/agent-fe/{{.HomePageVersion}}/index.js"></script>
</body>
</html>