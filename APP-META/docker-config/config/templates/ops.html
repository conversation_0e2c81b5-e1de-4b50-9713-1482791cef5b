<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能体排查系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .user-id-section {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
            position: relative;
        }

        .input-group {
            margin-bottom: 15px;
            display: none;
        }

        input {
            padding: 8px;
            width: 200px;
        }

        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-left: 10px;
        }

        button:hover {
            background-color: #45a049;
        }

        .display-user-id {
            margin-top: 20px;
            font-size: 16px;
        }

        .user-id-value {
            font-weight: bold;
            color: #2196F3;
        }

        .config-button {
            background-color: #2196F3;
        }

        .config-button:hover {
            background-color: #1976D2;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border-radius: 4px;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
            z-index: 1000;
        }

        .toast.show {
            opacity: 1;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .data-table th {
            background-color: #f4f4f4;
        }

        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .data-table tr:hover {
            background-color: #f5f5f5;
        }

        .state-Running {
            color: #2196F3;
            font-weight: bold;
        }

        .state-Success {
            color: #4CAF50;
            font-weight: bold;
        }

        .refresh-button {
            background-color: #FF9800;
            margin-top: 20px;
        }

        .refresh-button:hover {
            background-color: #F57C00;
        }

        .external-link {
            color: #2196F3;
            text-decoration: none;
        }

        .external-link:hover {
            text-decoration: underline;
        }
        .action-button {
            background-color: #673AB7;
            color: white;
            padding: 6px 12px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
        }

        .action-button:hover {
            background-color: #5E35B1;
        }

        /* 调整表格中操作按钮列的宽度 */
        .data-table td:last-child,
        .data-table th:last-child {
            width: 180px;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="header">
    <h1>智能体排查系统</h1>
</div>

<div class="user-id-section">
    <div class="display-user-id">
        当前用户ID：<span id="currentUserId" class="user-id-value">检查中...</span>
        <button onclick="showConfig()" id="configButton" class="config-button" style="display: none;">修改配置</button>
    </div>
    <div class="input-group" id="inputGroup">
        <label for="userId">请输入用户ID：</label>
        <input type="text" id="userId" placeholder="输入用户ID">
        <button onclick="setUserId()">确定</button>
    </div>
</div>

<div id="dataSection" style="display: none;">
    <button onclick="refreshData()" class="refresh-button">刷新执行记录</button>
    <div id="tableContainer"></div>
</div>

<div id="toast" class="toast"></div>

<script>
    function showToast(message, duration = 2000) {
        const toast = document.getElementById('toast');
        toast.textContent = message;
        toast.classList.add('show');
        setTimeout(() => {
            toast.classList.remove('show');
        }, duration);
    }

    function initializeUserIdCheck() {
        const storedUserId = localStorage.getItem('userId');
        const currentUserIdSpan = document.getElementById('currentUserId');
        const inputGroup = document.getElementById('inputGroup');
        const configButton = document.getElementById('configButton');
        const dataSection = document.getElementById('dataSection');

        if (storedUserId) {
            currentUserIdSpan.textContent = storedUserId;
            inputGroup.style.display = 'none';
            configButton.style.display = 'inline-block';
            dataSection.style.display = 'block';
            refreshData(); // 自动加载数据
        } else {
            currentUserIdSpan.textContent = '未设置';
            inputGroup.style.display = 'block';
            configButton.style.display = 'none';
            dataSection.style.display = 'none';
        }
    }

    function showConfig() {
        const inputGroup = document.getElementById('inputGroup');
        const userId = localStorage.getItem('userId');
        document.getElementById('userId').value = userId || '';
        inputGroup.style.display = 'block';
    }

    function setUserId() {
        const userIdInput = document.getElementById('userId');
        const userId = userIdInput.value.trim();

        if (!userId) {
            showToast('请输入用户ID');
            return;
        }

        localStorage.setItem('userId', userId);
        initializeUserIdCheck();
        showToast('设置成功');
    }

    function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    async function refreshData() {
        const userId = localStorage.getItem('userId');
        if (!userId) {
            showToast('请先设置用户ID');
            return;
        }

        try {
            const response = await fetch(`/admin/agent-sessions?pageNumber=1&pageSize=100`, {
                headers: {
                    'x-user-id': userId
                }
            });
            const data = await response.json();

            if (data.code === 'Ok') {
                renderTable(data.data.items);
                showToast('数据刷新成功');
            } else {
                showToast('获取数据失败');
            }
        } catch (error) {
            showToast('请求失败：' + error.message);
        }
    }
    
    function renderTable(items) {
        const tableHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>会话名称</th>
                        <th>代理名称</th>
                        <th>仓库名称</th>
                        <th>作者</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>外部链接</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map(item => `
                        <tr>
                            <td>${item.sessionName || '-'}</td>
                            <td>${item.agentName || '-'}</td>
                            <td>${item.repositoryName || '-'}</td>
                            <td>${item.author || '-'}</td>
                            <td class="state-${item.state}">${item.state}</td>
                            <td>${formatTimestamp(item.createTimestamp)}</td>
                            <td>${item.externalLink ?
            `<a href="${item.externalLink}" target="_blank" class="external-link">查看</a>` :
            '-'}</td>
                            <td>
                                <a href="/admin/ops-detail?user-id=${getCurrentUserId()}&session-id=${item.sessionId}"
                                   class="action-button"
                                   target="_blank">查看详细信息</a>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
        document.getElementById('tableContainer').innerHTML = tableHTML;
    }

    // 确保有获取当前用户ID的函数
    function getCurrentUserId() {
        return localStorage.getItem('userId');
    }

    window.onload = initializeUserIdCheck;
</script>
</body>
</html>
