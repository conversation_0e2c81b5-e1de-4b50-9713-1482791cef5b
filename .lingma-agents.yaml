# 评审配置
reviewRules:
  # 启用代码评审
  enable: true
  # 输出设置
  outputSetting:
    # 评审后输出语言 zh-CN / en-US
    language: zh-CN
    # 问题等级设置为 BLOCKER/CRITICAL/MAJOR/MINOR
    problemLevel: MAJOR

    walkThrough:
      enable: true
      collapse: true
      summary: true
      changes: true
      diagram: true
      statistics: true

    report:
      enable: true
      collapse: true
      summary: true
      codeSuggestion: true
      highLevelFeedback: true
      scan: true
      unitTest: true

  # 自定义指令
  pathInstructions:
    - pathSelectors:
        - "**/README.md"
      text: "请认真阅读README，注意是否有typo。"

  # 触发器过滤配置
  triggerFilter:
    # 标题关键词匹配
    titleKeywords:
      - daily
    # 源分支匹配模式
    sourceBranchGlobs:
      - daily-*
    # 目标分支匹配模式
    targetBranchGlobs:
      - autotest-*
    # 文件数量限制，上限100
    fileLimit: 100
    # 提交数量限制，上限100
    commitLimit: 100
    # 代码行数限制，上限10000
    lineLimit: 10000

  # 内容过滤配置
  contentFilter:
    fileGlobs:
      - vendor/*
      - bin/*
      - test/*
      - config/*

  # 工具设置
  toolSetting:
    # SonarQube配置
    sonarQube:
      enable: true
      problemLevel: MAJOR

    # Maven测试配置
    mvnTest:
      enable: true

    # Biome配置
    biome:
      enable: true

    # ESLint配置
    eslint:
      enable: false

    # Golang CI Lint配置
    golangciLint:
      enable: true
      configFile: ""

    # Ruff配置
    ruff:
      enable: true

    # Shell检查配置
    shellCheck:
      enable: true

    # YAML检查配置
    yamlLint:
      enable: true

    # GitLeaks配置
    gitLeaks:
      enable: false

  # 上下文设置
  contextSetting:
    # issue集成
    issue:
      enable: true
