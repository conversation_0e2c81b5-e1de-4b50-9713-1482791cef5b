# 使用官方的Go镜像作为构建环境
FROM golang:1.23.4 AS builder

# 设置工作目录
RUN mkdir -p /workspace/src/gitlab.alibaba-inc.com/LingMaWorks/agent-controller
WORKDIR /workspace/src/gitlab.alibaba-inc.com/LingMaWorks/agent-controller
ENV GOPATH=/workspace

# 复制源代码到工作目录，依赖从vendor目录直接复制
COPY cmd/ cmd/
COPY pkg/ pkg/
COPY test/ test/
COPY main.go main.go
COPY go.mod go.mod
ENV GOPROXY="http://gomodule-repository.aone.alibaba-inc.com,https://mirrors.aliyun.com/goproxy/,https://goproxy.cn/,http://goproxy.alibaba-inc.com,direct"
RUN go mod tidy && go mod vendor

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 GO111MODULE=on go build -a -o /tmp/agent-controller main.go

# 使用官方的Alpine镜像作为运行环境
FROM alpine:latest

# 安装curl
RUN apk add --no-cache curl

# 设置工作目录
RUN mkdir -p /root/agent-controller/bin/templates && mkdir -p /root/agent-controller/bin/locales && mkdir -p /root/agent-controller/bin/statics
WORKDIR /root/agent-controller/bin

COPY config/server.toml.example /tmp/config/server.toml
COPY templates/ ./templates/
COPY locales/ ./locales/
COPY statics/ ./statics/

# 从构建阶段复制二进制文件
COPY --from=builder /tmp/agent-controller ./agent-controller

# 运行应用
CMD ["./agent-controller", "server", "run", "-c", "/tmp/config/server.toml"]