#component.agent-runtime.endpoint AR无损发布会自动更新这个地址，这个地址仅适用于开服流程
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('component','component.agent-runtime.endpoint','http://agent-runtime',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('component','component.sandbox-server.endpoint','http://sandbox-server',0,'*');

INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.yunxiao.endpoint','http://devops-nezha.yunxiao:9080',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.yunxiao.cdn-path','public',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.icon','',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.title','集成智能体',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.description','集成智能体',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.env','dedicated',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.id','0e209be7-51e8-4b8b-b8e7-fd5900e24c5e',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.home-page-version','1.0.1',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.teamix-ui-version','1.5.27',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.navigation.sdk.version','2.3.18',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.app.codelib-version','1.1.0',0,'*');
INSERT INTO "t_config" ("group","config_key","config_value","encrypt","env_tag") VALUES ('system','system.ingress.prefix','/agents',0,'*');

