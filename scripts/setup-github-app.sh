#!/bin/bash

# GitHub App 配置管理脚本
# 用于安全地配置GitHub App的私钥和相关信息到数据库

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 默认配置
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_NAME="${DB_NAME:-agent_controller}"
DB_USER="${DB_USER:-postgres}"
DB_PASSWORD="${DB_PASSWORD:-}"
ENV_TAG="${ENV_TAG:-any}"

# 显示使用说明
show_usage() {
    echo "GitHub App 配置管理脚本"
    echo ""
    echo "用法: $0 <command> [选项]"
    echo ""
    echo "命令:"
    echo "  setup-app        配置传统GitHub App"
    echo "  setup-oidc-app   配置OIDC专用GitHub App"
    echo "  list-apps        列出已配置的GitHub App"
    echo "  remove-app       删除GitHub App配置"
    echo "  test-config      测试配置连接"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  --db-host HOST          数据库主机 (默认: localhost)"
    echo "  --db-port PORT          数据库端口 (默认: 5432)"
    echo "  --db-name NAME          数据库名称 (默认: agent_controller)"
    echo "  --db-user USER          数据库用户 (默认: postgres)"
    echo "  --db-password PASSWORD  数据库密码"
    echo "  --env-tag TAG           环境标签 (默认: any)"
    echo ""
    echo "环境变量:"
    echo "  DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD, ENV_TAG"
    echo ""
    echo "示例:"
    echo "  # 配置传统GitHub App"
    echo "  $0 setup-app"
    echo ""
    echo "  # 配置OIDC专用GitHub App"
    echo "  $0 setup-oidc-app"
    echo ""
    echo "  # 列出已配置的App"
    echo "  $0 list-apps"
    echo ""
    echo "  # 使用自定义数据库连接"
    echo "  $0 setup-oidc-app --db-host mydb.com --db-user myuser"
}

# 检查依赖
check_dependencies() {
    if ! command -v psql &> /dev/null; then
        log_error "psql 未安装，请安装 PostgreSQL 客户端"
        exit 1
    fi
}

# 构建数据库连接字符串
build_db_connection() {
    if [ -n "$DB_PASSWORD" ]; then
        echo "postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:$DB_PORT/$DB_NAME"
    else
        echo "postgresql://$DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
    fi
}

# 测试数据库连接
test_db_connection() {
    local conn_str=$(build_db_connection)
    log_info "测试数据库连接..."
    
    if psql "$conn_str" -c "SELECT 1;" > /dev/null 2>&1; then
        log_info "数据库连接成功"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 执行SQL查询
execute_sql() {
    local sql="$1"
    local conn_str=$(build_db_connection)
    psql "$conn_str" -c "$sql"
}

# 配置GitHub App
setup_github_app() {
    log_info "开始配置GitHub App..."
    
    # 获取App ID
    echo -n "请输入GitHub App ID: "
    read -r app_id
    
    if [[ ! "$app_id" =~ ^[0-9]+$ ]]; then
        log_error "App ID必须是数字"
        exit 1
    fi
    
    # 获取私钥文件路径
    echo -n "请输入GitHub App私钥文件路径: "
    read -r private_key_path
    
    if [ ! -f "$private_key_path" ]; then
        log_error "私钥文件不存在: $private_key_path"
        exit 1
    fi
    
    # 读取私钥内容
    private_key_content=$(cat "$private_key_path")
    
    # 验证私钥格式
    if [[ ! "$private_key_content" =~ "BEGIN RSA PRIVATE KEY" ]] && [[ ! "$private_key_content" =~ "BEGIN PRIVATE KEY" ]]; then
        log_error "私钥文件格式不正确"
        exit 1
    fi
    
    # 获取Client ID（可选）
    echo -n "请输入GitHub App Client ID (可选): "
    read -r client_id
    
    # 获取Client Secret（可选）
    echo -n "请输入GitHub App Client Secret (可选): "
    read -r client_secret
    
    # 获取Webhook Secret（可选）
    echo -n "请输入GitHub App Webhook Secret (可选): "
    read -r webhook_secret
    
    log_info "开始保存配置到数据库..."
    
    # 转义单引号
    escaped_private_key=$(echo "$private_key_content" | sed "s/'/''/g")
    
    # 保存私钥（加密存储）
    log_info "保存GitHub App私钥..."
    execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag) 
                 VALUES ('github', 'github.app.config.$app_id', '$escaped_private_key', 1, '$ENV_TAG')
                 ON CONFLICT (config_key, env_tag) 
                 DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;"
    
    # 保存Client ID
    if [ -n "$client_id" ]; then
        log_info "保存GitHub App Client ID..."
        execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag) 
                     VALUES ('github', 'github.app.client-id.$app_id', '$client_id', 0, '$ENV_TAG')
                     ON CONFLICT (config_key, env_tag) 
                     DO UPDATE SET config_value = EXCLUDED.config_value, gmt_modified = CURRENT_TIMESTAMP;"
    fi
    
    # 保存Client Secret
    if [ -n "$client_secret" ]; then
        log_info "保存GitHub App Client Secret..."
        execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag) 
                     VALUES ('github', 'github.app.client-secret.$app_id', '$client_secret', 1, '$ENV_TAG')
                     ON CONFLICT (config_key, env_tag) 
                     DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;"
    fi
    
    # 保存Webhook Secret
    if [ -n "$webhook_secret" ]; then
        log_info "保存GitHub App Webhook Secret..."
        execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag) 
                     VALUES ('github', 'github.app.webhook-secret.$app_id', '$webhook_secret', 1, '$ENV_TAG')
                     ON CONFLICT (config_key, env_tag) 
                     DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;"
    fi
    
    # 设置为默认App（如果是第一个）
    default_app_id=$(execute_sql "SELECT config_value FROM t_config WHERE config_key = 'github.default.app.id' AND env_tag = '$ENV_TAG';" | tail -n +3 | head -n 1 | xargs)
    
    if [ -z "$default_app_id" ] || [ "$default_app_id" = "" ]; then
        log_info "设置为默认GitHub App..."
        execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag) 
                     VALUES ('github', 'github.default.app.id', '$app_id', 0, '$ENV_TAG')
                     ON CONFLICT (config_key, env_tag) 
                     DO UPDATE SET config_value = EXCLUDED.config_value, gmt_modified = CURRENT_TIMESTAMP;"
    fi
    
    log_info "GitHub App配置完成！"
    log_info "App ID: $app_id"
    log_info "环境标签: $ENV_TAG"
    log_warn "私钥已加密存储在数据库中"
}

# 配置OIDC专用GitHub App
setup_oidc_github_app() {
    log_info "开始配置OIDC专用GitHub App..."
    log_warn "这是专门用于GitHub Actions OIDC令牌交换的GitHub App"

    # 获取App ID
    echo -n "请输入OIDC GitHub App ID: "
    read -r app_id

    if [[ ! "$app_id" =~ ^[0-9]+$ ]]; then
        log_error "App ID必须是数字"
        exit 1
    fi

    # 获取私钥文件路径
    echo -n "请输入OIDC GitHub App私钥文件路径: "
    read -r private_key_path

    if [ ! -f "$private_key_path" ]; then
        log_error "私钥文件不存在: $private_key_path"
        exit 1
    fi

    # 读取私钥内容
    private_key_content=$(cat "$private_key_path")

    # 验证私钥格式
    if [[ ! "$private_key_content" =~ "BEGIN RSA PRIVATE KEY" ]] && [[ ! "$private_key_content" =~ "BEGIN PRIVATE KEY" ]]; then
        log_error "私钥文件格式不正确"
        exit 1
    fi

    # 获取Client ID（可选）
    echo -n "请输入OIDC GitHub App Client ID (可选): "
    read -r client_id

    # 获取Client Secret（可选）
    echo -n "请输入OIDC GitHub App Client Secret (可选): "
    read -r client_secret

    # 获取Webhook Secret（可选）
    echo -n "请输入OIDC GitHub App Webhook Secret (可选): "
    read -r webhook_secret

    log_info "开始保存OIDC配置到数据库..."

    # 转义单引号
    escaped_private_key=$(echo "$private_key_content" | sed "s/'/''/g")

    # 保存OIDC私钥（加密存储）
    log_info "保存OIDC GitHub App私钥..."
    execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag)
                 VALUES ('github', 'github.oidc.app.config.$app_id', '$escaped_private_key', 1, '$ENV_TAG')
                 ON CONFLICT (config_key, env_tag)
                 DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;"

    # 保存OIDC Client ID
    if [ -n "$client_id" ]; then
        log_info "保存OIDC GitHub App Client ID..."
        execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag)
                     VALUES ('github', 'github.oidc.app.client-id.$app_id', '$client_id', 0, '$ENV_TAG')
                     ON CONFLICT (config_key, env_tag)
                     DO UPDATE SET config_value = EXCLUDED.config_value, gmt_modified = CURRENT_TIMESTAMP;"
    fi

    # 保存OIDC Client Secret
    if [ -n "$client_secret" ]; then
        log_info "保存OIDC GitHub App Client Secret..."
        execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag)
                     VALUES ('github', 'github.oidc.app.client-secret.$app_id', '$client_secret', 1, '$ENV_TAG')
                     ON CONFLICT (config_key, env_tag)
                     DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;"
    fi

    # 保存OIDC Webhook Secret
    if [ -n "$webhook_secret" ]; then
        log_info "保存OIDC GitHub App Webhook Secret..."
        execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag)
                     VALUES ('github', 'github.oidc.app.webhook-secret.$app_id', '$webhook_secret', 1, '$ENV_TAG')
                     ON CONFLICT (config_key, env_tag)
                     DO UPDATE SET config_value = EXCLUDED.config_value, encrypt = 1, gmt_modified = CURRENT_TIMESTAMP;"
    fi

    # 设置OIDC App ID
    log_info "设置OIDC GitHub App ID..."
    execute_sql "INSERT INTO t_config (\"group\", config_key, config_value, encrypt, env_tag)
                 VALUES ('github', 'github.oidc.app.id', '$app_id', 0, '$ENV_TAG')
                 ON CONFLICT (config_key, env_tag)
                 DO UPDATE SET config_value = EXCLUDED.config_value, gmt_modified = CURRENT_TIMESTAMP;"

    log_info "OIDC GitHub App配置完成！"
    log_info "OIDC App ID: $app_id"
    log_info "环境标签: $ENV_TAG"
    log_warn "私钥已加密存储在数据库中"
    log_info "此配置专用于GitHub Actions OIDC令牌交换，不会影响现有的GitHub App配置"
}

# 列出已配置的GitHub App
list_github_apps() {
    log_info "查询已配置的GitHub App..."

    echo "传统GitHub App配置:"
    echo "===================="

    # 查询所有传统GitHub App配置
    execute_sql "SELECT
                    SUBSTRING(config_key FROM 'github\.app\.config\.(.+)') as app_id,
                    CASE WHEN encrypt = 1 THEN '[加密]' ELSE config_value END as private_key_status,
                    env_tag,
                    gmt_modified
                 FROM t_config
                 WHERE config_key LIKE 'github.app.config.%'
                 ORDER BY app_id;"

    echo ""
    echo "OIDC专用GitHub App配置:"
    echo "======================="

    # 查询所有OIDC GitHub App配置
    execute_sql "SELECT
                    SUBSTRING(config_key FROM 'github\.oidc\.app\.config\.(.+)') as oidc_app_id,
                    CASE WHEN encrypt = 1 THEN '[加密]' ELSE config_value END as private_key_status,
                    env_tag,
                    gmt_modified
                 FROM t_config
                 WHERE config_key LIKE 'github.oidc.app.config.%'
                 ORDER BY oidc_app_id;"

    echo ""
    echo "默认配置:"
    echo "=========="
    echo "传统默认App ID:"
    execute_sql "SELECT config_value as default_app_id, env_tag
                 FROM t_config
                 WHERE config_key = 'github.default.app.id';"

    echo ""
    echo "OIDC专用App ID:"
    execute_sql "SELECT config_value as oidc_app_id, env_tag
                 FROM t_config
                 WHERE config_key = 'github.oidc.app.id';"
}

# 删除GitHub App配置
remove_github_app() {
    echo -n "请输入要删除的GitHub App ID: "
    read -r app_id
    
    if [[ ! "$app_id" =~ ^[0-9]+$ ]]; then
        log_error "App ID必须是数字"
        exit 1
    fi
    
    log_warn "即将删除GitHub App $app_id 的所有配置"
    echo -n "确认删除? (y/N): "
    read -r confirm
    
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "取消删除"
        exit 0
    fi
    
    log_info "删除GitHub App配置..."
    
    # 删除所有相关配置
    execute_sql "DELETE FROM t_config WHERE config_key IN (
                    'github.app.config.$app_id',
                    'github.app.client-id.$app_id',
                    'github.app.client-secret.$app_id',
                    'github.app.webhook-secret.$app_id'
                 ) AND env_tag = '$ENV_TAG';"
    
    log_info "GitHub App $app_id 配置已删除"
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            --db-host)
                DB_HOST="$2"
                shift 2
                ;;
            --db-port)
                DB_PORT="$2"
                shift 2
                ;;
            --db-name)
                DB_NAME="$2"
                shift 2
                ;;
            --db-user)
                DB_USER="$2"
                shift 2
                ;;
            --db-password)
                DB_PASSWORD="$2"
                shift 2
                ;;
            --env-tag)
                ENV_TAG="$2"
                shift 2
                ;;
            setup-app|setup-oidc-app|list-apps|remove-app|test-config)
                COMMAND="$1"
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 主函数
main() {
    if [ $# -eq 0 ]; then
        show_usage
        exit 1
    fi
    
    parse_args "$@"
    
    if [ -z "$COMMAND" ]; then
        log_error "请指定命令"
        show_usage
        exit 1
    fi
    
    check_dependencies
    
    log_info "数据库连接信息:"
    log_info "  主机: $DB_HOST:$DB_PORT"
    log_info "  数据库: $DB_NAME"
    log_info "  用户: $DB_USER"
    log_info "  环境标签: $ENV_TAG"
    echo
    
    if ! test_db_connection; then
        exit 1
    fi
    
    case $COMMAND in
        setup-app)
            setup_github_app
            ;;
        setup-oidc-app)
            setup_oidc_github_app
            ;;
        list-apps)
            list_github_apps
            ;;
        remove-app)
            remove_github_app
            ;;
        test-config)
            log_info "数据库连接测试通过"
            ;;
        *)
            log_error "未知命令: $COMMAND"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
