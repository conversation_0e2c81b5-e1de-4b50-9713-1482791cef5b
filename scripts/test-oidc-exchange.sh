#!/bin/bash

# GitHub Actions OIDC Token Exchange 测试脚本

set -e

# 配置
SERVICE_URL="${SERVICE_URL:-http://localhost:8080}"
REPOSITORY="${REPOSITORY:-owner/repo}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v curl &> /dev/null; then
        log_error "curl 未安装"
        exit 1
    fi
    
    if ! command -v jq &> /dev/null; then
        log_error "jq 未安装"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 生成模拟的OIDC令牌（仅用于测试）
generate_mock_oidc_token() {
    log_warn "生成模拟OIDC令牌（仅用于测试）"
    
    # 注意：这是一个无效的令牌，仅用于测试API的错误处理
    # 在实际环境中，OIDC令牌由GitHub Actions自动提供
    echo "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************.invalid-signature"
}

# 测试健康检查
test_health_check() {
    log_info "测试健康检查..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/health_response.json \
        "$SERVICE_URL/v1/github-actions/health" || echo "000")
    
    if [ "$response" = "200" ]; then
        log_info "健康检查通过"
        cat /tmp/health_response.json | jq .
    else
        log_error "健康检查失败，HTTP状态码: $response"
        return 1
    fi
}

# 测试获取支持的权限
test_get_permissions() {
    log_info "测试获取支持的权限..."
    
    response=$(curl -s -w "%{http_code}" -o /tmp/permissions_response.json \
        "$SERVICE_URL/v1/github-actions/permissions" || echo "000")
    
    if [ "$response" = "200" ]; then
        log_info "获取权限列表成功"
        cat /tmp/permissions_response.json | jq .
    else
        log_error "获取权限列表失败，HTTP状态码: $response"
        return 1
    fi
}

# 测试OIDC令牌验证
test_oidc_validation() {
    log_info "测试OIDC令牌验证..."
    
    oidc_token=$(generate_mock_oidc_token)
    
    response=$(curl -s -w "%{http_code}" -o /tmp/validate_response.json \
        -X POST "$SERVICE_URL/v1/github-actions/validate-oidc" \
        -H "Content-Type: application/json" \
        -d "{
            \"oidc_token\": \"$oidc_token\",
            \"repository\": \"$REPOSITORY\"
        }" || echo "000")
    
    log_info "OIDC验证响应，HTTP状态码: $response"
    cat /tmp/validate_response.json | jq .
    
    # 预期这会失败，因为是模拟令牌
    if [ "$response" = "200" ]; then
        valid=$(cat /tmp/validate_response.json | jq -r '.valid')
        if [ "$valid" = "false" ]; then
            log_info "OIDC验证正确返回无效（预期行为）"
        else
            log_warn "OIDC验证意外返回有效"
        fi
    fi
}

# 测试令牌交换
test_token_exchange() {
    log_info "测试令牌交换..."
    
    oidc_token=$(generate_mock_oidc_token)
    
    response=$(curl -s -w "%{http_code}" -o /tmp/exchange_response.json \
        -X POST "$SERVICE_URL/v1/github-actions/token-exchange" \
        -H "Content-Type: application/json" \
        -d "{
            \"oidc_token\": \"$oidc_token\",
            \"repository\": \"$REPOSITORY\",
            \"permissions\": {
                \"contents\": \"read\",
                \"pull_requests\": \"write\",
                \"issues\": \"read\"
            }
        }" || echo "000")
    
    log_info "令牌交换响应，HTTP状态码: $response"
    cat /tmp/exchange_response.json | jq .
    
    # 预期这会失败，因为是模拟令牌
    if [ "$response" != "200" ]; then
        log_info "令牌交换正确拒绝无效令牌（预期行为）"
    else
        log_warn "令牌交换意外成功"
    fi
}

# 测试错误处理
test_error_handling() {
    log_info "测试错误处理..."
    
    # 测试空请求
    log_info "测试空请求..."
    response=$(curl -s -w "%{http_code}" -o /tmp/error1_response.json \
        -X POST "$SERVICE_URL/v1/github-actions/token-exchange" \
        -H "Content-Type: application/json" \
        -d "{}" || echo "000")
    
    log_info "空请求响应，HTTP状态码: $response"
    cat /tmp/error1_response.json | jq .
    
    # 测试无效仓库格式
    log_info "测试无效仓库格式..."
    response=$(curl -s -w "%{http_code}" -o /tmp/error2_response.json \
        -X POST "$SERVICE_URL/v1/github-actions/token-exchange" \
        -H "Content-Type: application/json" \
        -d "{
            \"oidc_token\": \"invalid\",
            \"repository\": \"invalid-format\"
        }" || echo "000")
    
    log_info "无效仓库格式响应，HTTP状态码: $response"
    cat /tmp/error2_response.json | jq .
    
    # 测试无效权限
    log_info "测试无效权限..."
    response=$(curl -s -w "%{http_code}" -o /tmp/error3_response.json \
        -X POST "$SERVICE_URL/v1/github-actions/token-exchange" \
        -H "Content-Type: application/json" \
        -d "{
            \"oidc_token\": \"invalid\",
            \"repository\": \"owner/repo\",
            \"permissions\": {
                \"invalid_permission\": \"read\"
            }
        }" || echo "000")
    
    log_info "无效权限响应，HTTP状态码: $response"
    cat /tmp/error3_response.json | jq .
}

# 主函数
main() {
    log_info "开始GitHub Actions OIDC Token Exchange测试"
    log_info "服务URL: $SERVICE_URL"
    log_info "测试仓库: $REPOSITORY"
    
    check_dependencies
    
    echo
    test_health_check || true
    
    echo
    test_get_permissions || true
    
    echo
    test_oidc_validation || true
    
    echo
    test_token_exchange || true
    
    echo
    test_error_handling || true
    
    echo
    log_info "测试完成"
    log_warn "注意：由于使用模拟OIDC令牌，大部分测试预期会失败"
    log_warn "在实际GitHub Actions环境中使用真实OIDC令牌进行测试"
    
    # 清理临时文件
    rm -f /tmp/*_response.json
}

# 显示使用说明
show_usage() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -u, --url URL  设置服务URL (默认: http://localhost:8080)"
    echo "  -r, --repo REPO 设置测试仓库 (默认: owner/repo)"
    echo ""
    echo "环境变量:"
    echo "  SERVICE_URL    服务URL"
    echo "  REPOSITORY     测试仓库"
    echo ""
    echo "示例:"
    echo "  $0"
    echo "  $0 -u https://your-service.com -r myorg/myrepo"
    echo "  SERVICE_URL=https://your-service.com REPOSITORY=myorg/myrepo $0"
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -u|--url)
            SERVICE_URL="$2"
            shift 2
            ;;
        -r|--repo)
            REPOSITORY="$2"
            shift 2
            ;;
        *)
            log_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 运行主函数
main
