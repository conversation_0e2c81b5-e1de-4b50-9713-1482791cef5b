package main

import (
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/cmd"
	i18n "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/in18"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/logger"
	"log/slog"
	"os"
)

func main() {
	logger.InitLog()
	i18n.MustInitI18n()

	err := cmd.Execute(os.Args[1:])
	if err != nil {
		slog.Error("execute command failed. Exit (1)", "error", err)
		os.Exit(1)
	}
}
