package redismock

import (
	"context"
	"fmt"
	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
	acredis "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/redis"
	"sync"
	"testing"
	"time"
)

var r *miniredis.Miniredis
var initFlag bool
var lock = &sync.Mutex{}

func InitStandaloneRedis(t *testing.T) {
	lock.Lock()
	defer lock.Unlock()
	// 全局初始化一次
	if initFlag {
		return
	}

	redisClient, err := acredis.Setup(&redis.Options{
		Addr: fmt.Sprintf("%s:%d", "127.0.0.1", 6379),
	})

	assert.Nil(t, err)

	// 测试case是并行的，这里设置一个全局标识位，防止相互删数据影响
	flag, err := redisClient.Incr(context.Background(), "flush-flag").Result()
	assert.Nil(t, err)

	// 假设30秒内能全部完成初始化调用，且不会出现第二次执行
	err = redisClient.Expire(context.Background(), "flush-flag", 30*time.Second).Err()
	assert.Nil(t, err)

	if flag == 1 {
		fmt.Println("flushing....")
		_, err = redisClient.FlushAll(context.Background()).Result()
		// 不加这个sleep会出来一些奇怪的情况
		time.Sleep(2 * time.Second)
		assert.Nil(t, err)
	}

	fmt.Println("init....")

	initFlag = true
}
