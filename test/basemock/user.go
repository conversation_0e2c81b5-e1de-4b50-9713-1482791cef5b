package basemock

import "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"

type MockUser struct {
	Bid      string
	Uid      string
	LoginUid string
	UserType base.UserType
	Domain   string
	ActBy    string
}

func (u *MockUser) GetBid() string {
	return u.Bid
}

func (u *MockUser) GetUid() string {
	return u.Uid
}

func (u *MockUser) GetLoginUid() string {
	return u.LoginUid
}

func (u *MockUser) GetUserType() base.UserType {
	return u.UserType
}

func (u *MockUser) GetDomain() string {
	return u.Domain
}
func (u *MockUser) GetActBy() string {
	return u.ActBy
}

func (u *MockUser) GetSiteType() base.SiteType {
	return base.SiteTypeUnknown
}
