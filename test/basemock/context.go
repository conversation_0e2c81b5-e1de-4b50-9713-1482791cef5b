package basemock

import (
	"context"
	"github.com/gin-gonic/gin"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/context/base"
	i18n "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/helper/in18"
	"log/slog"
)

type MockContext struct {
	context.Context
	base.UserCtx
	base.PopCtx
	traceId string
	ctx     *gin.Context
	logger  *slog.Logger
}

func (c *MockContext) GetLocale() i18n.Locale {
	return i18n.SimplifiedChinese
}

func (c *MockContext) GetLogger() *slog.Logger {
	if c.logger == nil {
		c.logger = slog.Default()
	}
	return c.logger
}

func (c *MockContext) GetGinContext() *gin.Context {
	return c.ctx
}

func (c *MockContext) IsPopRequest() bool {
	return c.PopCtx != nil
}

func (c *MockContext) GetSource() string {
	if c.PopCtx == nil {
		return "gin"
	}

	return "pop|" + c.PopCtx.GetAction()
}

func (c *MockContext) GetTraceId() string {
	return c.traceId
}

func NewMockContext(userId string) base.Context {
	return &MockContext{
		Context: context.TODO(),
		UserCtx: &MockUser{
			UserType: base.UserTypeNone,
			Uid:      userId,
		},
		PopCtx: &MockPopInfo{
			RequestId: "req-123",
		},
	}
}
