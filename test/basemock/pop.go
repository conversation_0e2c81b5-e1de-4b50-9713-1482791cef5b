package basemock

type MockPopInfo struct {
	CallerUid           string
	CallerBid           string
	CallerParentId      string
	CallerType          string
	RequestId           string
	AccessKeyId         string
	Action              string
	SecurityToken       string
	StsTokenPrincipalId string
	StsTokenRoleId      string
	StsTokenCallerUid   string
	StsTokenCallerBid   string
	AkMfaPresent        string
	SecurityTransport   string
	SourceIp            string
	ClientIp            string
	RamAuthContext      map[string][]string
}

func (p *MockPopInfo) GetRAMAuthContext() map[string][]string {
	return p.RamAuthContext
}

func (p *MockPopInfo) GetAkProven() string {
	return ""
}

func (p *MockPopInfo) GetCallerUid() string {
	return p.CallerUid
}

func (p *MockPopInfo) GetCallerBid() string {
	return p.CallerBid
}

func (p *MockPopInfo) GetCallerParentId() string {
	return p.CallerParentId
}

func (p *MockPopInfo) GetCallerType() string {
	return p.CallerType
}

func (p *MockPopInfo) GetRequestId() string {
	return p.RequestId
}

func (p *MockPopInfo) GetAccessKeyId() string {
	return p.AccessKeyId
}

func (p *MockPopInfo) GetAction() string {
	return p.Action
}

func (p *MockPopInfo) GetSecurityToken() string {
	return p.SecurityToken
}

func (p *MockPopInfo) GetStsTokenPrincipalId() string {
	return p.StsTokenPrincipalId
}

func (p *MockPopInfo) GetStsTokenRoleId() string {
	return p.StsTokenRoleId
}

func (p *MockPopInfo) GetStsTokenCallerUid() string {
	return p.StsTokenCallerUid
}

func (p *MockPopInfo) GetStsTokenCallerBid() string {
	return p.StsTokenCallerBid
}

func (p *MockPopInfo) GetAkMfaPresent() string {
	return p.AkMfaPresent
}

func (p *MockPopInfo) GetSecurityTransport() string {
	return p.SecurityTransport
}

func (p *MockPopInfo) GetSourceIp() string {
	return p.SourceIp
}

func (p *MockPopInfo) GetClientIp() string {
	return p.ClientIp
}
