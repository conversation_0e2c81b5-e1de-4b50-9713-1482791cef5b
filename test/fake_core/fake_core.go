package fakecore

import (
	"context"
	"github.com/prometheus/client_golang/prometheus"
	comncore "gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/common/core"
	"gitlab.alibaba-inc.com/LingMaWorks/agent-controller/pkg/config"
)

var _ comncore.Core = (*fakeCore)(nil)

func NewFakeCore() comncore.Core {
	return &fakeCore{}
}

type fakeCore struct {
}

func (f fakeCore) GetAgentService() comncore.AgentService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetCodePlatformService() comncore.CodePlatformService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetWebhookService() comncore.WebhookService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetSessionService() comncore.SessionService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetAgentRuntimeService() comncore.AgentRuntimeService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetSandboxService() comncore.SandboxService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetIdentityService() comncore.IdentityService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetEventService() comncore.EventService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetCronjobService() comncore.CronjobService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetCallbackService() comncore.CallbackService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetAgentConnectService() comncore.AgentConnectService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetStatsService() comncore.StatsService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetRepositoryService() comncore.RepositoryService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetFeatureService() comncore.FeatureService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetOverviewService() comncore.OverviewService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetAuthorizationService() comncore.AuthorizationService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetUserService() comncore.UserService {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) Start(ctx context.Context) error {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) Started() <-chan struct{} {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) IsReady() bool {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) Stop() {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) Metrics() []prometheus.Collector {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) GetConfig() config.Config {
	//TODO implement me
	panic("implement me")
}

func (f fakeCore) ErrorChan() chan error {
	//TODO implement me
	panic("implement me")
}
