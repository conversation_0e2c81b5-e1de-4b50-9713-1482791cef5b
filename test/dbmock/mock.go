package dbmock

import (
	"database/sql/driver"
	"github.com/DATA-DOG/go-sqlmock"
	"github.com/glebarez/sqlite"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"os"
	"strings"
	"testing"
)

func GetSqliteDb(initSqlFile string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open("file::memory:?cache=shared"), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	sqlDb, err := db.DB()

	if err != nil {
		return nil, err
	}

	// sqlite 不允许并发操作，必须串行，否则会报错 database is locked
	sqlDb.SetMaxOpenConns(1)

	execSQL := func(sqlFilePath string) error {
		bytes, err := os.ReadFile(sqlFilePath)
		if err != nil {
			return err
		}
		for _, stmt := range strings.Split(string(bytes), ";") {
			if strings.TrimSpace(stmt) == "" {
				continue
			}
			if result := db.Exec(stmt); result.Error != nil {
				return nil
			}
		}
		return nil
	}

	if err = execSQL(initSqlFile); err != nil {
		return nil, err
	}

	return db, err
}

func WithMockDb(t *testing.T, fn func(tt *testing.T, mock sqlmock.Sqlmock)) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}
	defer db.Close()

	// 使用mock的数据库连接初始化gorm数据库对象
	_, err = gorm.Open(mysql.New(mysql.Config{
		Conn:                      db,
		SkipInitializeWithVersion: true, // 跳过数据库初始化
	}), &gorm.Config{})
	if err != nil {
		t.Fatalf("an error '%s' was not expected when initiating mock database", err)
	}

	fn(t, mock)
}

type AnyData struct{}

func (a AnyData) Match(v driver.Value) bool {
	return true
}
