# 构建源码语言类型
code.language=go
# go version
build.tools.go=1.23.4
# path to output
build.output=APP-META/docker-config/
#自定义build脚本
build.command=./aone-build.sh
#docker build的参数，根据需要加
build.tools.docker.args=--net host
#定义代码路径
build.gosrc.path=gitlab.alibaba-inc.com:LingMaWorks/agent-controller
#镜像待commitID方便回溯版本，融合需要x86和arm的tag一致，因此这里不能填写了，需要注释掉，非融合态可以放开
#docker.tag=${TIMESTAMP}_${CODE_BRANCH_COMMIT_ID}
