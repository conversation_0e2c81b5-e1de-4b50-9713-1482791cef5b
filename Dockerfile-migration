# 使用 golang:1.24（基于 Debian）
FROM reg.docker.alibaba-inc.com/yunxiao-paas/golang:1.23.8  AS builder

ENV GOPROXY=https://mirrors.aliyun.com/goproxy/,direct

# 安装依赖（使用 apt-get）
RUN apt-get update && apt-get install -y git && apt-get clean

# 安装 migrate CLI
RUN go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@v4.18.3


# 使用 Alpine 作为运行时（轻量级）
FROM reg.docker.alibaba-inc.com/yunxiao-paas/yunxiao-base-alpine:v3.18.0

COPY --from=builder /go/bin/migrate /usr/local/bin/
RUN apk add --no-cache ca-certificates libc6-compat  # 添加证书和运行时依赖
RUN chmod +x /usr/local/bin/migrate  # 确保文件有执行权限

WORKDIR /
COPY migrations /migrations/
CMD ["sh", "-c", "migrate -database \"$DATABASE_URL\" -path /migrations up"]